const express = require('express');
const router = express.Router();
const SupportTicket = require('../../models/SupportTicket');
const User = require('../../models/User');
const logger = require('../../utils/logger');
const { authMiddleware } = require('../../middleware/auth');

// Apply auth middleware to all admin routes
router.use(authMiddleware);

// Middleware to check if user is admin
const requireAdmin = async (req, res, next) => {
  try {
    const userEmail = req.user?.email;
    if (!userEmail) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    const user = await User.findOne({ email: userEmail });
    if (!user || user.userType !== 'Admin') {
      return res.status(403).json({
        success: false,
        message: 'Admin access required'
      });
    }

    req.adminUser = user;
    next();
  } catch (error) {
    logger.error('Admin middleware error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

// Get all support tickets with filtering and pagination
router.get('/', requireAdmin, async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      status,
      priority,
      type,
      assignedTo,
      search,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    const filter = {};
    
    if (status && status !== 'all') {
      filter.status = status;
    }
    
    if (priority && priority !== 'all') {
      filter.priority = priority;
    }
    
    if (type && type !== 'all') {
      filter.type = type;
    }
    
    if (assignedTo && assignedTo !== 'all') {
      filter.assignedTo = assignedTo;
    }
    
    if (search) {
      filter.$or = [
        { subject: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { userEmail: { $regex: search, $options: 'i' } }
      ];
    }

    const sortOptions = {};
    sortOptions[sortBy] = sortOrder === 'desc' ? -1 : 1;

    const skip = (parseInt(page) - 1) * parseInt(limit);

    const tickets = await SupportTicket.find(filter)
      .populate('userId', 'firstName lastName email userType')
      .populate('assignedTo', 'firstName lastName email')
      .sort(sortOptions)
      .skip(skip)
      .limit(parseInt(limit));

    const totalCount = await SupportTicket.countDocuments(filter);

    // Get summary statistics
    const statusStats = await SupportTicket.aggregate([
      { $group: { _id: '$status', count: { $sum: 1 } } }
    ]);

    const priorityStats = await SupportTicket.aggregate([
      { $group: { _id: '$priority', count: { $sum: 1 } } }
    ]);

    const typeStats = await SupportTicket.aggregate([
      { $group: { _id: '$type', count: { $sum: 1 } } }
    ]);

    logger.info(`Retrieved ${tickets.length} support tickets for admin`);

    res.json({
      success: true,
      data: {
        tickets,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(totalCount / parseInt(limit)),
          totalCount,
          limit: parseInt(limit)
        },
        statistics: {
          statusBreakdown: statusStats.reduce((acc, stat) => {
            acc[stat._id] = stat.count;
            return acc;
          }, {}),
          priorityBreakdown: priorityStats.reduce((acc, stat) => {
            acc[stat._id] = stat.count;
            return acc;
          }, {}),
          typeBreakdown: typeStats.reduce((acc, stat) => {
            acc[stat._id] = stat.count;
            return acc;
          }, {})
        }
      }
    });

  } catch (error) {
    logger.error('Error fetching support tickets:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch support tickets',
      error: error.message
    });
  }
});

// Get single support ticket by ID
router.get('/:ticketId', requireAdmin, async (req, res) => {
  try {
    const { ticketId } = req.params;

    const ticket = await SupportTicket.findById(ticketId)
      .populate('userId', 'firstName lastName email userType cognitoId')
      .populate('assignedTo', 'firstName lastName email')
      .populate('comments.createdBy', 'firstName lastName email userType');

    if (!ticket) {
      return res.status(404).json({
        success: false,
        message: 'Support ticket not found'
      });
    }

    logger.info(`Retrieved support ticket ${ticketId} for admin`);

    res.json({
      success: true,
      data: ticket
    });

  } catch (error) {
    logger.error('Error fetching support ticket:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch support ticket',
      error: error.message
    });
  }
});

// Update support ticket
router.patch('/:ticketId', requireAdmin, async (req, res) => {
  try {
    const { ticketId } = req.params;
    const { status, priority, assignedTo, resolution } = req.body;
    const adminUser = req.user.dbUser;

    const ticket = await SupportTicket.findById(ticketId);
    if (!ticket) {
      return res.status(404).json({
        success: false,
        message: 'Support ticket not found'
      });
    }

    const updates = {};
    if (status) updates.status = status;
    if (priority) updates.priority = priority;
    if (assignedTo) updates.assignedTo = assignedTo;
    if (resolution) {
      updates.resolution = resolution;
      if (status === 'Resolved' || status === 'Closed') {
        updates.resolutionDate = new Date();
        // Calculate time to resolution
        const createdAt = new Date(ticket.createdAt);
        const resolutionDate = new Date();
        updates.timeToResolution = Math.round((resolutionDate - createdAt) / (1000 * 60 * 60)); // hours
      }
    }

    const updatedTicket = await SupportTicket.findByIdAndUpdate(
      ticketId,
      updates,
      { new: true }
    ).populate('userId', 'firstName lastName email userType')
     .populate('assignedTo', 'firstName lastName email');

    // Add system comment for status change
    if (status && status !== ticket.status) {
      updatedTicket.comments.push({
        text: `Status changed from ${ticket.status} to ${status} by admin`,
        createdBy: adminUser._id,
        isInternal: true,
        createdAt: new Date()
      });
      await updatedTicket.save();
    }

    logger.info(`Support ticket ${ticketId} updated by admin ${adminUser.email}`);

    res.json({
      success: true,
      data: updatedTicket,
      message: 'Support ticket updated successfully'
    });

  } catch (error) {
    logger.error('Error updating support ticket:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update support ticket',
      error: error.message
    });
  }
});

// Add comment to support ticket
router.post('/:ticketId/comments', requireAdmin, async (req, res) => {
  try {
    const { ticketId } = req.params;
    const { text, isInternal = false } = req.body;
    const adminUser = req.user.dbUser;

    if (!text || text.trim().length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Comment text is required'
      });
    }

    const ticket = await SupportTicket.findById(ticketId);
    if (!ticket) {
      return res.status(404).json({
        success: false,
        message: 'Support ticket not found'
      });
    }

    ticket.comments.push({
      text: text.trim(),
      createdBy: adminUser._id,
      isInternal: isInternal,
      createdAt: new Date()
    });

    await ticket.save();

    const updatedTicket = await SupportTicket.findById(ticketId)
      .populate('userId', 'firstName lastName email userType')
      .populate('assignedTo', 'firstName lastName email')
      .populate('comments.createdBy', 'firstName lastName email userType');

    logger.info(`Comment added to support ticket ${ticketId} by admin ${adminUser.email}`);

    res.json({
      success: true,
      data: updatedTicket,
      message: 'Comment added successfully'
    });

  } catch (error) {
    logger.error('Error adding comment to support ticket:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to add comment',
      error: error.message
    });
  }
});

// Get support ticket statistics for dashboard
router.get('/stats/summary', requireAdmin, async (req, res) => {
  try {
    const totalTickets = await SupportTicket.countDocuments();
    const openTickets = await SupportTicket.countDocuments({ status: 'Open' });
    const inProgressTickets = await SupportTicket.countDocuments({ status: 'InProgress' });
    const resolvedTickets = await SupportTicket.countDocuments({ status: 'Resolved' });
    const criticalTickets = await SupportTicket.countDocuments({ priority: 'Critical' });
    const highPriorityTickets = await SupportTicket.countDocuments({ priority: 'High' });

    // Get recent tickets (last 7 days)
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    const recentTickets = await SupportTicket.countDocuments({
      createdAt: { $gte: sevenDaysAgo }
    });

    // Get average resolution time
    const resolvedWithTime = await SupportTicket.find({
      status: { $in: ['Resolved', 'Closed'] },
      timeToResolution: { $exists: true }
    });

    const avgResolutionTime = resolvedWithTime.length > 0
      ? resolvedWithTime.reduce((sum, ticket) => sum + ticket.timeToResolution, 0) / resolvedWithTime.length
      : 0;

    const stats = {
      totalTickets,
      openTickets,
      inProgressTickets,
      resolvedTickets,
      criticalTickets,
      highPriorityTickets,
      recentTickets,
      avgResolutionTime: Math.round(avgResolutionTime * 100) / 100
    };

    logger.info('Support ticket statistics retrieved for admin dashboard');

    res.json({
      success: true,
      data: stats
    });

  } catch (error) {
    logger.error('Error fetching support ticket statistics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch support ticket statistics',
      error: error.message
    });
  }
});

module.exports = router;
