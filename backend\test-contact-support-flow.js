const dotenv = require('dotenv');
const path = require('path');

// Load environment variables
dotenv.config({
  path: path.resolve(__dirname, '.env.uat'),
  override: false,
});

const mongoose = require('mongoose');
const User = require('./models/User');
const SupportTicket = require('./models/SupportTicket');
const emailService = require('./services/emailService');
const logger = require('./utils/logger');

async function testContactSupportFlow() {
  console.log('🧪 Testing Complete Contact Support Flow\n');

  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    // Test data
    const testEmail = '<EMAIL>';
    const testTicketData = {
      email: testEmail,
      subject: 'Account Status Issue - ACCOUNT_SUSPENDED',
      message: 'I cannot access my account after it was suspended. Please help me understand why and how to resolve this.',
      priority: 'high',
      status: 'ACCOUNT_SUSPENDED',
      errorCode: 'ACCOUNT_SUSPENDED'
    };

    console.log(`📧 Testing with email: ${testEmail}`);

    // Test 1: Find user in database
    console.log('\n1️⃣ Finding user in database...');
    const user = await User.findOne({ email: testEmail });
    
    if (user) {
      console.log(`✅ User found: ${user.email} (Status: ${user.status})`);
    } else {
      console.log(`⚠️ User not found in database`);
    }

    // Test 2: Create support ticket
    console.log('\n2️⃣ Creating support ticket...');
    
    const ticketData = {
      userId: user ? user._id : null,
      subject: testTicketData.subject,
      description: testTicketData.message,
      type: 'Account',
      priority: testTicketData.priority,
      status: 'Open',
      relatedEntities: [{
        entityType: 'AccountStatus',
        entityId: null
      }],
      comments: [{
        text: `Account Status Support Request
        
Status: ${testTicketData.status}
Error Code: ${testTicketData.errorCode}
User Email: ${testTicketData.email}
Priority: ${testTicketData.priority}

User Message:
${testTicketData.message}`,
        createdBy: user ? user._id : null,
        isInternal: false,
        createdAt: new Date()
      }]
    };

    const ticket = new SupportTicket(ticketData);
    await ticket.save();
    
    const ticketNumber = `#${ticket._id.toString().slice(-6).toUpperCase()}`;
    console.log(`✅ Support ticket created: ${ticketNumber}`);

    // Test 3: Send email notification to support team
    console.log('\n3️⃣ Sending email notification to support team...');
    
    try {
      await emailService.sendAccountStatusTicketNotification({
        ticketId: ticket._id,
        userEmail: testTicketData.email,
        subject: testTicketData.subject,
        message: testTicketData.message,
        priority: testTicketData.priority,
        accountStatus: testTicketData.status,
        errorCode: testTicketData.errorCode
      });
      console.log(`✅ Support team notification sent successfully`);
    } catch (emailError) {
      console.log(`❌ Failed to send support team notification: ${emailError.message}`);
    }

    // Test 4: Test contact options
    console.log('\n4️⃣ Testing contact options...');
    
    const contactOptions = {
      phone: '+33 1 23 45 67 89',
      email: '<EMAIL>',
      supportTicket: ticketNumber,
      hours: 'Monday to Friday, 9:00 AM - 6:00 PM'
    };

    console.log('📞 Phone Support:', contactOptions.phone);
    console.log('📧 Email Support:', contactOptions.email);
    console.log('🎫 Support Ticket:', contactOptions.supportTicket);
    console.log('🕒 Support Hours:', contactOptions.hours);

    // Test 5: Verify ticket in database
    console.log('\n5️⃣ Verifying ticket in database...');
    
    const savedTicket = await SupportTicket.findById(ticket._id);
    if (savedTicket) {
      console.log(`✅ Ticket verified in database:`);
      console.log(`   ID: ${savedTicket._id}`);
      console.log(`   Subject: ${savedTicket.subject}`);
      console.log(`   Type: ${savedTicket.type}`);
      console.log(`   Priority: ${savedTicket.priority}`);
      console.log(`   Status: ${savedTicket.status}`);
      console.log(`   Comments: ${savedTicket.comments.length}`);
    } else {
      console.log(`❌ Ticket not found in database`);
    }

    // Test 6: Clean up test ticket
    console.log('\n6️⃣ Cleaning up test ticket...');
    await SupportTicket.findByIdAndDelete(ticket._id);
    console.log(`✅ Test ticket deleted`);

    console.log('\n🎉 Contact Support Flow Test Completed!');
    console.log('\n📝 Test Summary:');
    console.log('✅ User lookup: Working');
    console.log('✅ Ticket creation: Working');
    console.log('✅ Email notification: Working');
    console.log('✅ Contact options: Available');
    console.log('✅ Database persistence: Working');

    console.log('\n🧪 Frontend Integration Test:');
    console.log('1. Go to AccountStatus page (suspended user)');
    console.log('2. Click "Contact Support" button');
    console.log('3. Should see modal with 3 contact options:');
    console.log('   - Phone Support (copy number)');
    console.log('   - Email Support (mailto link)');
    console.log('   - Support Ticket (form)');
    console.log('4. Fill out support ticket form');
    console.log('5. Submit ticket');
    console.log('6. Should see success message with ticket number');
    console.log('7. Support team should receive email notification');

    console.log('\n🎯 Expected User Experience:');
    console.log('- Professional contact options');
    console.log('- Multiple ways to get help');
    console.log('- Ticket tracking capability');
    console.log('- Immediate confirmation');
    console.log('- Support team notification');

  } catch (error) {
    console.error('💥 Test failed:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n✨ Disconnected from MongoDB');
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  testContactSupportFlow()
    .then(() => {
      console.log('\n✨ Test completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Test failed:', error);
      process.exit(1);
    });
}

module.exports = { testContactSupportFlow };
