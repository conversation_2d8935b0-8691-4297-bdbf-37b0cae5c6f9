import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import logoImage from '../assets/logo.jpeg';
import AppName from './AppName';

const HomeHeader = () => {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);
  const location = useLocation();

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 768);
      if (window.innerWidth > 768) {
        setMobileMenuOpen(false);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Toggle mobile menu
  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  // Check if a link is active
  const isActive = (path) => {
    return location.pathname === path;
  };

  return (
    <header className="home-header">
      <div className="home-logo-container">
        <img src={logoImage} alt="MY ENERGY BILL Logo" className="home-logo" />
        <AppName />
      </div>

      {/* Mobile menu toggle button */}
      <button
        className={`home-menu-toggle ${isMobile ? 'visible' : ''}`}
        onClick={toggleMobileMenu}
        aria-label="Toggle menu"
      >
        <span className={mobileMenuOpen ? 'open' : ''}></span>
        <span className={mobileMenuOpen ? 'open' : ''}></span>
        <span className={mobileMenuOpen ? 'open' : ''}></span>
      </button>

      {/* Desktop navigation */}
      <nav className={`home-nav ${isMobile ? 'hidden' : 'desktop-nav'}`}>
        <Link to="/" className={`home-nav-link ${isActive('/') ? 'active' : ''}`}>Homepage</Link>
        <Link to="/features" className={`home-nav-link ${isActive('/features') ? 'active' : ''}`}>Features</Link>
        <Link to="/how-it-works" className={`home-nav-link ${isActive('/how-it-works') ? 'active' : ''}`}>How It Works</Link>
        <Link to="/about" className={`home-nav-link ${isActive('/about') ? 'active' : ''}`}>About Us</Link>
        <Link to="/commitment-charter" className={`home-nav-link ${isActive('/commitment-charter') ? 'active' : ''}`}>Charter</Link>
        <Link to="/contact" className={`home-nav-link ${isActive('/contact') ? 'active' : ''}`}>Contact Us</Link>
      </nav>

      {/* Desktop auth buttons */}
      <div className={`home-auth-buttons ${isMobile ? 'hidden' : 'desktop-auth'}`}>
        <Link
          to="/login"
          className="btn btn-outline"
          style={{
            backgroundColor: 'transparent !important',
            color: '#000000 !important',
            border: '2px solid #000000 !important',
            backgroundImage: 'none !important'
          }}
        >
          Login
        </Link>
        <Link
          to="/signup"
          className="btn btn-black"
          style={{
            backgroundColor: '#000000',
            color: '#ffffff',
            border: '2px solid #000000',
            backgroundImage: 'none',
            padding: '0.75rem 1.5rem',
            borderRadius: '6px',
            textDecoration: 'none',
            display: 'inline-flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontWeight: '700',
            letterSpacing: '1px',
            transition: 'all 0.3s ease'
          }}
        >
          Sign Up
        </Link>
      </div>

      {/* Mobile navigation menu */}
      {mobileMenuOpen && isMobile && (
        <div className="home-mobile-menu">
          <div className="mobile-menu-header">
            <button
              className="mobile-menu-close"
              onClick={() => setMobileMenuOpen(false)}
              aria-label="Close menu"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 16 16">
                <path d="M4.646 4.646a.5.5 0 0 1 .708 0L8 7.293l2.646-2.647a.5.5 0 0 1 .708.708L8.707 8l2.647 2.646a.5.5 0 0 1-.708.708L8 8.707l-2.646 2.647a.5.5 0 0 1-.708-.708L7.293 8 4.646 5.354a.5.5 0 0 1 0-.708z"/>
              </svg>
            </button>
          </div>
          <nav className="mobile-nav">
            <Link to="/"
              className={`mobile-nav-link ${isActive('/') ? 'active' : ''}`}
              onClick={() => setMobileMenuOpen(false)}
            >
              Homepage
            </Link>
            <Link to="/features"
              className={`mobile-nav-link ${isActive('/features') ? 'active' : ''}`}
              onClick={() => setMobileMenuOpen(false)}
            >
              Features
            </Link>
            <Link to="/how-it-works"
              className={`mobile-nav-link ${isActive('/how-it-works') ? 'active' : ''}`}
              onClick={() => setMobileMenuOpen(false)}
            >
              How It Works
            </Link>
            <Link to="/about"
              className={`mobile-nav-link ${isActive('/about') ? 'active' : ''}`}
              onClick={() => setMobileMenuOpen(false)}
            >
              About Us
            </Link>
            <Link to="/commitment-charter"
              className={`mobile-nav-link ${isActive('/commitment-charter') ? 'active' : ''}`}
              onClick={() => setMobileMenuOpen(false)}
            >
              Charter
            </Link>
            <Link to="/contact"
              className={`mobile-nav-link ${isActive('/contact') ? 'active' : ''}`}
              onClick={() => setMobileMenuOpen(false)}
            >
              Contact Us
            </Link>
          </nav>
          <div className="mobile-auth">
            <Link
              to="/login"
              className="btn btn-outline btn-block"
              onClick={() => setMobileMenuOpen(false)}
              style={{
                backgroundColor: 'transparent !important',
                color: '#000000 !important',
                border: '2px solid #000000 !important',
                backgroundImage: 'none !important'
              }}
            >
              Login
            </Link>
            <Link
              to="/signup"
              className="btn btn-black btn-block"
              onClick={() => setMobileMenuOpen(false)}
              style={{
                backgroundColor: '#000000',
                color: '#ffffff',
                border: '2px solid #000000',
                backgroundImage: 'none',
                padding: '0.75rem 1.5rem',
                borderRadius: '6px',
                textDecoration: 'none',
                display: 'inline-flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontWeight: '700',
                letterSpacing: '1px',
                transition: 'all 0.3s ease',
                width: '100%'
              }}
            >
              Sign Up
            </Link>
          </div>
        </div>
      )}
    </header>
  );
};

export default HomeHeader;
