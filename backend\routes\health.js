const express = require('express');
const router = express.Router();
const mongoose = require('mongoose');
const logger = require('../utils/logger');

// Health check endpoint
router.get('/health', (req, res) => {
  res.status(200).json({ status: 'ok', message: 'Server is running' });
});

// MongoDB connection check endpoint
router.get('/mongodb-status', async (req, res) => {
  try {
    logger.info('MongoDB status check requested');

    // Check if MongoDB is connected
    const state = mongoose.connection.readyState;
    const stateMap = {
      0: 'disconnected',
      1: 'connected',
      2: 'connecting',
      3: 'disconnecting'
    };

    logger.info(`MongoDB connection state: ${stateMap[state]} (${state})`);

    if (state === 1) {
      // If connected, try to get a list of collections
      const collections = await mongoose.connection.db.listCollections().toArray();
      const collectionNames = collections.map(c => c.name);

      logger.info('MongoDB collections:', collectionNames);

      // Try to count documents in the invoices collection if it exists
      let invoiceCount = 0;
      if (collectionNames.includes('invoices')) {
        invoiceCount = await mongoose.connection.db.collection('invoices').countDocuments();
        logger.info(`Found ${invoiceCount} documents in invoices collection`);
      }

      res.status(200).json({
        status: 'ok',
        connection: {
          state: stateMap[state],
          stateCode: state,
          database: mongoose.connection.name,
          host: mongoose.connection.host,
          port: mongoose.connection.port
        },
        collections: collectionNames,
        invoices: {
          count: invoiceCount
        }
      });
    } else {
      res.status(503).json({
        status: 'error',
        message: `MongoDB is not connected (state: ${stateMap[state]})`,
        connection: {
          state: stateMap[state],
          stateCode: state
        }
      });
    }
  } catch (error) {
    logger.error('Error checking MongoDB status:', error);
    res.status(500).json({
      status: 'error',
      message: 'Error checking MongoDB status',
      error: error.message
    });
  }
});

module.exports = router;
