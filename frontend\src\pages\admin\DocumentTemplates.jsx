import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import DashboardLayout from '../../components/DashboardLayout';
import Spinner from '../../components/Spinner';
import { showSuccessMessage, showErrorMessage } from '../../utils/toastNotifications';
import logger from '../../utils/logger';
import '../../styles/admin-management.css';

const DocumentTemplates = () => {
  const [loading, setLoading] = useState(true);
  const [templates, setTemplates] = useState([]);
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [showTemplateEditor, setShowTemplateEditor] = useState(false);
  const [showUploadModal, setShowUploadModal] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    fetchTemplates();
  }, []);

  const fetchTemplates = async () => {
    try {
      setLoading(true);
      logger.info('Fetching document templates');

      // Mock data for now - replace with actual API call
      const mockTemplates = [
        {
          _id: '1',
          name: 'PDF Comparison Template',
          type: 'comparison',
          description: 'Template for generating PDF comparisons',
          lastModified: new Date(),
          isActive: true,
          usage: 45
        },
        {
          _id: '2',
          name: 'Mandate Template',
          type: 'mandate',
          description: 'Template for client mandates',
          lastModified: new Date(Date.now() - 86400000),
          isActive: true,
          usage: 23
        },
        {
          _id: '3',
          name: 'Data Authorization Template',
          type: 'authorization',
          description: 'Template for data authorization forms',
          lastModified: new Date(Date.now() - 172800000),
          isActive: false,
          usage: 12
        }
      ];

      setTemplates(mockTemplates);
      logger.info('Document templates fetched successfully');
    } catch (error) {
      logger.error('Error fetching templates:', error);
      showErrorMessage('TEMPLATES_LOAD_FAILED', 'Failed to load document templates');
    } finally {
      setLoading(false);
    }
  };

  const handleEditTemplate = (template) => {
    setSelectedTemplate(template);
    setShowTemplateEditor(true);
  };

  const handleToggleStatus = async (templateId, currentStatus) => {
    try {
      // Mock API call - replace with actual implementation
      const newStatus = !currentStatus;
      
      setTemplates(prev => prev.map(template => 
        template._id === templateId 
          ? { ...template, isActive: newStatus }
          : template
      ));

      showSuccessMessage('TEMPLATE_STATUS_UPDATED', 
        `Template ${newStatus ? 'activated' : 'deactivated'} successfully`);
      
    } catch (error) {
      logger.error('Error updating template status:', error);
      showErrorMessage('STATUS_UPDATE_FAILED', 'Failed to update template status');
    }
  };

  const handleUploadTemplate = () => {
    setShowUploadModal(true);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getTemplateTypeIcon = (type) => {
    switch (type) {
      case 'comparison': return 'fas fa-chart-bar';
      case 'mandate': return 'fas fa-file-signature';
      case 'authorization': return 'fas fa-shield-alt';
      case 'contract': return 'fas fa-file-contract';
      default: return 'fas fa-file-alt';
    }
  };

  const getTemplateTypeBadge = (type) => {
    const badges = {
      comparison: 'type-badge comparison',
      mandate: 'type-badge mandate',
      authorization: 'type-badge authorization',
      contract: 'type-badge contract'
    };
    return badges[type] || 'type-badge default';
  };

  if (loading) {
    return <Spinner fullScreen={true} message="Loading document templates..." size="large" />;
  }

  return (
    <DashboardLayout>
      <div className="admin-management-container">
        {/* Header */}
        <div className="management-header">
          <div className="header-content">
            <h1>Document & Template Settings</h1>
            <p>Manage PDF templates, mandates, and document settings</p>
          </div>
          <div className="header-actions">
            <button 
              className="btn-primary"
              onClick={handleUploadTemplate}
            >
              <i className="fas fa-plus"></i>
              Upload Template
            </button>
          </div>
        </div>

        {/* Templates Grid */}
        <div className="templates-grid">
          {templates.map(template => (
            <div key={template._id} className={`template-card ${!template.isActive ? 'inactive' : ''}`}>
              <div className="template-header">
                <div className="template-icon">
                  <i className={getTemplateTypeIcon(template.type)}></i>
                </div>
                <div className="template-status">
                  <label className="toggle-switch">
                    <input
                      type="checkbox"
                      checked={template.isActive}
                      onChange={() => handleToggleStatus(template._id, template.isActive)}
                    />
                    <span className="toggle-slider"></span>
                  </label>
                </div>
              </div>
              
              <div className="template-content">
                <h3>{template.name}</h3>
                <p>{template.description}</p>
                
                <div className="template-meta">
                  <span className={getTemplateTypeBadge(template.type)}>
                    {template.type}
                  </span>
                  <span className="usage-count">
                    {template.usage} uses
                  </span>
                </div>
                
                <div className="template-footer">
                  <span className="last-modified">
                    Modified: {formatDate(template.lastModified)}
                  </span>
                  <div className="template-actions">
                    <button
                      className="action-btn edit"
                      onClick={() => handleEditTemplate(template)}
                      title="Edit Template"
                    >
                      <i className="fas fa-edit"></i>
                    </button>
                    <button
                      className="action-btn download"
                      title="Download Template"
                    >
                      <i className="fas fa-download"></i>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Settings Section */}
        <div className="settings-section">
          <h2>Document Settings</h2>
          <div className="settings-grid">
            <div className="setting-item">
              <label>Company Logo</label>
              <div className="file-upload-area">
                <i className="fas fa-image"></i>
                <span>Upload company logo for documents</span>
                <button className="btn-upload">Choose File</button>
              </div>
            </div>
            
            <div className="setting-item">
              <label>Legal Mentions</label>
              <textarea
                placeholder="Enter legal mentions to be displayed in documents..."
                rows="4"
                defaultValue="This document is generated automatically by the Energy Bill Comparison Platform. All information is subject to verification and approval."
              />
            </div>
            
            <div className="setting-item">
              <label>Calculation Formulas</label>
              <div className="formula-settings">
                <div className="formula-item">
                  <label>Tax Rate (%)</label>
                  <input type="number" defaultValue="20" min="0" max="100" step="0.1" />
                </div>
                <div className="formula-item">
                  <label>Service Fee (%)</label>
                  <input type="number" defaultValue="2.5" min="0" max="10" step="0.1" />
                </div>
                <div className="formula-item">
                  <label>Currency</label>
                  <select defaultValue="EUR">
                    <option value="EUR">EUR (€)</option>
                    <option value="USD">USD ($)</option>
                    <option value="GBP">GBP (£)</option>
                  </select>
                </div>
              </div>
            </div>
          </div>
          
          <div className="settings-actions">
            <button className="btn-save">
              <i className="fas fa-save"></i>
              Save Settings
            </button>
          </div>
        </div>

        {/* Upload Modal */}
        {showUploadModal && (
          <div className="modal-overlay" onClick={() => setShowUploadModal(false)}>
            <div className="modal-content" onClick={(e) => e.stopPropagation()}>
              <div className="modal-header">
                <h3>Upload New Template</h3>
                <button 
                  className="modal-close"
                  onClick={() => setShowUploadModal(false)}
                >
                  <i className="fas fa-times"></i>
                </button>
              </div>
              <div className="modal-body">
                <div className="upload-form">
                  <div className="form-group">
                    <label>Template Name</label>
                    <input type="text" placeholder="Enter template name..." />
                  </div>
                  
                  <div className="form-group">
                    <label>Template Type</label>
                    <select>
                      <option value="">Select type...</option>
                      <option value="comparison">PDF Comparison</option>
                      <option value="mandate">Mandate</option>
                      <option value="authorization">Data Authorization</option>
                      <option value="contract">Contract</option>
                    </select>
                  </div>
                  
                  <div className="form-group">
                    <label>Description</label>
                    <textarea placeholder="Enter template description..." rows="3" />
                  </div>
                  
                  <div className="form-group">
                    <label>Template File</label>
                    <div className="file-upload-area">
                      <i className="fas fa-upload"></i>
                      <span>Drag and drop file here or click to browse</span>
                      <input type="file" accept=".html,.pdf,.docx" />
                    </div>
                  </div>
                </div>
                
                <div className="modal-actions">
                  <button 
                    className="btn-cancel"
                    onClick={() => setShowUploadModal(false)}
                  >
                    Cancel
                  </button>
                  <button className="btn-confirm">
                    Upload Template
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </DashboardLayout>
  );
};

export default DocumentTemplates;
