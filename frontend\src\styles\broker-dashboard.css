/* Broker Dashboard Styles - Updated for Alignment Fix v2.0 */
.broker-dashboard-container {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
  background: transparent;
  min-height: 100vh;
  color: #000;
  overflow: visible !important; /* Ensure dropdowns are not clipped */
}

/* Header - Fixed Alignment */
.broker-dashboard-container .dashboard-header {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  margin-bottom: 2rem;
  padding: 1.5rem 2rem;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid #000;
  min-height: 80px;
  flex-wrap: nowrap;
  gap: 1rem;
}

.broker-dashboard-container .header-content {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
}

.broker-dashboard-container .header-content h1 {
  font-size: 1.8rem;
  font-weight: 600;
  margin: 0 0 0.25rem 0;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: #000;
  line-height: 1.2;
}

.broker-dashboard-container .header-content h1 i {
  color: #000;
  font-size: 1.6rem;
  flex-shrink: 0;
}

.broker-dashboard-container .header-content p {
  color: #666;
  margin: 0;
  font-size: 0.95rem;
  line-height: 1.4;
}

.broker-dashboard-container .header-actions {
  flex-shrink: 0;
  display: flex !important;
  align-items: center !important;
  justify-content: flex-end;
  height: 100%;
}

.broker-dashboard-container .header-actions .btn-primary {
  background: #000;
  color: #fff;
  border: 1px solid #000;
  padding: 0.75rem 1.25rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  white-space: nowrap;
  height: fit-content;
}

.broker-dashboard-container .header-actions .btn-primary:hover {
  background: #fff;
  color: #000;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Broker Stats Grid - Identical to dashboard-stats.css but with broker- prefix */
.broker-stats-container {
  width: 100%;
  margin-bottom: 25px;
  padding: 0;
  max-width: 100%;
  background-color: transparent;
  overflow: hidden;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

.broker-stats-grid {
  display: grid !important;
  grid-template-columns: repeat(4, 1fr) !important;
  gap: 20px;
  padding: 0;
  width: 100%;
  align-items: stretch;
}

@media (max-width: 1200px) {
  .broker-stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }
}

/* New elegant card design */
.broker-stat-card {
  background: #fff;
  border-radius: 10px;
  padding: 20px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-start;
  transition: all 0.25s ease;
  cursor: pointer;
  position: relative;
  border: 1px solid #000;
  height: 140px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  color: #000;
  overflow: hidden;
  width: 100%;
}

.broker-stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
  background: #f9f9f9;
}

/* Refined icon style */
.broker-stat-card-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  color: #000;
  background: transparent;
}

.broker-stat-card-icon svg {
  width: 30px;
  height: 30px;
}

.broker-stat-card-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
}

.broker-stat-card-title {
  font-size: 14px;
  font-weight: 600;
  color: #000;
  margin: 0 0 5px 0;
  letter-spacing: 0.3px;
}

.broker-stat-card-value {
  font-size: 42px;
  font-weight: 700;
  color: #000;
  margin: 0;
  line-height: 1;
  position: absolute;
  bottom: 20px;
  right: 20px;
}

.broker-stat-card-description {
  font-size: 13px;
  color: #666;
  margin: 5px 0 0 0;
  line-height: 1.4;
  max-width: 90%;
}

/* Dashboard Content Grid */
.dashboard-content-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
  gap: 1.5rem;
}

.dashboard-card {
  background: #fff;
  border: 1px solid #000;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  min-height: 300px;
  display: flex;
  flex-direction: column;
}

.dashboard-card:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
}

.card-header {
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #f3f4f6;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #fff;
}

.card-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #111827;
}

.card-header h3 i {
  color: #000;
}

.btn-link {
  background: none;
  border: none;
  color: #000;
  cursor: pointer;
  font-size: 0.9rem;
  text-decoration: none;
  transition: color 0.3s ease;
  font-weight: 500;
}

.btn-link:hover {
  color: #333;
}

.card-content {
  padding: 2rem;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

/* Specific fix for Quick Actions card */
.quick-actions-card {
  min-height: 350px !important;
}

.quick-actions-card .card-content {
  min-height: 250px !important;
  justify-content: flex-start !important;
}

/* Clients List */
.clients-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.client-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
}

.client-item:hover {
  background: #e9ecef;
  transform: translateY(-1px);
}

.client-info h4 {
  margin: 0 0 0.25rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #2c3e50;
}

.client-info p {
  margin: 0 0 0.25rem 0;
  color: #6c757d;
  font-size: 0.9rem;
}

.client-status {
  color: #27ae60;
  font-size: 0.85rem;
  font-weight: 500;
}

.client-value {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.client-value .value {
  font-size: 1.2rem;
  font-weight: 700;
  color: #27ae60;
}

.client-value .period {
  font-size: 0.85rem;
  color: #6c757d;
}

/* Deals List */
.deals-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.deal-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
}

.deal-item:hover {
  background: #e9ecef;
  transform: translateY(-1px);
}

.deal-info h4 {
  margin: 0 0 0.25rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #2c3e50;
}

.deal-info p {
  margin: 0 0 0.25rem 0;
  color: #6c757d;
  font-size: 0.9rem;
}

.deal-date {
  color: #000;
  font-size: 0.85rem;
  font-weight: 500;
}

.deal-commission {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.5rem;
}

.deal-commission .commission {
  font-size: 1.2rem;
  font-weight: 700;
  color: #27ae60;
}

.status-badge {
  padding: 0.4rem 0.8rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.status-badge.completed {
  background: #d4edda;
  color: #155724;
}

.status-badge.pending {
  background: #fff3cd;
  color: #856404;
}

/* Quick Actions */
.quick-actions {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
  min-height: 200px;
  align-items: stretch;
}

.quick-actions .action-btn {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 0.75rem !important;
  padding: 1.5rem 1rem !important;
  background: #f8f9fa !important;
  border: 1px solid #e9ecef !important;
  border-radius: 8px !important;
  color: #2c3e50 !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  text-decoration: none !important;
  min-height: 100px !important;
  text-align: center !important;
  width: auto !important;
  height: auto !important;
}

.quick-actions .action-btn:hover {
  background: #e9ecef !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
}

.quick-actions .action-btn i {
  font-size: 1.5rem !important;
  color: #000 !important;
  flex-shrink: 0 !important;
  margin-bottom: 0.25rem !important;
}

.quick-actions .action-btn span {
  font-size: 0.9rem !important;
  font-weight: 600 !important;
  color: #2c3e50 !important;
  line-height: 1.2 !important;
  word-wrap: break-word !important;
  max-width: 100% !important;
}

.quick-actions .action-btn.primary-action {
  background: #000 !important;
  color: #fff !important;
  border: 1px solid #000 !important;
}

.quick-actions .action-btn.primary-action:hover {
  background: #333 !important;
  color: #fff !important;
}

.quick-actions .action-btn.primary-action i {
  color: #fff !important;
}

.quick-actions .action-btn.primary-action span {
  color: #fff !important;
}

/* Commission Metrics */
.commission-metrics {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.metric {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 0;
  border-bottom: 1px solid #e9ecef;
}

.metric:last-child {
  border-bottom: none;
}

.metric-label {
  color: #6c757d;
  font-size: 0.95rem;
  font-weight: 500;
}

.metric-value {
  color: #27ae60;
  font-weight: 700;
  font-size: 1.1rem;
}

/* Pipeline Stages */
.pipeline-stages {
  display: flex;
  justify-content: space-between;
  gap: 1rem;
}

.stage {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  flex: 1;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.stage-label {
  color: #6c757d;
  font-size: 0.85rem;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.stage-count {
  font-size: 1.8rem;
  font-weight: 700;
  color: #000;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 3rem 2rem;
  color: #6c757d;
}

.empty-state i {
  font-size: 3rem;
  color: #dee2e6;
  margin-bottom: 1rem;
}

.empty-state p {
  margin: 0 0 1.5rem 0;
  font-size: 1rem;
}

/* Loading Container */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .broker-dashboard-container {
    padding: 1rem;
    background: transparent;
    min-height: calc(100vh - 60px);
  }

  .broker-dashboard-container .dashboard-header {
    flex-direction: column;
    align-items: center;
    gap: 1.5rem;
    text-align: center;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
  }

  .broker-dashboard-container .header-content {
    text-align: center;
    width: 100%;
  }

  .broker-dashboard-container .header-content h1 {
    font-size: 1.6rem;
    justify-content: center;
  }

  .broker-dashboard-container .header-content h1 i {
    font-size: 1.4rem;
  }

  .broker-dashboard-container .header-actions {
    width: 100%;
    justify-content: center;
  }

  .broker-dashboard-container .header-actions .btn-primary {
    padding: 0.7rem 1.3rem;
    font-size: 0.9rem;
  }

  /* Broker Stats Mobile Styles */
  .broker-stats-container {
    padding: 0 10px !important;
    margin-bottom: 20px !important;
    width: calc(100% - 20px) !important;
    box-sizing: border-box !important;
  }

  .broker-stats-container .broker-stats-grid {
    grid-template-columns: 1fr !important;
    gap: 15px !important;
    width: 100% !important;
    box-sizing: border-box !important;
    padding: 0 !important;
    margin: 0 !important;
  }

  .broker-stats-container .broker-stat-card {
    padding: 15px !important;
    height: auto !important;
    min-height: 80px !important;
    width: 100% !important;
    box-sizing: border-box !important;
    overflow: hidden !important;
    display: grid !important;
    grid-template-columns: 1fr auto !important;
    gap: 15px !important;
    align-items: center !important;
    position: relative !important;
  }

  .broker-stats-container .broker-stat-card-content {
    grid-column: 1 !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: flex-start !important;
    justify-content: center !important;
    min-width: 0 !important;
  }

  .broker-stats-container .broker-stat-card-icon {
    width: 32px !important;
    height: 32px !important;
    margin-bottom: 8px !important;
  }

  .broker-stats-container .broker-stat-card-icon svg {
    width: 28px !important;
    height: 28px !important;
  }

  .broker-stats-container .broker-stat-card-title {
    font-size: 14px !important;
    line-height: 1.2 !important;
    margin-bottom: 4px !important;
    word-wrap: break-word !important;
  }

  .broker-stats-container .broker-stat-card-description {
    font-size: 12px !important;
    line-height: 1.3 !important;
    word-wrap: break-word !important;
  }

  .broker-stats-container .broker-stat-card-value {
    grid-column: 2 !important;
    font-size: 36px !important;
    font-weight: 700 !important;
    color: #000 !important;
    margin: 0 !important;
    line-height: 1 !important;
    position: static !important;
    text-align: center !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    min-width: 60px !important;
    height: auto !important;
    bottom: auto !important;
    right: auto !important;
  }

  .header-content h1 {
    font-size: 1.6rem;
  }

  .header-content h1 i {
    font-size: 1.4rem;
  }

  .header-actions .btn-primary {
    padding: 0.7rem 1.3rem;
    font-size: 0.9rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
    margin-bottom: 1.5rem;
  }

  .stat-card {
    padding: 1.5rem;
    flex-direction: row;
    text-align: left;
    gap: 1rem;
  }

  .stat-icon {
    width: 56px;
    height: 56px;
    font-size: 1.3rem;
  }

  .stat-content h3 {
    font-size: 1.8rem;
  }

  .dashboard-content-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .card-header {
    padding: 1.5rem;
  }

  .card-header h3 {
    font-size: 1.1rem;
  }

  .card-content {
    padding: 1.5rem;
  }

  .client-item,
  .deal-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
    padding: 1.5rem;
  }

  .client-value,
  .deal-commission {
    width: 100%;
    align-items: flex-start;
  }

  .quick-actions {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .quick-actions .action-btn {
    padding: 1.2rem !important;
  }

  .quick-actions .action-btn i {
    font-size: 1.3rem !important;
  }

  .quick-actions .action-btn span {
    font-size: 0.9rem !important;
  }

  .pipeline-stages {
    flex-direction: column;
    gap: 1rem;
  }

  .stage {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 8px;
  }

  .stage-label {
    font-size: 0.9rem;
    margin-bottom: 0;
  }

  .stage-count {
    font-size: 1.3rem;
  }
}

/* Small Mobile Screens */
@media (max-width: 480px) {
  .broker-dashboard-container {
    padding: 0.5rem;
  }

  .broker-dashboard-container .dashboard-header {
    padding: 0.75rem;
    margin-bottom: 1rem;
  }

  .broker-stats-container {
    padding: 0 5px !important;
    width: calc(100% - 10px) !important;
  }

  .broker-stats-container .broker-stat-card {
    padding: 12px !important;
    min-height: 70px !important;
    gap: 10px !important;
  }

  .broker-stats-container .broker-stat-card-icon {
    width: 28px !important;
    height: 28px !important;
  }

  .broker-stats-container .broker-stat-card-icon svg {
    width: 24px !important;
    height: 24px !important;
  }

  .broker-stats-container .broker-stat-card-title {
    font-size: 13px !important;
  }

  .broker-stats-container .broker-stat-card-description {
    font-size: 11px !important;
  }

  .broker-stats-container .broker-stat-card-value {
    font-size: 32px !important;
    min-width: 50px !important;
  }

  .header-content h1 {
    font-size: 1.3rem;
  }

  .stats-grid {
    gap: 0.75rem;
    margin-bottom: 1rem;
  }

  .stat-card {
    padding: 0.75rem;
  }

  .stat-icon {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }

  .stat-content h3 {
    font-size: 1.3rem;
  }

  .dashboard-content-grid {
    gap: 0.75rem;
  }

  .card-header {
    padding: 0.75rem;
  }

  .card-content {
    padding: 0.75rem;
  }

  .stage {
    padding: 0.5rem;
  }

  .stage-label {
    font-size: 0.8rem;
  }

  .stage-count {
    font-size: 1.1rem;
  }
}
