.contracts-container {
  padding: 40px;
  width: 100%;
  min-height: calc(100vh - 80px);
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  position: relative;
}

.contracts-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

.contracts-header {
  margin-bottom: 48px;
  text-align: center;
  position: relative;
  z-index: 1;
}

.contracts-header h1 {
  font-size: 36px;
  font-weight: 800;
  margin-bottom: 16px;
  color: #0f172a;
  letter-spacing: -1px;
  background: linear-gradient(135deg, #0f172a 0%, #334155 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.contracts-header p {
  font-size: 18px;
  color: #64748b;
  margin: 0;
  font-weight: 500;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.contracts-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

.contracts-tabs {
  display: flex;
  justify-content: center;
  margin-bottom: 48px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  padding: 8px;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.08),
    0 1px 0px rgba(255, 255, 255, 0.5) inset;
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  position: relative;
  z-index: 1;
}

.tab-button {
  padding: 16px 32px;
  background: none;
  border: none;
  border-radius: 12px;
  font-size: 15px;
  font-weight: 700;
  color: #64748b;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  white-space: nowrap;
  min-width: 140px;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.tab-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s;
}

.tab-button:hover::before {
  left: 100%;
}

.tab-button:hover {
  color: #0f172a;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.tab-button.active {
  color: white;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
  box-shadow:
    0 8px 24px rgba(15, 23, 42, 0.3),
    0 2px 8px rgba(15, 23, 42, 0.2);
  transform: translateY(-2px);
}

.contracts-list {
  display: flex;
  flex-direction: column;
  gap: 24px;
  position: relative;
  z-index: 1;
}

.contract-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  box-shadow:
    0 12px 40px rgba(0, 0, 0, 0.08),
    0 4px 16px rgba(0, 0, 0, 0.04),
    0 1px 0px rgba(255, 255, 255, 0.5) inset;
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.contract-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 5px;
  background: linear-gradient(90deg, #0f172a 0%, #1e293b 50%, #334155 100%);
  box-shadow: 0 2px 8px rgba(15, 23, 42, 0.3);
}

.contract-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.6s;
}

.contract-card:hover::after {
  left: 100%;
}

.contract-card:hover {
  transform: translateY(-6px) scale(1.01);
  box-shadow:
    0 20px 60px rgba(0, 0, 0, 0.12),
    0 8px 24px rgba(0, 0, 0, 0.08),
    0 2px 0px rgba(255, 255, 255, 0.7) inset;
  border-color: rgba(255, 255, 255, 0.4);
}

.contract-header {
  padding: 24px 28px 20px 28px;
  border-bottom: 1px solid rgba(241, 245, 249, 0.8);
  background: linear-gradient(135deg, rgba(248, 250, 252, 0.8) 0%, rgba(255, 255, 255, 0.9) 100%);
  position: relative;
}

.contract-provider {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.provider-name {
  font-weight: 800;
  color: #0f172a;
  font-size: 18px;
  letter-spacing: -0.5px;
  background: linear-gradient(135deg, #0f172a 0%, #334155 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.contract-status {
  padding: 8px 16px;
  border-radius: 30px;
  font-size: 11px;
  font-weight: 800;
  text-transform: uppercase;
  letter-spacing: 1px;
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.15),
    0 1px 0px rgba(255, 255, 255, 0.3) inset;
  position: relative;
  overflow: hidden;
}

.contract-status::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s;
}

.contract-status:hover::before {
  left: 100%;
}

.contract-status.active {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
}

.contract-status.completed {
  background: linear-gradient(135deg, #64748b 0%, #475569 100%);
  color: white;
}

.contract-status.pending {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
}

.contract-number {
  font-size: 20px;
  font-weight: 800;
  margin: 0;
  color: #0f172a;
  letter-spacing: -0.5px;
}

.contract-content {
  padding: 24px 28px;
}

.contract-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
}

.detail-column {
  background: linear-gradient(135deg, rgba(248, 250, 252, 0.6) 0%, rgba(255, 255, 255, 0.8) 100%);
  padding: 20px;
  border-radius: 12px;
  border: 1px solid rgba(241, 245, 249, 0.8);
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.04),
    0 1px 0px rgba(255, 255, 255, 0.5) inset;
  transition: all 0.3s ease;
}

.detail-column:hover {
  transform: translateY(-2px);
  box-shadow:
    0 8px 24px rgba(0, 0, 0, 0.08),
    0 2px 0px rgba(255, 255, 255, 0.7) inset;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid rgba(226, 232, 240, 0.6);
  font-size: 14px;
  transition: all 0.2s ease;
}

.detail-item:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.detail-item:first-child {
  padding-top: 0;
}

.detail-item:hover {
  padding-left: 6px;
}

.detail-label {
  color: #64748b;
  font-weight: 600;
  font-size: 13px;
}

.detail-value {
  font-weight: 700;
  color: #0f172a;
  font-size: 13px;
}

.special-terms {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(147, 197, 253, 0.05) 100%);
  padding: 16px 20px;
  border-radius: 10px;
  border-left: 4px solid #3b82f6;
  font-size: 13px;
  display: flex;
  align-items: center;
  box-shadow:
    0 2px 8px rgba(59, 130, 246, 0.1),
    0 1px 0px rgba(255, 255, 255, 0.5) inset;
  transition: all 0.3s ease;
  margin-top: 4px;
}

.special-terms:hover {
  transform: translateX(4px);
  border-left-width: 6px;
}

.terms-label {
  font-weight: 700;
  color: #1e40af;
  margin-right: 12px;
  font-size: 12px;
}

.terms-value {
  color: #475569;
  font-weight: 500;
  font-size: 12px;
}

.contract-footer {
  padding: 20px 28px;
  border-top: 1px solid rgba(241, 245, 249, 0.8);
  background: linear-gradient(135deg, rgba(248, 250, 252, 0.4) 0%, rgba(255, 255, 255, 0.6) 100%);
  display: flex;
  gap: 12px;
  align-items: center;
}

.btn-view-document,
.btn-renew {
  padding: 14px 24px;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 160px;
  position: relative;
  overflow: hidden;
}

.btn-view-document::before,
.btn-renew::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-view-document:hover::before,
.btn-renew:hover::before {
  left: 100%;
}

.btn-view-document i,
.btn-renew i {
  margin-right: 10px;
  font-size: 14px;
  transition: transform 0.3s ease;
}

.btn-view-document:hover i,
.btn-renew:hover i {
  transform: scale(1.1);
}

.btn-view-document {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(203, 213, 225, 0.8);
  color: #475569;
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.06),
    0 1px 0px rgba(255, 255, 255, 0.5) inset;
}

.btn-view-document:hover {
  background: rgba(255, 255, 255, 1);
  border-color: rgba(148, 163, 184, 0.8);
  color: #0f172a;
  transform: translateY(-2px);
  box-shadow:
    0 8px 24px rgba(0, 0, 0, 0.1),
    0 2px 0px rgba(255, 255, 255, 0.7) inset;
}

.btn-renew {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
  border: none;
  color: white;
  box-shadow:
    0 6px 20px rgba(15, 23, 42, 0.25),
    0 1px 0px rgba(255, 255, 255, 0.1) inset;
}

.btn-renew:hover {
  background: linear-gradient(135deg, #1e293b 0%, #334155 50%, #475569 100%);
  transform: translateY(-3px);
  box-shadow:
    0 10px 32px rgba(15, 23, 42, 0.35),
    0 2px 0px rgba(255, 255, 255, 0.15) inset;
}

.no-contracts {
  text-align: center;
  padding: 60px 24px;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f3f4f6;
}

.no-contracts i {
  font-size: 56px;
  color: #d1d5db;
  margin-bottom: 24px;
}

.no-contracts h3 {
  font-size: 20px;
  margin-bottom: 12px;
  color: #111827;
  text-transform: capitalize;
  font-weight: 600;
}

.no-contracts p {
  color: #6b7280;
  max-width: 400px;
  margin: 0 auto 24px;
  line-height: 1.5;
}

.btn-browse-offers {
  padding: 14px 28px;
  background: linear-gradient(135deg, #111827 0%, #374151 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 15px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn-browse-offers:hover {
  background: linear-gradient(135deg, #374151 0%, #4b5563 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Responsive styles */
@media (max-width: 768px) {
  .contracts-container {
    padding: 24px 16px;
  }

  .contracts-header {
    margin-bottom: 32px;
  }

  .contracts-header h1 {
    font-size: 28px;
  }

  .contracts-tabs {
    flex-direction: column;
    gap: 8px;
    padding: 6px;
  }

  .tab-button {
    padding: 16px 24px;
    min-width: auto;
  }

  .contract-header {
    padding: 20px 16px 16px 16px;
  }

  .contract-content {
    padding: 20px 16px;
  }

  .contract-details {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .contract-footer {
    flex-direction: column;
    gap: 12px;
    padding: 16px;
  }

  .btn-view-document,
  .btn-renew {
    width: 100%;
    justify-content: center;
    min-width: auto;
  }

  .no-contracts {
    padding: 48px 24px;
  }

  .no-contracts i {
    font-size: 52px;
  }
}

@media (max-width: 480px) {
  .contracts-container {
    padding: 16px 12px;
  }

  .contracts-header h1 {
    font-size: 24px;
  }

  .contracts-header p {
    font-size: 16px;
  }

  .contract-header {
    padding: 16px 12px 12px 12px;
  }

  .contract-content {
    padding: 16px 12px;
  }

  .contract-footer {
    padding: 12px;
  }

  .provider-name {
    font-size: 16px;
  }

  .contract-number {
    font-size: 18px;
  }

  .detail-column {
    padding: 16px;
  }

  .detail-item {
    padding: 8px 0;
  }

  .detail-item:first-child {
    padding-top: 0;
  }

  .detail-item:last-child {
    padding-bottom: 0;
  }

  .tab-button {
    padding: 14px 20px;
    font-size: 14px;
  }
}
