// Import patches and polyfills first
import './axios-patch.js'
import './polyfills.js'

import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import { Amplify } from 'aws-amplify'
import amplifyConfig from './config/amplify-config'
import './index.css'
import './styles/app.css'
import './styles/home.css'
import './styles/pages.css'
import App from './App.jsx'
import ErrorBoundary from './components/ErrorBoundary'
import logger from './utils/logger'

// Configure AWS Amplify with error handling
try {
  Amplify.configure(amplifyConfig)
  console.log('AWS Amplify configured successfully')
} catch (error) {
  console.warn('Failed to configure AWS Amplify:', error)
  // Continue without Amplify - the app should still render
}

// Log application startup
logger.info('Application starting up', {
  environment: process.env.NODE_ENV,
  version: process.env.npm_package_version || 'unknown'
})

const rootElement = document.getElementById('root');

if (rootElement) {
  try {
    createRoot(rootElement).render(
      <StrictMode>
        <ErrorBoundary>
          <App />
        </ErrorBoundary>
      </StrictMode>
    );
  } catch (error) {
    console.error('Error rendering React app:', error);
    rootElement.innerHTML = '<h1>Error loading app: ' + error.message + '</h1>';
  }
} else {
  console.error('Root element not found!');
}
