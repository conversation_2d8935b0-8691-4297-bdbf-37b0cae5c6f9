#!/usr/bin/env node

/**
 * Migration script to transition from old hardcoded email service to new template-based service
 * 
 * This script will:
 * 1. Backup the current email service
 * 2. Replace the old service with the new template-based service
 * 3. Test the new service with sample emails
 * 4. Provide rollback instructions if needed
 */

const fs = require('fs').promises;
const path = require('path');
const logger = require('../utils/logger');

class EmailServiceMigration {
  constructor() {
    this.backupDir = path.join(__dirname, '../backups');
    this.servicesDir = path.join(__dirname, '../services');
    this.oldServicePath = path.join(this.servicesDir, 'emailService.js');
    this.newServicePath = path.join(this.servicesDir, 'emailServiceNew.js');
    this.backupServicePath = path.join(this.backupDir, `emailService.backup.${Date.now()}.js`);
  }

  async run() {
    try {
      console.log('🚀 Starting Email Service Migration...\n');

      // Step 1: Create backup directory
      await this.createBackupDirectory();

      // Step 2: Backup current service
      await this.backupCurrentService();

      // Step 3: Test new service
      await this.testNewService();

      // Step 4: Replace old service
      await this.replaceService();

      // Step 5: Verify migration
      await this.verifyMigration();

      console.log('\n✅ Email Service Migration Completed Successfully!');
      console.log('\n📋 Summary:');
      console.log(`   - Old service backed up to: ${this.backupServicePath}`);
      console.log('   - New template-based service is now active');
      console.log('   - Email templates are located in: backend/templates/emails/');
      console.log('\n🔄 To rollback if needed:');
      console.log(`   cp "${this.backupServicePath}" "${this.oldServicePath}"`);

    } catch (error) {
      console.error('❌ Migration failed:', error.message);
      console.log('\n🔄 Rollback instructions:');
      console.log('   1. Restore the backup file if it was created');
      console.log('   2. Check the logs for specific error details');
      process.exit(1);
    }
  }

  async createBackupDirectory() {
    console.log('📁 Creating backup directory...');
    try {
      await fs.mkdir(this.backupDir, { recursive: true });
      console.log('✅ Backup directory created');
    } catch (error) {
      if (error.code !== 'EEXIST') {
        throw error;
      }
      console.log('✅ Backup directory already exists');
    }
  }

  async backupCurrentService() {
    console.log('💾 Backing up current email service...');
    try {
      await fs.copyFile(this.oldServicePath, this.backupServicePath);
      console.log(`✅ Current service backed up to: ${this.backupServicePath}`);
    } catch (error) {
      throw new Error(`Failed to backup current service: ${error.message}`);
    }
  }

  async testNewService() {
    console.log('🧪 Testing new email service...');
    try {
      // Import and test the new service
      const newEmailService = require('../services/emailServiceNew');
      const emailTemplateService = require('../services/emailTemplateService');

      // Test template loading
      const templates = await emailTemplateService.getAvailableTemplates();
      console.log(`✅ Found ${templates.length} email templates:`, templates.join(', '));

      // Test template rendering
      const testTemplate = await emailTemplateService.renderEmail('invitation', {
        subject: 'Test Email',
        inviteeName: 'Test User',
        userTypeDisplay: 'Energy Broker',
        registrationUrl: 'https://example.com/register',
        recipientEmail: '<EMAIL>'
      });

      if (testTemplate.htmlBody && testTemplate.textBody) {
        console.log('✅ Template rendering test passed');
      } else {
        throw new Error('Template rendering failed');
      }

      console.log('✅ New email service tests passed');
    } catch (error) {
      throw new Error(`New service testing failed: ${error.message}`);
    }
  }

  async replaceService() {
    console.log('🔄 Replacing old service with new service...');
    try {
      // Copy new service over old service
      await fs.copyFile(this.newServicePath, this.oldServicePath);
      console.log('✅ Service replacement completed');
    } catch (error) {
      throw new Error(`Failed to replace service: ${error.message}`);
    }
  }

  async verifyMigration() {
    console.log('🔍 Verifying migration...');
    try {
      // Clear require cache to get fresh instance
      delete require.cache[require.resolve('../services/emailService')];
      
      // Test the replaced service
      const emailService = require('../services/emailService');
      
      // Verify it has the new methods
      const requiredMethods = [
        'sendTemplatedEmail',
        'sendInvitationEmail',
        'sendPasswordResetEmail',
        'sendUserSuspendedEmail',
        'sendProfileSubmittedEmail'
      ];

      for (const method of requiredMethods) {
        if (typeof emailService[method] !== 'function') {
          throw new Error(`Missing method: ${method}`);
        }
      }

      console.log('✅ Migration verification passed');
    } catch (error) {
      throw new Error(`Migration verification failed: ${error.message}`);
    }
  }

  async rollback() {
    console.log('🔄 Rolling back email service...');
    try {
      await fs.copyFile(this.backupServicePath, this.oldServicePath);
      console.log('✅ Rollback completed');
    } catch (error) {
      throw new Error(`Rollback failed: ${error.message}`);
    }
  }
}

// CLI interface
if (require.main === module) {
  const migration = new EmailServiceMigration();
  
  const command = process.argv[2];
  
  if (command === 'rollback') {
    migration.rollback()
      .then(() => {
        console.log('✅ Rollback completed successfully');
        process.exit(0);
      })
      .catch(error => {
        console.error('❌ Rollback failed:', error.message);
        process.exit(1);
      });
  } else {
    migration.run();
  }
}

module.exports = EmailServiceMigration;
