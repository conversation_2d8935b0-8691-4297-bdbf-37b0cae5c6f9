import { useState, useRef, useEffect, forwardRef, useImperativeHandle } from 'react';
import invoiceService from '../services/invoice.service';
import { showSuccessMessage, showErrorMessage, showInfoMessage } from '../utils/toastNotifications';

/**
 * Component for uploading multi-page invoices
 */
const MultiPageInvoiceUpload = forwardRef(({ onUploadSuccess, onUploadError, onUploadStart, forceShowExtraction }, ref) => {
  // State for multi-file upload
  const [files, setFiles] = useState([]);
  const [previews, setPreviews] = useState([]);
  const [uploading, setUploading] = useState(false);
  const [extracting, setExtracting] = useState(false);
  const [error, setError] = useState(null);
  const [extractedFields, setExtractedFields] = useState([]);
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);
  const [metadata, setMetadata] = useState({
    invoiceDate: '',
    invoiceNumber: '',
    provider: '',
    energyType: 'electricity',
    pointOfDelivery: '',
    amount: '',
    currency: 'EUR',
    consumption: '',
    notes: ''
  });

  // Handle window resize for responsive layout
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  const fileInputRef = useRef(null);

  // Expose methods to parent component
  useImperativeHandle(ref, () => ({
    handleFinalSubmit: () => handleFinalSubmit()
  }));

  // Handle multiple file selection
  const handleFileChange = (e) => {
    const selectedFiles = Array.from(e.target.files);

    if (selectedFiles.length === 0) return;

    // Validate file types
    const validTypes = ['.pdf', '.jpg', '.jpeg', '.png'];
    const invalidFiles = selectedFiles.filter(file => {
      const extension = '.' + file.name.split('.').pop().toLowerCase();
      return !validTypes.includes(extension);
    });

    if (invalidFiles.length > 0) {
      setError(`Invalid file types: ${invalidFiles.map(f => f.name).join(', ')}. Please use PDF, JPG, or PNG files.`);
      return;
    }

    // Check if mixing PDFs with images
    const pdfFiles = selectedFiles.filter(file => file.type === 'application/pdf');
    const imageFiles = selectedFiles.filter(file => file.type.startsWith('image/'));

    if (pdfFiles.length > 0 && imageFiles.length > 0) {
      setError('Please upload either PDF files OR image files, not both types together.');
      return;
    }

    // Limit to 5 files maximum
    if (selectedFiles.length > 5) {
      setError('Maximum 5 files allowed. Please select fewer files.');
      return;
    }

    setFiles(selectedFiles);
    setError(null);

    // Create previews for image files
    const newPreviews = [];
    selectedFiles.forEach((file, index) => {
      if (file.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = () => {
          newPreviews[index] = reader.result;
          if (newPreviews.filter(p => p).length === imageFiles.length) {
            setPreviews([...newPreviews]);
          }
        };
        reader.readAsDataURL(file);
      } else {
        newPreviews[index] = null;
      }
    });

    if (imageFiles.length === 0) {
      setPreviews(new Array(selectedFiles.length).fill(null));
    }
  };

  // Handle drag and drop
  const handleDragOver = (e) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    
    const droppedFiles = Array.from(e.dataTransfer.files);
    
    // Create a fake event object to reuse handleFileChange logic
    const fakeEvent = {
      target: {
        files: droppedFiles
      }
    };
    
    handleFileChange(fakeEvent);
  };

  // Remove a file from the selection
  const removeFile = (indexToRemove) => {
    const newFiles = files.filter((_, index) => index !== indexToRemove);
    const newPreviews = previews.filter((_, index) => index !== indexToRemove);
    
    setFiles(newFiles);
    setPreviews(newPreviews);
    
    if (newFiles.length === 0) {
      setError(null);
    }
  };

  // Handle metadata changes
  const handleMetadataChange = (e) => {
    const { name, value } = e.target;
    setMetadata(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle form submission for multiple files
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (files.length === 0) {
      setError('Please select at least one file to upload');
      return;
    }

    try {
      // Set loading states
      if (!onUploadStart) {
        setUploading(true);
        setExtracting(true);
      } else {
        setUploading(false);
        setExtracting(false);
      }

      setError(null);
      setExtractedFields([]);

      // Notify parent component that upload has started
      if (onUploadStart) {
        onUploadStart();
      }

      console.log(`Uploading ${files.length} files:`, files.map(f => ({ name: f.name, type: f.type, size: f.size })));

      // Get user info from localStorage
      const cognitoId = localStorage.getItem('cognitoId');
      const userType = localStorage.getItem('userType');
      console.log('User info from localStorage:', { cognitoId, userType });

      if (!cognitoId || !userType) {
        console.warn('Missing user information. This will cause the files to be uploaded to an unknown path.');
      }

      // Upload multiple files with metadata
      const result = await invoiceService.uploadMultipleInvoices(files, metadata);

      console.log('Multi-file upload successful, response:', result);

      // Process the combined extracted data
      if (result.extractedData && Object.keys(result.extractedData).length > 0) {
        console.log('Using combined extracted data from multiple files:', result.extractedData);

        // Track which fields were actually extracted
        const extractedFieldsList = [];
        const fieldsToCheck = ['invoiceNumber', 'invoiceDate', 'provider', 'amount', 'pointOfDelivery', 'consumption'];

        fieldsToCheck.forEach(field => {
          if (
            result.extractedData[field] &&
            result.extractedData[field].toString().trim() !== '' &&
            result.extractedData[field].toString().trim() !== 'null' &&
            result.extractedData[field].toString().trim() !== 'undefined'
          ) {
            console.log(`Field ${field} extracted with value: ${result.extractedData[field]}`);
            extractedFieldsList.push(field);
          }
        });

        // Update the form with extracted data
        const updatedMetadata = { ...metadata };

        if (extractedFieldsList.includes('invoiceNumber')) {
          updatedMetadata.invoiceNumber = result.extractedData.invoiceNumber;
        }
        if (extractedFieldsList.includes('invoiceDate')) {
          updatedMetadata.invoiceDate = result.extractedData.invoiceDate;
        }
        if (extractedFieldsList.includes('provider')) {
          updatedMetadata.provider = result.extractedData.provider;
        }
        if (extractedFieldsList.includes('amount')) {
          updatedMetadata.amount = result.extractedData.amount;
        }
        if (extractedFieldsList.includes('pointOfDelivery')) {
          updatedMetadata.pointOfDelivery = result.extractedData.pointOfDelivery;
        }
        if (extractedFieldsList.includes('consumption')) {
          updatedMetadata.consumption = result.extractedData.consumption;
        }

        setMetadata(updatedMetadata);
        setExtractedFields(extractedFieldsList);

        // Store combined file details
        if (result.files && result.files.length > 0) {
          const combinedFileDetails = {
            files: result.files.map(file => ({
              name: file.originalname,
              size: file.size,
              type: file.mimetype,
              s3Key: file.key,
              s3Bucket: file.bucket,
              publicUrl: file.location
            })),
            totalFiles: result.files.length,
            uploadedAt: new Date().toISOString()
          };

          localStorage.setItem('tempMultiFileDetails', JSON.stringify(combinedFileDetails));
          console.log('Stored multi-file details in localStorage');
        }

        // Store the invoice ID if available
        if (result.invoice && result.invoice.id) {
          localStorage.setItem('tempInvoiceId', result.invoice.id);
          console.log('Stored invoice ID in localStorage:', result.invoice.id);
        }
      } else {
        console.warn('No extracted data available from the backend for multi-file upload.');
        result.extractedData = {};
      }

      // Reset loading states
      setUploading(false);
      setExtracting(false);

      // Call success callback
      setTimeout(() => {
        if (onUploadSuccess) {
          onUploadSuccess({
            message: `Data extracted successfully from ${files.length} file(s)`,
            extractedData: result.extractedData,
            files: result.files,
            invoice: result.invoice
          });
        }
      }, 100);

    } catch (error) {
      console.error('Error uploading multiple files:', error);

      let errorMessage = 'Failed to upload files';
      if (error.response && error.response.data) {
        errorMessage = error.response.data.error || error.response.data.message || errorMessage;
      } else if (error.message) {
        errorMessage = error.message;
      }

      setError(errorMessage);

      if (onUploadError) {
        onUploadError(error);
      }
    } finally {
      setUploading(false);
      setExtracting(false);
    }
  };

  // Handle final submission (same as single file version)
  const handleFinalSubmit = async () => {
    // Implementation similar to single file version
    // This will be called when user confirms the extracted data
    console.log('Final submit for multi-file upload');
    // TODO: Implement final submission logic
  };

  return (
    <div className="invoice-upload-container">
      <h2>Upload Multi-Page Invoice</h2>

      {extractedFields.length > 0 ? (
        // Show extraction form when data is extracted
        <div>Extraction form will be shown here</div>
      ) : (
        <form onSubmit={handleSubmit}>
          {/* Multi-file upload section */}
          <div className="file-upload-section">
            <label>Invoice Files (Multiple Pages)</label>
            <div 
              className="upload-area multi-file-upload" 
              onClick={() => fileInputRef.current.click()}
              onDragOver={handleDragOver}
              onDrop={handleDrop}
            >
              <div className="upload-placeholder">
                <p>Click to upload multiple invoice pages or drag and drop files here</p>
                <p className="upload-hint">
                  📄 Upload up to 5 files (PDF, JPG, PNG)<br/>
                  💡 For multi-page invoices: upload all pages together
                </p>
              </div>
              <input
                type="file"
                style={{ display: 'none' }}
                onChange={handleFileChange}
                accept=".pdf,.jpg,.jpeg,.png"
                multiple
                ref={fileInputRef}
              />
            </div>

            {/* Files preview */}
            {files.length > 0 && (
              <div className="files-preview">
                <h4>Selected Files ({files.length}):</h4>
                <div className="files-grid">
                  {files.map((file, index) => (
                    <div key={index} className="file-preview-item">
                      {previews[index] ? (
                        <img src={previews[index]} alt={`Preview ${index + 1}`} className="file-preview-image" />
                      ) : (
                        <div className="file-preview-placeholder">
                          <span>📄</span>
                          <span>PDF</span>
                        </div>
                      )}
                      <div className="file-info">
                        <p className="file-name">{file.name}</p>
                        <p className="file-size">({(file.size / 1024).toFixed(2)} KB)</p>
                        <button 
                          type="button" 
                          className="remove-file-btn"
                          onClick={() => removeFile(index)}
                        >
                          ✕
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Error display */}
            {error && (
              <div className="error-message">
                {error}
              </div>
            )}

            {/* Submit button */}
            {files.length > 0 && (
              <div className="upload-actions">
                <button type="submit" className="black-button" disabled={uploading}>
                  {uploading ? 'Processing...' : `Upload ${files.length} File(s)`}
                </button>
              </div>
            )}
          </div>
        </form>
      )}

      {/* Extraction indicator */}
      {extracting && !onUploadStart && (
        <div className="extraction-overlay">
          <div className="extraction-content">
            <div className="extraction-spinner"></div>
            <p>Analyzing your {files.length} invoice file(s)...</p>
            <p className="extraction-detail">Combining text from all pages...</p>
          </div>
        </div>
      )}
    </div>
  );
});

export default MultiPageInvoiceUpload;
