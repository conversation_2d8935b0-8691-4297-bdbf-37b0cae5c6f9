/* My Energy Bill - App CSS */

/* ========== VARIABLES ========== */
:root {
  --primary-black: #000000;
  --primary-white: #ffffff;
  --light-gray: #f5f5f5;
  --medium-gray: #e0e0e0;
  --dark-gray: #333333;
  --text-color: #000000;
  --border-color: #e0e0e0;
  --border-radius: 8px;
  --input-height: 48px;
  --button-height: 48px;
  --shadow-color: rgba(0, 0, 0, 0.1);
  --error-color: #ff3b30;
  --success-color: #34c759;
}

/* ========== RESET & BASE STYLES ========== */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* ========== SCROLL-TO-TOP FIX ========== */
/* Ensure scroll-to-top works on ALL elements */
html, body, #root, .App,
.home-container, .app-container, .page-container, .container,
.wrapper, .fullscreen-container, .stepper-container,
.auth-container, .dashboard-content, .authenticated-content,
.authenticated-layout, .wizard-fullscreen, .content-wrapper,
.upload-container, .invoice-upload-container, .form-container,
.step-container, .wizard-container, .info-page-container,
.main-wrapper, .layout-container, .upload-first-invoice-page {
  scroll-behavior: auto !important;
  position: relative !important;
}

/* Override any smooth scrolling that might interfere */
* {
  scroll-behavior: auto !important;
}

/* SPECIFIC FIX for authenticated pages and steppers */
.authenticated-layout,
.authenticated-content,
.fullscreen-container,
.stepper-container,
.wizard-fullscreen,
.upload-first-invoice-page {
  scroll-behavior: auto !important;
  overflow-y: auto !important;
  height: auto !important;
  min-height: 100vh !important;
  position: relative !important;
}

/* Ensure stepper pages start from top */
.stepper-container,
.wizard-fullscreen {
  padding-top: 0 !important;
  margin-top: 0 !important;
}

html {
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
  /* Ensure scroll-to-top works properly */
  scroll-behavior: auto !important;
  position: relative !important;
}

body {
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  background-color: var(--light-gray);
  color: var(--text-color);
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
  /* Ensure scroll-to-top works properly */
  scroll-behavior: auto !important;
  position: relative !important;
}

/* ========== LAYOUT STYLES ========== */
.container {
  min-height: 100vh;
  width: 100%;
  max-width: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  padding: 1rem;
  background-color: #f5f5f5;
  background-image:
    linear-gradient(rgba(255, 255, 255, 0.7), rgba(255, 255, 255, 0.7)),
    url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='100' viewBox='0 0 100 100'%3E%3Cg fill-rule='evenodd'%3E%3Cg fill='%23000000' fill-opacity='0.03'%3E%3Cpath opacity='.5' d='M96 95h4v1h-4v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9zm-1 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  overflow-x: hidden;
  box-sizing: border-box;
}

/* Add a professional gradient overlay */
.container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.02) 0%, rgba(0, 0, 0, 0.05) 100%);
  z-index: -1;
}

/* Add a subtle vignette effect */
.container::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at center, transparent 0%, rgba(0, 0, 0, 0.03) 100%);
  z-index: -1;
}

/* ========== AUTHENTICATION STYLES ========== */
/* Auth container */
.auth-container {
  min-height: 100vh;
  width: 100%;
  max-width: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  padding: 1rem;
  background-color: #f5f5f5;
  overflow-x: hidden;
  box-sizing: border-box;
  background-image:
    linear-gradient(rgba(255, 255, 255, 0.7), rgba(255, 255, 255, 0.7)),
    url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='100' viewBox='0 0 100 100'%3E%3Cg fill-rule='evenodd'%3E%3Cg fill='%23000000' fill-opacity='0.03'%3E%3Cpath opacity='.5' d='M96 95h4v1h-4v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9zm-1 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}

/* Auth card */
.auth-card {
  width: 100%;
  max-width: 800px;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow:
    0 10px 30px rgba(0, 0, 0, 0.08),
    0 5px 15px rgba(0, 0, 0, 0.05),
    0 0 0 1px rgba(0, 0, 0, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.7);
  position: relative;
  z-index: 1;
  backdrop-filter: blur(5px);
  background-image: linear-gradient(to bottom, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.98));
  overflow-x: hidden;
  box-sizing: border-box;
}

/* Auth header */
.auth-header {
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid #eaeaea;
}

/* Auth title */
.auth-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #000;
  margin-top: 1.5rem;
  margin-bottom: 0.5rem;
  text-align: center;
}

/* Auth subtitle */
.auth-subtitle {
  font-size: 0.95rem;
  color: #666;
  text-align: center;
  max-width: 500px;
  margin: 0 auto;
}

/* Auth logo */
.auth-logo {
  width: 80px;
  height: 80px;
  object-fit: contain;
  margin-bottom: 0.5rem;
}

/* Auth content */
.auth-content {
  width: 100%;
  max-width: 600px;
}

/* Loading spinner */
.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  font-size: 1.2rem;
  color: #666;
}

/* Logo container */
.logo-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Logo container in auth pages */
.auth-header .logo-container {
  flex-direction: column;
  text-align: center;
}

/* Auth form */
.auth-form {
  width: 100%;
  max-width: 400px;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: transparent;
  padding: 2.5rem;
  border-radius: 12px;
  position: relative;
  z-index: 1;
}

/* Banner at the top of auth form - removed black border */
.auth-form::before {
  content: none;
}

/* Auth form background is transparent */
.auth-form {
  background-image: none;
}

/* Logo container */
.logo-container {
  margin-bottom: 2rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  text-align: center;
  width: 100%;
}

/* Add subtle shadow to logo */
.logo-container img.logo {
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
  margin-bottom: 1rem;
}

/* App name */
.app-name {
  font-size: 1.8rem;
  font-weight: 700;
  color: #000;
  margin-top: 0.5rem;
  margin-bottom: 1.5rem;
  text-align: center;
  position: relative;
  letter-spacing: 0.5px;
  padding: 0.5rem 0;
  text-shadow: 0px 1px 1px rgba(0, 0, 0, 0.2);
  border-bottom: 2px solid #000;
  display: inline-block;
}

/* ========== FORM ELEMENTS ========== */
/* Form group */
.form-group {
  position: relative;
  width: 100%;
  margin-bottom: 1.25rem;
}

/* Textarea specific styling */
.form-input[type="textarea"],
textarea.form-input {
  min-height: 80px !important;
  max-height: 200px !important;
  height: 80px !important;
  resize: vertical !important;
  line-height: 1.4 !important;
  padding: 12px 20px !important;
  align-items: flex-start !important;
  display: block !important;
  overflow-y: auto !important;
}

/* Textarea with icon */
.input-with-icon textarea.form-input {
  padding-left: 45px !important;
  padding-top: 12px !important;
  padding-bottom: 12px !important;
  display: block !important;
  align-items: flex-start !important;
  min-height: 80px !important;
  height: 80px !important;
}

/* Textarea container override */
.input-with-icon:has(textarea) {
  height: auto !important;
  align-items: flex-start !important;
  min-height: 80px !important;
}

/* Special styling for verification form group */
.form-group p + .verification-icon {
  top: calc(100% - 35px); /* Position relative to the paragraph */
}

/* Form input */
.form-input {
  width: 100%;
  height: var(--input-height);
  padding: 0 20px;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  font-size: 0.95rem;
  background-color: var(--primary-white);
  color: var(--text-color);
  transition: all 0.3s ease;
  margin-bottom: 0.5rem;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
  text-overflow: ellipsis;
  position: relative;
  background-color: rgba(255, 255, 255, 0.8);
  line-height: var(--input-height) !important;
  vertical-align: middle !important;
  display: flex !important;
  align-items: center !important;
  box-sizing: border-box !important;
}

/* Hide number input spinners */
.form-input[type="number"]::-webkit-outer-spin-button,
.form-input[type="number"]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.form-input[type="number"] {
  -moz-appearance: textfield;
}

/* Form input with icon */
.input-with-icon {
  position: relative !important;
  display: flex !important;
  align-items: center !important;
  width: 100% !important;
  height: var(--input-height) !important;
}

.input-with-icon .form-input {
  padding-left: 45px !important;
  line-height: var(--input-height) !important;
  display: flex !important;
  align-items: center !important;
  vertical-align: middle !important;
  text-indent: 0 !important;
  height: var(--input-height) !important;
  margin: 0 !important;
}

.form-input:focus {
  outline: none;
  border-color: var(--primary-black);
  box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.05);
  transform: translateY(-1px);
  background-color: #fff;
}

.form-input:read-only {
  background-color: #f8f8f8;
  border-color: #ddd;
  color: #666;
  cursor: not-allowed;
}

.form-input:read-only:focus {
  border-color: #ddd;
  box-shadow: none;
  transform: none;
  background-color: #f8f8f8;
}

.form-input::placeholder {
  color: #aaa !important;
  opacity: 0.8 !important;
  line-height: var(--input-height) !important;
  vertical-align: middle !important;
  text-align: left !important;
  padding: 0 !important;
  margin: 0 !important;
}

.form-input.input-error,
.phone-input.input-error {
  border-color: var(--error-color);
  background-color: rgba(255, 59, 48, 0.05);
}

.form-input.input-error:focus,
.phone-input.input-error:focus {
  box-shadow: 0 0 0 3px rgba(255, 59, 48, 0.1);
}

/* Universal Input Icon - Perfect Vertical Centering */
.input-icon {
  position: absolute !important;
  left: 16px !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  color: #666 !important;
  z-index: 10 !important;
  transition: all 0.3s ease !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 18px !important;
  height: 18px !important;
  pointer-events: none !important;
}

.input-icon svg {
  width: 18px !important;
  height: 18px !important;
  display: block !important;
  vertical-align: middle !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* Clean and simple textarea icon positioning */
/* Specific class-based targeting for textarea icons */
.textarea-icon {
  position: absolute !important;
  top: 20px !important;
  left: 16px !important;
  transform: none !important;
  z-index: 10 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 18px !important;
  height: 18px !important;
  color: #666 !important;
}

/* Textarea container specific styling */
.textarea-container {
  position: relative !important;
  display: block !important;
  width: 100% !important;
  height: auto !important;
  min-height: 80px !important;
}

/* Ensure textarea icon SVG is properly sized */
.textarea-icon svg {
  width: 18px !important;
  height: 18px !important;
  display: block !important;
}

/* Universal Form Field Alignment - Apply to ALL form pages */
/* This ensures consistent icon and placeholder alignment across the entire application */

/* All input containers with icons */
.input-with-icon,
.textarea-container {
  position: relative !important;
  width: 100% !important;
}

/* Regular input containers */
.input-with-icon {
  display: flex !important;
  align-items: center !important;
  height: var(--input-height) !important;
}

/* All form inputs with perfect vertical centering */
.input-with-icon .form-input {
  padding-left: 45px !important;
  line-height: var(--input-height) !important;
  display: flex !important;
  align-items: center !important;
  vertical-align: middle !important;
  text-indent: 0 !important;
  height: var(--input-height) !important;
  margin: 0 !important;
  box-sizing: border-box !important;
}

/* Perfect placeholder alignment */
.input-with-icon .form-input::placeholder {
  color: #aaa !important;
  opacity: 0.8 !important;
  line-height: var(--input-height) !important;
  vertical-align: middle !important;
  text-align: left !important;
  padding: 0 !important;
  margin: 0 !important;
}

/* Ensure all icons are perfectly centered */
.input-with-icon .input-icon {
  position: absolute !important;
  left: 16px !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  color: #666 !important;
  z-index: 10 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 18px !important;
  height: 18px !important;
  pointer-events: none !important;
}

/* Ensure perfect horizontal alignment for all input types */
.input-with-icon input.form-input {
  text-align: left;
  padding-top: 0;
  padding-bottom: 0;
  height: var(--input-height);
  line-height: var(--input-height);
}

/* Specific alignment for number inputs */
.input-with-icon input[type="number"].form-input {
  text-align: left !important;
}

/* Force proper alignment for all input types with icons */
.input-with-icon input.form-input,
.input-with-icon .form-input {
  padding-left: 45px !important;
  text-indent: 0 !important;
  vertical-align: baseline !important;
  line-height: var(--input-height) !important;
  height: var(--input-height) !important;
  box-sizing: border-box !important;
}

/* Ensure icon and text are on same baseline */
.input-with-icon {
  line-height: var(--input-height) !important;
}

/* Specific styling for verification icon */
.verification-icon {
  top: 50px; /* Position after the paragraph text */
}

/* This section was moved above */

/* ========== BUTTONS ========== */
.btn {
  width: 100%;
  height: var(--button-height);
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: 0.5px;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 1rem;
  margin-bottom: 1.5rem;
  position: relative;
  overflow: hidden;
}

/* Only target homepage buttons specifically */
.home-container .btn-primary {
  background-color: #000000 !important;
  color: #ffffff !important;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15) !important;
  letter-spacing: 1px;
  font-weight: 700;
  position: relative;
  overflow: hidden;
  border: 2px solid #000000 !important;
}

.home-container .btn-primary:hover {
  background-color: #333333 !important;
  color: #ffffff !important;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2) !important;
  border-color: #333333 !important;
}

/* Default btn-primary for other pages */
.btn-primary {
  background-color: var(--primary-black);
  color: var(--primary-white);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
  letter-spacing: 1px;
  font-weight: 700;
  position: relative;
  overflow: hidden;
}

.btn-primary:hover {
  background-color: #333;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

/* Add subtle shine effect */
.btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    to right,
    transparent 0%,
    rgba(255, 255, 255, 0.2) 50%,
    transparent 100%
  );
  transition: left 0.7s ease;
}

.btn-primary:hover::before {
  left: 100%;
}

/* ========== LINKS ========== */
.links-container {
  display: flex;
  justify-content: center;
  width: 100%;
  margin-top: 1.5rem;
  position: relative;
}

/* Add subtle divider */
.links-container::before {
  content: '';
  position: absolute;
  top: -10px;
  left: 20%;
  right: 20%;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, rgba(0, 0, 0, 0.1) 50%, transparent 100%);
}

.auth-links {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  width: 100%;
}

.auth-link {
  color: var(--text-color);
  text-decoration: none;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  font-weight: 500;
  padding: 0.4rem 0.6rem;
  border-radius: 4px;
}

.auth-link:hover {
  color: #000;
  background-color: rgba(0, 0, 0, 0.03);
}

/* Password Reset Specific Styles */
.email-sent-content {
  text-align: center;
  padding: 20px 0;
}

.success-icon {
  font-size: 64px;
  color: #22c55e;
  margin-bottom: 20px;
}

.email-sent-info h3 {
  color: #111827;
  font-size: 20px;
  margin-bottom: 16px;
}

.email-display {
  background: #f3f4f6;
  padding: 12px 16px;
  border-radius: 8px;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-weight: 600;
  color: #111827;
  margin: 16px 0;
  border: 1px solid #e5e7eb;
}

.instructions {
  text-align: left;
  margin: 24px 0;
  padding: 20px;
  background: #f8fafc;
  border-radius: 8px;
  border-left: 4px solid #3b82f6;
}

.instructions h4 {
  color: #111827;
  font-size: 16px;
  margin-bottom: 12px;
}

.instructions ol {
  margin: 0;
  padding-left: 20px;
}

.instructions li {
  margin-bottom: 8px;
  color: #374151;
  line-height: 1.5;
}

.security-note {
  display: flex;
  align-items: center;
  gap: 8px;
  background: #fef3c7;
  padding: 12px 16px;
  border-radius: 8px;
  border-left: 4px solid #f59e0b;
  margin-top: 20px;
}

.security-note i {
  color: #d97706;
  font-size: 16px;
}

.security-note p {
  margin: 0;
  color: #92400e;
  font-size: 14px;
  font-weight: 500;
}

.link-button {
  background: none;
  border: none;
  color: #3b82f6;
  text-decoration: underline;
  cursor: pointer;
  font-size: inherit;
  padding: 0;
  margin-left: 4px;
}

.link-button:hover {
  color: #1d4ed8;
}

.loading-spinner {
  font-size: 32px;
  color: #3b82f6;
  margin-bottom: 16px;
}

.password-strength {
  margin-top: 8px;
}

.strength-bar {
  width: 100%;
  height: 4px;
  background: #e5e7eb;
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: 4px;
}

.strength-fill {
  height: 100%;
  transition: all 0.3s ease;
  border-radius: 2px;
}

.strength-text {
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.password-requirements {
  margin-top: 12px;
  padding: 12px;
  background: #f9fafb;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
}

.password-requirements p {
  margin: 0 0 8px 0;
  font-size: 12px;
  font-weight: 600;
  color: #374151;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.password-requirements ul {
  margin: 0;
  padding: 0;
  list-style: none;
}

.password-requirements li {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
  font-size: 12px;
  color: #6b7280;
  transition: color 0.2s ease;
}

.password-requirements li.valid {
  color: #059669;
}

.password-requirements li i {
  width: 12px;
  font-size: 10px;
}

.password-requirements li.valid i {
  color: #059669;
}

.password-mismatch {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-top: 6px;
  color: #dc2626;
  font-size: 12px;
  font-weight: 500;
}

.password-mismatch i {
  font-size: 12px;
}

.security-info {
  margin-top: 24px;
  padding: 16px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.security-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 12px;
  color: #6b7280;
}

.security-item:last-child {
  margin-bottom: 0;
}

.security-item i {
  color: #3b82f6;
  width: 14px;
  font-size: 12px;
}

/* ========== PHONE INPUT STYLES ========== */
.phone-input-container {
  display: flex;
  align-items: center;
  width: 100%;
  position: relative;
}

.country-code-select {
  height: var(--input-height);
  border: 1px solid var(--border-color);
  border-radius: 8px 0 0 8px;
  background-color: var(--primary-white);
  padding: 0 10px;
  font-size: 0.95rem;
  color: var(--text-color);
  cursor: pointer;
  border-right: none;
  min-width: 100px;
  flex-shrink: 0;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 8px center;
  background-size: 16px;
  padding-right: 30px;
}

.phone-input-wrapper {
  position: relative;
  flex: 1;
  display: flex;
  align-items: center;
}

.phone-input {
  flex: 1;
  height: var(--input-height);
  border: 1px solid var(--border-color);
  border-radius: 0 8px 8px 0;
  padding: 0 15px 0 45px;
  font-size: 0.95rem;
  background-color: var(--primary-white);
  color: var(--text-color);
  transition: all 0.3s ease;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
  line-height: var(--input-height);
  display: flex;
  align-items: center;
}

.phone-input-wrapper .input-icon {
  left: 16px;
  z-index: 3;
  width: 18px;
  height: 18px;
}

.phone-input::placeholder {
  line-height: var(--input-height);
  vertical-align: middle;
}

.phone-input:focus,
.country-code-select:focus {
  outline: none;
  border-color: var(--primary-black);
  box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.05);
}

/* Password strength indicator */
.password-strength {
  width: 100%;
  margin-bottom: 1.25rem;
  font-size: 0.85rem;
}

.password-strength ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.password-strength li {
  margin-bottom: 0.25rem;
  display: flex;
  align-items: center;
}

.password-strength li.valid {
  color: var(--success-color);
}

.password-strength li.invalid {
  color: var(--text-color);
  opacity: 0.7;
}

/* ========== MESSAGES ========== */
.error-message {
  color: var(--error-color);
  font-size: 0.85rem;
  margin-top: 0.25rem;
  text-align: left;
  width: 100%;
}

.success-message {
  color: var(--success-color);
  font-size: 0.85rem;
  margin-top: 0.25rem;
  text-align: left;
  width: 100%;
}

/* ========== NAVBAR ========== */
.navbar {
  width: 100%;
  height: 70px;
  background-color: var(--primary-white);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 2rem;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 100;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.navbar-logo {
  display: flex;
  align-items: center;
  height: 100%;
  flex-direction: row;
  flex-wrap: nowrap;
  white-space: nowrap;
  background: none !important;
}

.navbar-logo::before,
.navbar-logo::after {
  display: none !important;
  content: none !important;
  background: none !important;
  width: 0 !important;
  height: 0 !important;
}

.navbar-logo-img {
  width: 50px;
  height: 50px;
  margin-right: 5px;
  object-fit: contain;
  display: inline-block;
}

.navbar-brand {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-color);
  letter-spacing: 0.5px;
  margin-left: 0;
  white-space: nowrap;
  display: inline-block;
  background: none !important;
  position: relative;
}

.navbar-brand::before,
.navbar-brand::after {
  display: none !important;
  content: none !important;
  background: none !important;
  width: 0 !important;
  height: 0 !important;
  position: absolute !important;
  opacity: 0 !important;
  visibility: hidden !important;
}

.navbar-links {
  display: flex;
  align-items: center;
  height: 100%;
}

.navbar-link {
  color: var(--text-color);
  text-decoration: none;
  margin-left: 2rem;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  align-items: center;
  position: relative;
}

.navbar-link.active {
  color: var(--primary-black);
  font-weight: 600;
}

.navbar-link.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background-color: var(--primary-black);
}

/* User menu styles */
.navbar-right {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-left: auto;
  gap: 1.25rem;
  min-width: 140px;
  padding-right: 0.75rem;
}

/* Notification container */
.notification-container {
  position: relative;
}

/* Notification button */
.notification-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #333;
  position: relative;
  transition: color 0.2s ease;
  width: 42px;
  height: 42px;
  line-height: 1;
  border-radius: 50%;
}

.notification-button:hover {
  color: #000;
}

/* Notification icon wrapper */
.notification-icon-wrapper, .user-icon-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Notification badge */
.notification-badge {
  position: absolute;
  top: -2px;
  right: -2px;
  width: 8px;
  height: 8px;
  background-color: #ff3b30;
  border-radius: 50%;
  border: 2px solid #fff;
}

/* Notification dropdown */
.notification-dropdown,
.notifications-dropdown {
  position: absolute;
  top: calc(100% + 8px);
  right: 0;
  width: 320px;
  max-width: calc(100vw - 20px);
  max-height: 400px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 9999;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* Notification header */
.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #eaeaea;
}

.notification-header h3 {
  font-size: 1rem;
  font-weight: 600;
  margin: 0;
}

.mark-read-button {
  background: none;
  border: none;
  color: #666;
  font-size: 0.8rem;
  cursor: pointer;
  padding: 0;
  transition: color 0.2s ease;
}

.mark-read-button:hover {
  color: #000;
  text-decoration: underline;
}

/* Notification list */
.notification-list {
  overflow-y: auto;
  max-height: 350px;
}

/* Notification item */
.notification-item {
  padding: 1rem;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: flex-start;
  position: relative;
  transition: background-color 0.2s ease;
}

.notification-item:last-child {
  border-bottom: none;
}

.notification-item:hover {
  background-color: #f9f9f9;
}

.notification-item.unread {
  background-color: #f0f8ff;
}

.notification-item.unread:hover {
  background-color: #e6f3ff;
}

.notification-content {
  flex: 1;
}

.notification-content p {
  margin: 0 0 0.25rem 0;
  font-size: 0.9rem;
  color: #333;
}

.notification-date {
  font-size: 0.75rem;
  color: #888;
}

.unread-indicator {
  width: 8px;
  height: 8px;
  background-color: #007aff;
  border-radius: 50%;
  margin-left: 0.5rem;
  flex-shrink: 0;
}

.no-notifications {
  padding: 2rem;
  text-align: center;
  color: #888;
}

.no-notifications p {
  margin: 0;
  font-size: 0.9rem;
}

/* User menu container */
.user-menu-container {
  position: relative;
}

.user-menu-button {
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem;
  border-radius: 50%;
  transition: all 0.3s ease;
  color: var(--text-color);
  width: 42px;
  height: 42px;
  line-height: 1;
}

.user-menu-button:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

/* User dropdown styles moved to header.css to avoid conflicts */

.dropdown-item {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  color: var(--text-color);
  text-decoration: none;
  transition: all 0.2s ease;
  cursor: pointer;
  border: none;
  background: none;
  width: 100%;
  text-align: left;
  font-size: 0.95rem;
}

.dropdown-item svg {
  margin-right: 0.75rem;
  color: #666;
}

.dropdown-item:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.dropdown-divider {
  height: 1px;
  background-color: var(--border-color);
  margin: 0.25rem 0;
}

/* Mobile menu additions */
.mobile-divider {
  height: 1px;
  background-color: rgba(255, 255, 255, 0.1);
  margin: 0.5rem 0;
  width: 100%;
}

.logout-button {
  color: #ff3b30;
  background: none;
  border: none;
  width: 100%;
  text-align: left;
  font-size: 1rem;
  padding: 1rem;
  cursor: pointer;
}

/* ========== DASHBOARD ========== */
.dashboard-container {
  padding-top: 70px;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.dashboard-content {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.dashboard-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0;
}

.new-request-btn {
  background-color: #000;
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  font-size: 0.95rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  letter-spacing: 0.5px;
}

.welcome-message {
  background-color: #f8f9fa;
  border-left: 4px solid #000;
  padding: 1rem 1.5rem;
  margin-bottom: 1.5rem;
  border-radius: 4px;
  font-size: 1rem;
  color: #333;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* ========== UPLOAD BILL STYLES ========== */
.upload-card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.upload-title {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--text-color);
}

.upload-description {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 1.5rem;
}

.upload-area {
  border: 2px dashed #ccc;
  border-radius: 8px;
  padding: 2rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 1.5rem;
  background-color: #f9f9f9;
}

.upload-area:hover {
  border-color: #000;
  background-color: #f5f5f5;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  color: #666;
}

.upload-placeholder svg {
  color: #999;
}

.upload-placeholder p {
  margin: 0;
  font-size: 0.95rem;
}

.pdf-preview, .image-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.pdf-preview svg {
  color: #e74c3c;
}

.image-preview img {
  max-width: 100%;
  max-height: 200px;
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.file-name {
  font-size: 0.9rem;
  color: #333;
  word-break: break-all;
}

.upload-btn {
  background-color: #000;
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  font-size: 0.95rem;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
  margin-top: 1rem;
}

.upload-btn:hover {
  background-color: #333;
  transform: translateY(-2px);
}

.upload-btn:disabled {
  background-color: #ccc;
  cursor: not-allowed;
  transform: none;
}

.success-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background-color: #d4edda;
  color: #155724;
  border-radius: 4px;
  margin-bottom: 1rem;
}

.success-message svg {
  color: #28a745;
}

/* ========== CARDS ========== */
.card {
  background-color: var(--primary-white);
  border-radius: var(--border-radius);
  box-shadow: 0 2px 10px var(--shadow-color);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.card-title {
  font-size: 1.2rem;
  font-weight: 500;
  margin-bottom: 1rem;
}

/* ========== FILTER STYLES ========== */
.filter-card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.filter-header {
  font-size: 1rem;
  font-weight: 500;
  margin-bottom: 1rem;
}

.filter-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.filter-btn {
  background-color: #f5f5f5;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.filter-btn.active {
  background-color: #000;
  color: #fff;
  border-color: #000;
}

/* ========== TABLE STYLES ========== */
.table-card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  overflow: hidden;
}

.table-container {
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
  margin-bottom: 10px; /* Add space at the bottom */
  border-radius: 8px; /* Match the card border radius */
}

.data-table {
  width: 100%;
  border-collapse: collapse;
  min-width: 600px; /* Ensure table doesn't get too compressed */
  table-layout: fixed; /* Improve rendering performance */
}

.data-table th,
.data-table td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid #e0e0e0;
}

.data-table th {
  font-weight: 600;
  color: #666;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  white-space: nowrap; /* Prevent header text wrapping */
}

.data-table tbody tr {
  transition: all 0.3s ease;
  cursor: pointer;
}

.data-table tbody tr:hover {
  background-color: #f9f9f9;
}

.status-badge {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 50px;
  font-size: 0.8rem;
  font-weight: 500;
}

.status-pending {
  background-color: #fff8e1;
  color: #ffa000;
}

.status-in-progress {
  background-color: #e3f2fd;
  color: #1976d2;
}

.status-resolved {
  background-color: #e8f5e9;
  color: #388e3c;
}

.priority-badge {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 50px;
  font-size: 0.8rem;
  font-weight: 500;
}

.priority-high {
  background-color: #ffebee;
  color: #d32f2f;
}

.priority-medium {
  background-color: #fff8e1;
  color: #ffa000;
}

.priority-low {
  background-color: #e8f5e9;
  color: #388e3c;
}

.no-data {
  text-align: center;
  padding: 2rem;
  color: #666;
}

/* ========== MOBILE MENU STYLES ========== */
.navbar-toggle {
  display: none;
  background-color: rgba(0, 0, 0, 0.08);
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  position: relative;
  width: 42px;
  height: 42px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border-radius: 4px;
}

.mobile-only {
  display: none !important;
}

.navbar-toggle span {
  display: block;
  width: 24px;
  height: 2.5px;
  background-color: var(--text-color);
  margin: 3px 0;
  transition: all 0.3s ease;
  border-radius: 1px;
}

.navbar-toggle span.open:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
}

.navbar-toggle span.open:nth-child(2) {
  opacity: 0;
}

.navbar-toggle span.open:nth-child(3) {
  transform: rotate(-45deg) translate(5px, -5px);
}

.mobile-menu {
  display: none;
  position: fixed;
  top: 70px;
  left: 0;
  width: 100%;
  background-color: #000;
  padding: 1rem 0;
  z-index: 99;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  max-height: calc(100vh - 70px);
  overflow-y: auto;
}

.mobile-menu-header {
  display: flex;
  justify-content: flex-end;
  padding: 0 1rem 0.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  margin-bottom: 0.5rem;
}

.mobile-menu-close {
  background: none;
  border: none;
  cursor: pointer;
  color: white;
  padding: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.mobile-menu-close:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.mobile-link {
  display: block;
  padding: 1rem;
  color: white;
  text-decoration: none;
  font-size: 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  transition: all 0.2s ease;
}

.mobile-link.active {
  background-color: rgba(255, 255, 255, 0.1);
  border-left: 3px solid #fff;
  padding-left: calc(1rem - 3px);
}

.mobile-link:hover {
  background-color: rgba(255, 255, 255, 0.05);
  padding-left: 1.25rem;
}

/* ========== INFO DISPLAY COMPONENTS ========== */
.info-display-card {
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  padding: 1.5rem;
  margin-bottom: 20px;
  border: 1px solid #eaeaea;
}

/* Enhanced version with modern styling */
.info-display-card.enhanced {
  padding: 0;
  overflow: hidden;
  border: 1px solid #eaeaea;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  border-radius: 10px;
}

.info-display-card.enhanced:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border-color: #d0d0d0;
}

.info-display-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.25rem 1.5rem;
  border-bottom: 1px solid #eaeaea;
  background-color: #fafafa;
}

/* Edit button styling */
.edit-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 4px;
  color: #555;
  font-size: 0.85rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.edit-button:hover {
  background-color: #e9e9e9;
  border-color: #ccc;
  color: #333;
}

.edit-button svg {
  width: 14px;
  height: 14px;
}

.edit-toggle-input:checked + .edit-toggle-label {
  background-color: #e9e9e9;
  color: #333;
}

.info-display-title {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0;
  color: var(--text-color);
}

.info-display-content {
  display: flex;
  flex-wrap: wrap;
  padding: 1rem 1.5rem;
  background-color: #fff;
}

.info-display-column {
  flex: 1;
  min-width: 250px;
  padding: 0 1rem;
}

.info-display-section {
  margin-bottom: 1rem;
}

.info-display-subtitle {
  font-size: 0.95rem;
  font-weight: 600;
  margin-bottom: 1.25rem;
  color: #444;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #f0f0f0;
}

.info-display-subtitle svg {
  color: #666;
  width: 14px;
  height: 14px;
}

.info-display-row {
  display: flex;
  margin-bottom: 0.75rem;
  flex-wrap: wrap;
}

.info-display-item {
  margin-bottom: 1rem;
  position: relative;
  padding-left: 0.25rem;
}

.info-display-label {
  font-weight: 600;
  color: #777;
  font-size: 0.8rem;
  margin-bottom: 0.25rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.info-display-value {
  color: #333;
  font-size: 1rem;
  padding: 0.25rem 0;
}

.info-display-value.highlight {
  font-weight: 600;
  color: #000;
}

.info-display-hint {
  font-size: 0.75rem;
  color: #888;
  margin-top: 0.25rem;
}

.info-display-footer {
  margin-top: 0;
  padding: 0.75rem 1.5rem;
  border-top: 1px solid #eaeaea;
  display: flex;
  justify-content: flex-start;
  background-color: #fafafa;
}

.info-display-badge {
  display: flex;
  align-items: center;
  gap: 0.4rem;
  font-size: 0.8rem;
  color: #28a745;
  font-weight: 500;
  padding: 0.3rem 0.6rem;
  background-color: rgba(40, 167, 69, 0.1);
  border-radius: 4px;
}

.info-display-badge svg {
  width: 14px;
  height: 14px;
}

.btn-sm {
  padding: 0.5rem 1rem;
  font-size: 0.85rem;
}

.btn-outline {
  background-color: transparent;
  border: 1px solid #ddd;
  color: #555;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s ease;
}

.btn-outline:hover {
  background-color: #f5f5f5;
  border-color: #ccc;
  color: #333;
}

@media (max-width: 768px) {
  .info-display-content {
    flex-direction: column;
  }

  .info-display-column {
    padding: 0;
  }
}

/* ========== PROFESSIONAL INFO FORM ========== */
.professional-form, .individual-form {
  max-width: 700px;
}

.form-section-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 1.5rem 0 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #eee;
  color: #333;
}

.info-display-subtitle {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #444;
}

.form-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  text-align: center;
  color: var(--text-color);
}

.form-description {
  font-size: 0.95rem;
  color: #666;
  margin-bottom: 2rem;
  text-align: center;
}

.form-row {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
  width: 100%;
}

.form-group-half {
  flex: 1;
  margin-bottom: 0;
}

@media (max-width: 600px) {
  .form-row {
    flex-direction: column;
    gap: 0;
  }
}

.form-group {
  flex: 1;
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  font-size: 0.95rem;
}

.required {
  color: #ff3b30;
}

.optional {
  color: #666;
  font-weight: normal;
  font-size: 0.85rem;
}

.hint-text {
  font-size: 0.8rem;
  color: #666;
  margin-top: 0.25rem;
}

.checkbox-group {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-top: 0.5rem;
}

.checkbox-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
}

.checkbox-item input[type="checkbox"] {
  width: 24px;
  height: 24px;
  cursor: pointer;
  margin-right: 8px;
  accent-color: #000;
}

.checkbox-item label {
  margin-bottom: 0;
  cursor: pointer;
  display: flex;
  align-items: center;
}

.authorization-checkbox {
  margin-top: 1rem;
  margin-bottom: 1rem;
  width: 100%;
}

.authorization-checkbox input[type="checkbox"] {
  width: 24px;
  height: 24px;
  cursor: pointer;
  margin-right: 10px;
  accent-color: #000;
}

.authorization-checkbox label {
  font-size: 0.9rem;
  line-height: 1.4;
  display: flex;
  align-items: flex-start;
}

.authorization-checkbox input[type="checkbox"] {
  margin-top: 2px;
}

.form-buttons {
  display: flex;
  justify-content: space-between;
  gap: 1rem;
  margin-top: 2rem;
}

.btn-secondary {
  background-color: #f5f5f5;
  color: #333;
  border: 1px solid #ddd;
}

.btn-secondary:hover {
  background-color: #e5e5e5;
}

/* ========== USER TYPE SELECTION ========== */
.user-type-form {
  max-width: 600px;
}

.user-type-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  text-align: center;
  color: var(--text-color);
}

.user-type-description {
  font-size: 0.95rem;
  color: #666;
  margin-bottom: 2rem;
  text-align: center;
}

.user-type-options {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
  width: 100%;
}

.user-type-options.second-row {
  margin-bottom: 2rem;
}

@media (max-width: 600px) {
  .user-type-options {
    flex-direction: column;
  }
}

.user-type-option {
  flex: 1;
  padding: 1.5rem;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.user-type-option:hover {
  border-color: #ccc;
  background-color: #f9f9f9;
}

.user-type-option.selected {
  border-color: #000;
  background-color: rgba(0, 0, 0, 0.03);
}

.user-type-icon {
  margin-bottom: 1rem;
  color: #333;
  background-color: rgba(0, 0, 0, 0.05);
  width: 64px;
  height: 64px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.user-type-option.selected .user-type-icon {
  background-color: #000;
  color: #fff;
}

.user-type-option h3 {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--text-color);
}

.user-type-option p {
  font-size: 0.9rem;
  color: #666;
  margin: 0;
}

/* ========== WIZARD STYLES ========== */
.wizard-container {
  width: 100%;
  max-width: 1000px;
  margin: 0 auto;
  background-color: #fff;
  border-radius: 10px;
  overflow: hidden;
  /* Remove box-shadow when inside auth-card */
  box-shadow: none;
}

/* Only apply box-shadow when not inside auth-card */
.dashboard-container .wizard-container {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.wizard-container {
  padding: 2rem 1.5rem 1.5rem;
  background-color: #f8f8f8;
  border-bottom: 1px solid #eaeaea;
}

/* Stepper styles */
.stepper-wrapper {
  display: flex;
  justify-content: space-between;
  margin: 0 auto;
  max-width: 800px; /* Increased from 600px to 800px for better spacing on desktop */
  width: 80%; /* Added to ensure it takes up most of the container width */
  padding: 20px 0;
  position: relative;
}

/* Removed the ::before pseudo-element that was causing the extra line */

.stepper-item {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  z-index: 2;
  padding: 0 30px; /* Added padding for better spacing between items on desktop */
}

.stepper-item::before {
  position: absolute;
  content: "";
  border-bottom: 2px solid;
  border-image: linear-gradient(90deg, #000000, #2ecc71) 1;
  width: 100%;
  top: 20px;
  left: -50%;
  z-index: 1;
  opacity: 0;
  transition: all 0.3s ease;
}

.stepper-item::after {
  position: absolute;
  content: "";
  border-bottom: 2px solid;
  border-image: linear-gradient(90deg, #000000, #2ecc71) 1;
  width: 100%;
  top: 20px;
  left: 50%;
  z-index: 1;
  opacity: 0;
  transition: all 0.3s ease;
}

.stepper-item.completed::before {
  opacity: 1;
}

.stepper-item.active::after {
  opacity: 1;
}

.stepper-item:first-child::before {
  display: none;
}

.stepper-item:last-child::after {
  display: none;
}

.step-counter {
  position: relative;
  z-index: 5;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #fff;
  border: 2px solid #e0e0e0;
  color: #666;
  font-weight: 600;
  transition: all 0.3s ease;
  margin-bottom: 10px;
}

.stepper-item.active .step-counter {
  background-color: #000;
  color: #fff;
  border-color: #000;
  transform: scale(1.15);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.stepper-item.completed .step-counter {
  background-color: #000;
  color: #fff;
  border-color: #000;
}

.step-name {
  font-size: 0.8rem;
  color: #666;
  text-align: center;
  font-weight: 400;
  transition: all 0.3s ease;
}

.stepper-item.active .step-name {
  color: #000;
  font-weight: 600;
}

.step-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 2;
  flex: 1;
  padding: 0 10px;
}

.step-connector {
  flex: 1;
  height: 3px;
  z-index: 1;
  transition: background-color 0.3s ease;
  position: relative;
}

.step-number {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: #fff;
  border: 2px solid #e0e0e0;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  font-weight: 600;
  margin: 0 auto 0.75rem;
  position: relative;
  z-index: 5; /* Ensure it's above the progress bar */
  transition: all 0.3s ease;
}

.step-indicator.active .step-number {
  background-color: #000;
  color: #fff;
  border-color: #000;
  transform: scale(1.15);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.step-indicator.completed .step-number {
  background-color: #000;
  color: #fff;
  border-color: #000;
}

.step-label {
  font-size: 0.8rem;
  color: #666;
  text-align: center;
}

.step-indicator.active .step-label {
  color: #000;
  font-weight: 600;
}

.wizard-content {
  padding: 1.5rem;
}

.wizard-step {
  animation: fadeIn 0.3s ease;
}

.wizard-buttons {
  display: flex;
  justify-content: space-between;
  margin-top: 1.5rem;
  gap: 1rem;
}

.wizard-title {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: #000;
}

.confirmation-summary {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 1.25rem;
  margin-bottom: 1.5rem;
}

.confirmation-summary h4 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #333;
}

.summary-section {
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #eaeaea;
}

.summary-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
  margin-bottom: 0;
}

.summary-section h5 {
  font-size: 0.95rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #555;
}

.summary-row {
  display: flex;
  margin-bottom: 0.5rem;
  flex-wrap: wrap;
}

.summary-label {
  font-weight: 600;
  color: #666;
  width: 40%;
  font-size: 0.9rem;
  min-width: 120px;
}

.summary-value {
  color: #333;
  font-size: 0.9rem;
  flex: 1;
  word-break: break-word;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* ========== RESPONSIVE STYLES ========== */
/* Edge browser specific fixes */
@supports (-ms-ime-align: auto) {
  .container,
  .auth-container {
    min-height: 100vh;
    height: auto;
  }

  .auth-card {
    width: 100%;
    max-width: 800px;
  }
}

@media (max-width: 768px) {
  /* Ensure proper viewport behavior in Edge */
  html {
    width: 100%;
    height: 100%;
  }

  body {
    width: 100%;
    min-height: 100vh;
    -ms-overflow-style: scrollbar;
  }

  .container,
  .auth-container {
    width: 100vw;
    min-width: 100vw;
    max-width: 100vw;
    padding: 1rem;
    box-sizing: border-box;
  }

  .navbar {
    padding: 0 1rem;
    width: 100%;
    box-sizing: border-box;
  }

  .navbar-links {
    display: none;
  }

  .navbar-toggle,
  .mobile-only {
    display: -webkit-box !important;
    display: -ms-flexbox !important;
    display: flex !important;
  }

  .mobile-menu {
    display: block;
  }

  /* Adjust navbar-right for mobile */
  .navbar-right {
    gap: 0.75rem;
    padding-right: 0.5rem;
  }

  /* Fix notification dropdown positioning on mobile */
  .notification-dropdown,
  .notifications-dropdown {
    position: absolute;
    top: calc(100% + 8px);
    right: 0;
    width: 290px;
    max-width: calc(100vw - 20px);
    border-radius: 8px;
    z-index: 9999;
  }

  .notification-button, .user-menu-button {
    padding: 0.35rem;
    width: 38px;
    height: 38px;
  }

  /* Wizard mobile styles */
  .wizard-content {
    padding: 1.25rem 1rem;
  }

  .wizard-container {
    padding: 1.5rem 1rem 1rem;
  }

  /* Professional info form mobile improvements */
  .form-row {
    flex-direction: column;
    gap: 1rem;
  }

  .form-group-half {
    flex: 1;
    width: 100%;
  }

  .professional-form, .individual-form {
    max-width: 100%;
    padding: 0 0.5rem;
  }

  .form-title {
    font-size: 1.3rem;
  }

  .form-description {
    font-size: 0.9rem;
    margin-bottom: 1.5rem;
  }

  .stepper-wrapper {
    padding: 15px 0;
    width: 90%;
  }

  .stepper-item {
    padding: 0 15px;
  }

  .step-counter {
    width: 32px;
    height: 32px;
    font-size: 0.9rem;
  }

  .step-name {
    font-size: 0.75rem;
  }

  .wizard-buttons {
    flex-wrap: wrap;
  }

  .wizard-buttons .btn {
    flex: 1;
    min-width: 120px;
    text-align: center;
  }

  .dashboard-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .dashboard-header .new-request-btn {
    width: 100%;
    text-align: center;
    padding: 0.75rem;
  }

  .mobile-hide {
    display: none;
  }

  /* Auth form responsive styles */
  .auth-form {
    padding: 2rem 1.5rem;
    max-width: 90%;
  }

  .logo-container img.logo {
    width: 150px !important;
    height: 150px !important;
  }

  .app-name {
    font-size: 1.5rem;
  }

  .auth-links {
    flex-wrap: wrap;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .form-input {
    font-size: 0.9rem;
    padding: 0 15px 0 45px;
    height: 44px;
    line-height: 44px;
  }

  .input-icon {
    left: 16px;
    width: 18px;
    height: 18px;
  }

  .input-icon svg {
    width: 16px;
    height: 16px;
  }

  /* Mobile textarea adjustments */
  textarea.form-input {
    padding: 12px 15px 12px 45px;
    line-height: 1.5;
  }

  .input-with-icon:has(textarea) .input-icon {
    top: 20px;
  }

  .btn {
    font-size: 0.9rem;
    height: 44px;
  }

  .navbar-brand {
    font-size: 1rem;
  }

  .navbar-link {
    font-size: 0.8rem;
    margin-left: 1rem;
  }

  /* Navbar mobile adjustments */
  .navbar-right {
    gap: 0.6rem;
    min-width: 120px;
    justify-content: flex-end;
  }

  /* Adjust notification dropdown for smaller screens */
  .notification-dropdown,
  .notifications-dropdown {
    max-width: 300px;
  }

  .notification-button, .user-menu-button {
    padding: 0.25rem;
    width: 36px;
    height: 36px;
  }

  .notification-button svg {
    width: 20px;
    height: 20px;
  }

  .user-menu-button svg {
    width: 20px;
    height: 20px;
  }

  .navbar-toggle {
    width: 36px;
    height: 36px;
    margin-left: 0.2rem;
    background-color: rgba(0, 0, 0, 0.1);
  }

  /* Wizard mobile styles */
  .wizard-content {
    padding: 1rem 0.75rem;
  }

  .wizard-container {
    padding: 1.25rem 0.75rem 0.75rem;
  }

  .stepper-wrapper {
    padding: 10px 0;
    width: 95%;
  }

  .stepper-item {
    padding: 0 10px;
  }

  .stepper-item::before,
  .stepper-item::after {
    top: 15px;
  }

  .step-counter {
    width: 28px;
    height: 28px;
    font-size: 0.8rem;
    margin-bottom: 6px;
  }

  .step-name {
    font-size: 0.7rem;
  }

  .wizard-title {
    font-size: 1.1rem;
    margin-bottom: 1.25rem;
  }

  .wizard-buttons {
    margin-top: 1.25rem;
  }

  .wizard-buttons .btn {
    padding: 0.6rem 0.75rem;
    font-size: 0.85rem;
  }

  .hint-text {
    font-size: 0.75rem;
  }

  .tablet-hide {
    display: none;
  }

  /* Auth form responsive styles */
  .auth-form {
    padding: 1.5rem 1rem;
    max-width: 95%;
    margin: 0 auto;
  }

  .logo-container img.logo {
    width: 120px !important;
    height: 120px !important;
  }

  .app-name {
    font-size: 1.3rem;
    margin-bottom: 1rem;
  }

  .auth-links {
    flex-direction: column;
    gap: 0.75rem;
  }

  .auth-links span {
    display: none;
  }

  .form-group {
    margin-bottom: 1rem;
  }

  /* Dashboard responsive styles */
  .dashboard-content {
    padding: 1rem 0.75rem;
  }

  .dashboard-title {
    font-size: 1.3rem;
  }

  .table-card {
    padding: 1rem 0.5rem;
    border-radius: 6px;
  }

  .data-table th,
  .data-table td {
    padding: 0.75rem 0.5rem;
    font-size: 0.9rem;
    word-break: break-word; /* Allow text to break to prevent overflow */
  }

  .data-table th {
    font-size: 0.8rem;
    white-space: normal; /* Allow header text to wrap */
  }

  /* Improve table scrolling on mobile */
  .table-container {
    margin: 0 -10px; /* Extend beyond padding to use full width */
    width: calc(100% + 20px);
    max-width: none;
    padding: 0 10px;
  }

  .status-badge,
  .priority-badge {
    padding: 0.2rem 0.5rem;
    font-size: 0.8rem;
  }

  .filter-card {
    padding: 1rem 0.75rem;
  }

  .filter-buttons {
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .filter-btn {
    padding: 0.5rem 0.75rem;
    font-size: 0.8rem;
    flex: 1 0 calc(50% - 0.5rem);
  }
}

@media (max-width: 320px) {
  .auth-form {
    padding: 1.25rem 0.75rem;
  }

  .logo-container img.logo {
    width: 100px !important;
    height: 100px !important;
  }

  .app-name {
    font-size: 1.2rem;
  }

  .form-input {
    height: 40px;
  }

  .btn {
    height: 40px;
    font-size: 0.85rem;
  }

  /* Navbar mobile adjustments */
  .navbar-right {
    gap: 0.5rem;
    min-width: 110px;
    justify-content: flex-end;
  }

  /* Adjust notification dropdown for very small screens */
  .notification-dropdown,
  .notifications-dropdown {
    max-width: 280px;
  }

  .notification-button, .user-menu-button {
    padding: 0.2rem;
    width: 34px;
    height: 34px;
  }

  .notification-button svg, .user-menu-button svg {
    width: 18px;
    height: 18px;
  }

  .navbar-toggle {
    width: 34px;
    height: 34px;
    margin-left: 0.2rem;
    background-color: rgba(0, 0, 0, 0.12);
  }

  .navbar-toggle span {
    width: 20px;
    margin: 2px 0;
    height: 2.5px;
  }

  /* Wizard mobile styles */
  .wizard-content {
    padding: 0.75rem 0.5rem;
  }

  .wizard-container {
    padding: 1rem 0.5rem 0.5rem;
  }

  .stepper-wrapper {
    padding: 8px 0;
    width: 100%;
  }

  .stepper-item {
    padding: 0 5px;
  }

  .stepper-item::before,
  .stepper-item::after {
    top: 12px;
  }

  .step-counter {
    width: 24px;
    height: 24px;
    font-size: 0.7rem;
    margin-bottom: 4px;
  }

  .step-name {
    font-size: 0.65rem;
  }

  .progress-bar-container {
    left: 5%;
    right: 5%;
  }

  .step-indicator {
    margin: 0 0.15rem;
  }

  .step-number {
    width: 20px;
    height: 20px;
    font-size: 0.7rem;
  }

  .step-label {
    font-size: 0.65rem;
  }

  .wizard-title {
    font-size: 1rem;
    margin-bottom: 1rem;
  }

  .wizard-buttons {
    margin-top: 1rem;
    flex-direction: column;
    gap: 0.5rem;
  }

  .wizard-buttons .btn {
    width: 100%;
    padding: 0.5rem;
    font-size: 0.8rem;
  }

  .hint-text {
    font-size: 0.7rem;
  }

  /* Dashboard responsive styles */
  .dashboard-title {
    font-size: 1.2rem;
  }

  .new-request-btn {
    font-size: 0.85rem;
    padding: 0.6rem;
  }

  .data-table th,
  .data-table td {
    padding: 0.6rem 0.4rem;
    font-size: 0.8rem;
    line-height: 1.3;
  }

  /* Improve table display on very small screens */
  .table-container {
    margin: 0 -12px;
    width: calc(100% + 24px);
    padding: 0 12px;
  }

  .table-card {
    padding: 0.75rem 0.4rem;
  }

  .filter-btn {
    padding: 0.4rem 0.5rem;
    font-size: 0.75rem;
  }

  .navbar-brand {
    font-size: 0.9rem;
  }

  .navbar-toggle {
    width: 30px;
    height: 30px;
  }
}

/* ========== COMPREHENSIVE MOBILE RESPONSIVENESS FIXES ========== */

/* ========== SCROLL BEHAVIOR ========== */
body {
  overflow-x: hidden;
}

#root {
  min-height: 100vh;
}

/* ========== TOAST NOTIFICATIONS ========== */
/* Fix toast z-index to appear above all elements */
.Toastify__toast-container {
  z-index: 999999 !important;
  position: fixed !important;
}

.Toastify__toast {
  z-index: 999999 !important;
}

.Toastify__toast--success {
  z-index: 999999 !important;
}

.Toastify__toast--error {
  z-index: 999999 !important;
}

.Toastify__toast--info {
  z-index: 999999 !important;
}

.Toastify__toast--warning {
  z-index: 999999 !important;
}

/* Global mobile fixes */
@media screen and (max-width: 768px) {
  /* Ensure all containers respect viewport width */
  * {
    max-width: 100% !important;
    box-sizing: border-box !important;
  }

  html, body {
    overflow-x: hidden !important;
    width: 100% !important;
    max-width: 100% !important;
  }

  #root {
    overflow-x: hidden !important;
    width: 100% !important;
    max-width: 100% !important;
  }

  /* Fix container widths */
  .container,
  .auth-container,
  .home-container {
    width: 100% !important;
    max-width: 100% !important;
    overflow-x: hidden !important;
    padding-left: 1rem !important;
    padding-right: 1rem !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
  }

  /* Fix auth card mobile responsiveness */
  .auth-card {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
    padding: 1.5rem !important;
    overflow-x: hidden !important;
  }

  /* Fix auth form mobile responsiveness */
  .auth-form {
    width: 100% !important;
    max-width: 100% !important;
    padding: 1rem !important;
    overflow-x: hidden !important;
  }

  /* Fix form inputs mobile responsiveness */
  .form-input,
  .form-group,
  .input-with-icon {
    width: 100% !important;
    max-width: 100% !important;
    overflow-x: hidden !important;
    box-sizing: border-box !important;
  }

  /* Fix button mobile responsiveness */
  .btn,
  .stepper-button {
    width: 100% !important;
    max-width: 100% !important;
    overflow-x: hidden !important;
    box-sizing: border-box !important;
  }

  /* Fix wizard and stepper mobile responsiveness */
  .wizard-container,
  .stepper-container {
    width: 100% !important;
    max-width: 100% !important;
    overflow-x: hidden !important;
    padding: 1rem !important;
  }

  /* Fix dashboard mobile responsiveness */
  .dashboard-container,
  .dashboard-content {
    width: 100% !important;
    max-width: 100% !important;
    overflow-x: hidden !important;
    padding: 1rem !important;
  }

  /* Fix table mobile responsiveness */
  .table-container,
  .data-table {
    width: 100% !important;
    max-width: 100% !important;
    overflow-x: auto !important;
  }

  /* Fix navbar mobile responsiveness */
  .navbar,
  .home-header {
    width: 100% !important;
    max-width: 100% !important;
    overflow-x: hidden !important;
    padding-left: 1rem !important;
    padding-right: 1rem !important;
  }

  /* Fix hero section mobile responsiveness */
  .hero-section {
    width: 100% !important;
    max-width: 100% !important;
    overflow-x: hidden !important;
    padding: 2rem 1rem !important;
    flex-direction: column !important;
  }

  /* Fix features grid mobile responsiveness */
  .features-grid,
  .steps-container {
    width: 100% !important;
    max-width: 100% !important;
    grid-template-columns: 1fr !important;
    overflow-x: hidden !important;
    padding: 0 1rem !important;
  }

  /* Fix card mobile responsiveness */
  .feature-card,
  .step-card {
    width: 100% !important;
    max-width: 100% !important;
    overflow-x: hidden !important;
    margin: 0 !important;
  }

  /* Fix modal mobile responsiveness */
  .modal,
  .modal-content {
    width: 100% !important;
    max-width: 100% !important;
    overflow-x: hidden !important;
    margin: 0 !important;
    padding: 1rem !important;
  }

  /* Fix any remaining overflow issues */
  .row,
  .col,
  .column {
    width: 100% !important;
    max-width: 100% !important;
    overflow-x: hidden !important;
    margin: 0 !important;
    padding: 0 0.5rem !important;
  }
}
