const mongoose = require('mongoose');

const conditionSchema = new mongoose.Schema({
  field: {
    type: String,
    required: true,
    trim: true
  },
  operator: {
    type: String,
    enum: ['equals', 'not_equals', 'greater_than', 'less_than', 'contains', 'not_contains', 'in', 'not_in'],
    required: true
  },
  value: {
    type: mongoose.Schema.Types.Mixed,
    required: true
  },
  logicalOperator: {
    type: String,
    enum: ['AND', 'OR'],
    default: 'AND'
  }
}, { _id: false });

const systemAlertSchema = new mongoose.Schema({
  title: {
    type: String,
    required: true,
    trim: true
  },
  message: {
    type: String,
    required: true,
    trim: true
  },
  alertType: {
    type: String,
    enum: [
      'ContractExpiration',
      'PaymentDue',
      'SystemMaintenance',
      'SecurityAlert',
      'PerformanceWarning',
      'UserActivity',
      'DataBackup',
      'APIStatus',
      'ComplianceReminder',
      'MarketingCampaign',
      'SystemUpdate',
      'Other'
    ],
    required: true
  },
  priority: {
    type: String,
    enum: ['Low', 'Normal', 'High', 'Critical'],
    default: 'Normal'
  },
  targetAudience: {
    type: String,
    enum: ['All', 'Admins', 'Brokers', 'Suppliers', 'Clients', 'Individual', 'Professional', 'Custom'],
    required: true
  },
  customAudience: {
    userTypes: [{
      type: String,
      enum: ['Individual', 'Professional', 'Broker', 'Supplier', 'Admin']
    }],
    userIds: [{
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    }],
    conditions: [conditionSchema]
  },
  frequency: {
    type: String,
    enum: ['Once', 'Daily', 'Weekly', 'Monthly', 'OnCondition'],
    default: 'Once'
  },
  schedule: {
    startDate: {
      type: Date,
      default: Date.now
    },
    endDate: Date,
    time: {
      hour: { type: Number, min: 0, max: 23, default: 9 },
      minute: { type: Number, min: 0, max: 59, default: 0 }
    },
    daysOfWeek: [{
      type: Number,
      min: 0,
      max: 6 // 0 = Sunday, 6 = Saturday
    }],
    dayOfMonth: {
      type: Number,
      min: 1,
      max: 31
    }
  },
  conditions: [conditionSchema],
  deliveryChannels: {
    inApp: {
      type: Boolean,
      default: true
    },
    email: {
      type: Boolean,
      default: false
    },
    sms: {
      type: Boolean,
      default: false
    },
    push: {
      type: Boolean,
      default: false
    }
  },
  isActive: {
    type: Boolean,
    default: true
  },
  expiresAt: {
    type: Date
  },
  execution: {
    lastExecuted: Date,
    nextExecution: Date,
    executionCount: {
      type: Number,
      default: 0
    },
    successCount: {
      type: Number,
      default: 0
    },
    failureCount: {
      type: Number,
      default: 0
    },
    lastError: {
      timestamp: Date,
      message: String,
      details: mongoose.Schema.Types.Mixed
    }
  },
  template: {
    emailSubject: String,
    emailBody: String,
    smsBody: String,
    pushTitle: String,
    pushBody: String,
    variables: [{
      name: String,
      type: String,
      defaultValue: mongoose.Schema.Types.Mixed
    }]
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  lastModifiedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  lastModifiedAt: {
    type: Date
  },
  approvedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  approvedAt: {
    type: Date
  },
  tags: [{
    type: String,
    trim: true
  }],
  metadata: {
    category: String,
    department: String,
    businessImpact: {
      type: String,
      enum: ['Low', 'Medium', 'High', 'Critical']
    },
    estimatedRecipients: Number,
    actualRecipients: Number,
    costCenter: String,
    campaignId: String
  }
}, {
  timestamps: true
});

// Pre-save middleware to calculate next execution
systemAlertSchema.pre('save', function(next) {
  if (this.isModified('frequency') || this.isModified('schedule') || this.isNew) {
    this.calculateNextExecution();
  }
  next();
});

// Instance methods
systemAlertSchema.methods.calculateNextExecution = function() {
  if (!this.isActive || this.frequency === 'Once') {
    this.execution.nextExecution = null;
    return;
  }

  const now = new Date();
  const startDate = this.schedule.startDate || now;
  let nextExecution = new Date(startDate);

  // Set time
  nextExecution.setHours(this.schedule.time.hour, this.schedule.time.minute, 0, 0);

  switch (this.frequency) {
    case 'Daily':
      if (nextExecution <= now) {
        nextExecution.setDate(nextExecution.getDate() + 1);
      }
      break;
    
    case 'Weekly':
      if (this.schedule.daysOfWeek && this.schedule.daysOfWeek.length > 0) {
        // Find next occurrence of specified days
        let found = false;
        for (let i = 0; i < 7 && !found; i++) {
          const testDate = new Date(nextExecution);
          testDate.setDate(testDate.getDate() + i);
          if (this.schedule.daysOfWeek.includes(testDate.getDay()) && testDate > now) {
            nextExecution = testDate;
            found = true;
          }
        }
        if (!found) {
          nextExecution.setDate(nextExecution.getDate() + 7);
        }
      } else {
        nextExecution.setDate(nextExecution.getDate() + 7);
      }
      break;
    
    case 'Monthly':
      if (this.schedule.dayOfMonth) {
        nextExecution.setDate(this.schedule.dayOfMonth);
        if (nextExecution <= now) {
          nextExecution.setMonth(nextExecution.getMonth() + 1);
        }
      } else {
        nextExecution.setMonth(nextExecution.getMonth() + 1);
      }
      break;
  }

  // Check if within end date
  if (this.schedule.endDate && nextExecution > this.schedule.endDate) {
    this.execution.nextExecution = null;
    this.isActive = false;
  } else {
    this.execution.nextExecution = nextExecution;
  }
};

systemAlertSchema.methods.shouldExecute = function() {
  if (!this.isActive) return false;
  if (this.expiresAt && new Date() > this.expiresAt) return false;
  if (!this.execution.nextExecution) return false;
  
  return new Date() >= this.execution.nextExecution;
};

systemAlertSchema.methods.markExecuted = function(success = true, error = null) {
  this.execution.lastExecuted = new Date();
  this.execution.executionCount += 1;
  
  if (success) {
    this.execution.successCount += 1;
  } else {
    this.execution.failureCount += 1;
    if (error) {
      this.execution.lastError = {
        timestamp: new Date(),
        message: error.message || error,
        details: error
      };
    }
  }
  
  // Calculate next execution for recurring alerts
  if (this.frequency !== 'Once') {
    this.calculateNextExecution();
  } else {
    this.isActive = false;
  }
  
  return this.save();
};

systemAlertSchema.methods.evaluateConditions = function(data) {
  if (!this.conditions || this.conditions.length === 0) {
    return true;
  }
  
  let result = true;
  let currentLogicalOp = 'AND';
  
  for (const condition of this.conditions) {
    const fieldValue = this.getNestedValue(data, condition.field);
    const conditionResult = this.evaluateCondition(fieldValue, condition);
    
    if (currentLogicalOp === 'AND') {
      result = result && conditionResult;
    } else {
      result = result || conditionResult;
    }
    
    currentLogicalOp = condition.logicalOperator || 'AND';
  }
  
  return result;
};

systemAlertSchema.methods.getNestedValue = function(obj, path) {
  return path.split('.').reduce((current, key) => current && current[key], obj);
};

systemAlertSchema.methods.evaluateCondition = function(fieldValue, condition) {
  switch (condition.operator) {
    case 'equals':
      return fieldValue === condition.value;
    case 'not_equals':
      return fieldValue !== condition.value;
    case 'greater_than':
      return fieldValue > condition.value;
    case 'less_than':
      return fieldValue < condition.value;
    case 'contains':
      return String(fieldValue).includes(String(condition.value));
    case 'not_contains':
      return !String(fieldValue).includes(String(condition.value));
    case 'in':
      return Array.isArray(condition.value) && condition.value.includes(fieldValue);
    case 'not_in':
      return Array.isArray(condition.value) && !condition.value.includes(fieldValue);
    default:
      return false;
  }
};

// Static methods
systemAlertSchema.statics.getActiveAlerts = function() {
  return this.find({
    isActive: true,
    $or: [
      { expiresAt: { $exists: false } },
      { expiresAt: null },
      { expiresAt: { $gt: new Date() } }
    ]
  });
};

systemAlertSchema.statics.getAlertsToExecute = function() {
  const now = new Date();
  return this.find({
    isActive: true,
    'execution.nextExecution': { $lte: now },
    $or: [
      { expiresAt: { $exists: false } },
      { expiresAt: null },
      { expiresAt: { $gt: now } }
    ]
  });
};

// Create indexes
systemAlertSchema.index({ isActive: 1, 'execution.nextExecution': 1 });
systemAlertSchema.index({ alertType: 1 });
systemAlertSchema.index({ targetAudience: 1 });
systemAlertSchema.index({ priority: 1 });
systemAlertSchema.index({ createdBy: 1 });
systemAlertSchema.index({ expiresAt: 1 });

const SystemAlert = mongoose.model('SystemAlert', systemAlertSchema);

module.exports = SystemAlert;
