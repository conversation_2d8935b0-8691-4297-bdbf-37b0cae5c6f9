// Polyfills for Node.js globals in the browser environment
import { <PERSON><PERSON><PERSON> } from 'buffer';
import process from 'process';

// Make Buffer available globally
window.Buffer = Buffer;
window.Buffer.isBuffer = Buffer.isBuffer;
window.Buffer.from = Buffer.from;
window.Buffer.alloc = Buffer.alloc;
window.Buffer.allocUnsafe = Buffer.allocUnsafe;
window.Buffer.byteLength = Buffer.byteLength;
window.Buffer.compare = Buffer.compare;
window.Buffer.concat = Buffer.concat;

// Make process available globally
window.process = process;

// Ensure global is defined
if (typeof global === 'undefined') {
  window.global = window;
}

// Ensure globalThis has the same polyfills
globalThis.Buffer = window.Buffer;
globalThis.process = window.process;
globalThis.global = window.global;

// Fix for axios
window._global = window;
globalThis._global = window;
