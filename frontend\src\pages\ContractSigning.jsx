import { useState, useEffect } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { showSuccessMessage, showErrorMessage } from '../utils/toastNotifications';
import DashboardLayout from '../components/DashboardLayout';
import Spinner from '../components/Spinner';
import contractService from '../services/contract.service';
import logger from '../utils/logger';
import { loadCSS, unloadCSS, COMPONENT_CSS, CSS_IDS } from '../utils/cssLoader';

const ContractSigning = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [contract, setContract] = useState(null);
  const [signingStatus, setSigningStatus] = useState(null);
  const [error, setError] = useState(null);

  const contractId = searchParams.get('contractId');
  const envelopeId = searchParams.get('envelopeId');
  const isMock = searchParams.get('mock') === 'true';

  // Dynamically load CSS only for this component
  useEffect(() => {
    loadCSS(COMPONENT_CSS.CONTRACT_SIGNING, CSS_IDS.CONTRACT_SIGNING);

    // Cleanup function to remove CSS when component unmounts
    return () => {
      unloadCSS(CSS_IDS.CONTRACT_SIGNING);
    };
  }, []);

  useEffect(() => {
    if (!contractId) {
      setError('No contract ID provided');
      setLoading(false);
      return;
    }

    fetchContractDetails();

    // If this is a return from DocuSign, update the signature status
    if (envelopeId) {
      handleSigningReturn();
    }
  }, [contractId, envelopeId]);

  const fetchContractDetails = async () => {
    try {
      logger.info('Fetching contract details:', contractId);
      const response = await contractService.getContractById(contractId);

      if (response.success) {
        setContract(response.data);
        logger.info('Contract details loaded');
      } else {
        setError('Failed to load contract details');
      }
    } catch (error) {
      logger.error('Error fetching contract:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleSigningReturn = async () => {
    try {
      logger.info('Processing signing return for envelope:', envelopeId);

      // Update signature status
      await contractService.updateSignatureStatus(contractId, {
        envelopeId,
        status: 'completed',
        signedDate: new Date().toISOString()
      });

      // Refresh contract details
      await fetchContractDetails();

      setSigningStatus('completed');
      logger.info('Contract signing completed successfully');

    } catch (error) {
      logger.error('Error processing signing return:', error);
      setError('Failed to update signing status');
    }
  };

  const handleResendSigning = async () => {
    try {
      setLoading(true);
      logger.info('Resending signing invitation for contract:', contractId);

      const response = await contractService.resendSigning(contractId);

      if (response.success && response.data.signingUrl) {
        window.open(response.data.signingUrl, '_blank');
      } else {
        setError('Failed to generate signing URL');
      }
    } catch (error) {
      logger.error('Error resending signing invitation:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleDownloadContract = async () => {
    try {
      logger.info('Downloading contract:', contractId);
      await contractService.downloadContract(contractId);
    } catch (error) {
      logger.error('Error downloading contract:', error);
      showErrorMessage('OPERATION_FAILED', `Failed to download contract: ${error.message}`);
    }
  };

  const handleBackToOffers = () => {
    navigate('/offers');
  };

  const handleViewContracts = () => {
    navigate('/contracts');
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="contract-signing-container">
          <div className="loading-container">
            <Spinner size="large" message="Loading contract details..." />
          </div>
        </div>
      </DashboardLayout>
    );
  }

  if (error) {
    return (
      <DashboardLayout>
        <div className="contract-signing-container">
          <div className="error-container">
            <div className="error-icon">
              <i className="fas fa-exclamation-triangle"></i>
            </div>
            <h2>Error Loading Contract</h2>
            <p>{error}</p>
            <button className="btn-primary" onClick={handleBackToOffers}>
              Back to Offers
            </button>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  if (!contract) {
    return (
      <DashboardLayout>
        <div className="contract-signing-container">
          <div className="error-container">
            <div className="error-icon">
              <i className="fas fa-file-contract"></i>
            </div>
            <h2>Contract Not Found</h2>
            <p>The requested contract could not be found.</p>
            <button className="btn-primary" onClick={handleBackToOffers}>
              Back to Offers
            </button>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  const isContractSigned = contract.signatureStatus?.completed;
  const isContractActive = contract.status === 'Active';

  return (
    <DashboardLayout>
      <div className="contract-signing-container">
        <div className="contract-header">
          <div className="header-content">
            <h1>
              <i className="fas fa-file-contract"></i>
              Contract Signing
            </h1>
            <div className="contract-status">
              <span className={`status-badge ${contract.status.toLowerCase()}`}>
                {contract.status}
              </span>
            </div>
          </div>
        </div>

        <div className="contract-content">
          {/* Contract Details Card */}
          <div className="contract-card">
            <div className="card-header">
              <h3>Contract Details</h3>
              <span className="contract-number">#{contract.contractDetails.contractNumber}</span>
            </div>

            <div className="card-body">
              <div className="contract-info-grid">
                <div className="info-item">
                  <label>Energy Type:</label>
                  <span>{contract.contractDetails.energyType}</span>
                </div>
                <div className="info-item">
                  <label>Rate Type:</label>
                  <span>{contract.contractDetails.rateType}</span>
                </div>
                <div className="info-item">
                  <label>Rate:</label>
                  <span>{contract.contractDetails.rate} €/kWh</span>
                </div>
                <div className="info-item">
                  <label>Standing Charge:</label>
                  <span>{contract.contractDetails.standingCharge} €/month</span>
                </div>
                <div className="info-item">
                  <label>Start Date:</label>
                  <span>{new Date(contract.startDate).toLocaleDateString()}</span>
                </div>
                <div className="info-item">
                  <label>End Date:</label>
                  <span>{new Date(contract.endDate).toLocaleDateString()}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Signing Status Card */}
          <div className="signing-status-card">
            <div className="card-header">
              <h3>Signing Status</h3>
            </div>

            <div className="card-body">
              {isContractSigned ? (
                <div className="signing-completed">
                  <div className="success-icon">
                    <i className="fas fa-check-circle"></i>
                  </div>
                  <h4>Contract Successfully Signed!</h4>
                  <p>
                    Signed on: {new Date(contract.signatureStatus.userSignedDate).toLocaleDateString()}
                  </p>
                  <div className="action-buttons">
                    <button className="btn-primary" onClick={handleDownloadContract}>
                      <i className="fas fa-download"></i> Download Contract
                    </button>
                    <button className="btn-secondary" onClick={handleViewContracts}>
                      <i className="fas fa-list"></i> View All Contracts
                    </button>
                  </div>
                </div>
              ) : (
                <div className="signing-pending">
                  <div className="pending-icon">
                    <i className="fas fa-signature"></i>
                  </div>
                  <h4>Signature Required</h4>
                  <p>
                    Your contract is ready for signing. Please complete the digital signature process.
                  </p>
                  <div className="action-buttons">
                    <button className="btn-primary" onClick={handleResendSigning}>
                      <i className="fas fa-pen"></i> Sign Contract
                    </button>
                    <button className="btn-secondary" onClick={handleBackToOffers}>
                      <i className="fas fa-arrow-left"></i> Back to Offers
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Mock Mode Notice */}
          {isMock && (
            <div className="mock-notice">
              <div className="notice-icon">
                <i className="fas fa-info-circle"></i>
              </div>
              <div className="notice-content">
                <h4>Demo Mode</h4>
                <p>This is a demonstration of the contract signing process. In production, you would be redirected to DocuSign for actual document signing.</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </DashboardLayout>
  );
};

export default ContractSigning;
