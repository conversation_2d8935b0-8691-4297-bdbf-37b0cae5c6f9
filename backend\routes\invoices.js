const express = require('express');
const router = express.Router();
const invoiceController = require('../controllers/invoiceController');
const { authMiddleware } = require('../middleware/auth');
const logger = require('../utils/logger');

// Test endpoint that doesn't require authentication
router.get('/test-connection', (_req, res) => {
  logger.info('Test connection endpoint called');
  res.status(200).json({
    message: 'API connection successful',
    timestamp: new Date().toISOString()
  });
});

// Debug endpoint to check MongoDB connection and count invoices
router.get('/debug', async (_req, res) => {
  try {
    logger.info('Debug endpoint called');

    // Get the Invoice model
    const Invoice = require('../models/Invoice');

    // Count invoices
    const count = await Invoice.countDocuments();
    logger.info(`Found ${count} invoices in MongoDB`);

    // Get the most recent 5 invoices
    const recentInvoices = await Invoice.find()
      .sort({ createdAt: -1 })
      .limit(5)
      .select('_id originalFilename status createdAt');

    res.status(200).json({
      message: 'MongoDB connection successful',
      invoiceCount: count,
      recentInvoices: recentInvoices,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Error in debug endpoint:', error);
    res.status(500).json({
      message: 'Error checking MongoDB connection',
      error: error.message
    });
  }
});

// Apply authentication middleware to all other routes
router.use(authMiddleware);

// Upload an invoice (supports multiple files)
router.post('/upload', invoiceController.upload.array('invoiceFiles', 5), invoiceController.uploadInvoice);

// Upload a single invoice (backward compatibility)
router.post('/upload-single', invoiceController.upload.single('invoiceFile'), invoiceController.uploadInvoice);

// Get all invoices for a user
router.get('/user/:cognitoId', invoiceController.getUserInvoices);

// Get invoice count for a user
router.get('/count/:cognitoId', invoiceController.getInvoiceCount);

// Get a single invoice by ID
router.get('/:id', invoiceController.getInvoiceById);

// Get a download URL for an invoice
router.get('/:id/download', invoiceController.getInvoiceDownloadUrl);

// Update invoice metadata
router.put('/:id/metadata', invoiceController.updateInvoiceMetadata);

// Create a new invoice record (without file upload)
router.post('/', async (req, res) => {
  try {
    logger.info('Create invoice record API called');
    logger.debug('Request body:', req.body);

    const {
      cognitoId,
      userType,
      originalFilename,
      s3Key,
      s3Bucket,
      fileSize,
      mimeType,
      publicUrl,
      metadata,
      status
    } = req.body;

    // Validate required fields
    if (!cognitoId || !s3Key || !s3Bucket) {
      return res.status(400).json({ message: 'Missing required fields: cognitoId, s3Key, and s3Bucket are required' });
    }

    // Find the user in MongoDB
    const User = require('../models/User');
    let user = await User.findOne({ cognitoId });

    if (!user) {
      logger.info('User not found in MongoDB, creating temporary user');
      user = new User({
        cognitoId,
        email: `temp-${cognitoId}@example.com`,
        firstName: 'Temporary',
        lastName: 'User',
        userType: userType || 'individual',
        status: 'Active',
        verificationStatus: 'Verified',
        profileComplete: true
      });

      await user.save();
      logger.info('Temporary user created with ID:', user._id);
    } else {
      logger.debug('User found in MongoDB:', user._id);
    }

    // Create a new invoice record
    const Invoice = require('../models/Invoice');
    const invoice = new Invoice({
      userId: user._id,
      cognitoId,
      userType: userType || 'individual',
      originalFilename,
      s3Key,
      s3Bucket,
      fileSize,
      mimeType,
      publicUrl,
      metadata: metadata || {},
      status: status || 'pending'
    });

    // Save the invoice to MongoDB
    await invoice.save();
    logger.info('Invoice saved to MongoDB with ID:', invoice._id);

    // Return success response
    res.status(201).json({
      message: 'Invoice record created successfully',
      invoice: {
        id: invoice._id,
        originalFilename: invoice.originalFilename,
        s3Key: invoice.s3Key,
        publicUrl: invoice.publicUrl,
        status: invoice.status,
        createdAt: invoice.createdAt
      }
    });
  } catch (error) {
    logger.error('Error creating invoice record:', error);
    res.status(500).json({ message: 'Failed to create invoice record', error: error.message });
  }
});

module.exports = router;
