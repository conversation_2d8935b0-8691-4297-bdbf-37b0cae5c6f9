import { useState } from 'react';
import DashboardLayout from '../components/DashboardLayout';
import { showSuccessMessage, showErrorMessage } from '../utils/toastNotifications';
import '../styles/settings.css';

const Settings = () => {
  const [activeTab, setActiveTab] = useState('account');
  const [accountSettings, setAccountSettings] = useState({
    emailNotifications: true,
    smsNotifications: false,
    twoFactorAuth: false,
    language: 'english'
  });
  const [privacySettings, setPrivacySettings] = useState({
    shareDataWithPartners: false,
    allowAnonymousAnalytics: true
  });
  const [notificationSettings, setNotificationSettings] = useState({
    newOffers: true,
    contractUpdates: true,
    invoiceReminders: true,
    savingsTips: true,
    marketUpdates: false
  });

  const handleAccountSettingChange = (e) => {
    const { name, value, type, checked } = e.target;
    setAccountSettings({
      ...accountSettings,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  const handlePrivacySettingChange = (e) => {
    const { name, checked } = e.target;
    setPrivacySettings({
      ...privacySettings,
      [name]: checked
    });
  };

  const handleNotificationSettingChange = (e) => {
    const { name, checked } = e.target;
    setNotificationSettings({
      ...notificationSettings,
      [name]: checked
    });
  };

  const handleSaveSettings = () => {
    // In a real app, this would be an API call to save settings
    console.log('Saving settings:', {
      accountSettings,
      privacySettings,
      notificationSettings
    });

    // Show success message
    showSuccessMessage('UPDATED_SUCCESSFULLY', 'Settings saved successfully!');
  };

  return (
    <DashboardLayout>
      <div className="settings-container">
        <h1 className="settings-title">Settings</h1>

        <div className="settings-content">
          <div className="settings-tabs">
            <button
              className={`tab-button ${activeTab === 'account' ? 'active' : ''}`}
              onClick={() => setActiveTab('account')}
            >
              <i className="fas fa-user-cog"></i>
              Account
            </button>
            <button
              className={`tab-button ${activeTab === 'privacy' ? 'active' : ''}`}
              onClick={() => setActiveTab('privacy')}
            >
              <i className="fas fa-shield-alt"></i>
              Privacy
            </button>
            <button
              className={`tab-button ${activeTab === 'notifications' ? 'active' : ''}`}
              onClick={() => setActiveTab('notifications')}
            >
              <i className="fas fa-bell"></i>
              Notifications
            </button>
            <button
              className={`tab-button ${activeTab === 'security' ? 'active' : ''}`}
              onClick={() => setActiveTab('security')}
            >
              <i className="fas fa-lock"></i>
              Security
            </button>
          </div>

          <div className="settings-panel">
            {activeTab === 'account' && (
              <div className="settings-section">
                <h2>Account Settings</h2>

                <div className="settings-group">
                  <div className="setting-item">
                    <div className="setting-info">
                      <label htmlFor="emailNotifications">Email Notifications</label>
                      <p className="setting-description">Receive notifications via email</p>
                    </div>
                    <div className="setting-control">
                      <label className="toggle-switch">
                        <input
                          type="checkbox"
                          id="emailNotifications"
                          name="emailNotifications"
                          checked={accountSettings.emailNotifications}
                          onChange={handleAccountSettingChange}
                        />
                        <span className="toggle-slider"></span>
                      </label>
                    </div>
                  </div>

                  <div className="setting-item">
                    <div className="setting-info">
                      <label htmlFor="smsNotifications">SMS Notifications</label>
                      <p className="setting-description">Receive notifications via SMS</p>
                    </div>
                    <div className="setting-control">
                      <label className="toggle-switch">
                        <input
                          type="checkbox"
                          id="smsNotifications"
                          name="smsNotifications"
                          checked={accountSettings.smsNotifications}
                          onChange={handleAccountSettingChange}
                        />
                        <span className="toggle-slider"></span>
                      </label>
                    </div>
                  </div>

                  <div className="setting-item">
                    <div className="setting-info">
                      <label htmlFor="twoFactorAuth">Two-Factor Authentication</label>
                      <p className="setting-description">Add an extra layer of security to your account</p>
                    </div>
                    <div className="setting-control">
                      <label className="toggle-switch">
                        <input
                          type="checkbox"
                          id="twoFactorAuth"
                          name="twoFactorAuth"
                          checked={accountSettings.twoFactorAuth}
                          onChange={handleAccountSettingChange}
                        />
                        <span className="toggle-slider"></span>
                      </label>
                    </div>
                  </div>

                  <div className="setting-item">
                    <div className="setting-info">
                      <label htmlFor="language">Language</label>
                      <p className="setting-description">Select your preferred language</p>
                    </div>
                    <div className="setting-control">
                      <select
                        id="language"
                        name="language"
                        value={accountSettings.language}
                        onChange={handleAccountSettingChange}
                        className="settings-select"
                      >
                        <option value="english">English</option>
                        <option value="french">French</option>
                      </select>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'privacy' && (
              <div className="settings-section">
                <h2>Privacy Settings</h2>

                <div className="settings-group">
                  <div className="setting-item">
                    <div className="setting-info">
                      <label htmlFor="shareDataWithPartners">Share Data with Partners</label>
                      <p className="setting-description">Allow us to share your data with trusted partners to improve service</p>
                    </div>
                    <div className="setting-control">
                      <label className="toggle-switch">
                        <input
                          type="checkbox"
                          id="shareDataWithPartners"
                          name="shareDataWithPartners"
                          checked={privacySettings.shareDataWithPartners}
                          onChange={handlePrivacySettingChange}
                        />
                        <span className="toggle-slider"></span>
                      </label>
                    </div>
                  </div>

                  <div className="setting-item">
                    <div className="setting-info">
                      <label htmlFor="allowAnonymousAnalytics">Anonymous Analytics</label>
                      <p className="setting-description">Allow anonymous usage data collection to improve our services</p>
                    </div>
                    <div className="setting-control">
                      <label className="toggle-switch">
                        <input
                          type="checkbox"
                          id="allowAnonymousAnalytics"
                          name="allowAnonymousAnalytics"
                          checked={privacySettings.allowAnonymousAnalytics}
                          onChange={handlePrivacySettingChange}
                        />
                        <span className="toggle-slider"></span>
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'notifications' && (
              <div className="settings-section">
                <h2>Notification Settings</h2>

                <div className="settings-group">
                  <div className="setting-item">
                    <div className="setting-info">
                      <label htmlFor="newOffers">New Offers</label>
                      <p className="setting-description">Get notified when new energy offers are available</p>
                    </div>
                    <div className="setting-control">
                      <label className="toggle-switch">
                        <input
                          type="checkbox"
                          id="newOffers"
                          name="newOffers"
                          checked={notificationSettings.newOffers}
                          onChange={handleNotificationSettingChange}
                        />
                        <span className="toggle-slider"></span>
                      </label>
                    </div>
                  </div>

                  <div className="setting-item">
                    <div className="setting-info">
                      <label htmlFor="contractUpdates">Contract Updates</label>
                      <p className="setting-description">Get notified about changes to your energy contracts</p>
                    </div>
                    <div className="setting-control">
                      <label className="toggle-switch">
                        <input
                          type="checkbox"
                          id="contractUpdates"
                          name="contractUpdates"
                          checked={notificationSettings.contractUpdates}
                          onChange={handleNotificationSettingChange}
                        />
                        <span className="toggle-slider"></span>
                      </label>
                    </div>
                  </div>

                  <div className="setting-item">
                    <div className="setting-info">
                      <label htmlFor="invoiceReminders">Invoice Reminders</label>
                      <p className="setting-description">Get reminders about upcoming invoice due dates</p>
                    </div>
                    <div className="setting-control">
                      <label className="toggle-switch">
                        <input
                          type="checkbox"
                          id="invoiceReminders"
                          name="invoiceReminders"
                          checked={notificationSettings.invoiceReminders}
                          onChange={handleNotificationSettingChange}
                        />
                        <span className="toggle-slider"></span>
                      </label>
                    </div>
                  </div>

                  <div className="setting-item">
                    <div className="setting-info">
                      <label htmlFor="savingsTips">Savings Tips</label>
                      <p className="setting-description">Receive energy saving tips and recommendations</p>
                    </div>
                    <div className="setting-control">
                      <label className="toggle-switch">
                        <input
                          type="checkbox"
                          id="savingsTips"
                          name="savingsTips"
                          checked={notificationSettings.savingsTips}
                          onChange={handleNotificationSettingChange}
                        />
                        <span className="toggle-slider"></span>
                      </label>
                    </div>
                  </div>

                  <div className="setting-item">
                    <div className="setting-info">
                      <label htmlFor="marketUpdates">Market Updates</label>
                      <p className="setting-description">Get updates about energy market changes</p>
                    </div>
                    <div className="setting-control">
                      <label className="toggle-switch">
                        <input
                          type="checkbox"
                          id="marketUpdates"
                          name="marketUpdates"
                          checked={notificationSettings.marketUpdates}
                          onChange={handleNotificationSettingChange}
                        />
                        <span className="toggle-slider"></span>
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'security' && (
              <div className="settings-section">
                <h2>Security Settings</h2>

                <div className="settings-group">
                  <div className="setting-item">
                    <div className="setting-info">
                      <label>Change Password</label>
                      <p className="setting-description">Update your account password</p>
                    </div>
                    <div className="setting-control">
                      <button className="change-password-btn">Change Password</button>
                    </div>
                  </div>

                  <div className="setting-item">
                    <div className="setting-info">
                      <label>Login History</label>
                      <p className="setting-description">View your recent login activity</p>
                    </div>
                    <div className="setting-control">
                      <button className="view-history-btn">View History</button>
                    </div>
                  </div>

                  <div className="setting-item danger-zone">
                    <div className="setting-info">
                      <label>Delete Account</label>
                      <p className="setting-description">Permanently delete your account and all data</p>
                    </div>
                    <div className="setting-control">
                      <button className="delete-account-btn">Delete Account</button>
                    </div>
                  </div>
                </div>
              </div>
            )}

            <div className="settings-actions">
              <button className="save-settings-btn" onClick={handleSaveSettings}>
                Save Settings
              </button>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default Settings;
