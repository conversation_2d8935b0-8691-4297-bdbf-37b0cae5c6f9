/* Invoices Page - Enhanced Professional Design */

.invoices-container {
  padding: 20px 30px;
  width: 100%;
  margin: 0;
  background-color: #fafafa;
  min-height: calc(100vh - 80px);
}

.invoices-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 35px;
  padding: 25px 0;
  border-bottom: 2px solid #f0f0f0;
}

.invoices-header-content {
  flex: 1;
}

.invoices-header h1 {
  font-size: 28px;
  font-weight: 700;
  color: #000;
  margin: 0 0 8px 0;
  letter-spacing: -0.5px;
}

.invoices-header p {
  font-size: 16px;
  color: #666;
  margin: 0;
  line-height: 1.5;
}

.invoices-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

.invoices-actions {
  margin-bottom: 30px;
  background-color: #fff;
  padding: 20px 25px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
}

.filter-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  align-items: center;
}

.filter-btn {
  padding: 12px 20px;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  color: #495057;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.filter-btn:hover {
  background-color: #e9ecef;
  border-color: #dee2e6;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.filter-btn.active {
  background: linear-gradient(135deg, #000 0%, #333 100%);
  color: #fff;
  border-color: #000;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.upload-invoice-btn {
  background: linear-gradient(to right, #000, #333);
  color: #fff;
  border: none;
  border-radius: 6px;
  padding: 10px 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: all 0.2s ease;
  gap: 8px;
}

.upload-invoice-btn:hover {
  opacity: 0.9;
  transform: translateY(-2px);
}

.upload-invoice-btn i {
  margin-right: 8px;
}

.invoices-table-container {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  border: 1px solid #f0f0f0;
}

.invoices-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.invoices-table th,
.invoices-table td {
  padding: 18px 20px;
  text-align: left;
  border-bottom: 1px solid #f5f5f5;
  vertical-align: middle;
}

.invoices-table th {
  background: linear-gradient(135deg, #f8f9fa 0%, #f1f3f4 100%);
  font-weight: 700;
  color: #2c3e50;
  font-size: 13px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border-bottom: 2px solid #e9ecef;
}

.invoices-table tr:last-child td {
  border-bottom: none;
}

.invoices-table tbody tr {
  transition: all 0.2s ease;
}

.invoices-table tbody tr:hover {
  background-color: #f8f9fa;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.invoice-name-cell {
  min-width: 280px;
}

.invoice-name {
  display: flex;
  align-items: center;
  gap: 15px;
}

.invoice-icon {
  font-size: 22px;
  color: #777;
  padding: 8px;
  background-color: #f8f9fa;
  border-radius: 8px;
  width: 38px;
  height: 38px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.invoice-icon.electricity {
  color: #0070f3;
  background-color: #f0f8ff;
}

.invoice-icon.gas {
  color: #fa8c16;
  background-color: #fff7e6;
}

.invoice-details {
  flex: 1;
}

.filename {
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 4px;
  font-size: 14px;
  line-height: 1.3;
}

.invoice-number {
  font-size: 12px;
  color: #6c757d;
  font-weight: 500;
}

.amount-cell {
  font-weight: 700;
  color: #2c3e50;
  font-size: 15px;
}

/* Professional Action Buttons */
.actions-cell {
  text-align: center;
  width: 100px;
  padding: 15px 8px;
}

.actions-container {
  display: flex;
  gap: 6px;
  justify-content: center;
  align-items: center;
}

.action-btn {
  width: 26px;
  height: 26px;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  background-color: #fafafa;
  color: #333;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 11px;
  padding: 0;
  margin: 0;
}

.action-btn:hover {
  border-color: #d0d0d0;
  background-color: #f0f0f0;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Override clients.css black background */
.actions-container .view-btn {
  background-color: #fafafa !important;
}

.view-btn:hover {
  color: #1890ff;
  border-color: #1890ff;
  background-color: #f6ffed;
}

.download-btn:hover {
  color: #52c41a;
  border-color: #52c41a;
  background-color: #f6ffed;
}

.delete-btn:hover {
  color: #ff4d4f;
  border-color: #ff4d4f;
  background-color: #fff2f0;
}

.no-invoices {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 40px;
  text-align: center;
}

.no-invoices p {
  font-size: 16px;
  color: #666;
  margin-bottom: 20px;
}

/* Error state */
.invoices-error {
  text-align: center;
  padding: 50px 20px;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.invoices-error i {
  font-size: 48px;
  color: #ff6b6b;
  margin-bottom: 20px;
}

.invoices-error h3 {
  font-size: 20px;
  margin-bottom: 10px;
  color: #333;
}

.invoices-error p {
  color: #666;
  max-width: 400px;
  margin: 0 auto 20px;
}

.retry-btn {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 500;
  transition: background-color 0.2s ease;
}

.retry-btn:hover {
  background-color: #0056b3;
}

.retry-btn i {
  margin-right: 8px;
}

/* Responsive styles */
@media (max-width: 992px) {
  .invoices-actions {
    flex-direction: column;
    align-items: flex-start;
  }

  .filter-buttons {
    width: 100%;
    overflow-x: auto;
    padding-bottom: 10px;
  }

  .upload-invoice-btn {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .invoices-container {
    padding: 1rem;
    width: 100%;
    box-sizing: border-box;
  }

  .invoices-table {
    display: block;
    overflow-x: auto;
    white-space: nowrap;
    width: 100%;
    box-sizing: border-box;
  }

  .invoices-table th,
  .invoices-table td {
    padding: 12px 15px;
  }

  .invoice-name-cell {
    min-width: 200px;
  }
}
