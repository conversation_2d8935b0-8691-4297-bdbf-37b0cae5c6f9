@echo off
echo Starting deployment process...

echo Building frontend...
cd frontend
call npm run build
if %ERRORLEVEL% neq 0 (
  echo Frontend build failed!
  exit /b %ERRORLEVEL%
)
cd ..

echo Building backend...
cd backend
call npm run build
if %ERRORLEVEL% neq 0 (
  echo Backend build failed!
  exit /b %ERRORLEVEL%
)
cd ..

echo Deployment build completed successfully!
echo You can now deploy the application to your hosting provider.

echo Frontend files are in: frontend/dist
echo Backend files are in: backend/dist

pause
