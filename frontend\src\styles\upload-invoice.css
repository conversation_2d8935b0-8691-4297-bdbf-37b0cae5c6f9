.upload-first-invoice-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
  width: 100%;
  box-sizing: border-box;
  position: relative;
}

.upload-first-invoice-container .page-title {
  position: absolute;
  top: 3rem;
  left: 50%;
  transform: translateX(-50%);
  background: #fff;
  padding: 0.5rem 2rem;
  margin: 0;
  font-size: 1.4rem;
  font-weight: 600;
  color: #333;
  z-index: 10;
  white-space: nowrap;
  border-radius: 25px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.upload-first-invoice-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #fff;
  border-radius: 12px;
  padding: 2.5rem 2.5rem 1.5rem 2.5rem; /* Reduced bottom padding */
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  position: relative;
  overflow: hidden;
  margin-bottom: 0; /* Ensure no margin at the bottom */
}

.upload-first-invoice-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 6px;
  background: linear-gradient(90deg, #000000, #2ecc71);
}

.upload-icon {
  margin-top: 1.5rem;
  margin-bottom: 2rem;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.upload-icon svg {
  width: 120px;
  height: 120px;
  filter: drop-shadow(0 4px 6px rgba(0, 0, 0, 0.1));
  transition: transform 0.3s ease;
}

.upload-icon::after {
  content: '';
  position: absolute;
  width: 140px;
  height: 140px;
  background-color: rgba(0, 0, 0, 0.08);
  border-radius: 50%;
  z-index: -1;
}

.upload-icon:hover svg {
  transform: scale(1.05);
}

.upload-description {
  font-size: 1.2rem;
  color: #444;
  margin-bottom: 2.5rem;
  text-align: center;
  line-height: 1.6;
  max-width: 90%;
}

.upload-area {
  width: 100%;
  min-height: 220px;
  border: 2px dashed #ccc;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 2.5rem;
  margin-bottom: 2rem;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: #f9f9f9;
  position: relative;
}

.upload-area:hover {
  border-color: #000000;
  background-color: rgba(0, 0, 0, 0.08);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.upload-area::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23e0e0e0' fill-opacity='0.4' fill-rule='evenodd'/%3E%3C/svg%3E");
  opacity: 0.5;
  z-index: 0;
  pointer-events: none;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #555;
  position: relative;
  z-index: 1;
}

.upload-placeholder svg {
  margin-bottom: 1.2rem;
  color: #000000;
  width: 60px;
  height: 60px;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.upload-placeholder p {
  font-size: 1.1rem;
  text-align: center;
  font-weight: 500;
  max-width: 80%;
  margin: 0 auto;
}

.pdf-preview, .image-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  position: relative;
  z-index: 1;
}

.pdf-preview svg {
  color: #e74c3c;
  margin-bottom: 1.2rem;
  width: 64px;
  height: 64px;
  filter: drop-shadow(0 3px 6px rgba(231, 76, 60, 0.2));
}

.image-preview img {
  max-width: 100%;
  max-height: 200px;
  border-radius: 8px;
  margin-bottom: 1.2rem;
  object-fit: contain;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 3px solid white;
}

.file-name {
  font-size: 1rem;
  color: #444;
  word-break: break-all;
  max-width: 100%;
  background-color: rgba(255, 255, 255, 0.8);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  font-weight: 500;
}

.error-message {
  color: #e74c3c;
  margin: 1.2rem 0;
  font-size: 1rem;
  background-color: rgba(231, 76, 60, 0.1);
  padding: 0.8rem 1.5rem;
  border-radius: 8px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.error-message::before {
  content: '⚠️';
  margin-right: 0.5rem;
}

.upload-actions {
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: 1rem;
  margin-top: 1.5rem;
}

.upload-btn, .skip-btn {
  padding: 1rem 2rem;
  border-radius: 50px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.upload-btn::after, .skip-btn::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: -100%;
  background: linear-gradient(90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0) 100%);
  transition: all 0.6s ease;
}

.upload-btn:hover::after, .skip-btn:hover::after {
  left: 100%;
}

.primary-btn {
  background-color: #111;
  color: white;
  border: none;
}

.primary-btn:hover {
  background-color: #333;
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.primary-btn:active {
  transform: translateY(0);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.primary-btn:disabled {
  background-color: #999;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.secondary-btn {
  background-color: white;
  color: #444;
  border: 1px solid #ddd;
}

.secondary-btn:hover {
  background-color: #f8f8f8;
  border-color: #999;
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.08);
}

.secondary-btn:active {
  transform: translateY(0);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.secondary-btn:disabled {
  color: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Page title styling */
.page-title {
  font-size: 2.2rem;
  margin-bottom: 1.5rem;
  color: #333;
  position: relative;
  display: inline-block;
}

.page-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background: linear-gradient(90deg, #000000, #2ecc71);
  border-radius: 2px;
}

/* Mobile responsiveness */
@media (max-width: 767px) {
  .upload-first-invoice-container {
    padding: 0.75rem;
    margin: 0;
    width: 100%;
  }

  .upload-first-invoice-content {
    padding: 1.25rem 1rem 1rem 1rem; /* Reduced padding for mobile */
    margin-bottom: 0;
    width: 100%;
    box-sizing: border-box;
  }

  .upload-icon svg {
    width: 80px;
    height: 80px;
  }

  .upload-description {
    font-size: 1rem;
    line-height: 1.4;
  }

  .upload-area {
    min-height: 160px;
    padding: 1rem;
  }

  .upload-area p {
    font-size: 0.9rem;
    margin: 0;
  }

  .page-title {
    font-size: 1.6rem;
    margin-bottom: 1rem;
    text-align: center;
    padding: 0 1rem;
  }

  /* Improve form layout on mobile */
  .metadata-section {
    padding: 1rem;
    margin-bottom: 1.5rem;
  }

  .metadata-section label {
    font-size: 0.85rem;
    margin-bottom: 0.4rem;
  }

  .form-input {
    padding: 0.6rem;
    font-size: 0.85rem;
  }

  .form-group {
    margin-bottom: 0.8rem;
  }

  /* Improve amount/currency layout on mobile */
  .amount-currency {
    gap: 0.4rem;
  }

  .currency-field {
    width: 70px;
  }
}

@media (min-width: 768px) {
  .upload-actions {
    flex-direction: row;
    justify-content: center;
    gap: 1.5rem;
  }

  .upload-btn, .skip-btn {
    width: auto;
    min-width: 180px;
  }
}

@media (min-width: 992px) {
  .upload-first-invoice-content {
    padding: 3rem 3rem 1.5rem 3rem; /* Reduced bottom padding */
    margin-bottom: 0;
  }

  .upload-btn, .skip-btn {
    min-width: 200px;
  }
}

/* Invoice Upload Component Styles */
.invoice-upload-container {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  padding-bottom: 0;
  margin-bottom: 0;
}

.invoice-upload-container h2 {
  margin-bottom: 1.5rem;
  color: #333;
  font-size: 1.5rem;
  font-weight: 600;
  text-align: center;
}

.file-upload-section {
  margin-bottom: 2rem;
}

.file-upload-section label {
  display: block;
  font-weight: 500;
  margin-bottom: 0.5rem;
  font-size: 1rem;
  color: #333;
}

.file-preview {
  margin-top: 1rem;
  text-align: center;
}

.metadata-section {
  margin-bottom: 2rem;
  background-color: #f9f9f9;
  padding: 1.5rem;
  border-radius: 8px;
  border: 1px solid #eee;
}

.metadata-section label {
  font-weight: 500;
  display: block;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
  color: #333;
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

@media (min-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr 1fr;
  }
}

.form-group {
  margin-bottom: 1rem;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.9rem;
}

.form-input:focus {
  outline: none;
  border-color: #000;
  box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.1);
}

/* Textarea specific styling */
.form-input[type="textarea"],
textarea.form-input {
  min-height: 80px;
  resize: vertical;
  font-family: inherit;
  line-height: 1.4;
}

.amount-currency {
  display: flex;
  gap: 0.5rem;
}

.amount-field {
  flex: 1;
}

.currency-field {
  width: 80px;
}

/* Success message */
.success-message {
  background-color: rgba(46, 204, 113, 0.1);
  border: 1px solid #2ecc71;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1.5rem;
  color: #333;
}

.success-message ul {
  margin-left: 1.5rem;
}

/* Extracted fields styling */
.extracted-field label {
  color: #2ecc71;
  font-weight: 600;
}

.extracted-input {
  border-color: #2ecc71 !important;
  background-color: rgba(46, 204, 113, 0.05);
}

.extracted-badge {
  background-color: #2ecc71;
  color: white;
  font-size: 0.7rem;
  padding: 0.1rem 0.4rem;
  border-radius: 4px;
  margin-left: 0.5rem;
  font-weight: normal;
  vertical-align: middle;
}

/* Extraction overlay */
.extraction-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.extraction-content {
  background-color: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  text-align: center;
  max-width: 400px;
}

.extraction-spinner {
  display: inline-block;
  width: 50px;
  height: 50px;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: #000;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

.extraction-content p {
  font-size: 1.2rem;
  margin-bottom: 0.5rem;
  color: #333;
}

.extraction-subtext {
  font-size: 0.9rem !important;
  color: #666 !important;
}

/* Black button styling */
.black-button {
  background-color: #000000 !important;
  color: white !important;
  transition: all 0.3s ease !important;
}

.black-button:hover {
  background-color: #333333 !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
}

.black-button:active {
  transform: translateY(0) !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

.black-button:disabled {
  background-color: #666666 !important;
  cursor: not-allowed !important;
  transform: none !important;
  box-shadow: none !important;
}

/* Direct black button styling without !important */
.black-button-direct {
  background-color: #000000;
  color: white;
  transition: all 0.3s ease;
}

.black-button-direct:hover {
  background-color: #333333;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.black-button-direct:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.black-button-direct:disabled {
  background-color: #666666;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Extra small mobile devices */
@media (max-width: 480px) {
  .upload-first-invoice-container {
    padding: 0.5rem;
    margin: 0;
    width: 100%;
  }

  .upload-first-invoice-content {
    padding: 1rem 0.75rem 0.75rem 0.75rem;
    width: 100%;
    box-sizing: border-box;
  }

  .upload-icon svg {
    width: 60px;
    height: 60px;
  }

  .upload-description {
    font-size: 0.9rem;
  }

  .upload-area {
    min-height: 140px;
    padding: 0.75rem;
  }

  .page-title {
    font-size: 1.4rem;
    text-align: center;
    padding: 0 0.75rem;
  }

  .metadata-section {
    padding: 0.75rem;
  }

  .form-input {
    padding: 0.5rem;
    font-size: 0.8rem;
  }

  .extraction-content {
    padding: 1.5rem;
    margin: 1rem;
    max-width: calc(100vw - 2rem);
  }

  .extraction-content p {
    font-size: 1rem;
  }

  .extraction-subtext {
    font-size: 0.8rem !important;
  }
}

/* Spinner animation */
@keyframes spin {
  to { transform: rotate(360deg); }
}

/* ========== MULTI-FILE UPLOAD STYLES ========== */
.multi-file-upload {
  min-height: 180px;
  border: 2px dashed #ccc;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 2rem;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: #f9f9f9;
  position: relative;
}

.multi-file-upload:hover {
  border-color: #000000;
  background-color: rgba(0, 0, 0, 0.05);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.upload-hint {
  font-size: 0.85rem;
  color: #666;
  text-align: center;
  margin-top: 0.5rem;
  line-height: 1.4;
}

.files-preview {
  margin-top: 1.5rem;
  padding: 1rem;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.files-preview h4 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 1rem;
  font-weight: 600;
}

.files-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.file-preview-item {
  background: white;
  border-radius: 8px;
  padding: 1rem;
  border: 1px solid #e0e0e0;
  position: relative;
  transition: all 0.2s ease;
}

.file-preview-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.file-preview-image {
  width: 100%;
  height: 120px;
  object-fit: cover;
  border-radius: 4px;
  margin-bottom: 0.5rem;
}

.file-preview-placeholder {
  width: 100%;
  height: 120px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  border-radius: 4px;
  margin-bottom: 0.5rem;
  color: #666;
}

.file-preview-placeholder span:first-child {
  font-size: 2rem;
  margin-bottom: 0.25rem;
}

.file-preview-placeholder span:last-child {
  font-size: 0.8rem;
  font-weight: 500;
}

.file-info {
  text-align: center;
}

.file-name {
  font-size: 0.85rem;
  color: #333;
  margin: 0 0 0.25rem 0;
  word-break: break-word;
  font-weight: 500;
}

.file-size {
  font-size: 0.75rem;
  color: #666;
  margin: 0 0 0.5rem 0;
}

.remove-file-btn {
  background: #ff4757;
  color: white;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  font-size: 0.8rem;
  cursor: pointer;
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.remove-file-btn:hover {
  background: #ff3742;
  transform: scale(1.1);
}

.extraction-detail {
  font-size: 0.85rem;
  color: #666;
  margin-top: 0.5rem;
  font-style: italic;
}

/* Mobile responsiveness for multi-file upload */
@media (max-width: 768px) {
  .files-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .file-preview-item {
    padding: 0.75rem;
  }

  .file-preview-image,
  .file-preview-placeholder {
    height: 100px;
  }

  .multi-file-upload {
    padding: 1.5rem;
    min-height: 150px;
  }

  .upload-hint {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .files-grid {
    grid-template-columns: 1fr;
  }

  .file-preview-image,
  .file-preview-placeholder {
    height: 80px;
  }

  .multi-file-upload {
    padding: 1rem;
    min-height: 120px;
  }

  .file-name {
    font-size: 0.8rem;
  }

  .file-size {
    font-size: 0.7rem;
  }
}
