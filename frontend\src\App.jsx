import { useState, useEffect } from 'react';
import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom';
import { Auth } from 'aws-amplify';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import Login from './pages/Login';
import Signup from './pages/Signup';
import ForgotPassword from './pages/ForgotPassword';
import ResetPassword from './pages/ResetPassword';
import UserTypeSelection from './pages/UserTypeSelection';
import IndividualInfoPage from './pages/IndividualInfoPage';
import SupplierInfoPage from './pages/SupplierInfoPage';
import BrokerInfoPage from './pages/BrokerInfoPage';
import ProfessionalInfoPage from './pages/ProfessionalInfoPage';
import UploadFirstInvoicePage from './pages/UploadFirstInvoicePage';
import HomePage from './pages/HomePage';
import ContactPage from './pages/ContactPage';
import AboutPage from './pages/AboutPage';
import HowItWorksPage from './pages/HowItWorksPage';
import FeaturesPage from './pages/FeaturesPage';
import Dashboard from './pages/Dashboard';
import Offers from './pages/Offers';
import Contracts from './pages/Contracts';
import ContractSigning from './pages/ContractSigning';
import Invoices from './pages/Invoices';
import Appointments from './pages/Appointments';
import Profile from './pages/Profile';
import Settings from './pages/Settings';
import Support from './pages/Support';
import TicketForm from './pages/TicketForm';
import BusinessDashboard from './pages/BusinessDashboard';
import Clients from './pages/Clients';
import Analytics from './pages/Analytics';
import CreateOffer from './pages/CreateOffer';
// Admin Management Pages
import AdminDashboard from './pages/AdminDashboard';
import ManageUsers from './pages/admin/ManageUsers';
import ManageBrokers from './pages/admin/ManageBrokers';
import ManageSuppliers from './pages/admin/ManageSuppliers';
import AllContracts from './pages/admin/AllContracts';
import QuoteManagement from './pages/admin/QuoteManagement';
import DocumentTemplates from './pages/admin/DocumentTemplates';
import NotificationManagement from './pages/admin/NotificationManagement';
import AdminAnalytics from './pages/admin/Analytics';
import InvitationManagement from './pages/admin/InvitationManagement';
import InvitedRegistration from './pages/InvitedRegistration';
import RegistrationSuccess from './pages/RegistrationSuccess';
import ScrollToTop from './components/ScrollToTop';
import ProtectedRoute from './components/ProtectedRoute';
import PrivateRoute from './components/PrivateRoute';
import AdminRouteGuard from './components/AdminRouteGuard';

// Placeholder imports for components that will be created
const MyOffers = () => <div>My Offers - Coming Soon</div>;
const SupplierContracts = () => <div>Supplier Contracts - Coming Soon</div>;
const AddClient = () => <div>Add Client - Coming Soon</div>;
const Deals = () => <div>Deals - Coming Soon</div>;
const CompareOffers = () => <div>Compare Offers - Coming Soon</div>;
import AuthenticatedLayout from './components/layouts/AuthenticatedLayout';
import { AuthProvider } from './context/AuthContext';
import './styles/app.css';
import './styles/layout.css';
import './styles/upload-invoice.css';

function App() {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    // SCROLL-TO-TOP FIX: Override browser scroll restoration
    if ('scrollRestoration' in history) {
      history.scrollRestoration = 'manual';
      console.log('🔧 App: Disabled browser scroll restoration');
    }

    // Force scroll to top on app initialization
    window.scrollTo(0, 0);
    document.documentElement.scrollTop = 0;
    document.body.scrollTop = 0;
    console.log('🚀 App: Initial scroll to top executed');

    // Add global scroll-to-top enforcement
    const forceScrollToTopGlobal = () => {
      window.scrollTo(0, 0);
      document.documentElement.scrollTop = 0;
      document.body.scrollTop = 0;
    };

    // Listen for popstate events (back/forward navigation)
    window.addEventListener('popstate', forceScrollToTopGlobal);

    // Listen for hashchange events
    window.addEventListener('hashchange', forceScrollToTopGlobal);

    console.log('🔧 App: Global scroll-to-top event listeners added');

    // Clean up authentication state on app initialization
    const cleanupAuthState = async () => {
      try {
        // First, check if there are any residual authentication tokens
        const hasResidualTokens = () => {
          try {
            // Check for AWS Amplify related keys in localStorage
            const allKeys = Object.keys(localStorage);

            // Check for Cognito tokens
            const hasCognitoTokens = allKeys.some(key =>
              key.startsWith('CognitoIdentityServiceProvider') ||
              key.includes('idToken') ||
              key.includes('accessToken') ||
              key.includes('refreshToken')
            );

            // Check for our own auth flags
            const hasAuthFlags =
              localStorage.getItem('isAuthenticated') === 'true' ||
              localStorage.getItem('userType') !== null;

            return hasCognitoTokens || hasAuthFlags;
          } catch (error) {
            console.error('Error checking for residual auth state:', error);
            return false;
          }
        };

        // Check if we have residual tokens
        if (hasResidualTokens()) {
          // Try to verify if the user is actually authenticated
          try {
            await Auth.currentAuthenticatedUser();
            // If we get here, the user is authenticated, so we don't need to clean up
            console.log('App initialization: User is authenticated, keeping auth state');
          } catch (error) {
            // If we get here, the user is not authenticated, so clean up
            console.log('App initialization: Found residual tokens but user is not authenticated, cleaning up');
            localStorage.clear();
            sessionStorage.clear();
          }
        }

        // Remove problematic keys
        localStorage.removeItem('undefined');
        localStorage.removeItem('__uploadInProgress');
        localStorage.removeItem('__uploadInProgress__');
      } catch (error) {
        // Silently fail
        console.error('Error during auth state cleanup:', error);
      }
    };

    // Run the cleanup
    cleanupAuthState();

    // Simulate initial loading
    const timer = setTimeout(() => {
      setLoading(false);
    }, 1500);

    return () => {
      clearTimeout(timer);
      // Clean up event listeners
      window.removeEventListener('popstate', forceScrollToTopGlobal);
      window.removeEventListener('hashchange', forceScrollToTopGlobal);
      console.log('🧹 App: Cleaned up scroll-to-top event listeners');
    };
  }, []);

  // Error boundary fallback
  if (error) {
    return (
      <div style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '100vh',
        padding: '2rem',
        textAlign: 'center'
      }}>
        <h1 style={{ color: '#ff3b30', marginBottom: '1rem' }}>Something went wrong</h1>
        <p style={{ marginBottom: '1rem' }}>{error.message || 'An unexpected error occurred'}</p>
        <button
          onClick={() => window.location.reload()}
          style={{
            padding: '0.5rem 1rem',
            backgroundColor: '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          Reload Page
        </button>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="container">
        <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center' }}>
          <h1 style={{ fontSize: '2rem', marginBottom: '1rem' }}>MY ENERGY BILL</h1>
          <div style={{ marginTop: '1rem', fontSize: '1.2rem', fontWeight: '500' }}>Loading...</div>
        </div>
      </div>
    );
  }

  try {
    return (
      <AuthProvider>
        <BrowserRouter
          future={{
            v7_startTransition: true,
            v7_relativeSplatPath: true
          }}
        >
          <ScrollToTop />
          <Routes>
          {/* Public Routes */}
          <Route path="/" element={<HomePage />} />
          <Route path="/contact" element={<ContactPage />} />
          <Route path="/about" element={<AboutPage />} />
          <Route path="/how-it-works" element={<HowItWorksPage />} />
          <Route path="/features" element={<FeaturesPage />} />
          <Route path="/login" element={<Login />} />
          <Route path="/signup" element={<Signup />} />
          <Route path="/register/invited" element={<InvitedRegistration />} />
          <Route path="/registration-success" element={<RegistrationSuccess />} />
          <Route path="/forgot-password" element={<ForgotPassword />} />
          <Route path="/reset-password" element={<ResetPassword />} />

          {/* Authentication Routes - These require login but don't check profile completion */}
          <Route path="/user-type" element={
            <PrivateRoute>
              <AdminRouteGuard>
                <AuthenticatedLayout>
                  <UserTypeSelection />
                </AuthenticatedLayout>
              </AdminRouteGuard>
            </PrivateRoute>
          } />
          <Route path="/individual-info" element={
            <PrivateRoute>
              <AuthenticatedLayout>
                <IndividualInfoPage />
              </AuthenticatedLayout>
            </PrivateRoute>
          } />
          <Route path="/professional-info" element={
            <PrivateRoute>
              <AuthenticatedLayout>
                <ProfessionalInfoPage />
              </AuthenticatedLayout>
            </PrivateRoute>
          } />
          <Route path="/supplier-info" element={
            <PrivateRoute>
              <AuthenticatedLayout>
                <SupplierInfoPage />
              </AuthenticatedLayout>
            </PrivateRoute>
          } />
          <Route path="/broker-info" element={
            <PrivateRoute>
              <AuthenticatedLayout>
                <BrokerInfoPage />
              </AuthenticatedLayout>
            </PrivateRoute>
          } />

          <Route path="/upload-first-invoice" element={
            <PrivateRoute>
              <AuthenticatedLayout>
                <UploadFirstInvoicePage />
              </AuthenticatedLayout>
            </PrivateRoute>
          } />

          {/* Dashboard Route - Direct access for suppliers/brokers, protected for others */}
          <Route path="/dashboard" element={
            <PrivateRoute>
              <Dashboard />
            </PrivateRoute>
          } />
          <Route path="/new-ticket" element={
            <PrivateRoute>
              <ProtectedRoute>
                <TicketForm />
              </ProtectedRoute>
            </PrivateRoute>
          } />
          <Route path="/offers" element={
            <PrivateRoute>
              <ProtectedRoute>
                <Offers />
              </ProtectedRoute>
            </PrivateRoute>
          } />
          <Route path="/contracts" element={
            <PrivateRoute>
              <ProtectedRoute>
                <Contracts />
              </ProtectedRoute>
            </PrivateRoute>
          } />
          <Route path="/contract-signed" element={
            <PrivateRoute>
              <ProtectedRoute>
                <ContractSigning />
              </ProtectedRoute>
            </PrivateRoute>
          } />
          <Route path="/invoices" element={
            <PrivateRoute>
              <ProtectedRoute>
                <Invoices />
              </ProtectedRoute>
            </PrivateRoute>
          } />
          <Route path="/appointments" element={
            <PrivateRoute>
              <ProtectedRoute>
                <Appointments />
              </ProtectedRoute>
            </PrivateRoute>
          } />

          <Route path="/profile" element={
            <PrivateRoute>
              <ProtectedRoute>
                <Profile />
              </ProtectedRoute>
            </PrivateRoute>
          } />
          <Route path="/settings" element={
            <PrivateRoute>
              <ProtectedRoute>
                <Settings />
              </ProtectedRoute>
            </PrivateRoute>
          } />
          <Route path="/support" element={
            <PrivateRoute>
              <ProtectedRoute>
                <Support />
              </ProtectedRoute>
            </PrivateRoute>
          } />

          {/* Business-specific routes */}
          <Route path="/business-dashboard" element={
            <PrivateRoute>
              <ProtectedRoute>
                <BusinessDashboard />
              </ProtectedRoute>
            </PrivateRoute>
          } />
          <Route path="/clients" element={
            <PrivateRoute>
              <ProtectedRoute>
                <Clients />
              </ProtectedRoute>
            </PrivateRoute>
          } />
          <Route path="/analytics" element={
            <PrivateRoute>
              <ProtectedRoute>
                <Analytics />
              </ProtectedRoute>
            </PrivateRoute>
          } />

          {/* Supplier-specific routes - No profile completion check needed */}
          <Route path="/create-offer" element={
            <PrivateRoute>
              <CreateOffer />
            </PrivateRoute>
          } />
          <Route path="/my-offers" element={
            <PrivateRoute>
              <MyOffers />
            </PrivateRoute>
          } />
          <Route path="/supplier-contracts" element={
            <PrivateRoute>
              <SupplierContracts />
            </PrivateRoute>
          } />

          {/* Broker-specific routes - No profile completion check needed */}
          <Route path="/add-client" element={
            <PrivateRoute>
              <AddClient />
            </PrivateRoute>
          } />
          <Route path="/broker-deals" element={
            <PrivateRoute>
              <Deals />
            </PrivateRoute>
          } />
          <Route path="/commission-tracker" element={
            <PrivateRoute>
              <div>Commission Tracker - Coming Soon</div>
            </PrivateRoute>
          } />
          <Route path="/compare-offers" element={
            <PrivateRoute>
              <CompareOffers />
            </PrivateRoute>
          } />

          {/* Admin-specific routes */}
          <Route path="/admin" element={
            <PrivateRoute>
              <AdminDashboard />
            </PrivateRoute>
          } />
          <Route path="/admin/dashboard" element={
            <PrivateRoute>
              <AdminDashboard />
            </PrivateRoute>
          } />
          <Route path="/admin/users" element={
            <PrivateRoute>
              <ManageUsers />
            </PrivateRoute>
          } />
          <Route path="/admin/brokers" element={
            <PrivateRoute>
              <ManageBrokers />
            </PrivateRoute>
          } />
          <Route path="/admin/suppliers" element={
            <PrivateRoute>
              <ManageSuppliers />
            </PrivateRoute>
          } />
          <Route path="/admin/contracts" element={
            <PrivateRoute>
              <AllContracts />
            </PrivateRoute>
          } />
          <Route path="/admin/quotes" element={
            <PrivateRoute>
              <QuoteManagement />
            </PrivateRoute>
          } />
          <Route path="/admin/templates" element={
            <PrivateRoute>
              <DocumentTemplates />
            </PrivateRoute>
          } />
          <Route path="/admin/notifications" element={
            <PrivateRoute>
              <NotificationManagement />
            </PrivateRoute>
          } />
          <Route path="/admin/analytics" element={
            <PrivateRoute>
              <AdminAnalytics />
            </PrivateRoute>
          } />
          <Route path="/admin/invitations" element={
            <PrivateRoute>
              <InvitationManagement />
            </PrivateRoute>
          } />

          {/* Fallback Route */}
          <Route path="*" element={<Navigate to="/" replace />} />
        </Routes>

        {/* Toast Container */}
        <ToastContainer
          position="top-right"
          autoClose={5000}
          hideProgressBar={false}
          newestOnTop={false}
          closeOnClick
          rtl={false}
          pauseOnFocusLoss
          draggable
          pauseOnHover
          theme="light"
        />
      </BrowserRouter>
    </AuthProvider>
  );
  } catch (renderError) {
    console.error('App render error:', renderError);
    setError(renderError);
    return null;
  }
}

export default App;
