const axios = require('axios');

const freshToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************.LUxr8mgk_g-6HT7WT5NguTqwRrFkosPIK1MploKxs8Q';

async function testAPI() {
  console.log('Testing password reset token verification API...');
  
  try {
    const response = await axios.post('http://localhost:3000/auth/verify-reset-token', {
      token: freshToken
    });
    
    console.log('✅ API Response Success:');
    console.log('Status:', response.status);
    console.log('Data:', JSON.stringify(response.data, null, 2));
    
  } catch (error) {
    console.log('❌ API Error:');
    if (error.response) {
      console.log('Status:', error.response.status);
      console.log('Data:', JSON.stringify(error.response.data, null, 2));
    } else {
      console.log('Error:', error.message);
    }
  }
}

testAPI();
