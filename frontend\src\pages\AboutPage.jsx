import React, { useEffect } from 'react';
import { Link } from 'react-router-dom';
import logoImage from '../assets/logo.jpeg';
import HomeHeader from '../components/HomeHeader';
import Footer from '../components/Footer';
import { useForceScrollToTop } from '../utils/scrollToTop';
import '../styles/app.css';
import '../styles/pages.css';

const AboutPage = () => {
  // Force scroll to top when page loads
  useEffect(() => {
    useForceScrollToTop();
  }, []);

  return (
    <div className="home-container">
      {/* Header */}
      <HomeHeader />

      {/* Hero Section */}
      <section className="page-hero">
        <div className="page-hero-content">
          <h1 className="page-hero-title">About Us</h1>
          <p className="page-hero-subtitle">
            Ethical, independent, and truly client-focused.
          </p>
        </div>
      </section>

      {/* Our Story Section */}
      <section className="about-section">
        <div className="about-container">
          <div className="about-content">
            <h2 className="section-title">Our Story</h2>
            <p className="about-text">
              My Energy Bill was created by professionals with over 10 years of experience in the energy sector — having worked both with market-leading brokerage firms and independently.
            </p>
            <p className="about-text">
              After years of observing the lack of regulation and transparency in the energy brokerage industry, they decided to build a better model: One that prioritizes the client's interest, avoids aggressive tactics, and ensures that every partner — broker or supplier — is carefully selected.
            </p>
            <p className="about-text">
              We believe in limiting unjustified margins, offering honest and clear comparisons, and putting an end to unsolicited sales calls. Our mission is to bring clarity, fairness, and trust back into energy contract management — for both individuals and professionals.
            </p>
          </div>
          <div className="about-image">
            <div className="about-image-placeholder">
              <svg xmlns="http://www.w3.org/2000/svg" width="80" height="80" fill="currentColor" viewBox="0 0 16 16">
                <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
                <path d="M10.97 4.97a.235.235 0 0 0-.02.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05z"/>
              </svg>
              <p>Trusted Excellence</p>
            </div>
          </div>
        </div>
      </section>

      {/* Our Mission Section */}
      <section className="mission-section">
        <div className="mission-container">
          <div className="mission-content">
            <h2 className="section-title">Our Values</h2>
            <div className="mission-cards">
              <div className="mission-card">
                <div className="mission-icon">
                  <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="currentColor" viewBox="0 0 16 16">
                    <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
                    <path d="M10.97 4.97a.235.235 0 0 0-.02.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05z"/>
                  </svg>
                </div>
                <h3>Client-First Approach</h3>
                <p>We prioritize our clients' interests above all else, avoiding aggressive tactics and ensuring every recommendation serves their best interests.</p>
              </div>
              <div className="mission-card">
                <div className="mission-icon">
                  <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="currentColor" viewBox="0 0 16 16">
                    <path d="M8 1a2.5 2.5 0 0 1 2.5 2.5V4h-5v-.5A2.5 2.5 0 0 1 8 1zm3.5 3v-.5a3.5 3.5 0 1 0-7 0V4H1v10a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V4h-3.5zM2 5h12v9a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V5z"/>
                  </svg>
                </div>
                <h3>Transparency & Trust</h3>
                <p>We believe in honest and clear comparisons, limiting unjustified margins, and bringing clarity back to energy contract management.</p>
              </div>
              <div className="mission-card">
                <div className="mission-icon">
                  <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="currentColor" viewBox="0 0 16 16">
                    <path d="M7 14s-1 0-1-1 1-4 5-4 5 3 5 4-1 1-1 1H7zm4-6a3 3 0 1 0 0-6 3 3 0 0 0 0 6z"/>
                    <path fillRule="evenodd" d="M5.216 14A2.238 2.238 0 0 1 5 13c0-1.355.68-2.75 1.936-3.72A6.325 6.325 0 0 0 5 9c-4 0-5 3-5 4s1 1 1 1h4.216z"/>
                    <path d="M4.5 8a2.5 2.5 0 1 0 0-5 2.5 2.5 0 0 0 0 5z"/>
                  </svg>
                </div>
                <h3>Ethical Standards</h3>
                <p>We carefully select every partner and put an end to unsolicited sales calls, maintaining the highest ethical standards in the industry.</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Our Commitment Section */}
      <section className="commitment-section">
        <div className="commitment-container">
          <h2 className="section-title">Our Commitment</h2>
          <div className="commitment-content">
            <div className="commitment-text">
              <p className="commitment-description">
                Our mission is to bring clarity, fairness, and trust back into energy contract management — for both individuals and professionals.
              </p>
              <p className="commitment-description">
                We are committed to maintaining the highest standards of integrity in everything we do, ensuring that our clients always come first.
              </p>
            </div>
            <div className="commitment-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="120" height="120" fill="currentColor" viewBox="0 0 16 16">
                <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
                <path d="M10.97 4.97a.235.235 0 0 0-.02.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05z"/>
              </svg>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="cta-section">
        <div className="cta-content">
          <h2 className="cta-title">Experience the Difference</h2>
          <p className="cta-description">
            Join thousands who have chosen transparency, fairness, and expert guidance for their energy needs.
          </p>
          <Link to="/signup" className="btn btn-primary btn-large">Get Started Today</Link>
        </div>
      </section>

      {/* Footer */}
      <Footer />
    </div>
  );
};

export default AboutPage;
