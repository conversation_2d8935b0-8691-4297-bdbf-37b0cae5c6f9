import React, { useEffect } from 'react';
import { Link } from 'react-router-dom';
import logoImage from '../assets/logo.jpeg';
import HomeHeader from '../components/HomeHeader';
import Footer from '../components/Footer';
import { useForceScrollToTop } from '../utils/scrollToTop';
import '../styles/app.css';
import '../styles/pages.css';

const AboutPage = () => {
  // Force scroll to top when page loads
  useEffect(() => {
    useForceScrollToTop();
  }, []);

  return (
    <div className="home-container">
      {/* Header */}
      <HomeHeader />

      {/* Hero Section */}
      <section className="page-hero">
        <div className="page-hero-content">
          <h1 className="page-hero-title">About Us</h1>
          <p className="page-hero-subtitle">
            Learn about our mission to revolutionize the energy market and help customers save money.
          </p>
        </div>
      </section>

      {/* Our Story Section */}
      <section className="about-section">
        <div className="about-container">
          <div className="about-content">
            <h2 className="section-title">Our Story</h2>
            <p className="about-text">
              My Energy Bill was founded in 2018 with a simple mission: to make energy bills more transparent and help consumers save money.
              Our founders, experienced professionals from the energy sector, recognized that many people were paying too much for their energy
              due to complex tariffs and a lack of market transparency.
            </p>
            <p className="about-text">
              What started as a small startup has grown into a trusted platform connecting thousands of consumers with energy suppliers across France.
              We've helped our customers save an average of €350 per year on their energy bills, and we're just getting started.
            </p>
            <p className="about-text">
              Our platform uses advanced algorithms to analyze your current energy consumption and find the best deals tailored to your specific needs.
              We believe that everyone deserves access to affordable energy and the tools to make informed decisions about their energy providers.
            </p>
          </div>
          <div className="about-image">
            <div className="about-image-placeholder">
              <svg xmlns="http://www.w3.org/2000/svg" width="80" height="80" fill="currentColor" viewBox="0 0 16 16">
                <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
                <path d="M7.002 11a1 1 0 1 1 2 0 1 1 0 0 1-2 0zM7.1 4.995a.905.905 0 1 1 1.8 0l-.35 3.507a.552.552 0 0 1-1.1 0L7.1 4.995z"/>
              </svg>
              <p>Company History Image</p>
            </div>
          </div>
        </div>
      </section>

      {/* Our Mission Section */}
      <section className="mission-section">
        <div className="mission-container">
          <div className="mission-content">
            <h2 className="section-title">Our Mission</h2>
            <div className="mission-cards">
              <div className="mission-card">
                <div className="mission-icon">
                  <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="currentColor" viewBox="0 0 16 16">
                    <path d="M8 0a8 8 0 1 0 0 16A8 8 0 0 0 8 0ZM2.04 4.326c.325 1.329 2.532 2.54 3.717 3.19.48.263.793.434.743.484-.08.08-.162.158-.242.234-.416.396-.787.749-.758 1.266.035.634.618.824 1.214 1.017.577.188 1.168.38 1.286.983.082.417-.075.988-.22 1.52-.215.782-.406 1.48.22 1.48 1.5-.5 3.798-3.186 4-5 .138-1.243-2-2-3.5-2.5-.478-.16-.755.081-.99.284-.172.15-.322.279-.51.216-.445-.148-2.5-2-1.5-2.5.78-.39.952-.171 1.227.182.078.099.163.208.273.318.609.304.662-.132.723-.633.039-.322.081-.671.277-.867.434-.434 1.265-.791 2.028-1.12.712-.306 1.365-.587 1.579-.88A7 7 0 1 1 2.04 4.327Z"/>
                  </svg>
                </div>
                <h3>Transparency</h3>
                <p>We believe in complete transparency in the energy market. Our platform provides clear, easy-to-understand information about energy tariffs and contracts.</p>
              </div>
              <div className="mission-card">
                <div className="mission-icon">
                  <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="currentColor" viewBox="0 0 16 16">
                    <path d="M6.5 1A1.5 1.5 0 0 0 5 2.5V3H1.5A1.5 1.5 0 0 0 0 4.5v8A1.5 1.5 0 0 0 1.5 14h13a1.5 1.5 0 0 0 1.5-1.5v-8A1.5 1.5 0 0 0 14.5 3H11v-.5A1.5 1.5 0 0 0 9.5 1h-3zm0 1h3a.5.5 0 0 1 .5.5V3H6v-.5a.5.5 0 0 1 .5-.5zm1.886 6.914L15 7.151V12.5a.5.5 0 0 1-.5.5h-13a.5.5 0 0 1-.5-.5V7.15l6.614 1.764a1.5 1.5 0 0 0 .772 0zM1.5 4h13a.5.5 0 0 1 .5.5v1.616L8.129 7.948a.5.5 0 0 1-.258 0L1 6.116V4.5a.5.5 0 0 1 .5-.5z"/>
                  </svg>
                </div>
                <h3>Savings</h3>
                <p>Our goal is to help consumers save money on their energy bills by finding the best deals and negotiating better rates with suppliers.</p>
              </div>
              <div className="mission-card">
                <div className="mission-icon">
                  <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="currentColor" viewBox="0 0 16 16">
                    <path d="M8 2a5.53 5.53 0 0 0-3.594 1.342c-.766.66-1.321 1.52-1.464 2.383C1.266 6.095 0 7.555 0 9.318 0 11.366 1.708 13 3.781 13h8.906C14.502 13 16 11.57 16 9.773c0-1.636-1.242-2.969-2.834-3.194C12.923 3.999 10.69 2 8 2zm2.354 5.146a.5.5 0 0 1-.708.708L8.5 6.707V10.5a.5.5 0 0 1-1 0V6.707L6.354 7.854a.5.5 0 1 1-.708-.708l2-2a.5.5 0 0 1 .708 0l2 2z"/>
                  </svg>
                </div>
                <h3>Sustainability</h3>
                <p>We're committed to promoting sustainable energy solutions and helping consumers reduce their carbon footprint through better energy choices.</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="team-section">
        <div className="team-container">
          <h2 className="section-title">Our Team</h2>
          <p className="team-intro">
            Our diverse team of energy experts, data scientists, and customer service professionals is dedicated to making energy more affordable for everyone.
          </p>
          <div className="team-grid">
            <div className="team-member">
              <div className="team-member-photo">
                <div className="photo-placeholder">
                  <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" fill="currentColor" viewBox="0 0 16 16">
                    <path d="M11 6a3 3 0 1 1-6 0 3 3 0 0 1 6 0z"/>
                    <path fillRule="evenodd" d="M0 8a8 8 0 1 1 16 0A8 8 0 0 1 0 8zm8-7a7 7 0 0 0-5.468 11.37C3.242 11.226 4.805 10 8 10s4.757 1.225 5.468 2.37A7 7 0 0 0 8 1z"/>
                  </svg>
                </div>
              </div>
              <h3>Sophie Martin</h3>
              <p className="team-member-role">CEO & Co-Founder</p>
              <p className="team-member-bio">
                With over 15 years of experience in the energy sector, Sophie leads our company's vision and strategy.
              </p>
            </div>
            <div className="team-member">
              <div className="team-member-photo">
                <div className="photo-placeholder">
                  <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" fill="currentColor" viewBox="0 0 16 16">
                    <path d="M11 6a3 3 0 1 1-6 0 3 3 0 0 1 6 0z"/>
                    <path fillRule="evenodd" d="M0 8a8 8 0 1 1 16 0A8 8 0 0 1 0 8zm8-7a7 7 0 0 0-5.468 11.37C3.242 11.226 4.805 10 8 10s4.757 1.225 5.468 2.37A7 7 0 0 0 8 1z"/>
                  </svg>
                </div>
              </div>
              <h3>Thomas Dubois</h3>
              <p className="team-member-role">CTO & Co-Founder</p>
              <p className="team-member-bio">
                Thomas leads our technology team, developing innovative solutions to analyze energy consumption patterns.
              </p>
            </div>
            <div className="team-member">
              <div className="team-member-photo">
                <div className="photo-placeholder">
                  <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" fill="currentColor" viewBox="0 0 16 16">
                    <path d="M11 6a3 3 0 1 1-6 0 3 3 0 0 1 6 0z"/>
                    <path fillRule="evenodd" d="M0 8a8 8 0 1 1 16 0A8 8 0 0 1 0 8zm8-7a7 7 0 0 0-5.468 11.37C3.242 11.226 4.805 10 8 10s4.757 1.225 5.468 2.37A7 7 0 0 0 8 1z"/>
                  </svg>
                </div>
              </div>
              <h3>Marie Lefevre</h3>
              <p className="team-member-role">Head of Customer Relations</p>
              <p className="team-member-bio">
                Marie ensures that our customers receive exceptional service and support throughout their journey with us.
              </p>
            </div>
            <div className="team-member">
              <div className="team-member-photo">
                <div className="photo-placeholder">
                  <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" fill="currentColor" viewBox="0 0 16 16">
                    <path d="M11 6a3 3 0 1 1-6 0 3 3 0 0 1 6 0z"/>
                    <path fillRule="evenodd" d="M0 8a8 8 0 1 1 16 0A8 8 0 0 1 0 8zm8-7a7 7 0 0 0-5.468 11.37C3.242 11.226 4.805 10 8 10s4.757 1.225 5.468 2.37A7 7 0 0 0 8 1z"/>
                  </svg>
                </div>
              </div>
              <h3>Pierre Moreau</h3>
              <p className="team-member-role">Energy Market Analyst</p>
              <p className="team-member-bio">
                Pierre analyzes energy market trends to help us negotiate the best deals with suppliers for our customers.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Partners Section */}
      <section className="partners-section">
        <div className="partners-container">
          <h2 className="section-title">Our Partners</h2>
          <p className="partners-intro">
            We work with leading energy suppliers and technology companies to provide the best service to our customers.
          </p>
          <div className="partners-grid">
            <div className="partner-logo">
              <div className="logo-placeholder">
                <p>Partner Logo</p>
              </div>
            </div>
            <div className="partner-logo">
              <div className="logo-placeholder">
                <p>Partner Logo</p>
              </div>
            </div>
            <div className="partner-logo">
              <div className="logo-placeholder">
                <p>Partner Logo</p>
              </div>
            </div>
            <div className="partner-logo">
              <div className="logo-placeholder">
                <p>Partner Logo</p>
              </div>
            </div>
            <div className="partner-logo">
              <div className="logo-placeholder">
                <p>Partner Logo</p>
              </div>
            </div>
            <div className="partner-logo">
              <div className="logo-placeholder">
                <p>Partner Logo</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="cta-section">
        <div className="cta-content">
          <h2 className="cta-title">Join Our Mission</h2>
          <p className="cta-description">
            Start saving on your energy bills today and be part of our mission to make energy more affordable for everyone.
          </p>
          <Link to="/signup" className="btn btn-primary btn-large">Sign Up</Link>
        </div>
      </section>

      {/* Footer */}
      <Footer />
    </div>
  );
};

export default AboutPage;
