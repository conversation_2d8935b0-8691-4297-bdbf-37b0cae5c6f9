import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { nodePolyfills } from 'vite-plugin-node-polyfills'

// https://vitejs.dev/config/
export default defineConfig({
  // Use relative paths for assets so the app works when opened directly
  base: '',
  plugins: [
    react(),
    nodePolyfills({
      // Whether to polyfill `node:` protocol imports.
      protocolImports: true,
    }),
  ],
  server: {
    port: 8080,
    strictPort: false,
    hmr: {
      overlay: true
    },
    watch: {
      usePolling: false
    },
    proxy: {
      '/api': {
        target: 'http://localhost:3000',
        changeOrigin: true,
        secure: false
      },
      '/auth': {
        target: 'http://localhost:3000',
        changeOrigin: true,
        secure: false
      }
    }
  },
  define: {
    'process.env.REACT_APP_API_URL': JSON.stringify(process.env.VITE_APP_API_URL || 'http://localhost:3000'),
    // Fix for Buffer is not defined
    'global': 'window',
    // Ensure process.env.NODE_ENV is properly set
    'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV || 'development'),
  },
  resolve: {
    alias: {
      // This is needed for AWS Amplify and other libraries that use Node.js modules
      './runtimeConfig': './runtimeConfig.browser',
    }
  },
  optimizeDeps: {
    include: ['buffer', 'process'],
    esbuildOptions: {
      // Node.js global to browser globalThis
      define: {
        global: 'globalThis',
      },
    },
  },
  build: {
    // Output to build folder instead of dist
    outDir: 'build',
    // Set target to a more compatible version
    target: 'es2015',
    // Disable minification for better debugging if needed
    minify: process.env.NODE_ENV === 'production' ? 'terser' : false,
    commonjsOptions: {
      // This prevents the build from trying to process axios
      transformMixedEsModules: true
    },
    rollupOptions: {
      // Handle warnings
      onwarn(warning, warn) {
        // Ignore specific warnings
        if (warning.code === 'CIRCULAR_DEPENDENCY' && warning.importer && warning.importer.includes('axios')) {
          return;
        }
        // Use default warning behavior for other warnings
        warn(warning);
      },
      output: {
        // Improve chunking for better performance
        manualChunks: {
          vendor: ['react', 'react-dom', 'react-router-dom'],
          amplify: ['aws-amplify'],
        },
      },
    },
  }
})
