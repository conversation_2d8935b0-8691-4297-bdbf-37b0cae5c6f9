import React from 'react';

const SupplierAreasStep = ({ formData, onChange, onMultiSelectChange, onNext, onPrev }) => {
  const serviceAreaOptions = [
    'Paris', 'Lyon', 'Marseille', 'Toulouse', 'Nice', 'Nantes',
    'Strasbourg', 'Montpellier', 'Bordeaux', 'Lille', 'Rennes',
    'Reims', 'Le Havre', 'Saint-Étienne', 'Toulon', 'Grenoble'
  ];

  const handleNext = () => {
    onNext();
  };

  return (
    <div className="step-content">
      <div className="stepper-header">
        <h2 className="page-title">Service Areas</h2>
        <p className="page-subtitle">Select the areas where you provide energy services</p>
      </div>

      <div className="stepper-form">
        <div className="form-row">
          <div className="form-group">
            <label className="form-label">Service Areas <span className="optional">(optional)</span></label>
            <p className="hint-text">Choose the cities and regions where you offer energy supply services</p>

            <div className="checkbox-group">
              {serviceAreaOptions.map(area => (
                <div key={area} className="checkbox-item">
                  <input
                    type="checkbox"
                    id={`area-${area}`}
                    checked={formData.serviceAreas.includes(area)}
                    onChange={() => onMultiSelectChange('serviceAreas', area)}
                  />
                  <label htmlFor={`area-${area}`}>{area}</label>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      <div className="stepper-buttons">
        <button type="button" className="stepper-button stepper-button-prev" onClick={onPrev}>
          <i className="fas fa-arrow-left"></i> Previous
        </button>
        <button type="button" className="stepper-button stepper-button-next" onClick={handleNext}>
          Next <i className="fas fa-arrow-right"></i>
        </button>
      </div>
    </div>
  );
};

export default SupplierAreasStep;
