import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { showSuccessMessage, showErrorMessage, showInfoMessage } from '../utils/toastNotifications';
import DashboardLayout from '../components/DashboardLayout';
import Spinner from '../components/Spinner';
import brokerService from '../services/broker.service';
import logger from '../utils/logger';
import '../styles/clients.css';

const Clients = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [clients, setClients] = useState([]);
  const [filteredClients, setFilteredClients] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [filter, setFilter] = useState('all');
  const [userType, setUserType] = useState(null);

  useEffect(() => {
    // Check if user is broker or supplier
    const storedUserType = localStorage.getItem('userType');

    if (storedUserType !== 'broker' && storedUserType !== 'supplier') {
      navigate('/dashboard');
      return;
    }

    setUserType(storedUserType);

    // Fetch clients data
    const fetchClients = async () => {
      try {
        setLoading(true);

        // In a real app, this would be an API call
        // For now, we'll use mock data
        setTimeout(() => {
          const mockClients = [
            {
              id: 1,
              name: 'John Smith',
              email: '<EMAIL>',
              phone: '+33 6 12 34 56 78',
              type: 'individual',
              status: 'active',
              contracts: 2,
              lastActivity: '2023-05-15'
            },
            {
              id: 2,
              name: 'Acme Corporation',
              email: '<EMAIL>',
              phone: '+33 1 23 45 67 89',
              type: 'business',
              status: 'active',
              contracts: 3,
              lastActivity: '2023-05-12'
            },
            {
              id: 3,
              name: 'Sarah Johnson',
              email: '<EMAIL>',
              phone: '+33 6 98 76 54 32',
              type: 'individual',
              status: 'inactive',
              contracts: 1,
              lastActivity: '2023-04-20'
            },
            {
              id: 4,
              name: 'Tech Solutions Inc.',
              email: '<EMAIL>',
              phone: '+33 1 87 65 43 21',
              type: 'business',
              status: 'active',
              contracts: 5,
              lastActivity: '2023-05-10'
            },
            {
              id: 5,
              name: 'Marie Dupont',
              email: '<EMAIL>',
              phone: '+33 6 11 22 33 44',
              type: 'individual',
              status: 'pending',
              contracts: 0,
              lastActivity: '2023-05-05'
            }
          ];

          setClients(mockClients);
          setFilteredClients(mockClients);
          setLoading(false);
        }, 1000);
      } catch (error) {
        console.error('Error fetching clients:', error);
        setLoading(false);
      }
    };

    fetchClients();
  }, [navigate]);

  // Filter clients based on search term and filter
  useEffect(() => {
    let result = clients;

    // Apply search filter
    if (searchTerm) {
      result = result.filter(client =>
        client.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        client.email.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Apply status filter
    if (filter !== 'all') {
      result = result.filter(client => client.status === filter);
    }

    setFilteredClients(result);
  }, [clients, searchTerm, filter]);

  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
  };

  const handleFilterChange = (newFilter) => {
    setFilter(newFilter);
  };

  const handleClientClick = (clientId) => {
    logger.info('Viewing client details:', clientId);
    showInfoMessage('FEATURE_COMING_SOON', `Viewing details for client #${clientId}`);
    // In a real app, this would navigate to a client detail page
    // navigate(`/clients/${clientId}`);
  };

  const formatDate = (dateString) => {
    const options = { year: 'numeric', month: 'long', day: 'numeric' };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  const handleAddClient = () => {
    logger.info('Navigating to add client page');
    showInfoMessage('FEATURE_COMING_SOON', 'Add new client functionality coming soon!');
    // In a real app, this would navigate to a new client form
    // navigate('/add-client');
  };

  return (
    <DashboardLayout>
      <div className="clients-container">
        <div className="clients-header">
          <h1>My Clients</h1>
          <button className="add-client-btn" onClick={handleAddClient}>
            <i className="fas fa-plus"></i> Add New Client
          </button>
        </div>

        {loading ? (
          <Spinner message="Loading clients..." />
        ) : (
          <>
            <div className="clients-filters">
              <div className="search-container">
                <i className="fas fa-search search-icon"></i>
                <input
                  type="text"
                  placeholder="Search clients..."
                  value={searchTerm}
                  onChange={handleSearchChange}
                  className="search-input"
                />
              </div>
              <div className="filter-buttons">
                <button
                  className={`filter-btn ${filter === 'all' ? 'active' : ''}`}
                  onClick={() => handleFilterChange('all')}
                >
                  All
                </button>
                <button
                  className={`filter-btn ${filter === 'active' ? 'active' : ''}`}
                  onClick={() => handleFilterChange('active')}
                >
                  Active
                </button>
                <button
                  className={`filter-btn ${filter === 'inactive' ? 'active' : ''}`}
                  onClick={() => handleFilterChange('inactive')}
                >
                  Inactive
                </button>
                <button
                  className={`filter-btn ${filter === 'pending' ? 'active' : ''}`}
                  onClick={() => handleFilterChange('pending')}
                >
                  Pending
                </button>
              </div>
            </div>

            <div className="clients-table-container">
              <table className="clients-table">
                <thead>
                  <tr>
                    <th>Name</th>
                    <th className="hide-mobile">Email</th>
                    <th className="hide-tablet">Phone</th>
                    <th>Type</th>
                    <th>Status</th>
                    <th className="hide-mobile">Contracts</th>
                    <th className="hide-tablet">Last Activity</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredClients.length > 0 ? (
                    filteredClients.map(client => (
                      <tr key={client.id} onClick={() => handleClientClick(client.id)}>
                        <td className="client-name">{client.name}</td>
                        <td className="hide-mobile">{client.email}</td>
                        <td className="hide-tablet">{client.phone}</td>
                        <td>
                          <span className={`client-type ${client.type}`}>
                            {client.type === 'individual' ? 'Individual' : 'Business'}
                          </span>
                        </td>
                        <td>
                          <span className={`client-status status-${client.status}`}>
                            {client.status.charAt(0).toUpperCase() + client.status.slice(1)}
                          </span>
                        </td>
                        <td className="hide-mobile">{client.contracts}</td>
                        <td className="hide-tablet">{formatDate(client.lastActivity)}</td>
                        <td>
                          <div className="client-actions">
                            <button className="action-btn view-btn" title="View Details">
                              <i className="fas fa-eye"></i>
                            </button>
                            <button className="action-btn edit-btn" title="Edit Client">
                              <i className="fas fa-edit"></i>
                            </button>
                            <button className="action-btn message-btn" title="Send Message">
                              <i className="fas fa-envelope"></i>
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan="8" className="no-clients">
                        No clients found
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </>
        )}
      </div>
    </DashboardLayout>
  );
};

export default Clients;
