/**
 * Utility functions for scrolling behavior
 */

/**
 * Scrolls the window to the top of the page using multiple methods for maximum compatibility
 * @param {boolean} smooth - Whether to use smooth scrolling (default: false)
 */
export const scrollToTop = (smooth = false) => {
  const behavior = smooth ? 'smooth' : 'instant';

  // Method 1: Modern scrollTo with options
  try {
    window.scrollTo({
      top: 0,
      left: 0,
      behavior: behavior
    });
  } catch (error) {
    // Fallback for older browsers
    window.scrollTo(0, 0);
  }

  // Method 2: Direct property assignment
  if (document.documentElement) {
    document.documentElement.scrollTop = 0;
  }
  if (document.body) {
    document.body.scrollTop = 0;
  }

  // Method 3: Traditional scrollTo
  window.scrollTo(0, 0);

  // Method 4: Force scroll after a short delay for race conditions
  if (!smooth) {
    setTimeout(() => {
      try {
        window.scrollTo({ top: 0, left: 0, behavior: 'instant' });
      } catch (error) {
        window.scrollTo(0, 0);
      }

      if (document.documentElement) {
        document.documentElement.scrollTop = 0;
      }
      if (document.body) {
        document.body.scrollTop = 0;
      }
    }, 10);

    // Final attempt after DOM is ready
    setTimeout(() => {
      window.scrollTo(0, 0);
      if (document.documentElement) {
        document.documentElement.scrollTop = 0;
      }
      if (document.body) {
        document.body.scrollTop = 0;
      }
    }, 100);
  }
};

/**
 * Scrolls to a specific element by ID
 * @param {string} elementId - The ID of the element to scroll to
 * @param {boolean} smooth - Whether to use smooth scrolling (default: true)
 * @param {number} offset - Offset from the top in pixels (default: 0)
 */
export const scrollToElement = (elementId, smooth = true, offset = 0) => {
  const element = document.getElementById(elementId);
  if (element) {
    const elementPosition = element.getBoundingClientRect().top;
    const offsetPosition = elementPosition + window.pageYOffset - offset;

    window.scrollTo({
      top: offsetPosition,
      behavior: smooth ? 'smooth' : 'instant'
    });
  }
};

/**
 * Adds an event listener to a link that scrolls to an element instead of navigating
 * @param {string} linkId - The ID of the link element
 * @param {string} targetId - The ID of the target element to scroll to
 * @param {boolean} smooth - Whether to use smooth scrolling (default: true)
 * @param {number} offset - Offset from the top in pixels (default: 0)
 */
export const setupScrollLink = (linkId, targetId, smooth = true, offset = 0) => {
  const link = document.getElementById(linkId);
  if (link) {
    link.addEventListener('click', (e) => {
      e.preventDefault();
      scrollToElement(targetId, smooth, offset);
    });
  }
};

/**
 * Force scroll to top with maximum compatibility - use this for critical scroll operations
 * This function uses every possible method to ensure the page scrolls to top
 */
export const forceScrollToTop = () => {
  // Method 1: Immediate scroll with all techniques
  const scrollMethods = [
    () => window.scrollTo(0, 0),
    () => window.scrollTo({ top: 0, left: 0, behavior: 'instant' }),
    () => {
      if (document.documentElement) {
        document.documentElement.scrollTop = 0;
        document.documentElement.scrollLeft = 0;
      }
    },
    () => {
      if (document.body) {
        document.body.scrollTop = 0;
        document.body.scrollLeft = 0;
      }
    },
    () => {
      // Try to scroll any scrollable containers
      const scrollableElements = document.querySelectorAll('[data-scroll-container], .scroll-container, .main-content, .authenticated-content');
      scrollableElements.forEach(el => {
        if (el.scrollTo) {
          el.scrollTo(0, 0);
        } else {
          el.scrollTop = 0;
          el.scrollLeft = 0;
        }
      });
    }
  ];

  // Execute all methods immediately
  scrollMethods.forEach((method) => {
    try {
      method();
    } catch (error) {
      // Silent fail
    }
  });

  // Execute again after short delay to handle async content
  setTimeout(() => {
    scrollMethods.forEach((method) => {
      try {
        method();
      } catch (error) {
        // Silent fail
      }
    });
  }, 50);
};
