import React from 'react';

const WizardProgress = ({ currentStep, totalSteps, stepLabels }) => {
  return (
    <div className="wizard-progress">
      {Array.from({ length: totalSteps }, (_, i) => i + 1).map((step) => (
        <div 
          key={step} 
          className={`step-indicator ${
            step < currentStep ? 'completed' : step === currentStep ? 'active' : ''
          }`}
        >
          <div className="step-number">
            {step < currentStep ? (
              <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="currentColor" viewBox="0 0 16 16">
                <path d="M13.854 3.646a.5.5 0 0 1 0 .708l-7 7a.5.5 0 0 1-.708 0l-3.5-3.5a.5.5 0 1 1 .708-.708L6.5 10.293l6.646-6.647a.5.5 0 0 1 .708 0z"/>
              </svg>
            ) : (
              step
            )}
          </div>
          <div className="step-label">{stepLabels[step - 1]}</div>
        </div>
      ))}
    </div>
  );
};

export default WizardProgress;
