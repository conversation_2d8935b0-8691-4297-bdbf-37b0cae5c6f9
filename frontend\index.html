<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/jpeg" href="./logo.jpeg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>My Energy Bill</title>
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <!-- Ensure no margins or padding -->
    <style>
      html, body, #root {
        margin: 0;
        padding: 0;
        width: 100%;
        max-width: 100%;
        height: 100%;
        overflow-x: hidden;
        box-sizing: border-box;
      }

      /* Prevent horizontal scrolling on all elements */
      * {
        box-sizing: border-box;
        max-width: 100%;
      }

      /* Edge browser specific viewport fix */
      @supports (-ms-ime-align: auto) {
        html {
          -ms-text-size-adjust: 100%;
          -webkit-text-size-adjust: 100%;
        }

        body {
          -ms-overflow-style: scrollbar;
        }
      }

      /* Mobile specific fixes */
      @media screen and (max-width: 768px) {
        html, body, #root {
          overflow-x: hidden !important;
          width: 100% !important;
          max-width: 100% !important;
        }

        * {
          max-width: 100% !important;
          box-sizing: border-box !important;
        }
      }

      /* ========== CRITICAL HOMEPAGE BUTTON OVERRIDES ========== */
      /* MAXIMUM SPECIFICITY - LOADED BEFORE ALL OTHER CSS */
      html body div.home-container .btn-primary,
      html body div.home-container .btn.btn-primary,
      html body div.home-container a.btn-primary,
      html body div.home-container a.btn.btn-primary,
      html body .hero-cta .btn-primary,
      html body .hero-cta .btn.btn-primary,
      html body .hero-cta a.btn-primary,
      html body .hero-cta a.btn.btn-primary,
      html body .cta-section .btn-primary,
      html body .cta-section .btn.btn-primary,
      html body .cta-section a.btn-primary,
      html body .cta-section a.btn.btn-primary,
      html body .home-auth-buttons .btn-primary,
      html body .home-auth-buttons .btn.btn-primary,
      html body .home-auth-buttons a.btn-primary,
      html body .home-auth-buttons a.btn.btn-primary {
        background-color: #000000 !important;
        background-image: none !important;
        background: #000000 !important;
        color: #ffffff !important;
        border: 2px solid #000000 !important;
        border-color: #000000 !important;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15) !important;
      }

      html body div.home-container .btn-primary:hover,
      html body div.home-container .btn.btn-primary:hover,
      html body div.home-container a.btn-primary:hover,
      html body div.home-container a.btn.btn-primary:hover,
      html body .hero-cta .btn-primary:hover,
      html body .hero-cta .btn.btn-primary:hover,
      html body .hero-cta a.btn-primary:hover,
      html body .hero-cta a.btn.btn-primary:hover,
      html body .cta-section .btn-primary:hover,
      html body .cta-section .btn.btn-primary:hover,
      html body .cta-section a.btn-primary:hover,
      html body .cta-section a.btn.btn-primary:hover,
      html body .home-auth-buttons .btn-primary:hover,
      html body .home-auth-buttons .btn.btn-primary:hover,
      html body .home-auth-buttons a.btn-primary:hover,
      html body .home-auth-buttons a.btn.btn-primary:hover {
        background-color: #333333 !important;
        background-image: none !important;
        background: #333333 !important;
        color: #ffffff !important;
        border-color: #333333 !important;
        border: 2px solid #333333 !important;
        transform: translateY(-2px) !important;
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2) !important;
      }
    </style>
    <!-- Polyfill for Buffer and process -->
    <script>
      window.global = window;
      window.process = { env: { NODE_ENV: 'development' } };
      window.Buffer = typeof Buffer !== 'undefined' ? Buffer : [];
      window._global = window; // Fix for axios
    </script>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.jsx"></script>
  </body>
</html>
