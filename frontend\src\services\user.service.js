/**
 * User service for handling user-related API calls
 */

import { API_BASE_URL } from '../config/api-config';
import { Auth } from 'aws-amplify';
import { getItem, STORAGE_KEYS } from '../utils/localStorage';

/**
 * Get the current user's profile
 * @returns {Promise} Promise object representing the user profile
 */
const getUserProfile = async () => {
  try {
    // First try to get token from localStorage
    let token = getItem(STORAGE_KEYS.ACCESS_TOKEN);

    // If no token in localStorage, try to get it from the current session
    if (!token) {
      try {
        const session = await Auth.currentSession();
        token = session.getAccessToken().getJwtToken();
      } catch (sessionError) {
        console.error('Error getting current session:', sessionError);
        throw new Error('No access token found. Please log in again.');
      }
    }

    const response = await fetch(`${API_BASE_URL}/api/users/profile`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      }
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to fetch user profile');
    }

    return await response.json();
  } catch (error) {
    console.error('Error fetching user profile:', error);
    throw error;
  }
};

/**
 * Update the user's profile
 * @param {Object} profileData - The profile data to update
 * @returns {Promise} Promise object representing the updated user profile
 */
const updateUserProfile = async (profileData) => {
  try {
    // First try to get token from localStorage
    let token = getItem(STORAGE_KEYS.ACCESS_TOKEN);

    // If no token in localStorage, try to get it from the current session
    if (!token) {
      try {
        const session = await Auth.currentSession();
        token = session.getAccessToken().getJwtToken();
      } catch (sessionError) {
        console.error('Error getting current session:', sessionError);
        throw new Error('No access token found. Please log in again.');
      }
    }

    const response = await fetch(`${API_BASE_URL}/api/users/profile`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(profileData)
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to update user profile');
    }

    return await response.json();
  } catch (error) {
    console.error('Error updating user profile:', error);
    throw error;
  }
};

/**
 * Update the user's type in MongoDB
 * @param {string} userType - The user type to update
 * @returns {Promise} Promise object representing the updated user
 */
const updateUserType = async (userType) => {
  try {
    // First try to get token from localStorage
    let token = getItem(STORAGE_KEYS.ACCESS_TOKEN);

    // If no token in localStorage, try to get it from the current session
    if (!token) {
      try {
        const session = await Auth.currentSession();
        token = session.getAccessToken().getJwtToken();
      } catch (sessionError) {
        console.error('Error getting current session:', sessionError);
        throw new Error('No access token found. Please log in again.');
      }
    }

    const response = await fetch(`${API_BASE_URL}/api/users/update-user-type`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({ userType })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to update user type');
    }

    return await response.json();
  } catch (error) {
    console.error('Error updating user type:', error);
    throw error;
  }
};

/**
 * Create or update individual profile
 * @param {Object} profileData - The profile data to update
 * @returns {Promise} Promise object representing the updated profile
 */
const updateIndividualProfile = async (profileData) => {
  try {
    // First try to get token from localStorage
    let token = getItem(STORAGE_KEYS.ACCESS_TOKEN);

    // If no token in localStorage, try to get it from the current session
    if (!token) {
      try {
        const session = await Auth.currentSession();
        token = session.getAccessToken().getJwtToken();
      } catch (sessionError) {
        console.error('Error getting current session:', sessionError);
        throw new Error('No access token found. Please log in again.');
      }
    }

    console.log('Submitting individual profile with token:', token ? 'Token found' : 'No token');

    const response = await fetch(`${API_BASE_URL}/api/users/individual-profile`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(profileData)
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to update individual profile');
    }

    return await response.json();
  } catch (error) {
    console.error('Error updating individual profile:', error);
    throw error;
  }
};

/**
 * Create or update professional profile
 * @param {Object} profileData - The profile data to update
 * @returns {Promise} Promise object representing the updated profile
 */
const updateProfessionalProfile = async (profileData) => {
  try {
    // First try to get token from localStorage
    let token = getItem(STORAGE_KEYS.ACCESS_TOKEN);

    // If no token in localStorage, try to get it from the current session
    if (!token) {
      try {
        const session = await Auth.currentSession();
        token = session.getAccessToken().getJwtToken();
      } catch (sessionError) {
        console.error('Error getting current session:', sessionError);
        throw new Error('No access token found. Please log in again.');
      }
    }

    console.log('Submitting professional profile with token:', token ? 'Token found' : 'No token');

    const response = await fetch(`${API_BASE_URL}/api/users/professional-profile`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(profileData)
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to update professional profile');
    }

    return await response.json();
  } catch (error) {
    console.error('Error updating professional profile:', error);
    throw error;
  }
};

const userService = {
  getUserProfile,
  updateUserProfile,
  updateUserType,
  updateIndividualProfile,
  updateProfessionalProfile
};

export default userService;
