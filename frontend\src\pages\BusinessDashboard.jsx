import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import DashboardLayout from '../components/DashboardLayout';
import Spinner from '../components/Spinner';
import '../styles/business-dashboard.css';

const BusinessDashboard = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [businessData, setBusinessData] = useState(null);
  const [userType, setUserType] = useState(null);

  useEffect(() => {
    // Check if user is professional
    const storedUserType = localStorage.getItem('userType');
    
    if (storedUserType !== 'professional') {
      navigate('/dashboard');
      return;
    }
    
    setUserType(storedUserType);
    
    // Fetch business data
    const fetchBusinessData = async () => {
      try {
        setLoading(true);
        
        // In a real app, this would be an API call
        // For now, we'll use mock data
        setTimeout(() => {
          const mockBusinessData = {
            businessName: 'Acme Corporation',
            businessType: 'Manufacturing',
            totalSavings: '€12,450',
            activeContracts: 3,
            pendingOffers: 2,
            recentActivity: [
              {
                id: 1,
                type: 'contract',
                title: 'Energy Contract Renewal',
                date: '2023-05-15',
                status: 'Completed'
              },
              {
                id: 2,
                type: 'offer',
                title: 'New Electricity Rate Offer',
                date: '2023-05-10',
                status: 'Pending'
              },
              {
                id: 3,
                type: 'invoice',
                title: 'April Energy Bill',
                date: '2023-05-05',
                status: 'Paid'
              }
            ],
            energyConsumption: {
              current: '45,230 kWh',
              previous: '48,120 kWh',
              change: '-6%'
            },
            costBreakdown: {
              electricity: '€8,450',
              gas: '€3,200',
              other: '€800'
            }
          };
          
          setBusinessData(mockBusinessData);
          setLoading(false);
        }, 1000);
      } catch (error) {
        console.error('Error fetching business data:', error);
        setLoading(false);
      }
    };
    
    fetchBusinessData();
  }, [navigate]);

  const formatDate = (dateString) => {
    const options = { year: 'numeric', month: 'long', day: 'numeric' };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  return (
    <DashboardLayout>
      <div className="business-dashboard-container">
        <div className="business-dashboard-header">
          <h1>Business Dashboard</h1>
        </div>

        {loading ? (
          <Spinner message="Loading business data..." />
        ) : (
          <div className="business-dashboard-content">
            {/* Business Overview */}
            <div className="business-overview-card">
              <div className="business-overview-header">
                <h2>Business Overview</h2>
              </div>
              <div className="business-overview-content">
                <div className="business-info">
                  <div className="business-info-item">
                    <span className="info-label">Business Name</span>
                    <span className="info-value">{businessData.businessName}</span>
                  </div>
                  <div className="business-info-item">
                    <span className="info-label">Business Type</span>
                    <span className="info-value">{businessData.businessType}</span>
                  </div>
                </div>
                <div className="business-stats">
                  <div className="business-stat-item">
                    <span className="stat-label">Total Savings</span>
                    <span className="stat-value highlight">{businessData.totalSavings}</span>
                  </div>
                  <div className="business-stat-item">
                    <span className="stat-label">Active Contracts</span>
                    <span className="stat-value">{businessData.activeContracts}</span>
                  </div>
                  <div className="business-stat-item">
                    <span className="stat-label">Pending Offers</span>
                    <span className="stat-value">{businessData.pendingOffers}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Energy Consumption */}
            <div className="business-card">
              <div className="business-card-header">
                <h2>Energy Consumption</h2>
              </div>
              <div className="business-card-content">
                <div className="energy-consumption">
                  <div className="consumption-item">
                    <span className="consumption-label">Current Month</span>
                    <span className="consumption-value">{businessData.energyConsumption.current}</span>
                  </div>
                  <div className="consumption-item">
                    <span className="consumption-label">Previous Month</span>
                    <span className="consumption-value">{businessData.energyConsumption.previous}</span>
                  </div>
                  <div className="consumption-item">
                    <span className="consumption-label">Change</span>
                    <span className={`consumption-change ${businessData.energyConsumption.change.startsWith('-') ? 'positive' : 'negative'}`}>
                      {businessData.energyConsumption.change}
                    </span>
                  </div>
                </div>
                <div className="consumption-chart">
                  <p className="chart-placeholder">Energy consumption chart will be displayed here</p>
                </div>
              </div>
            </div>

            {/* Cost Breakdown */}
            <div className="business-card">
              <div className="business-card-header">
                <h2>Cost Breakdown</h2>
              </div>
              <div className="business-card-content">
                <div className="cost-breakdown">
                  <div className="cost-item">
                    <span className="cost-label">Electricity</span>
                    <span className="cost-value">{businessData.costBreakdown.electricity}</span>
                  </div>
                  <div className="cost-item">
                    <span className="cost-label">Gas</span>
                    <span className="cost-value">{businessData.costBreakdown.gas}</span>
                  </div>
                  <div className="cost-item">
                    <span className="cost-label">Other</span>
                    <span className="cost-value">{businessData.costBreakdown.other}</span>
                  </div>
                </div>
                <div className="cost-chart">
                  <p className="chart-placeholder">Cost breakdown chart will be displayed here</p>
                </div>
              </div>
            </div>

            {/* Recent Activity */}
            <div className="business-card">
              <div className="business-card-header">
                <h2>Recent Activity</h2>
              </div>
              <div className="business-card-content">
                <div className="activity-list">
                  {businessData.recentActivity.map(activity => (
                    <div key={activity.id} className="activity-item">
                      <div className="activity-icon">
                        <i className={`fas ${
                          activity.type === 'contract' ? 'fa-file-signature' :
                          activity.type === 'offer' ? 'fa-tags' :
                          'fa-file-invoice'
                        }`}></i>
                      </div>
                      <div className="activity-details">
                        <div className="activity-title">{activity.title}</div>
                        <div className="activity-date">{formatDate(activity.date)}</div>
                      </div>
                      <div className="activity-status">
                        <span className={`status-badge status-${activity.status.toLowerCase()}`}>
                          {activity.status}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </DashboardLayout>
  );
};

export default BusinessDashboard;
