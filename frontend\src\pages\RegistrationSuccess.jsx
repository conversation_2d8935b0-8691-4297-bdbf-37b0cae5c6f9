import { useLocation, useNavigate } from 'react-router-dom';
import { useEffect } from 'react';
import '../styles/registration-success.css';

const RegistrationSuccess = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { userType, email, requiresApproval, message, nextSteps } = location.state || {};

  useEffect(() => {
    // If no state data, redirect to home
    if (!userType || !email) {
      navigate('/');
    }
  }, [userType, email, navigate]);

  const getUserTypeDisplay = () => {
    switch (userType) {
      case 'Broker': return 'Energy Broker';
      case 'Supplier': return 'Energy Supplier';
      case 'Individual': return 'Individual';
      case 'Professional': return 'Professional';
      default: return 'User';
    }
  };

  const getIconClass = () => {
    switch (userType) {
      case 'Broker': return 'fas fa-handshake';
      case 'Supplier': return 'fas fa-industry';
      case 'Individual': return 'fas fa-user';
      case 'Professional': return 'fas fa-user-tie';
      default: return 'fas fa-check-circle';
    }
  };

  return (
    <div className="registration-success-container">
      <div className="success-card">
        <div className="success-icon">
          <i className={getIconClass()}></i>
          <div className="check-mark">
            <i className="fas fa-check"></i>
          </div>
        </div>

        <h1>Registration Successful!</h1>
        
        <div className="success-message">
          <p>
            {message || `Welcome! Your ${getUserTypeDisplay()} account has been created successfully.`}
          </p>
          
          {requiresApproval ? (
            <div className="approval-notice">
              <div className="notice-icon">
                <i className="fas fa-clock"></i>
              </div>
              <div className="notice-content">
                <h3>Account Under Review</h3>
                <p>
                  Your account is currently pending approval from our admin team. 
                  This process typically takes 1-2 business days.
                </p>
                <p>
                  You'll receive an email notification at <strong>{email}</strong> once 
                  your account has been approved and you can start using the platform.
                </p>
              </div>
            </div>
          ) : (
            <div className="immediate-access">
              <div className="notice-icon">
                <i className="fas fa-rocket"></i>
              </div>
              <div className="notice-content">
                <h3>Ready to Get Started</h3>
                <p>
                  Your account is now active! You can log in and start using the platform immediately.
                </p>
              </div>
            </div>
          )}
        </div>

        <div className="next-steps">
          <h3>What happens next?</h3>
          <div className="steps-list">
            {requiresApproval ? (
              <>
                {nextSteps ? (
                  nextSteps.map((step, index) => (
                    <div key={index} className="step">
                      <div className="step-number">{index + 1}</div>
                      <div className="step-content">
                        <p>{step}</p>
                      </div>
                    </div>
                  ))
                ) : (
                  <>
                    <div className="step">
                      <div className="step-number">1</div>
                      <div className="step-content">
                        <h4>Account Review</h4>
                        <p>Our admin team will review your registration and verify your credentials</p>
                      </div>
                    </div>
                    <div className="step">
                      <div className="step-number">2</div>
                      <div className="step-content">
                        <h4>Email Notification</h4>
                        <p>You'll receive an email confirmation once your account is approved</p>
                      </div>
                    </div>
                    <div className="step">
                      <div className="step-number">3</div>
                      <div className="step-content">
                        <h4>Start Using Platform</h4>
                        <p>Log in and complete your profile to access all platform features</p>
                      </div>
                    </div>
                  </>
                )}
              </>
            ) : (
              <>
                <div className="step">
                  <div className="step-number">1</div>
                  <div className="step-content">
                    <h4>Log In</h4>
                    <p>Use your email and password to access your account</p>
                  </div>
                </div>
                <div className="step">
                  <div className="step-number">2</div>
                  <div className="step-content">
                    <h4>Complete Profile</h4>
                    <p>Fill in your profile information to get the most out of the platform</p>
                  </div>
                </div>
                <div className="step">
                  <div className="step-number">3</div>
                  <div className="step-content">
                    <h4>Explore Features</h4>
                    <p>Start using the platform's features tailored to your needs</p>
                  </div>
                </div>
              </>
            )}
          </div>
        </div>

        <div className="platform-benefits">
          <h3>Platform Benefits</h3>
          <div className="benefits-grid">
            {userType === 'Broker' && (
              <>
                <div className="benefit-item">
                  <i className="fas fa-users"></i>
                  <span>Client Management</span>
                </div>
                <div className="benefit-item">
                  <i className="fas fa-chart-line"></i>
                  <span>Performance Analytics</span>
                </div>
                <div className="benefit-item">
                  <i className="fas fa-euro-sign"></i>
                  <span>Commission Tracking</span>
                </div>
                <div className="benefit-item">
                  <i className="fas fa-handshake"></i>
                  <span>Supplier Network</span>
                </div>
              </>
            )}
            
            {userType === 'Supplier' && (
              <>
                <div className="benefit-item">
                  <i className="fas fa-store"></i>
                  <span>Offer Management</span>
                </div>
                <div className="benefit-item">
                  <i className="fas fa-upload"></i>
                  <span>Tariff Grid Upload</span>
                </div>
                <div className="benefit-item">
                  <i className="fas fa-plug"></i>
                  <span>API Integration</span>
                </div>
                <div className="benefit-item">
                  <i className="fas fa-target"></i>
                  <span>Lead Generation</span>
                </div>
              </>
            )}
            
            {(userType === 'Individual' || userType === 'Professional') && (
              <>
                <div className="benefit-item">
                  <i className="fas fa-search"></i>
                  <span>Compare Offers</span>
                </div>
                <div className="benefit-item">
                  <i className="fas fa-piggy-bank"></i>
                  <span>Save Money</span>
                </div>
                <div className="benefit-item">
                  <i className="fas fa-leaf"></i>
                  <span>Green Energy Options</span>
                </div>
                <div className="benefit-item">
                  <i className="fas fa-shield-alt"></i>
                  <span>Secure Platform</span>
                </div>
              </>
            )}
          </div>
        </div>

        <div className="action-buttons">
          {requiresApproval ? (
            <button 
              className="btn-primary"
              onClick={() => navigate('/')}
            >
              Return to Home
            </button>
          ) : (
            <button 
              className="btn-primary"
              onClick={() => navigate('/login')}
            >
              Log In Now
            </button>
          )}
        </div>

        <div className="support-info">
          <p>
            <i className="fas fa-question-circle"></i>
            Need help? Contact our support team at{' '}
            <a href="mailto:<EMAIL>"><EMAIL></a>
          </p>
        </div>
      </div>
    </div>
  );
};

export default RegistrationSuccess;
