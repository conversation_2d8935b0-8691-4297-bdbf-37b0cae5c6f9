import { useLocation, useNavigate } from 'react-router-dom';
import { useEffect } from 'react';
import '../styles/registration-success.css';
import logoImage from '../assets/login-logo.png';

const RegistrationSuccess = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { userType, email, requiresApproval, message, nextSteps } = location.state || {};

  useEffect(() => {
    // If no state data, redirect to home
    if (!userType || !email) {
      navigate('/');
    }
  }, [userType, email, navigate]);

  const getUserTypeDisplay = () => {
    switch (userType) {
      case 'Broker': return 'Energy Broker';
      case 'Supplier': return 'Energy Supplier';
      case 'Individual': return 'Individual';
      case 'Professional': return 'Professional';
      default: return 'User';
    }
  };

  const getIconClass = () => {
    switch (userType) {
      case 'Broker': return 'fas fa-handshake';
      case 'Supplier': return 'fas fa-industry';
      case 'Individual': return 'fas fa-user';
      case 'Professional': return 'fas fa-user-tie';
      default: return 'fas fa-check-circle';
    }
  };

  return (
    <div className="registration-success-container">
      <div className="success-card">
        <div className="card-header">
          <div className="logo-section">
            <img src={logoImage} alt="MY ENERGY BILL Logo" className="success-logo" />
            <div className="company-name">MY ENERGY BILL</div>
          </div>
          <div className="success-icon">
            <div className="icon-wrapper">
              <i className={getIconClass()}></i>
            </div>
            <div className="check-mark">
              <i className="fas fa-check"></i>
            </div>
          </div>
        </div>

        <div className="success-content">
          <h1>Registration Successful!</h1>

          <div className="welcome-message">
            <p>
              {message || `Welcome! Your ${getUserTypeDisplay()} account has been created successfully.`}
            </p>
          </div>

          {requiresApproval ? (
            <div className="status-card approval-status">
              <div className="status-icon">
                <i className="fas fa-clock"></i>
              </div>
              <div className="status-content">
                <h3>Account Under Review</h3>
                <p>
                  Your account is currently pending approval from our admin team.
                  This process typically takes 1-2 business days.
                </p>
                <div className="email-notification">
                  <i className="fas fa-envelope"></i>
                  <span>You'll receive an email notification at <strong>{email}</strong> once your account has been approved.</span>
                </div>
              </div>
            </div>
          ) : (
            <div className="status-card ready-status">
              <div className="status-icon">
                <i className="fas fa-rocket"></i>
              </div>
              <div className="status-content">
                <h3>Ready to Get Started</h3>
                <p>
                  Your account is now active! You can log in and start using the platform immediately.
                </p>
              </div>
            </div>
          )}
        </div>

        <div className="next-steps-section">
          <h3>What happens next?</h3>
          <div className="steps-container">
            {requiresApproval ? (
              <>
                {nextSteps ? (
                  nextSteps.map((step, index) => (
                    <div key={index} className="step-item">
                      <div className="step-number">{index + 1}</div>
                      <div className="step-text">
                        <p>{step}</p>
                      </div>
                    </div>
                  ))
                ) : (
                  <>
                    <div className="step-item">
                      <div className="step-number">1</div>
                      <div className="step-text">
                        <h4>Registration Submitted</h4>
                        <p>Your registration has been submitted for review</p>
                      </div>
                    </div>
                    <div className="step-item">
                      <div className="step-number">2</div>
                      <div className="step-text">
                        <h4>Admin Review</h4>
                        <p>Our admin team will review your application</p>
                      </div>
                    </div>
                    <div className="step-item">
                      <div className="step-number">3</div>
                      <div className="step-text">
                        <h4>Email Notification</h4>
                        <p>You will receive an email notification once approved</p>
                      </div>
                    </div>
                    <div className="step-item">
                      <div className="step-number">4</div>
                      <div className="step-text">
                        <h4>Complete Profile</h4>
                        <p>After approval, you can login and complete your profile</p>
                      </div>
                    </div>
                  </>
                )}
              </>
            ) : (
              <>
                <div className="step-item">
                  <div className="step-number">1</div>
                  <div className="step-text">
                    <h4>Log In</h4>
                    <p>Use your email and password to access your account</p>
                  </div>
                </div>
                <div className="step-item">
                  <div className="step-number">2</div>
                  <div className="step-text">
                    <h4>Complete Profile</h4>
                    <p>Fill in your profile information to get the most out of the platform</p>
                  </div>
                </div>
                <div className="step-item">
                  <div className="step-number">3</div>
                  <div className="step-text">
                    <h4>Explore Features</h4>
                    <p>Start using the platform's features tailored to your needs</p>
                  </div>
                </div>
              </>
            )}
          </div>
        </div>

        <div className="benefits-section">
          <h3>Platform Benefits</h3>
          <div className="benefits-grid">
            {userType === 'Broker' && (
              <>
                <div className="benefit-item">
                  <i className="fas fa-users"></i>
                  <span>Client Management</span>
                </div>
                <div className="benefit-item">
                  <i className="fas fa-chart-line"></i>
                  <span>Performance Analytics</span>
                </div>
                <div className="benefit-item">
                  <i className="fas fa-euro-sign"></i>
                  <span>Commission Tracking</span>
                </div>
                <div className="benefit-item">
                  <i className="fas fa-handshake"></i>
                  <span>Supplier Network</span>
                </div>
              </>
            )}
            
            {userType === 'Supplier' && (
              <>
                <div className="benefit-item">
                  <i className="fas fa-store"></i>
                  <span>Offer Management</span>
                </div>
                <div className="benefit-item">
                  <i className="fas fa-upload"></i>
                  <span>Tariff Grid Upload</span>
                </div>
                <div className="benefit-item">
                  <i className="fas fa-plug"></i>
                  <span>API Integration</span>
                </div>
                <div className="benefit-item">
                  <i className="fas fa-target"></i>
                  <span>Lead Generation</span>
                </div>
              </>
            )}
            
            {(userType === 'Individual' || userType === 'Professional') && (
              <>
                <div className="benefit-item">
                  <i className="fas fa-search"></i>
                  <span>Compare Offers</span>
                </div>
                <div className="benefit-item">
                  <i className="fas fa-piggy-bank"></i>
                  <span>Save Money</span>
                </div>
                <div className="benefit-item">
                  <i className="fas fa-leaf"></i>
                  <span>Green Energy Options</span>
                </div>
                <div className="benefit-item">
                  <i className="fas fa-shield-alt"></i>
                  <span>Secure Platform</span>
                </div>
              </>
            )}
          </div>
        </div>

        <div className="action-section">
          {requiresApproval ? (
            <button
              className="primary-button"
              onClick={() => navigate('/')}
            >
              <i className="fas fa-home"></i>
              Return to Home
            </button>
          ) : (
            <button
              className="primary-button"
              onClick={() => navigate('/login')}
            >
              <i className="fas fa-sign-in-alt"></i>
              Log In Now
            </button>
          )}
        </div>

        <div className="footer-info">
          <div className="support-contact">
            <i className="fas fa-question-circle"></i>
            <span>Need help? Contact our support team</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RegistrationSuccess;
