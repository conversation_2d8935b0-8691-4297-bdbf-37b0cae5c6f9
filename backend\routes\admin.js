const express = require('express');
const router = express.Router();
const User = require('../models/User');
const UserActivity = require('../models/UserActivity');
const BrokerProfile = require('../models/BrokerProfile');
const SupplierProfile = require('../models/SupplierProfile');
const Contract = require('../models/Contract');
const Offer = require('../models/Offer');
const EnergyRequest = require('../models/EnergyRequest');
const Notification = require('../models/Notification');
const QuoteRequest = require('../models/QuoteRequest');
const logger = require('../utils/logger');
const { authMiddleware } = require('../middleware/auth');

// Import sub-routes
const quotesRouter = require('./admin/quotes');
const brokersRouter = require('./admin/brokers');
const suppliersRouter = require('./admin/suppliers');
const templatesRouter = require('./admin/templates');
const notificationsRouter = require('./admin/notifications');
const analyticsRouter = require('./admin/analytics');
const tariffsRouter = require('./admin/tariffs');
const invitationsRouter = require('./admin/invitations');

// Apply auth middleware to all admin routes
router.use(authMiddleware);

// Use sub-routes
router.use('/quotes', quotesRouter);
router.use('/brokers', brokersRouter);
router.use('/suppliers', suppliersRouter);
router.use('/templates', templatesRouter);
router.use('/notifications', notificationsRouter);
router.use('/analytics', analyticsRouter);
router.use('/tariffs', tariffsRouter);
router.use('/invitations', invitationsRouter);

// Middleware to check if user is admin
const requireAdmin = async (req, res, next) => {
  try {
    logger.info('Admin middleware: Starting admin check');
    logger.debug('Admin middleware: req.user:', req.user);

    // Check if user is authenticated and is admin
    const userEmail = req.user?.email;
    const cognitoId = req.user?.sub;

    logger.debug('Admin middleware: userEmail:', userEmail);
    logger.debug('Admin middleware: cognitoId:', cognitoId);

    if (!userEmail) {
      logger.warn('Admin middleware: No user email found in request');
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    // Find user in database by email first
    let user = await User.findOne({ email: userEmail });
    logger.debug('Admin middleware: User found by email:', !!user);

    // If not found by email, try by cognitoId
    if (!user && cognitoId) {
      user = await User.findOne({ cognitoId: cognitoId });
      logger.debug('Admin middleware: User found by cognitoId:', !!user);
    }

    if (!user) {
      logger.warn('Admin middleware: User not found in database', { email: userEmail, cognitoId });
      return res.status(403).json({
        success: false,
        message: 'User not found in database'
      });
    }

    logger.debug('Admin middleware: User found:', {
      id: user._id,
      email: user.email,
      userType: user.userType,
      status: user.status
    });

    if (user.userType !== 'Admin') {
      logger.warn('Admin middleware: User is not admin', {
        email: userEmail,
        userType: user.userType
      });
      return res.status(403).json({
        success: false,
        message: 'Admin access required'
      });
    }

    logger.info('Admin middleware: Admin access granted', {
      email: userEmail,
      userId: user._id
    });

    req.adminUser = user;
    next();
  } catch (error) {
    logger.error('Admin middleware error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// Get dashboard statistics
router.get('/dashboard-stats', requireAdmin, async (req, res) => {
  try {
    logger.info('Fetching admin dashboard statistics');

    // Get user counts by type
    const userStats = await User.aggregate([
      {
        $group: {
          _id: '$userType',
          count: { $sum: 1 }
        }
      }
    ]);

    // Get total users
    const totalUsers = await User.countDocuments();

    // Get active users (logged in within last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    const activeUsers = await User.countDocuments({
      lastLogin: { $gte: thirtyDaysAgo }
    });

    // Get users by status
    const statusStats = await User.aggregate([
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 }
        }
      }
    ]);

    // Get verification status
    const verificationStats = await User.aggregate([
      {
        $group: {
          _id: '$verificationStatus',
          count: { $sum: 1 }
        }
      }
    ]);

    // Process user type stats
    const userTypeStats = {
      individual: 0,
      professional: 0,
      broker: 0,
      supplier: 0,
      admin: 0
    };

    userStats.forEach(stat => {
      const userType = stat._id?.toLowerCase();
      if (userTypeStats.hasOwnProperty(userType)) {
        userTypeStats[userType] = stat.count;
      }
    });

    // Calculate clients (individual + professional)
    const totalClients = userTypeStats.individual + userTypeStats.professional;

    // Get real data from collections
    const [contractsCount, offersCount, quotesCount, notificationsCount] = await Promise.all([
      Contract.countDocuments(),
      Offer.countDocuments(),
      QuoteRequest.countDocuments(),
      Notification.countDocuments()
    ]);

    // Mock data for invoices (replace with real query when Invoice model is available)
    const mockInvoicesCount = Math.floor(totalUsers * 2.8); // Assume 2.8 invoices per user

    const dashboardStats = {
      totalUsers,
      totalClients,
      totalBrokers: userTypeStats.broker,
      totalSuppliers: userTypeStats.supplier,
      totalAdmins: userTypeStats.admin,
      totalContracts: contractsCount,
      totalInvoices: mockInvoicesCount,
      totalOffers: offersCount,
      totalQuotes: quotesCount,
      totalNotifications: notificationsCount,
      activeUsers,
      userTypeBreakdown: userTypeStats,
      statusBreakdown: statusStats.reduce((acc, stat) => {
        acc[stat._id] = stat.count;
        return acc;
      }, {}),
      verificationBreakdown: verificationStats.reduce((acc, stat) => {
        acc[stat._id] = stat.count;
        return acc;
      }, {})
    };

    logger.info('Dashboard statistics fetched successfully');
    res.json({
      success: true,
      data: dashboardStats
    });

  } catch (error) {
    logger.error('Error fetching dashboard statistics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch dashboard statistics'
    });
  }
});

// Get all users with pagination and filtering
router.get('/users', requireAdmin, async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      userType,
      status,
      verificationStatus,
      search
    } = req.query;

    // Build filter object
    const filter = {};
    
    if (userType && userType !== 'all') {
      filter.userType = userType;
    }
    
    if (status && status !== 'all') {
      filter.status = status;
    }
    
    if (verificationStatus && verificationStatus !== 'all') {
      filter.verificationStatus = verificationStatus;
    }
    
    if (search) {
      filter.$or = [
        { email: { $regex: search, $options: 'i' } },
        { firstName: { $regex: search, $options: 'i' } },
        { lastName: { $regex: search, $options: 'i' } }
      ];
    }

    const skip = (parseInt(page) - 1) * parseInt(limit);

    // Get users with pagination
    const users = await User.find(filter)
      .select('-__v') // Exclude version field
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    // Get total count for pagination
    const totalUsers = await User.countDocuments(filter);
    const totalPages = Math.ceil(totalUsers / parseInt(limit));

    res.json({
      success: true,
      data: {
        users,
        pagination: {
          currentPage: parseInt(page),
          totalPages,
          totalUsers,
          hasNextPage: parseInt(page) < totalPages,
          hasPrevPage: parseInt(page) > 1
        }
      }
    });

  } catch (error) {
    logger.error('Error fetching users:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch users'
    });
  }
});

// Update user status
router.patch('/users/:userId/status', requireAdmin, async (req, res) => {
  try {
    const { userId } = req.params;
    const { status } = req.body;

    if (!['Active', 'Inactive', 'Pending', 'Suspended'].includes(status)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid status value'
      });
    }

    const user = await User.findByIdAndUpdate(
      userId,
      { status, updatedAt: new Date() },
      { new: true }
    );

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    logger.info(`Admin ${req.adminUser.email} updated user ${user.email} status to ${status}`);

    // Note: Cognito custom attributes sync removed since custom attributes
    // cannot be added to existing User Pools. Status is managed in MongoDB only.
    logger.info(`✅ User status updated in database for ${user.email}: ${status}`);

    // Send email notification for status changes
    logger.info(`🔍 DEBUG: Starting email notification process for status: ${status}`);
    logger.info(`🔍 DEBUG: User email: ${user.email}, User name: ${user.firstName} ${user.lastName}`);
    logger.info(`🔍 DEBUG: Admin: ${req.adminUser.email}, Admin name: ${req.adminUser.firstName} ${req.adminUser.lastName}`);

    try {
      const emailService = require('../services/emailService');
      logger.info(`🔍 DEBUG: Email service loaded successfully`);

      const userName = user.firstName ? `${user.firstName} ${user.lastName || ''}`.trim() : '';
      const adminName = req.adminUser.firstName ?
        `${req.adminUser.firstName} ${req.adminUser.lastName || ''}`.trim() :
        req.adminUser.email;

      logger.info(`🔍 DEBUG: Prepared names - userName: "${userName}", adminName: "${adminName}"`);

      if (status === 'Suspended') {
        logger.info(`🔍 DEBUG: Attempting to send suspension email to ${user.email}`);
        const result = await emailService.sendUserSuspendedEmail(user.email, userName, adminName);
        logger.info(`✅ Suspension notification email sent successfully to ${user.email}`, result);
      } else if (status === 'Inactive') {
        logger.info(`🔍 DEBUG: Attempting to send inactive email to ${user.email}`);
        const result = await emailService.sendUserInactiveEmail(user.email, userName, adminName);
        logger.info(`✅ Inactive status notification email sent successfully to ${user.email}`, result);
      } else {
        logger.info(`🔍 DEBUG: No email notification needed for status: ${status}`);
      }
    } catch (emailError) {
      logger.error('❌ Failed to send status change notification email:', {
        error: emailError.message,
        stack: emailError.stack,
        userEmail: user.email,
        status: status,
        adminEmail: req.adminUser.email
      });
      // Don't fail the status update if email fails
    }

    res.json({
      success: true,
      message: 'User status updated successfully',
      data: user
    });

  } catch (error) {
    logger.error('Error updating user status:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update user status'
    });
  }
});

// Get recent activities (mock data for now)
router.get('/recent-activities', requireAdmin, async (req, res) => {
  try {
    // Get recent users
    const recentUsers = await User.find()
      .sort({ createdAt: -1 })
      .limit(5)
      .select('email firstName lastName userType createdAt');

    // Create mock activities based on real data
    const activities = recentUsers.map((user, index) => ({
      id: user._id,
      type: 'user_registration',
      message: `New ${user.userType.toLowerCase()} registered: ${user.email}`,
      timestamp: user.createdAt,
      severity: 'info',
      user: {
        name: `${user.firstName} ${user.lastName}`.trim() || user.email,
        email: user.email,
        userType: user.userType
      }
    }));

    // Add some mock system activities
    const mockActivities = [
      {
        id: 'system-1',
        type: 'system_health',
        message: 'System health check completed successfully',
        timestamp: new Date(Date.now() - 1000 * 60 * 30),
        severity: 'success'
      },
      {
        id: 'system-2',
        type: 'backup',
        message: 'Daily database backup completed',
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2),
        severity: 'info'
      }
    ];

    const allActivities = [...activities, ...mockActivities]
      .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
      .slice(0, 10);

    res.json({
      success: true,
      data: allActivities
    });

  } catch (error) {
    logger.error('Error fetching recent activities:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch recent activities'
    });
  }
});

// Get system health status
router.get('/system-status', requireAdmin, async (req, res) => {
  try {
    // Calculate system metrics
    const totalUsers = await User.countDocuments();
    const activeUsers = await User.countDocuments({
      lastLogin: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) }
    });
    
    const pendingApprovals = await User.countDocuments({
      $or: [
        { status: 'Pending' },
        { verificationStatus: 'Pending' }
      ]
    });

    // Mock system health data
    const systemStatus = {
      serverUptime: '99.9%',
      activeUsers: activeUsers,
      pendingApprovals: pendingApprovals,
      systemHealth: pendingApprovals > 20 ? 'Warning' : 'Good',
      lastBackup: new Date(Date.now() - 1000 * 60 * 60 * 6), // 6 hours ago
      databaseStatus: 'Connected',
      apiResponseTime: '120ms'
    };

    res.json({
      success: true,
      data: systemStatus
    });

  } catch (error) {
    logger.error('Error fetching system status:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch system status'
    });
  }
});

// Get user activity logs
router.get('/users/:userId/activities', requireAdmin, async (req, res) => {
  try {
    const { userId } = req.params;
    const {
      page = 1,
      limit = 20,
      activityType,
      startDate,
      endDate,
      severity
    } = req.query;

    // Verify user exists
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    const result = await UserActivity.getUserActivities(userId, {
      page,
      limit,
      activityType,
      startDate,
      endDate,
      severity
    });

    res.json({
      success: true,
      data: result
    });

  } catch (error) {
    logger.error('Error fetching user activities:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch user activities'
    });
  }
});

// Reset user password (admin action) - Send reset email
router.post('/users/:userId/reset-password', requireAdmin, async (req, res) => {
  console.log('� NEW ADMIN ROUTE HIT: /users/:userId/reset-password');
  console.log('� Request params:', req.params);
  console.log('� Request body:', req.body);
  console.log('� Admin user:', req.adminUser?.email);

  try {
    const { userId } = req.params;

    logger.info('NEW Admin password reset request received', {
      userId,
      adminEmail: req.adminUser?.email,
      requestBody: req.body
    });

    const passwordResetService = require('../services/passwordResetService');

    const user = await User.findById(userId);
    if (!user) {
      logger.warn('User not found for password reset', { userId });
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Check if user is suspended
    if (user.status === 'Suspended') {
      logger.warn('Password reset attempted for suspended user', {
        userId,
        userEmail: user.email
      });
      return res.status(400).json({
        success: false,
        message: 'Cannot reset password for suspended user'
      });
    }

    // Initiate password reset via email
    const resetResult = await passwordResetService.initiatePasswordReset(
      user.email,
      `admin:${req.adminUser.email}`
    );

    if (!resetResult.success) {
      logger.error('Password reset service failed', {
        userId,
        userEmail: user.email,
        error: resetResult.message
      });
      return res.status(400).json({
        success: false,
        message: resetResult.message
      });
    }

    // Send admin password reset notification email
    try {
      const emailService = require('../services/emailService');
      const userName = user.firstName ? `${user.firstName} ${user.lastName || ''}`.trim() : '';
      const adminName = req.adminUser.firstName ?
        `${req.adminUser.firstName} ${req.adminUser.lastName || ''}`.trim() :
        req.adminUser.email;

      await emailService.sendAdminPasswordResetNotificationEmail(user.email, userName, adminName);
      logger.info(`Admin password reset notification email sent to ${user.email}`);
    } catch (emailError) {
      logger.error('Failed to send admin password reset notification email:', emailError);
      // Don't fail the password reset if notification email fails
    }

    // Log the admin-initiated password reset activity
    await UserActivity.logActivity({
      userId: user._id,
      activityType: 'PasswordResetInitiated',
      description: `Password reset email sent by admin ${req.adminUser.email}`,
      details: {
        adminId: req.adminUser._id,
        adminEmail: req.adminUser.email,
        resetMethod: 'admin_email_reset',
        userEmail: user.email
      },
      severity: 'High',
      isSystemGenerated: true
    });

    logger.info(`Admin ${req.adminUser.email} initiated password reset for user ${user.email}`);

    res.json({
      success: true,
      message: 'Password reset email sent successfully. User will receive a secure reset link.',
      data: {
        userId: user._id,
        email: user.email,
        resetInitiatedBy: req.adminUser.email,
        resetInitiatedAt: new Date()
      }
    });

  } catch (error) {
    logger.error('Error in NEW admin password reset route:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to send password reset email'
    });
  }
});

// Get user details with profile information
router.get('/users/:userId/details', requireAdmin, async (req, res) => {
  try {
    const { userId } = req.params;

    const user = await User.findById(userId).select('-__v');
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Get profile information based on user type
    let profile = null;
    switch (user.userType) {
      case 'Broker':
        profile = await BrokerProfile.findOne({ userId }).populate('verificationDocuments');
        break;
      case 'Supplier':
        profile = await SupplierProfile.findOne({ userId }).populate('verificationDocuments');
        break;
      // Add other profile types as needed
    }

    // Get recent activities
    const recentActivities = await UserActivity.find({ userId })
      .sort({ createdAt: -1 })
      .limit(10)
      .select('activityType description createdAt severity');

    // Get contracts count
    const contractsCount = await Contract.countDocuments({ userId });

    // Get offers count
    const offersCount = await Offer.countDocuments({
      $or: [
        { supplierId: userId },
        { 'requestId': { $in: await EnergyRequest.find({ userId }).distinct('_id') } }
      ]
    });

    res.json({
      success: true,
      data: {
        user,
        profile,
        recentActivities,
        stats: {
          contractsCount,
          offersCount,
          activitiesCount: await UserActivity.countDocuments({ userId })
        }
      }
    });

  } catch (error) {
    logger.error('Error fetching user details:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch user details'
    });
  }
});

module.exports = router;
