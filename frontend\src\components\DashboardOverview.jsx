import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import invoiceService from '../services/invoice.service';
import '../styles/dashboard-overview.css';

const DashboardOverview = () => {
  const [overviewData, setOverviewData] = useState({
    invoices: [],
    contracts: [],
    appointments: []
  });
  const [loading, setLoading] = useState(true);
  const [refreshKey, setRefreshKey] = useState(0);
  const navigate = useNavigate();

  useEffect(() => {
    // Fetch real overview data
    const fetchOverviewData = async () => {
      try {
        setLoading(true);

        // Fetch real invoices from the backend
        let invoices = [];
        try {
          console.log('Dashboard: Starting to fetch invoices...');
          const invoicesData = await invoiceService.getUserInvoices();
          console.log('Dashboard: Raw invoices data received:', invoicesData);

          // Handle the response structure (same as Invoices page)
          let invoiceList = [];
          if (invoicesData && invoicesData.invoices && Array.isArray(invoicesData.invoices)) {
            invoiceList = invoicesData.invoices;
          } else if (invoicesData && Array.isArray(invoicesData)) {
            invoiceList = invoicesData;
          } else {
            console.warn('Dashboard: Unexpected response structure:', invoicesData);
            invoiceList = [];
          }

          console.log('Dashboard: Processed invoice list:', invoiceList);

          // Transform the data to match the expected format (same as Invoices page)
          invoices = invoiceList.map(invoice => ({
            id: invoice._id || invoice.id,
            originalFilename: invoice.originalFilename || 'Unknown File',
            provider: invoice.metadata?.provider || 'Unknown Provider',
            invoiceDate: invoice.metadata?.invoiceDate || invoice.createdAt,
            amount: invoice.metadata?.amount || 0,
            currency: invoice.metadata?.currency || 'EUR',
            consumption: invoice.metadata?.consumption || 0,
            energyType: invoice.metadata?.energyType || 'Electricity',
            status: invoice.status || 'pending',
            uploadedAt: invoice.createdAt || new Date().toISOString()
          }));

          console.log('Dashboard: Transformed invoices for display:', invoices);
          console.log(`Dashboard: Found ${invoices.length} invoices`);
        } catch (invoiceError) {
          console.error('Dashboard: Error fetching invoices:', invoiceError);
          // Use empty array if fetching fails
          invoices = [];
        }

        const mockData = {
          invoices: invoices,
            contracts: [
              {
                id: 1,
                contractNumber: 'CNT-2023-001',
                provider: 'EDF Energy',
                energyType: 'Electricity',
                status: 'Active',
                startDate: '2023-01-15',
                endDate: '2025-01-14',
                estimatedAnnualCost: 950,
                currency: 'EUR'
              },
              {
                id: 2,
                contractNumber: 'CNT-2022-089',
                provider: 'Engie',
                energyType: 'Gas',
                status: 'Completed',
                startDate: '2022-05-10',
                endDate: '2023-05-09',
                estimatedAnnualCost: 720,
                currency: 'EUR'
              },
              {
                id: 3,
                contractNumber: 'CNT-2023-145',
                provider: 'Total Energies',
                energyType: 'Both',
                status: 'Active',
                startDate: '2023-06-01',
                endDate: '2024-05-31',
                estimatedAnnualCost: 880,
                currency: 'EUR'
              }
            ],
            appointments: [
              {
                id: 1,
                title: 'Energy Consultation',
                date: '2023-12-15',
                time: '10:00 AM',
                type: 'Video Call',
                status: 'Upcoming',
                with: 'Energy Advisor'
              },
              {
                id: 2,
                title: 'Contract Review',
                date: '2023-12-20',
                time: '2:30 PM',
                type: 'Phone Call',
                status: 'Upcoming',
                with: 'Legal Advisor'
              },
              {
                id: 3,
                title: 'Installation Planning',
                date: '2024-01-05',
                time: '9:00 AM',
                type: 'In-Person',
                status: 'Scheduled',
                with: 'Technical Team'
              }
            ]
          };

        setOverviewData(mockData);
        setLoading(false);
      } catch (error) {
        console.error('Error fetching overview data:', error);
        setLoading(false);
      }
    };

    fetchOverviewData();
  }, [refreshKey]); // Add refreshKey as dependency

  // Function to manually refresh the data
  const handleRefresh = () => {
    console.log('Dashboard: Manual refresh triggered');
    setRefreshKey(prev => prev + 1);
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-GB', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    });
  };

  const formatTimestamp = (timestamp) => {
    const date = new Date(timestamp);
    return date.toLocaleDateString('en-GB', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    });
  };

  const handleSectionClick = (section) => {
    navigate(`/${section}`);
  };

  const handleItemClick = (section, itemId) => {
    navigate(`/${section}/${itemId}`);
  };

  if (loading) {
    return (
      <div className="overview-loading">
        <div className="loading-spinner"></div>
        <p>Loading overview...</p>
      </div>
    );
  }

  return (
    <div className="dashboard-overview">
      {/* Recent Invoices Section */}
      <div className="overview-section">
        <div className="section-header">
          <h3 className="section-title">Recent Invoices</h3>
          <div className="section-actions">
            <button
              className="refresh-btn"
              onClick={handleRefresh}
              title="Refresh invoices"
            >
              <i className="fas fa-sync-alt"></i>
            </button>
            <button
              className="view-all-btn"
              onClick={() => handleSectionClick('invoices')}
            >
              View All
            </button>
          </div>
        </div>
        <div className="overview-items">
          {overviewData.invoices.slice(0, 3).map(invoice => (
            <div 
              key={invoice.id} 
              className="overview-item invoice-item"
              onClick={() => handleItemClick('invoices', invoice.id)}
            >
              <div className="item-icon">
                <i className={`fas fa-file-pdf ${invoice.energyType.toLowerCase()}`}></i>
              </div>
              <div className="item-details">
                <div className="item-title">{invoice.originalFilename}</div>
                <div className="item-subtitle">{invoice.provider}</div>
                <div className="item-meta">
                  {invoice.amount > 0 ? `${invoice.amount} ${invoice.currency}` : 'Amount not set'} • {formatDate(invoice.invoiceDate)}
                </div>
              </div>
              <div className="item-status">
                <span className={`status-badge status-${invoice.status}`}>
                  {invoice.status}
                </span>
              </div>
            </div>
          ))}
          {overviewData.invoices.length === 0 && (
            <div className="no-items">
              <i className="fas fa-file-invoice"></i>
              <p>No invoices uploaded yet</p>
              <button
                className="upload-first-btn"
                onClick={() => handleSectionClick('invoices')}
              >
                Upload Your First Invoice
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Active Contracts Section */}
      <div className="overview-section">
        <div className="section-header">
          <h3 className="section-title">Active Contracts</h3>
          <button 
            className="view-all-btn"
            onClick={() => handleSectionClick('contracts')}
          >
            View All
          </button>
        </div>
        <div className="overview-items">
          {overviewData.contracts.filter(contract => contract.status === 'Active').slice(0, 3).map(contract => (
            <div 
              key={contract.id} 
              className="overview-item contract-item"
              onClick={() => handleItemClick('contracts', contract.id)}
            >
              <div className="item-icon">
                <i className="fas fa-file-signature"></i>
              </div>
              <div className="item-details">
                <div className="item-title">{contract.contractNumber}</div>
                <div className="item-subtitle">{contract.provider}</div>
                <div className="item-meta">
                  {contract.energyType} • {contract.estimatedAnnualCost} {contract.currency}/year
                </div>
              </div>
              <div className="item-status">
                <span className={`status-badge status-${contract.status.toLowerCase()}`}>
                  {contract.status}
                </span>
              </div>
            </div>
          ))}
          {overviewData.contracts.filter(contract => contract.status === 'Active').length === 0 && (
            <div className="no-items">
              <p>No active contracts</p>
            </div>
          )}
        </div>
      </div>

      {/* Upcoming Appointments Section */}
      <div className="overview-section">
        <div className="section-header">
          <h3 className="section-title">Upcoming Appointments</h3>
          <button 
            className="view-all-btn"
            onClick={() => handleSectionClick('appointments')}
          >
            View All
          </button>
        </div>
        <div className="overview-items">
          {overviewData.appointments.filter(appointment => 
            appointment.status === 'Upcoming' || appointment.status === 'Scheduled'
          ).slice(0, 3).map(appointment => (
            <div 
              key={appointment.id} 
              className="overview-item appointment-item"
              onClick={() => handleItemClick('appointments', appointment.id)}
            >
              <div className="item-icon">
                <i className="fas fa-calendar-alt"></i>
              </div>
              <div className="item-details">
                <div className="item-title">{appointment.title}</div>
                <div className="item-subtitle">with {appointment.with}</div>
                <div className="item-meta">
                  {formatDate(appointment.date)} • {appointment.time} • {appointment.type}
                </div>
              </div>
              <div className="item-status">
                <span className={`status-badge status-${appointment.status.toLowerCase()}`}>
                  {appointment.status}
                </span>
              </div>
            </div>
          ))}
          {overviewData.appointments.filter(appointment => 
            appointment.status === 'Upcoming' || appointment.status === 'Scheduled'
          ).length === 0 && (
            <div className="no-items">
              <p>No upcoming appointments</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default DashboardOverview;
