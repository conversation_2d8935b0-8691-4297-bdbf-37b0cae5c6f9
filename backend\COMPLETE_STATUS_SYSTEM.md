# Complete User Status Management System

## 🎯 Overview

This document describes the complete user status management system that synchronizes user status between the database and AWS Cognito, provides email notifications, and blocks access for users with restricted statuses.

## 🔄 Status Synchronization Flow

### Admin Changes User Status
1. **Admin Dashboard**: Admin changes user status (Active, Inactive, Pending, Suspended)
2. **Database Update**: User status updated in MongoDB
3. **Cognito Sync**: Status automatically synced to AWS Cognito custom attributes
4. **Email Notification**: Professional email sent to user about status change
5. **Activity Logging**: All actions logged for audit trail

### Login Process
1. **Authentication**: User attempts to log in through Cognito
2. **Status Check**: System checks status from both database and Cognito
3. **Access Control**: Users with restricted statuses are blocked
4. **Error Handling**: User redirected to appropriate error page
5. **Activity Logging**: Failed login attempts logged

## 📊 User Status Types

| Status | Description | Login Allowed | Email Sent | Error Page |
|--------|-------------|---------------|------------|------------|
| **Active** | Normal user, full access | ✅ Yes | ❌ No | ❌ No |
| **Inactive** | Deactivated by admin | ❌ No | ✅ Yes | ✅ Yes |
| **Pending** | Awaiting admin approval | ❌ No | ❌ No | ✅ Yes |
| **Suspended** | Suspended by admin | ❌ No | ✅ Yes | ✅ Yes |

## 🔧 Technical Implementation

### Backend Components

#### 1. Admin Status Update Route
```javascript
PATCH /api/admin/users/:userId/status
```
- Updates database status
- Syncs with Cognito custom attributes
- Sends email notifications
- Logs admin actions

#### 2. Enhanced Login Route
```javascript
POST /auth/login
```
- Checks status from database and Cognito
- Blocks restricted users
- Returns appropriate error codes
- Logs failed attempts

#### 3. Auth Middleware
```javascript
verifyToken()
```
- Validates all API requests
- Blocks suspended users from API access
- Returns 403 with status information

### Frontend Components

#### 1. AccountStatus Page
- Professional error pages for each status type
- User-friendly explanations
- Contact support functionality
- Responsive design

#### 2. Enhanced Login Handling
- Detects status errors from backend
- Redirects to appropriate error page
- Preserves error context

#### 3. API Interceptor
- Global handling of status errors
- Automatic redirection to error pages
- Auth state cleanup

### Cognito Integration

#### Custom Attributes
- `custom:status`: User status (Active, Inactive, Pending, Suspended)
- `custom:userType`: User type for role-based access
- `custom:profileComplete`: Profile completion status

#### Synchronization
- Automatic sync on admin status changes
- Bidirectional verification during login
- Fallback to database if Cognito unavailable

## 📧 Email Notifications

### Suspension Email
- **Subject**: "Account Suspended - My Energy Bill"
- **Content**: Professional warning with admin details
- **Action**: Contact support for assistance

### Inactive Status Email
- **Subject**: "Account Status Changed - My Energy Bill"
- **Content**: Informative notification about status change
- **Action**: Contact support for reactivation

### Email Features
- Professional HTML templates
- Responsive design
- Security information
- Support contact details
- Admin action tracking

## 🚫 Access Control

### Login Blocking
- **Database Check**: Primary status verification
- **Cognito Check**: Secondary verification
- **Error Response**: Appropriate error code and message
- **Activity Logging**: Security audit trail

### API Blocking
- **Middleware Protection**: All protected routes checked
- **Real-time Validation**: Status checked on every request
- **Graceful Handling**: User-friendly error responses
- **Session Cleanup**: Auth tokens cleared

### Frontend Handling
- **Error Detection**: Recognizes status error responses
- **Page Redirection**: Routes to appropriate error page
- **Context Preservation**: Maintains error information
- **User Experience**: Clear explanations and next steps

## 🧪 Testing

### Automated Tests
```bash
# Test email notifications
node test-email-notifications.js

# Test complete status flow
node test-complete-status-flow.js

# Verify SES email
node verify-ses-email.js
```

### Manual Testing Checklist
- [ ] Admin can change user status
- [ ] Database status updates correctly
- [ ] Cognito status syncs automatically
- [ ] Email notifications sent
- [ ] Suspended users cannot login
- [ ] Error pages display correctly
- [ ] API access blocked for restricted users
- [ ] Activity logging works
- [ ] Status restoration works

## 🔍 Monitoring & Logging

### Activity Logs
- `SuspendedLoginAttempt`: Suspended user login attempts
- `InactiveLoginAttempt`: Inactive user login attempts
- `PendingLoginAttempt`: Pending user login attempts
- `SuspendedAccessAttempt`: API access attempts by suspended users

### Admin Action Logs
- Status change actions with admin details
- Email notification delivery status
- Cognito sync success/failure
- Error details for troubleshooting

### Email Delivery Logs
- AWS SES message IDs
- Delivery success/failure
- Bounce and complaint handling
- Quota usage monitoring

## 🚀 Production Deployment

### Prerequisites
- [ ] AWS SES email verified: `<EMAIL>`
- [ ] Cognito custom attributes configured
- [ ] Environment variables set
- [ ] Database indexes created
- [ ] Logging configured

### Deployment Steps
1. Deploy backend with status sync functionality
2. Deploy frontend with AccountStatus page
3. Verify email delivery
4. Test status change flow
5. Monitor logs for errors
6. Update documentation

### Post-Deployment Verification
- [ ] Admin status changes work
- [ ] Email notifications delivered
- [ ] Login blocking functional
- [ ] Error pages accessible
- [ ] Cognito sync working
- [ ] Logs capturing events

## 🔧 Troubleshooting

### Common Issues

#### Email Not Sending
- **Check**: AWS SES email verification
- **Solution**: Run `node verify-ses-email.js`

#### Cognito Sync Failing
- **Check**: Custom attributes configuration
- **Check**: AWS permissions for Cognito
- **Solution**: Verify userPoolConfig and credentials

#### Login Not Blocked
- **Check**: Database status value
- **Check**: Auth middleware execution
- **Solution**: Verify status check logic

#### Error Page Not Showing
- **Check**: Frontend routing configuration
- **Check**: API error response format
- **Solution**: Verify AccountStatus route and error handling

### Debug Commands
```bash
# Check environment variables
node -e "console.log(process.env.FROM_EMAIL)"

# Test database connection
node -e "require('./config/database')"

# Test Cognito connection
node -e "require('./config/cognito')"

# Check user status
node -e "const User = require('./models/User'); User.findOne({email: '<EMAIL>'}).then(u => console.log(u.status))"
```

## 📞 Support

### For Developers
- Check application logs first
- Use provided test scripts
- Verify environment configuration
- Contact development team with specific errors

### For Users
- Suspended users: Contact <EMAIL>
- Clear instructions provided in error pages
- Professional support experience
- Quick resolution process

## 🔮 Future Enhancements

### Planned Features
- Bulk status change operations
- Automated status change rules
- Enhanced email templates
- Multi-language support
- Advanced analytics dashboard
- Integration with external systems

### Scalability Considerations
- Database indexing optimization
- Cognito rate limiting
- Email delivery optimization
- Caching strategies
- Performance monitoring
