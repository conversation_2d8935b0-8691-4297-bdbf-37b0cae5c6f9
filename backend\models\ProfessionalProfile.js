const mongoose = require('mongoose');

const professionalProfileSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    unique: true
  },
  companyName: {
    type: String,
    required: true,
    trim: true
  },
  companyAddress: {
    street: {
      type: String,
      trim: true
    },
    city: {
      type: String,
      trim: true
    },
    postalCode: {
      type: String,
      trim: true
    },
    country: {
      type: String,
      default: 'France',
      trim: true
    }
  },
  siretNumber: {
    type: String,
    required: true,
    trim: true
  },
  vatNumber: {
    type: String,
    trim: true
  },
  companyRole: {
    type: String,
    trim: true
  },
  businessType: {
    type: String,
    trim: true
  },
  energyTypes: [{
    type: String,
    enum: ['Electricity', 'Gas', 'Both']
  }],
  numberOfLocations: {
    type: Number,
    default: 1
  },
  annualConsumption: {
    electricity: Number,
    gas: Number
  },
  currentSuppliers: [{
    energyType: {
      type: String,
      enum: ['Electricity', 'Gas']
    },
    supplierName: String,
    contractEndDate: Date
  }],
  documents: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Document'
  }]
}, {
  timestamps: true
});

// Create indexes
professionalProfileSchema.index({ userId: 1 });
professionalProfileSchema.index({ siretNumber: 1 });
professionalProfileSchema.index({ companyName: 1 });

const ProfessionalProfile = mongoose.model('ProfessionalProfile', professionalProfileSchema);

module.exports = ProfessionalProfile;
