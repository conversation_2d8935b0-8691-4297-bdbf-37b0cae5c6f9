const mongoose = require('mongoose');
const crypto = require('crypto');

const invitationSchema = new mongoose.Schema({
  email: {
    type: String,
    required: true,
    lowercase: true,
    trim: true
  },
  userType: {
    type: String,
    enum: ['Broker', 'Supplier'],
    required: true
  },
  invitedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  invitationToken: {
    type: String,
    required: true,
    unique: true
  },
  status: {
    type: String,
    enum: ['Pending', 'Accepted', 'Expired', 'Revoked'],
    default: 'Pending'
  },
  inviteeDetails: {
    firstName: {
      type: String,
      trim: true
    },
    lastName: {
      type: String,
      trim: true
    },
    phone: {
      type: String,
      trim: true
    },
    companyName: {
      type: String,
      trim: true
    },
    notes: {
      type: String,
      trim: true
    }
  },
  supplierSpecific: {
    apiEndpoint: {
      type: String,
      trim: true
    },
    hasApiIntegration: {
      type: Boolean,
      default: false
    },
    initialTariffData: {
      fileName: String,
      filePath: String,
      fileSize: Number,
      uploadedAt: Date
    }
  },
  expiresAt: {
    type: Date,
    required: true,
    default: function() {
      // Default expiration: 7 days from creation
      return new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);
    }
  },
  acceptedAt: {
    type: Date
  },
  registeredUserId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  emailSent: {
    type: Boolean,
    default: false
  },
  emailSentAt: {
    type: Date
  },
  emailDeliveryStatus: {
    messageId: String,
    status: {
      type: String,
      enum: ['Sent', 'Delivered', 'Bounced', 'Complained', 'Failed'],
      default: 'Sent'
    },
    deliveredAt: Date,
    errorMessage: String
  },
  remindersSent: {
    type: Number,
    default: 0
  },
  lastReminderSent: {
    type: Date
  },
  metadata: {
    ipAddress: String,
    userAgent: String,
    source: {
      type: String,
      default: 'AdminDashboard'
    },
    adminNotes: String
  }
}, {
  timestamps: true
});

// Pre-save middleware to generate invitation token
invitationSchema.pre('save', function(next) {
  if (this.isNew && !this.invitationToken) {
    this.invitationToken = crypto.randomBytes(32).toString('hex');
  }
  next();
});

// Instance methods
invitationSchema.methods.isExpired = function() {
  return new Date() > this.expiresAt;
};

invitationSchema.methods.isValid = function() {
  return this.status === 'Pending' && !this.isExpired();
};

invitationSchema.methods.accept = function(userId) {
  this.status = 'Accepted';
  this.acceptedAt = new Date();
  this.registeredUserId = userId;
  return this.save();
};

invitationSchema.methods.revoke = function() {
  this.status = 'Revoked';
  return this.save();
};

invitationSchema.methods.extend = function(days = 7) {
  this.expiresAt = new Date(Date.now() + days * 24 * 60 * 60 * 1000);
  return this.save();
};

invitationSchema.methods.markEmailSent = function(messageId) {
  this.emailSent = true;
  this.emailSentAt = new Date();
  if (messageId) {
    this.emailDeliveryStatus.messageId = messageId;
  }
  return this.save();
};

invitationSchema.methods.updateDeliveryStatus = function(status, deliveredAt, errorMessage) {
  this.emailDeliveryStatus.status = status;
  if (deliveredAt) {
    this.emailDeliveryStatus.deliveredAt = deliveredAt;
  }
  if (errorMessage) {
    this.emailDeliveryStatus.errorMessage = errorMessage;
  }
  return this.save();
};

invitationSchema.methods.sendReminder = function() {
  this.remindersSent += 1;
  this.lastReminderSent = new Date();
  return this.save();
};

invitationSchema.methods.getRegistrationUrl = function() {
  const baseUrl = process.env.FRONTEND_URL || 'http://localhost:5173';
  return `${baseUrl}/register/invited?token=${this.invitationToken}&type=${this.userType.toLowerCase()}`;
};

// Static methods
invitationSchema.statics.findByToken = function(token) {
  return this.findOne({ 
    invitationToken: token,
    status: 'Pending'
  }).populate('invitedBy', 'firstName lastName email');
};

invitationSchema.statics.findPendingInvitations = function() {
  return this.find({
    status: 'Pending',
    expiresAt: { $gt: new Date() }
  }).populate('invitedBy', 'firstName lastName email');
};

invitationSchema.statics.findExpiredInvitations = function() {
  return this.find({
    status: 'Pending',
    expiresAt: { $lte: new Date() }
  });
};

invitationSchema.statics.markExpiredInvitations = async function() {
  const expiredInvitations = await this.findExpiredInvitations();
  
  for (const invitation of expiredInvitations) {
    invitation.status = 'Expired';
    await invitation.save();
  }
  
  return expiredInvitations.length;
};

invitationSchema.statics.getInvitationStats = async function() {
  const stats = await this.aggregate([
    {
      $group: {
        _id: '$status',
        count: { $sum: 1 }
      }
    }
  ]);
  
  const typeStats = await this.aggregate([
    {
      $group: {
        _id: '$userType',
        count: { $sum: 1 }
      }
    }
  ]);
  
  return {
    byStatus: stats,
    byType: typeStats
  };
};

// Create indexes
invitationSchema.index({ invitationToken: 1 });
invitationSchema.index({ email: 1 });
invitationSchema.index({ status: 1 });
invitationSchema.index({ expiresAt: 1 });
invitationSchema.index({ userType: 1 });
invitationSchema.index({ invitedBy: 1 });
invitationSchema.index({ createdAt: -1 });

const Invitation = mongoose.model('Invitation', invitationSchema);

module.exports = Invitation;
