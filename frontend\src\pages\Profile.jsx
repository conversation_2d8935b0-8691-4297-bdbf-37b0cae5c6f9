import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import DashboardLayout from '../components/DashboardLayout';
import Spinner from '../components/Spinner';
import IndividualInfoDisplay from '../components/IndividualInfoDisplay';
import ProfessionalInfoDisplay from '../components/ProfessionalInfoDisplay';
import '../styles/profile.css';
import { API_BASE_URL } from '../config/api-config';

const Profile = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [profileData, setProfileData] = useState(null);
  const [editMode, setEditMode] = useState(false);
  const [formData, setFormData] = useState({});
  const [userType, setUserType] = useState(null);
  const [individualInfo, setIndividualInfo] = useState(null);
  const [professionalInfo, setProfessionalInfo] = useState(null);

  useEffect(() => {
    // Fetch real user data
    const fetchProfileData = async () => {
      try {
        setLoading(true);

        // Get user type and Cognito ID from localStorage
        const storedUserType = localStorage.getItem('userType');
        const cognitoId = localStorage.getItem('cognitoId');

        if (!storedUserType || !cognitoId) {
          console.error('User type or Cognito ID not found in localStorage');
          navigate('/login');
          return;
        }

        setUserType(storedUserType);

        // Fetch user data from API
        const response = await fetch(`${API_BASE_URL}/api/users/cognito/${cognitoId}`);

        if (!response.ok) {
          throw new Error('Failed to fetch user data');
        }

        const userData = await response.json();
        console.log('Profile - User data fetched:', userData);

        // Set the appropriate info based on user type
        if (storedUserType === 'individual') {
          setIndividualInfo(userData);
        } else if (['professional', 'broker', 'supplier'].includes(storedUserType)) {
          setProfessionalInfo(userData);
        }

        // Set general profile data for the form
        const profileInfo = userData.data?.profile || {};
        const userInfo = userData.data || {};

        const combinedData = {
          firstName: profileInfo.firstName || userInfo.firstName || '',
          lastName: profileInfo.lastName || userInfo.lastName || '',
          email: userInfo.email || '',
          phone: profileInfo.phoneNumber || profileInfo.phone || '',
          address: profileInfo.streetAddress || (profileInfo.address?.street || ''),
          city: profileInfo.city || (profileInfo.address?.city || ''),
          postalCode: profileInfo.postalCode || (profileInfo.address?.postalCode || ''),
          country: profileInfo.country || (profileInfo.address?.country || 'France'),
          userType: storedUserType,
          meterNumber: profileInfo.meterNumber ||
                      (profileInfo.energyIdentifiers?.pdl ||
                       profileInfo.energyIdentifiers?.prm ||
                       profileInfo.energyIdentifiers?.rae || ''),
          createdAt: userInfo.createdAt || new Date().toISOString()
        };

        setProfileData(combinedData);
        setFormData(combinedData);
        setLoading(false);
      } catch (error) {
        console.error('Error fetching profile data:', error);
        setLoading(false);
      }
    };

    fetchProfileData();
  }, [navigate]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    console.log('Updating profile with:', formData);

    setLoading(true);

    try {
      const cognitoId = localStorage.getItem('cognitoId');

      if (!cognitoId) {
        throw new Error('Cognito ID not found');
      }

      // Prepare the data for API
      const updateData = {
        profile: {
          firstName: formData.firstName,
          lastName: formData.lastName,
          phoneNumber: formData.phone,
          address: {
            street: formData.address,
            city: formData.city,
            postalCode: formData.postalCode,
            country: formData.country
          }
        }
      };

      // Add energy identifiers based on user type
      if (userType === 'individual') {
        updateData.profile.energyIdentifiers = {
          pdl: formData.meterNumber
        };
      }

      // Make API call to update profile
      const response = await fetch(`${API_BASE_URL}/api/users/cognito/${cognitoId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(updateData)
      });

      if (!response.ok) {
        throw new Error('Failed to update profile');
      }

      // Update the profile data state
      setProfileData(formData);

      // Refresh the individual or professional info
      const userResponse = await fetch(`${API_BASE_URL}/api/users/cognito/${cognitoId}`);
      const userData = await userResponse.json();

      if (userType === 'individual') {
        setIndividualInfo(userData);
      } else {
        setProfessionalInfo(userData);
      }

      setEditMode(false);
    } catch (error) {
      console.error('Error updating profile:', error);
      alert('Failed to update profile. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString) => {
    const options = { year: 'numeric', month: 'long', day: 'numeric' };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  return (
    <DashboardLayout>
      <div className="profile-container">
        <div className="profile-header">
          <h1>My Profile</h1>
          {!editMode && (
            <button
              className="edit-profile-btn"
              onClick={() => setEditMode(true)}
            >
              <i className="fas fa-edit"></i> Edit Profile
            </button>
          )}
        </div>

        {loading ? (
          <Spinner message="Loading profile data..." />
        ) : (
          <div className="profile-content">
            {/* Display user info based on user type */}
            {!editMode && userType === 'individual' && individualInfo && (
              <IndividualInfoDisplay
                individualInfo={individualInfo}
                onEdit={() => setEditMode(true)}
              />
            )}

            {!editMode && ['professional', 'broker', 'supplier'].includes(userType) && professionalInfo && (
              <ProfessionalInfoDisplay
                professionalInfo={professionalInfo}
                onEdit={() => setEditMode(true)}
                userType={userType}
              />
            )}

            {editMode ? (
              <form className="profile-form" onSubmit={handleSubmit}>
                <div className="form-section">
                  <h2>Personal Information</h2>
                  <div className="form-row">
                    <div className="form-group">
                      <label htmlFor="firstName">First Name</label>
                      <input
                        type="text"
                        id="firstName"
                        name="firstName"
                        value={formData.firstName}
                        onChange={handleInputChange}
                        required
                      />
                    </div>
                    <div className="form-group">
                      <label htmlFor="lastName">Last Name</label>
                      <input
                        type="text"
                        id="lastName"
                        name="lastName"
                        value={formData.lastName}
                        onChange={handleInputChange}
                        required
                      />
                    </div>
                  </div>
                  <div className="form-row">
                    <div className="form-group">
                      <label htmlFor="email">Email</label>
                      <input
                        type="email"
                        id="email"
                        name="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        required
                        disabled
                      />
                      <small>Email cannot be changed</small>
                    </div>
                    <div className="form-group">
                      <label htmlFor="phone">Phone Number</label>
                      <input
                        type="tel"
                        id="phone"
                        name="phone"
                        value={formData.phone}
                        onChange={handleInputChange}
                      />
                    </div>
                  </div>
                </div>

                <div className="form-section">
                  <h2>Address Information</h2>
                  <div className="form-row">
                    <div className="form-group full-width">
                      <label htmlFor="address">Address</label>
                      <input
                        type="text"
                        id="address"
                        name="address"
                        value={formData.address}
                        onChange={handleInputChange}
                      />
                    </div>
                  </div>
                  <div className="form-row">
                    <div className="form-group">
                      <label htmlFor="city">City</label>
                      <input
                        type="text"
                        id="city"
                        name="city"
                        value={formData.city}
                        onChange={handleInputChange}
                      />
                    </div>
                    <div className="form-group">
                      <label htmlFor="postalCode">Postal Code</label>
                      <input
                        type="text"
                        id="postalCode"
                        name="postalCode"
                        value={formData.postalCode}
                        onChange={handleInputChange}
                      />
                    </div>
                  </div>
                  <div className="form-row">
                    <div className="form-group">
                      <label htmlFor="country">Country</label>
                      <input
                        type="text"
                        id="country"
                        name="country"
                        value={formData.country}
                        onChange={handleInputChange}
                        disabled
                      />
                    </div>
                    <div className="form-group">
                      <label htmlFor="meterNumber">PDL/PRM/RAE Number</label>
                      <input
                        type="text"
                        id="meterNumber"
                        name="meterNumber"
                        value={formData.meterNumber}
                        onChange={handleInputChange}
                        placeholder="Enter your 14-digit meter number"
                        pattern="[0-9]{14}"
                        maxLength="14"
                        inputMode="numeric"
                      />
                      <small style={{ color: '#666', fontSize: '0.875rem' }}>
                        Your meter identification number (exactly 14 digits)
                      </small>
                    </div>
                  </div>
                </div>

                <div className="form-actions">
                  <button type="button" className="cancel-btn" onClick={() => setEditMode(false)}>
                    Cancel
                  </button>
                  <button type="submit" className="save-btn">
                    Save Changes
                  </button>
                </div>
              </form>
            ) : (
              <div className="profile-details">
                <div className="profile-section">
                  <h2>Personal Information</h2>
                  <div className="info-grid">
                    <div className="info-item">
                      <span className="info-label">First Name</span>
                      <span className="info-value">{profileData.firstName}</span>
                    </div>
                    <div className="info-item">
                      <span className="info-label">Last Name</span>
                      <span className="info-value">{profileData.lastName}</span>
                    </div>
                    <div className="info-item">
                      <span className="info-label">Email</span>
                      <span className="info-value">{profileData.email}</span>
                    </div>
                    <div className="info-item">
                      <span className="info-label">Phone</span>
                      <span className="info-value">{profileData.phone || 'Not provided'}</span>
                    </div>
                    <div className="info-item">
                      <span className="info-label">User Type</span>
                      <span className="info-value capitalize">{profileData.userType}</span>
                    </div>
                    <div className="info-item">
                      <span className="info-label">Member Since</span>
                      <span className="info-value">{formatDate(profileData.createdAt)}</span>
                    </div>
                  </div>
                </div>

                <div className="profile-section">
                  <h2>Address Information</h2>
                  <div className="info-grid">
                    <div className="info-item">
                      <span className="info-label">Address</span>
                      <span className="info-value">{profileData.address}</span>
                    </div>
                    <div className="info-item">
                      <span className="info-label">City</span>
                      <span className="info-value">{profileData.city}</span>
                    </div>
                    <div className="info-item">
                      <span className="info-label">Postal Code</span>
                      <span className="info-value">{profileData.postalCode}</span>
                    </div>
                    <div className="info-item">
                      <span className="info-label">Country</span>
                      <span className="info-value">{profileData.country}</span>
                    </div>
                    <div className="info-item">
                      <span className="info-label">Meter Number</span>
                      <span className="info-value">{profileData.meterNumber}</span>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </DashboardLayout>
  );
};

export default Profile;
