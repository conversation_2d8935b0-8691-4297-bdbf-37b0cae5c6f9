/**
 * Comprehensive logout handler to ensure all authentication state is properly cleared
 */
import { Auth } from 'aws-amplify';
import { clearAll } from './localStorage';

/**
 * Perform a complete logout, clearing all authentication state
 * @returns {Promise<void>}
 */
export const performLogout = async () => {
  try {
    console.log('Starting comprehensive logout process');
    
    // Step 1: Sign out from AWS Cognito with global option
    try {
      await Auth.signOut({ global: true });
      console.log('Successfully signed out from AWS Cognito');
    } catch (cognitoError) {
      console.error('Error signing out from AWS Cognito:', cognitoError);
      // Continue with the logout process even if Cognito signout fails
    }
    
    // Step 2: Clear all localStorage data
    try {
      clearAll();
      console.log('Successfully cleared all localStorage data');
    } catch (localStorageError) {
      console.error('Error clearing localStorage:', localStorageError);
    }
    
    // Step 3: Clear all AWS Amplify related keys from localStorage
    try {
      // Get all keys in localStorage
      const allKeys = Object.keys(localStorage);
      
      // Filter for AWS Amplify keys
      const amplifyKeys = allKeys.filter(key => 
        key.startsWith('CognitoIdentityServiceProvider') ||
        key.startsWith('amplify-') ||
        key.startsWith('aws.') ||
        key.startsWith('AWSCognito')
      );
      
      // Remove each AWS Amplify key
      amplifyKeys.forEach(key => {
        try {
          localStorage.removeItem(key);
        } catch (e) {
          // Silently fail
        }
      });
      
      console.log(`Removed ${amplifyKeys.length} AWS Amplify related keys from localStorage`);
    } catch (amplifyKeysError) {
      console.error('Error clearing AWS Amplify keys:', amplifyKeysError);
    }
    
    // Step 4: Clear all sessionStorage data
    try {
      sessionStorage.clear();
      console.log('Successfully cleared all sessionStorage data');
    } catch (sessionStorageError) {
      console.error('Error clearing sessionStorage:', sessionStorageError);
    }
    
    // Step 5: As a last resort, clear everything in localStorage
    try {
      localStorage.clear();
      console.log('Successfully cleared all localStorage data (final step)');
    } catch (finalClearError) {
      console.error('Error in final localStorage clear:', finalClearError);
    }
    
    // Step 6: Clear cookies related to authentication
    try {
      document.cookie.split(";").forEach(function(c) {
        document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/");
      });
      console.log('Successfully cleared all cookies');
    } catch (cookieError) {
      console.error('Error clearing cookies:', cookieError);
    }
    
    console.log('Comprehensive logout process completed');
  } catch (error) {
    console.error('Unexpected error during logout process:', error);
    
    // Final fallback: try to clear everything
    try {
      localStorage.clear();
      sessionStorage.clear();
    } catch (e) {
      // Silently fail
    }
  }
};

/**
 * Check if there are any residual authentication tokens
 * @returns {boolean} - True if there are residual tokens
 */
export const hasResidualAuthState = () => {
  try {
    // Check for AWS Amplify related keys in localStorage
    const allKeys = Object.keys(localStorage);
    
    // Check for Cognito tokens
    const hasCognitoTokens = allKeys.some(key => 
      key.startsWith('CognitoIdentityServiceProvider') ||
      key.includes('idToken') ||
      key.includes('accessToken') ||
      key.includes('refreshToken')
    );
    
    // Check for our own auth flags
    const hasAuthFlags = 
      localStorage.getItem('isAuthenticated') === 'true' ||
      localStorage.getItem('userType') !== null;
    
    return hasCognitoTokens || hasAuthFlags;
  } catch (error) {
    console.error('Error checking for residual auth state:', error);
    return false;
  }
};

export default {
  performLogout,
  hasResidualAuthState
};
