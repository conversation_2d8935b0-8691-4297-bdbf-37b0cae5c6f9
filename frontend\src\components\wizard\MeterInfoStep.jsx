import React from 'react';

const MeterInfoStep = ({ formData, onChange, onNext, onPrev }) => {
  const handleChange = (e) => {
    const { name, value } = e.target;
    onChange(name, value);
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    onNext();
  };

  return (
    <div className="wizard-step">
      <h3 className="wizard-title">Meter Information</h3>
      <form onSubmit={handleSubmit}>
        <div className="form-group">
          <label htmlFor="meterNumber">PDL/PRM/RAE Number <span className="required">*</span></label>
          <input
            type="text"
            id="meterNumber"
            name="meterNumber"
            className="form-input"
            value={formData.meterNumber}
            onChange={handleChange}
            placeholder="Enter your 14-digit meter number"
            pattern="[0-9]{14}"
            maxLength="14"
            inputMode="numeric"
            required
          />
          <div className="hint-text">Your meter identification number (exactly 14 digits)</div>
        </div>

        <div className="wizard-buttons">
          <button type="button" className="btn btn-secondary" onClick={onPrev}>
            Back
          </button>
          <button type="submit" className="btn btn-primary">
            Next
          </button>
        </div>
      </form>
    </div>
  );
};

export default MeterInfoStep;
