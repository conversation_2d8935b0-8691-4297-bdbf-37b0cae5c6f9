version: '3.8'

services:
  # MongoDB service
  mongodb:
    image: mongo:latest
    container_name: energy-mongodb-dev
    restart: always
    ports:
      - "27017:27017"
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=admin123
      - MONGO_INITDB_DATABASE=energy_bill
    volumes:
      - mongodb_data_dev:/data/db
    networks:
      - app-network-dev

  # Backend service with hot reloading
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    container_name: energy-backend-dev
    restart: always
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - MONGODB_URI=*******************************************************************
      - PORT=3000
    volumes:
      - ./backend:/app
      - /app/node_modules
    depends_on:
      - mongodb
    networks:
      - app-network-dev
    command: npm run dev

  # Frontend service with hot reloading
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    container_name: energy-frontend-dev
    restart: always
    ports:
      - "8080:8080"
    environment:
      - NODE_ENV=development
    volumes:
      - ./frontend:/app
      - /app/node_modules
    depends_on:
      - backend
    networks:
      - app-network-dev
    command: npm run dev

networks:
  app-network-dev:
    driver: bridge

volumes:
  mongodb_data_dev:
    name: energy-mongodb-data-dev
