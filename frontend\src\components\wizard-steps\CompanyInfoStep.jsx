import React from 'react';

const CompanyInfoStep = ({ formData, onChange, onNext, onCancel }) => {
  const handleChange = (e) => {
    const { name, value } = e.target;
    onChange(name, value);
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    onNext();
  };



  return (
    <div className="wizard-step">
      <h3 className="wizard-title">Company Information</h3>
      <form onSubmit={handleSubmit}>
        <div className="form-group">
          <label htmlFor="companyName">Company Name <span className="required">*</span></label>
          <input
            type="text"
            id="companyName"
            name="companyName"
            className="form-input"
            value={formData.companyName}
            onChange={handleChange}
            placeholder="Enter your company name"
            required
          />
        </div>

        <div className="form-group">
          <label htmlFor="siretNumber">SIRET Number <span className="required">*</span></label>
          <input
            type="text"
            id="siretNumber"
            name="siretNumber"
            className="form-input"
            value={formData.siretNumber}
            onChange={handleChange}
            placeholder="Enter your SIRET number"
            required
          />
        </div>

        <div className="form-group">
          <label htmlFor="role">Your Role <span className="required">*</span></label>
          <input
            type="text"
            id="role"
            name="role"
            className="form-input"
            value={formData.role}
            onChange={handleChange}
            placeholder="Enter your role in the company"
            required
          />
        </div>

        <div className="form-group">
          <label htmlFor="streetAddress">Street Address <span className="required">*</span></label>
          <input
            type="text"
            id="streetAddress"
            name="streetAddress"
            className="form-input"
            value={formData.streetAddress}
            onChange={handleChange}
            placeholder="Enter your street address"
            required
          />
        </div>

        <div className="form-group">
          <label htmlFor="city">City <span className="required">*</span></label>
          <input
            type="text"
            id="city"
            name="city"
            className="form-input"
            value={formData.city}
            onChange={handleChange}
            placeholder="Enter your city"
            required
          />
        </div>

        <div className="form-group">
          <label htmlFor="postalCode">Postal Code <span className="optional">(optional)</span></label>
          <input
            type="text"
            id="postalCode"
            name="postalCode"
            className="form-input"
            value={formData.postalCode}
            onChange={handleChange}
            placeholder="Enter postal code (optional)"
            maxLength="5"
          />
        </div>

        <div className="form-group">
          <label htmlFor="country">Country <span className="required">*</span></label>
          <select
            id="country"
            name="country"
            className="form-input"
            value={formData.country}
            onChange={handleChange}
            required
          >
            <option value="France">France</option>
            <option value="Germany">Germany</option>
            <option value="Spain">Spain</option>
            <option value="Italy">Italy</option>
            <option value="Belgium">Belgium</option>
            <option value="Netherlands">Netherlands</option>
            <option value="Other">Other</option>
          </select>
        </div>

        <div className="wizard-buttons">
          <button type="button" className="btn btn-secondary" onClick={onCancel}>
            Cancel
          </button>
          <button
            type="submit"
            className="btn btn-primary stepper-button-override"
            style={{
              backgroundColor: '#000000',
              borderColor: '#000000',
              color: '#ffffff',
              border: '1px solid #000000'
            }}
          >
            Next
          </button>
        </div>
      </form>
    </div>
  );
};

export default CompanyInfoStep;
