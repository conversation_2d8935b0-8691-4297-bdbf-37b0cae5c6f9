.analytics-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.analytics-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  flex-wrap: wrap;
  gap: 15px;
}

.analytics-header h1 {
  font-size: 24px;
  font-weight: 600;
  color: #000;
  margin: 0;
}

.time-range-selector {
  display: flex;
  gap: 10px;
}

.time-range-btn {
  padding: 8px 15px;
  border: 1px solid #ddd;
  border-radius: 6px;
  background-color: #fff;
  color: #333;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.time-range-btn.active {
  background-color: #000;
  color: #fff;
  border-color: #000;
}

.time-range-btn:hover:not(.active) {
  background-color: #f5f5f5;
}

.analytics-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* Summary Cards */
.analytics-summary {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
}

.summary-card {
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  padding: 20px;
  display: flex;
  align-items: center;
  border: 1px solid #000;
}

.summary-icon {
  width: 50px;
  height: 50px;
  border-radius: 10px;
  background-color: #000;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  flex-shrink: 0;
}

.summary-icon i {
  font-size: 20px;
}

.summary-details {
  flex: 1;
}

.summary-title {
  font-size: 14px;
  color: #666;
  margin: 0 0 5px 0;
}

.summary-value {
  font-size: 24px;
  font-weight: 700;
  color: #000;
  margin: 0;
}

/* Charts Section */
.analytics-charts {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
}

.chart-card {
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  border: 1px solid #000;
}

.chart-header {
  padding: 15px 20px;
  border-bottom: 1px solid #eaeaea;
}

.chart-header h2 {
  font-size: 18px;
  font-weight: 600;
  color: #000;
  margin: 0;
}

.chart-content {
  padding: 20px;
  height: 300px;
}

.chart-placeholder {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.chart-placeholder p {
  color: #666;
  font-style: italic;
  margin-bottom: 20px;
}

/* Mock Charts for Visualization */
.mock-bar-chart {
  display: flex;
  align-items: flex-end;
  justify-content: space-around;
  height: 200px;
  width: 100%;
  padding: 0 20px;
}

.mock-bar {
  width: 40px;
  background-color: #000;
  border-radius: 4px 4px 0 0;
  position: relative;
  transition: height 0.3s ease;
}

.mock-bar-value {
  position: absolute;
  top: -25px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 12px;
  font-weight: 600;
}

.mock-chart-labels {
  display: flex;
  justify-content: space-around;
  width: 100%;
  margin-top: 10px;
}

.mock-label {
  font-size: 12px;
  color: #666;
  text-align: center;
  width: 40px;
}

.mock-line-chart {
  position: relative;
  height: 200px;
  width: 100%;
  padding: 0 20px;
}

.mock-line {
  position: absolute;
  top: 50%;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: rgba(0, 0, 0, 0.1);
}

.mock-points {
  position: relative;
  height: 100%;
  width: 100%;
}

.mock-point {
  position: absolute;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #000;
  transform: translate(-50%, 50%);
}

/* Additional Analytics */
.analytics-additional {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
}

/* Distribution Chart */
.distribution-chart {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.distribution-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.distribution-label {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.distribution-bar-container {
  height: 30px;
  background-color: #f5f5f5;
  border-radius: 15px;
  position: relative;
  overflow: hidden;
}

.distribution-bar {
  height: 100%;
  border-radius: 15px;
  transition: width 0.3s ease;
}

.distribution-bar.individual {
  background-color: #000;
}

.distribution-bar.business {
  background-color: #333;
}

.distribution-value {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 12px;
  font-weight: 600;
  color: #fff;
}

/* Top Clients List */
.top-clients-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.top-client-item {
  display: flex;
  align-items: center;
  padding: 10px;
  background-color: #f9f9f9;
  border-radius: 8px;
  border: 1px solid #eaeaea;
}

.client-rank {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: #000;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  margin-right: 15px;
  flex-shrink: 0;
}

.client-info {
  flex: 1;
}

.client-name {
  font-size: 16px;
  font-weight: 500;
  color: #000;
  margin-bottom: 5px;
}

.client-details {
  font-size: 14px;
  color: #666;
}

/* Responsive styles */
@media (max-width: 992px) {
  .analytics-summary,
  .analytics-charts,
  .analytics-additional {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .analytics-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .analytics-summary {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .chart-content {
    height: 250px;
  }
}

@media (max-width: 480px) {
  .analytics-summary {
    grid-template-columns: 1fr;
  }
  
  .time-range-selector {
    width: 100%;
    justify-content: space-between;
  }
}
