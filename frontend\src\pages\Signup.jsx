import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import SignupForm from '../components/SignupForm';
import VerificationForm from '../components/VerificationForm';
import { useAuth } from '../context/AuthContext';
import authService from '../services/auth.service';
import verificationService from '../services/verification.service';
import { getItem, setItem, removeItem, STORAGE_KEYS } from '../utils/localStorage';
import Spinner from '../components/Spinner';
import { getLoadingMessage } from '../utils/loadingMessages';
import { showSuccessMessage, showErrorMessage, showInfoMessage } from '../utils/toastNotifications';
import logger from '../utils/logger';

const Signup = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const navigate = useNavigate();
  const { signUp, confirmSignUp, signIn, isAuthenticated, loading: authLoading } = useAuth();

  const [verificationStep, setVerificationStep] = useState(false);
  const [signupData, setSignupData] = useState(null);
  const [verificationCode, setVerificationCode] = useState('');
  const [formData, setFormData] = useState(null);

  // Check if user is already authenticated and redirect to dashboard
  useEffect(() => {
    // First, ensure we're not in a state where there are residual tokens but the user isn't actually authenticated
    const checkAndCleanupAuth = async () => {
      try {
        // Try to get the current authenticated user from Cognito
        await Auth.currentAuthenticatedUser();
        // If we get here, the user is authenticated
      } catch (error) {
        // If we get here, the user is not authenticated
        // But don't clear localStorage during signup flow - check if we have signup data
        const hasSignupData = getItem(STORAGE_KEYS.USER_DATA, true)?.signupCompleted;

        if (!hasSignupData) {
          // Only clear if we're not in the middle of a signup flow
          logger.info('User not authenticated and no signup in progress, clearing state');
          localStorage.clear();
          sessionStorage.clear();
        } else {
          logger.info('User not authenticated but signup in progress, preserving localStorage');
        }
        return; // Exit early
      }

      // Only proceed with redirection if the user is actually authenticated
      if (!authLoading && isAuthenticated) {
        logger.info('User is already authenticated, redirecting to dashboard');

        // Check if user has a user type and profile completion status
        const userType = getItem(STORAGE_KEYS.USER_TYPE);
        const profileComplete = getItem(STORAGE_KEYS.PROFILE_COMPLETION) === 'true';

        // Only redirect if we have valid user data
        if (userType) {
          if (!profileComplete) {
            // If profile not complete, redirect to appropriate info page
            if (userType.toLowerCase() === 'individual') {
              navigate('/individual-info');
            } else {
              navigate('/professional-info');
            }
          } else {
            // If profile is complete, redirect to dashboard
            navigate('/dashboard');
          }
        } else {
          // If authenticated but no user type, redirect to dashboard
          // The ProtectedRoute component will handle the redirection to user type selection
          navigate('/dashboard');
        }
      }
    };

    checkAndCleanupAuth();
  }, [isAuthenticated, authLoading, navigate]);

  // Clear localStorage on component mount
  useEffect(() => {
    // Clear localStorage items related to user info using our safe removeItem function
    removeItem('individualInfo');
    removeItem('professionalInfo');
    removeItem('userType');

    logger.debug('Cleared localStorage in Signup component');
  }, []);

  const handleSignup = async (userData) => {
    logger.debug('handleSignup called with userData:', userData);
    setLoading(true);
    setError(null);

    try {
      // Extract the data from the form
      const { email, password, phoneNumber, confirmPassword, firstName, lastName } = userData;
      logger.debug('Extracted user data:', { email, phoneNumber, firstName, lastName });

      // Validate passwords match
      if (password !== confirmPassword) {
        throw new Error('Passwords do not match');
      }

      // Format the phone number with French country code (+33) for Cognito
      let formattedPhoneNumber = '';
      if (phoneNumber) {
        // Remove all non-digit characters
        let digitsOnly = phoneNumber.replace(/\D/g, '');

        // Remove leading 0 if present
        if (digitsOnly.startsWith('0')) {
          digitsOnly = digitsOnly.substring(1);
        }

        // Add French country code
        formattedPhoneNumber = `+33${digitsOnly}`;
      }

      // Prepare user attributes
      const userAttributes = {
        email,
        phone_number: formattedPhoneNumber
      };

      // Add name attributes if provided
      if (firstName) {
        userAttributes.given_name = firstName;
      }

      if (lastName) {
        userAttributes.family_name = lastName;
      }

      logger.info('Signing up with Cognito:', { email, userAttributes });

      // Sign up with Cognito
      const result = await signUp(email, password, userAttributes);
      const cognitoUserId = result.userSub || result.userSubId || result.userId || result.id;

      logger.info('Signup successful:', result);
      logger.debug('Cognito User ID:', cognitoUserId);

      // Store initial user data in localStorage immediately after Cognito signup
      const initialUserData = {
        email: email,
        firstName: firstName || '',
        lastName: lastName || '',
        phoneNumber: formattedPhoneNumber,
        cognitoId: cognitoUserId,
        userType: null, // Will be set during user type selection
        profileCompletion: false,
        verified: false, // Not verified yet
        authenticated: false, // Not authenticated yet
        signupCompleted: true
      };

      // Store to localStorage
      setItem(STORAGE_KEYS.USER_DATA, initialUserData);
      setItem(STORAGE_KEYS.COGNITO_ID, cognitoUserId);
      setItem(STORAGE_KEYS.PROFILE_COMPLETION, 'false');
      setItem(STORAGE_KEYS.IS_AUTHENTICATED, 'false');
      setItem(STORAGE_KEYS.VERIFIED, 'false');

      logger.info('Stored initial user data in localStorage after Cognito signup:', initialUserData);

      // Register user in MongoDB
      if (cognitoUserId) {
        try {
          // Prepare user data for MongoDB
          const mongoUserData = {
            cognitoId: cognitoUserId,
            email,
            firstName,
            lastName,
            phone: formattedPhoneNumber,
            userType: 'Individual', // Default user type, will be updated later
            status: 'Pending',
            verificationStatus: 'Unverified'
          };

          logger.info('Registering user in MongoDB:', mongoUserData);

          // Call the service to register user in MongoDB
          const mongoResult = await authService.registerUserInBackend(mongoUserData);
          logger.debug('MongoDB registration result:', mongoResult);
        } catch (mongoError) {
          logger.error('Error registering user in MongoDB:', mongoError);
          // Continue with the flow even if MongoDB registration fails
          // We'll handle this during confirmation or login
        }
      } else {
        logger.warn('No Cognito User ID received, cannot register in MongoDB');
      }

      // Store signup data for verification step and later use
      setSignupData({ email, cognitoUserId });
      setFormData(userData);

      // Move to verification step
      showSuccessMessage('SIGNUP_SUCCESS');
      setVerificationStep(true);

    } catch (err) {
      logger.error('Signup error:', err);

      if (err.code === 'UsernameExistsException') {
        showErrorMessage('SIGNUP_FAILED', 'An account with this email already exists. Please sign in or use a different email.');
      } else if (err.code === 'InvalidPasswordException') {
        showErrorMessage('SIGNUP_FAILED', 'Password does not meet requirements. Please use at least 8 characters, including uppercase, lowercase, numbers, and special characters.');
      } else if (err.code === 'InvalidParameterException' && err.message.includes('phone')) {
        showErrorMessage('SIGNUP_FAILED', 'Invalid phone number format. Please use a valid phone number with country code.');
      } else {
        showErrorMessage('SIGNUP_FAILED', err.message || 'Failed to create account. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleVerification = async (e) => {
    e.preventDefault();

    if (!verificationCode) {
      setError('Please enter the verification code');
      return;
    }

    if (!formData || !signupData || !signupData.email) {
      setError('Registration data is missing. Please try signing up again.');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      logger.info('Confirming signup with Cognito:', { email: signupData.email, code: verificationCode });

      // Confirm signup with Cognito
      await confirmSignUp(signupData.email, verificationCode);

      logger.info('Account verification successful');

      // Update localStorage to mark as verified
      setItem(STORAGE_KEYS.VERIFIED, 'true');

      // Update user data in localStorage with verified status
      const currentUserData = getItem(STORAGE_KEYS.USER_DATA, true) || {};
      const updatedUserData = {
        ...currentUserData,
        verified: true
      };
      setItem(STORAGE_KEYS.USER_DATA, updatedUserData);
      logger.info('Updated localStorage with verified status:', updatedUserData);

      // Sign in the user immediately after confirmation
      try {
        logger.info('Attempting to sign in after confirmation');
        // Use the signIn function from the context instead of direct Auth import
        await signIn(signupData.email, formData.password);
        logger.info('Sign in successful');

        logger.debug('Authentication handled by context');
      } catch (signInError) {
        logger.error('Error signing in after confirmation:', signInError);
        // Continue with the flow even if sign-in fails
      }

      // Update user status in MongoDB after verification
      if (signupData.cognitoUserId) {
        try {
          // Use the verification service to update the user's status
          const updateData = {
            email: signupData.email,
            cognitoId: signupData.cognitoUserId,
            status: 'Active',
            verificationStatus: 'Verified'
          };

          logger.info('Updating verification status with data:', updateData);
          const result = await verificationService.updateVerificationStatus(updateData);
          logger.debug('Verification status update result:', result);
        } catch (statusUpdateError) {
          logger.warn('Error updating verification status in MongoDB:', statusUpdateError);
          // Continue with the flow even if the status update fails
        }
      }

      // Prepare user data to pass to the user type selection page
      let formattedPhoneNumber = '';
      if (formData.phoneNumber) {
        // Remove all non-digit characters
        let digitsOnly = formData.phoneNumber.replace(/\D/g, '');

        // Remove leading 0 if present
        if (digitsOnly.startsWith('0')) {
          digitsOnly = digitsOnly.substring(1);
        }

        // Add French country code
        formattedPhoneNumber = `+33${digitsOnly}`;
      }

      // Note: User is already authenticated after successful sign-in above
      // No need to store password - user should remain authenticated

      const userData = {
        email: formData.email,
        phoneNumber: formattedPhoneNumber,
        firstName: formData.firstName || '',
        lastName: formData.lastName || '',
        cognitoId: signupData.cognitoUserId || ''
      };

      logger.debug('User data for type selection:', userData);

      // Show success message and redirect
      showSuccessMessage('VERIFICATION_SUCCESS');

      // Redirect to user type selection page after successful verification
      navigate('/user-type', {
        state: {
          userData: userData
        }
      });
    } catch (err) {
      logger.error('Verification error:', err);

      if (err.code === 'CodeMismatchException') {
        showErrorMessage('VERIFICATION_FAILED', 'Invalid verification code. Please try again.');
      } else if (err.code === 'ExpiredCodeException') {
        showErrorMessage('VERIFICATION_FAILED', 'Verification code has expired. Please request a new code.');
      } else if (err.code === 'LimitExceededException') {
        showErrorMessage('VERIFICATION_FAILED', 'Too many attempts. Please try again later.');
      } else {
        showErrorMessage('VERIFICATION_FAILED', err.message || 'Failed to verify account. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleResendCode = async () => {
    if (!signupData || !signupData.email) {
      setError('Unable to resend code. Please try signing up again.');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Use the Auth context to resend the verification code
      // Since we don't have a resendSignUp in the context, we'll import Auth directly
      const { Auth } = await import('aws-amplify');
      await Auth.resendSignUp(signupData.email);
      showInfoMessage('CHECK_EMAIL', 'Verification code resent. Please check your email.');
    } catch (err) {
      logger.error('Resend code error:', err);

      if (err.code === 'LimitExceededException') {
        setError('Too many attempts. Please try again later.');
      } else {
        setError(err.message || 'Failed to resend code. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      {error && (
        <div style={{
          position: 'fixed',
          top: '20px',
          left: '50%',
          transform: 'translateX(-50%)',
          backgroundColor: '#fff',
          color: '#ff3b30',
          padding: '10px 20px',
          borderRadius: '8px',
          border: '1px solid #e0e0e0',
          boxShadow: '0 2px 10px rgba(0, 0, 0, 0.1)',
          zIndex: 100
        }}>
          {error}
        </div>
      )}

      {!verificationStep ? (
        // Sign up form
        <SignupForm onSubmit={handleSignup} />
      ) : (
        // Verification form
        <VerificationForm
          verificationCode={verificationCode}
          setVerificationCode={setVerificationCode}
          handleVerification={handleVerification}
          handleResendCode={handleResendCode}
        />
      )}

      {loading && (
        <Spinner
          fullScreen={true}
          message={verificationStep ? getLoadingMessage('VERIFICATION') : getLoadingMessage('SIGNUP')}
          size="large"
          color="#000000"
        />
      )}
    </>
  );
};

export default Signup;
