import { useEffect } from 'react';
import '../styles/offer-modal.css';

const OfferModal = ({ offer, isOpen, onClose, onAccept }) => {
  // Close modal on escape key press
  useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden'; // Prevent background scrolling
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  if (!isOpen || !offer) return null;

  return (
    <div className="modal-overlay" onClick={onClose}>
      <div className="modal-content" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <div className="modal-title-section">
            <div className="modal-provider">
              <span className="modal-provider-name">{offer.provider}</span>
              <span className={`modal-offer-type ${offer.energyType.toLowerCase()}`}>
                {offer.energyType}
              </span>
            </div>
            <h2 className="modal-offer-name">{offer.name}</h2>
          </div>
          <button className="modal-close-btn" onClick={onClose}>
            <i className="fas fa-times"></i>
          </button>
        </div>

        <div className="modal-body">
          <div className="modal-description">
            <p>{offer.description}</p>
          </div>

          <div className="modal-details-grid">
            <div className="modal-section">
              <h3>Pricing Details</h3>
              <div className="modal-detail-item">
                <span className="modal-detail-label">Rate Type:</span>
                <span className="modal-detail-value">{offer.rateType}</span>
              </div>
              <div className="modal-detail-item">
                <span className="modal-detail-label">Duration:</span>
                <span className="modal-detail-value">{offer.duration} months</span>
              </div>
              <div className="modal-detail-item">
                <span className="modal-detail-label">Base Rate:</span>
                <span className="modal-detail-value">{offer.price.baseRate} €/kWh</span>
              </div>
              <div className="modal-detail-item">
                <span className="modal-detail-label">Standing Charge:</span>
                <span className="modal-detail-value">{offer.price.standingCharge} €/month</span>
              </div>
              <div className="modal-detail-item">
                <span className="modal-detail-label">Estimated Annual Cost:</span>
                <span className="modal-detail-value">{offer.price.totalEstimatedAnnual} €</span>
              </div>
            </div>

            <div className="modal-section">
              <h3>Savings Information</h3>
              <div className="modal-savings-highlight">
                <div className="modal-savings-amount">
                  <span className="modal-savings-label">Estimated Annual Savings</span>
                  <span className="modal-savings-value">{offer.estimatedSavings.amount} €</span>
                </div>
                <div className="modal-savings-percentage">
                  <span className="modal-percentage-value">{offer.estimatedSavings.percentage}%</span>
                  <span className="modal-percentage-label">lower than your current rate</span>
                </div>
              </div>
            </div>
          </div>

          <div className="modal-section">
            <h3>Key Highlights</h3>
            <ul className="modal-highlights-list">
              {offer.highlights.map((highlight, index) => (
                <li key={index} className="modal-highlight-item">
                  <i className="fas fa-check-circle"></i>
                  {highlight}
                </li>
              ))}
            </ul>
          </div>

          <div className="modal-validity">
            <i className="fas fa-calendar-alt"></i>
            <span>Valid until: {new Date(offer.validUntil).toLocaleDateString('en-GB', {
              day: 'numeric',
              month: 'long',
              year: 'numeric'
            })}</span>
          </div>
        </div>

        <div className="modal-footer">
          <button className="modal-btn-cancel" onClick={onClose}>
            Close
          </button>
          <button className="modal-btn-accept" onClick={onAccept}>
            Accept This Offer
          </button>
        </div>
      </div>
    </div>
  );
};

export default OfferModal;
