/* Modern Modal System - Professional Design */

/* Modal Overlay */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  padding: 40px 20px;
  animation: modalOverlayFadeIn 0.3s ease-out;
  box-sizing: border-box;
}

@keyframes modalOverlayFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Modal Container */
.modal-container {
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  max-width: 600px;
  width: 100%;
  max-height: 85vh;
  overflow: hidden;
  position: relative;
  animation: modalSlideIn 0.3s ease-out;
  display: flex;
  flex-direction: column;
  z-index: 10000;
  margin: 20px;
}

/* Modal Size Classes */
.modal-container.max-w-md {
  max-width: 28rem; /* 448px */
}

.modal-container.max-w-2xl {
  max-width: 42rem; /* 672px */
}

.modal-container.max-w-4xl {
  max-width: 56rem; /* 896px */
}

.modal-container.max-w-6xl {
  max-width: 72rem; /* 1152px */
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Modal Header */
.modal-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 24px 32px;
  position: relative;
  border-radius: 16px 16px 0 0;
}

.modal-header h2,
.modal-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  line-height: 1.3;
}

.modal-header p {
  margin: 8px 0 0 0;
  opacity: 0.9;
  font-size: 14px;
  font-weight: 400;
}

/* Close Button */
.modal-close {
  position: absolute;
  top: 20px;
  right: 24px;
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 16px;
}

.modal-close:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

/* Modal Body */
.modal-body {
  padding: 32px;
  overflow-y: auto;
  flex: 1;
  min-height: 0;
}

.modal-body::-webkit-scrollbar {
  width: 6px;
}

.modal-body::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.modal-body::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.modal-body::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Step Indicator */
.step-indicator {
  display: flex;
  align-items: center;
  margin-bottom: 32px;
  padding: 0 16px;
}

.step-item {
  display: flex;
  align-items: center;
  flex: 1;
  position: relative;
}

.step-number {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
  margin-right: 12px;
  transition: all 0.3s ease;
}

.step-item.active .step-number {
  background: #667eea;
  color: white;
}

.step-item.completed .step-number {
  background: #10b981;
  color: white;
}

.step-item.pending .step-number {
  background: #e5e7eb;
  color: #6b7280;
}

.step-label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.step-item.active .step-label {
  color: #667eea;
}

.step-item.completed .step-label {
  color: #10b981;
}

.step-connector {
  flex: 1;
  height: 2px;
  background: #e5e7eb;
  margin: 0 16px;
}

.step-item.completed + .step-item .step-connector {
  background: #10b981;
}

/* Form Sections */
.form-section {
  margin-bottom: 32px;
}

.form-section:last-child {
  margin-bottom: 0;
}

/* Make modal section title more specific to avoid conflicts with home page */
.modal-content .section-title,
.form-section .section-title {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  text-align: left; /* Explicitly set for modals */
}

.section-title i {
  margin-right: 8px;
  color: #667eea;
}

/* Form Groups */
.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 6px;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.2s ease;
  background: #ffffff;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 80px;
}

/* File Upload */
.file-upload-area {
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  padding: 24px;
  text-align: center;
  transition: all 0.2s ease;
  cursor: pointer;
}

.file-upload-area:hover {
  border-color: #667eea;
  background: #f8faff;
}

.file-upload-area.dragover {
  border-color: #667eea;
  background: #f0f4ff;
}

.file-upload-icon {
  font-size: 32px;
  color: #9ca3af;
  margin-bottom: 12px;
}

.file-upload-text {
  font-size: 14px;
  color: #6b7280;
}

.file-upload-text strong {
  color: #667eea;
}

/* Modal Footer */
.modal-footer {
  padding: 24px 32px;
  background: #f9fafb;
  border-top: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 12px;
  flex-shrink: 0;
  border-radius: 0 0 16px 16px;
}

/* Button Styles */
.btn {
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
  min-width: 100px;
  justify-content: center;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.btn-secondary {
  background: #ffffff;
  color: #374151;
  border: 2px solid #e5e7eb;
}

.btn-secondary:hover {
  background: #f9fafb;
  border-color: #d1d5db;
}

.btn-success {
  background: #10b981;
  color: white;
}

.btn-success:hover {
  background: #059669;
  transform: translateY(-1px);
}

.btn-danger {
  background: #ef4444;
  color: white;
}

.btn-danger:hover {
  background: #dc2626;
  transform: translateY(-1px);
}

/* Loading State */
.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

.btn-loading {
  position: relative;
  color: transparent !important;
}

.btn-loading::after {
  content: '';
  position: absolute;
  width: 16px;
  height: 16px;
  top: 50%;
  left: 50%;
  margin-left: -8px;
  margin-top: -8px;
  border: 2px solid transparent;
  border-top-color: currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .modal-container {
    margin: 20px;
    max-width: calc(100vw - 40px);
    border-radius: 12px;
  }
  
  .modal-header {
    padding: 20px 24px;
    border-radius: 12px 12px 0 0;
  }
  
  .modal-body {
    padding: 24px;
  }
  
  .modal-footer {
    padding: 20px 24px;
    flex-direction: column-reverse;
    gap: 12px;
    border-radius: 0 0 12px 12px;
  }
  
  .btn {
    width: 100%;
  }
  
  .step-indicator {
    flex-direction: column;
    gap: 16px;
  }
  
  .step-connector {
    display: none;
  }
}
