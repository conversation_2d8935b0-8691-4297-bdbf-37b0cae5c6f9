# Setting Up AWS S3 for Invoice Upload

This document provides instructions for setting up an AWS S3 bucket for storing invoice files.

## Prerequisites

1. An AWS account
2. AWS CLI installed and configured (optional, but helpful)

## Deployment Environments

### Local Development
For local development, you need AWS credentials (Access Key ID and Secret Access Key) with permissions to access the S3 bucket.

### AWS Deployment (Dev/Staging/Production)
When deploying to AWS services (EC2, ECS, Lambda, etc.), use IAM roles instead of hardcoded credentials for better security.

## Step 1: Create an S3 Bucket

1. Sign in to the AWS Management Console
2. Navigate to the S3 service
3. Click "Create bucket"
4. Use the bucket name "energy-app-uat-backend-files"
5. Select the region (e.g., "eu-west-3" for Paris)
6. Configure bucket settings:
   - Block all public access (recommended for security)
   - Enable versioning (optional)
   - Enable encryption (recommended)
7. Click "Create bucket"

## Step 2: Create an IAM User for S3 Access

1. Navigate to the IAM service
2. Click "Users" and then "Add user"
3. Enter a username (e.g., "energy-bill-app")
4. Select "Programmatic access" for access type
5. Click "Next: Permissions"
6. Click "Attach existing policies directly"
7. Search for and select "AmazonS3FullAccess" (or create a custom policy with more limited permissions)
8. Click "Next: Tags" (optional)
9. Click "Next: Review"
10. Click "Create user"
11. **Important**: Save the Access Key ID and Secret Access Key - you will need these for the application

## Step 3: Configure CORS for the S3 Bucket

1. Go back to the S3 service
2. Select your bucket
3. Click the "Permissions" tab
4. Scroll down to "Cross-origin resource sharing (CORS)"
5. Click "Edit"
6. Add the following CORS configuration:

```json
[
  {
    "AllowedHeaders": ["*"],
    "AllowedMethods": ["GET", "PUT", "POST", "DELETE", "HEAD"],
    "AllowedOrigins": ["http://localhost:8080", "http://localhost:3000"],
    "ExposeHeaders": ["ETag"]
  }
]
```

7. Click "Save changes"

## Step 4: Update Environment Variables

1. Open the `.env` file in the backend directory
2. Update the following variables:

```
AWS_S3_BUCKET_NAME=energy-app-uat-backend-files
AWS_S3_BUCKET=energy-app-uat-backend-files
AWS_ACCESS_KEY_ID=your-access-key-id
AWS_SECRET_ACCESS_KEY=your-secret-access-key
AWS_REGION=eu-west-3
```

## Step 5: Test the Upload Functionality

1. Start the backend and frontend applications
2. Navigate to the invoice upload page
3. Upload a test invoice file
4. Verify that the file is uploaded to the S3 bucket and the metadata is saved in MongoDB

## Security Considerations

- Never commit AWS credentials to version control
- Use IAM roles instead of access keys for production deployments
- Implement server-side validation of file types and sizes
- Set up lifecycle policies to manage file retention
- Consider implementing server-side encryption for sensitive documents

## Using IAM Roles in AWS Deployment

When deploying to AWS, use IAM roles instead of hardcoded credentials:

### EC2 Deployment

1. Create an IAM role with S3 permissions:
   - Go to IAM in AWS Console
   - Create a new role for EC2
   - Attach the `AmazonS3ReadWrite` policy or a custom policy with specific permissions
   - Name the role (e.g., `energy-app-s3-role`)

2. Attach the role to your EC2 instance:
   - When launching the instance, select the IAM role
   - For existing instances, modify the instance to attach the role

### ECS Deployment

1. Create a Task Execution Role with S3 permissions
2. Reference this role in your task definition

### Lambda Deployment

1. Create an execution role with S3 permissions
2. Assign this role to your Lambda function

The application is designed to automatically use the instance role when deployed to AWS, without needing explicit credentials.

## Troubleshooting

- If you encounter CORS errors, double-check your CORS configuration
- If uploads fail, check the AWS credentials and bucket name
- Ensure the IAM user has the necessary permissions for S3 operations
- Check the server logs for detailed error messages
