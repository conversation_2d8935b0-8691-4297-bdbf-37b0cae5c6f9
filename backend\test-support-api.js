const axios = require('axios');

async function testSupportTicketAPI() {
  console.log('🧪 Testing Support Ticket API Endpoints\n');

  const baseURL = 'http://localhost:3000';
  
  try {
    // Test 1: Get support ticket statistics (without auth - should fail)
    console.log('📊 Testing support ticket statistics endpoint...');
    try {
      const response = await axios.get(`${baseURL}/api/admin/support-tickets/stats/summary`);
      console.log('❌ Expected authentication error, but got:', response.status);
    } catch (error) {
      if (error.response && error.response.status === 401) {
        console.log('✅ Authentication required (expected)');
      } else {
        console.log('❌ Unexpected error:', error.message);
      }
    }

    // Test 2: Get all support tickets (without auth - should fail)
    console.log('\n📋 Testing get all support tickets endpoint...');
    try {
      const response = await axios.get(`${baseURL}/api/admin/support-tickets`);
      console.log('❌ Expected authentication error, but got:', response.status);
    } catch (error) {
      if (error.response && error.response.status === 401) {
        console.log('✅ Authentication required (expected)');
      } else {
        console.log('❌ Unexpected error:', error.message);
      }
    }

    // Test 3: Check if the routes are properly registered
    console.log('\n🔍 Testing route registration...');
    try {
      const response = await axios.get(`${baseURL}/api/admin/dashboard-stats`);
      console.log('❌ Expected authentication error, but got:', response.status);
    } catch (error) {
      if (error.response && error.response.status === 401) {
        console.log('✅ Admin routes are properly registered and protected');
      } else {
        console.log('❌ Unexpected error:', error.message);
      }
    }

    console.log('\n🎉 Support Ticket API endpoints are properly configured!');
    console.log('📝 Next steps:');
    console.log('   1. Login as admin user to get authentication token');
    console.log('   2. Test the endpoints with proper authentication');
    console.log('   3. Access the admin dashboard to view support tickets');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testSupportTicketAPI();
