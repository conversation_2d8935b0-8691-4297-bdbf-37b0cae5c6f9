import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Auth } from 'aws-amplify';
import DashboardLayout from '../../components/DashboardLayout';
import Spinner from '../../components/Spinner';
import Modal, { FormSection, FormGroup, Button, ModalFooter, FileUpload } from '../../components/Modal';
import { showSuccessMessage, showErrorMessage } from '../../utils/toastNotifications';
import logger from '../../utils/logger';
import { loadCSS, unloadCSS, ADMIN_CSS, CSS_IDS } from '../../utils/cssLoader';
import { API_BASE_URL } from '../../config/api-config';
import { STORAGE_KEYS, getItem } from '../../utils/localStorage';

const InvitationManagement = () => {
  const [loading, setLoading] = useState(true);
  const [invitations, setInvitations] = useState([]);
  const [pagination, setPagination] = useState({});
  const [stats, setStats] = useState({});
  const [filters, setFilters] = useState({
    status: 'all',
    userType: 'all',
    search: '',
    sortBy: 'createdAt',
    sortOrder: 'desc'
  });
  const [showBrokerModal, setShowBrokerModal] = useState(false);
  const [showSupplierModal, setShowSupplierModal] = useState(false);
  const [selectedInvitations, setSelectedInvitations] = useState([]);
  const navigate = useNavigate();

  // Helper function to get authentication token for admin routes
  const getAuthToken = async () => {
    try {
      // For admin routes, use ID token
      let token = getItem(STORAGE_KEYS.ID_TOKEN);

      if (!token) {
        try {
          const session = await Auth.currentSession();
          token = session.getIdToken().getJwtToken();
          logger.debug('Got ID token from Cognito session for admin route');
        } catch (sessionError) {
          logger.warn('Could not get ID token from Cognito session:', sessionError);
          throw new Error('Authentication required. Please log in again.');
        }
      }

      return token;
    } catch (error) {
      logger.error('Error getting auth token:', error);
      throw error;
    }
  };

  // Dynamically load CSS only for this component
  useEffect(() => {
    loadCSS(ADMIN_CSS.INVITATIONS, CSS_IDS.ADMIN_INVITATIONS);

    // Cleanup function to remove CSS when component unmounts
    return () => {
      unloadCSS(CSS_IDS.ADMIN_INVITATIONS);
    };
  }, []);

  useEffect(() => {
    fetchInvitations();
  }, [filters]);

  const fetchInvitations = async () => {
    try {
      setLoading(true);
      const queryParams = new URLSearchParams({
        page: 1,
        limit: 10,
        ...filters
      });

      const token = await getAuthToken();
      const response = await fetch(`${API_BASE_URL}/api/admin/invitations?${queryParams}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        if (response.status === 401) {
          throw new Error('Authentication failed. Please log in again.');
        }
        throw new Error('Failed to fetch invitations');
      }

      const data = await response.json();
      setInvitations(data.data.invitations);
      setPagination(data.data.pagination);
      setStats(data.data.stats);
      
      logger.info('Invitations fetched successfully');
    } catch (error) {
      logger.error('Error fetching invitations:', error);
      showErrorMessage('INVITATIONS_LOAD_FAILED', 'Failed to load invitations');
    } finally {
      setLoading(false);
    }
  };

  const handleSendBrokerInvitation = async (formData) => {
    try {
      const token = await getAuthToken();
      const response = await fetch(`${API_BASE_URL}/api/admin/invitations/broker`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(formData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to send broker invitation');
      }

      showSuccessMessage('BROKER_INVITATION_SENT', 'Broker invitation sent successfully');
      setShowBrokerModal(false);
      fetchInvitations();
    } catch (error) {
      logger.error('Error sending broker invitation:', error);
      showErrorMessage('BROKER_INVITATION_FAILED', error.message);
    }
  };

  const handleSendSupplierInvitation = async (formData) => {
    try {
      const token = await getAuthToken();
      const response = await fetch(`${API_BASE_URL}/api/admin/invitations/supplier`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: formData // FormData object for file upload
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to send supplier invitation');
      }

      showSuccessMessage('SUPPLIER_INVITATION_SENT', 'Supplier invitation sent successfully');
      setShowSupplierModal(false);
      fetchInvitations();
    } catch (error) {
      logger.error('Error sending supplier invitation:', error);
      showErrorMessage('SUPPLIER_INVITATION_FAILED', error.message);
    }
  };

  const handleResendInvitation = async (invitationId) => {
    try {
      const token = await getAuthToken();
      const response = await fetch(`${API_BASE_URL}/api/admin/invitations/${invitationId}/resend`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to resend invitation');
      }

      showSuccessMessage('INVITATION_RESENT', 'Invitation reminder sent successfully');
      fetchInvitations();
    } catch (error) {
      logger.error('Error resending invitation:', error);
      showErrorMessage('INVITATION_RESEND_FAILED', 'Failed to resend invitation');
    }
  };

  const handleRevokeInvitation = async (invitationId) => {
    if (!confirm('Are you sure you want to revoke this invitation?')) {
      return;
    }

    try {
      const token = await getAuthToken();
      const response = await fetch(`${API_BASE_URL}/api/admin/invitations/${invitationId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to revoke invitation');
      }

      showSuccessMessage('INVITATION_REVOKED', 'Invitation revoked successfully');
      fetchInvitations();
    } catch (error) {
      logger.error('Error revoking invitation:', error);
      showErrorMessage('INVITATION_REVOKE_FAILED', 'Failed to revoke invitation');
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusBadgeClass = (status) => {
    switch (status) {
      case 'Pending': return 'status-badge pending';
      case 'Accepted': return 'status-badge accepted';
      case 'Expired': return 'status-badge expired';
      case 'Revoked': return 'status-badge revoked';
      default: return 'status-badge';
    }
  };

  const getDaysRemaining = (expiresAt) => {
    const now = new Date();
    const expiry = new Date(expiresAt);
    const diffTime = expiry - now;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  if (loading) {
    return <Spinner fullScreen={true} message="Loading invitations..." size="large" />;
  }

  return (
    <DashboardLayout>
      <div className="invitation-management-container">
        {/* Header */}
        <div className="page-header">
          <div className="header-content">
            <h1>Invitation Management</h1>
            <p>Send invitations to brokers and suppliers to join the platform</p>
          </div>
          
          <div className="header-actions">
            <button 
              className="btn-primary"
              onClick={() => setShowBrokerModal(true)}
            >
              <i className="fas fa-user-plus"></i>
              Invite Broker
            </button>
            <button 
              className="btn-secondary"
              onClick={() => setShowSupplierModal(true)}
            >
              <i className="fas fa-industry"></i>
              Invite Supplier
            </button>
          </div>
        </div>

        {/* Statistics Cards */}
        <div className="stats-grid">
          {stats.byStatus?.map(stat => (
            <div key={stat._id} className={`stat-card ${stat._id.toLowerCase()}`}>
              <div className="stat-icon">
                <i className={`fas fa-${
                  stat._id === 'Pending' ? 'clock' :
                  stat._id === 'Accepted' ? 'check-circle' :
                  stat._id === 'Expired' ? 'times-circle' :
                  'ban'
                }`}></i>
              </div>
              <div className="stat-content">
                <h3>{stat.count}</h3>
                <p>{stat._id} Invitations</p>
              </div>
            </div>
          ))}
        </div>

        {/* Filters */}
        <div className="filters-section">
          <div className="filters-row">
            <div className="filter-group">
              <label>Status:</label>
              <select 
                value={filters.status} 
                onChange={(e) => setFilters({...filters, status: e.target.value})}
              >
                <option value="all">All Status</option>
                <option value="Pending">Pending</option>
                <option value="Accepted">Accepted</option>
                <option value="Expired">Expired</option>
                <option value="Revoked">Revoked</option>
              </select>
            </div>

            <div className="filter-group">
              <label>User Type:</label>
              <select 
                value={filters.userType} 
                onChange={(e) => setFilters({...filters, userType: e.target.value})}
              >
                <option value="all">All Types</option>
                <option value="Broker">Broker</option>
                <option value="Supplier">Supplier</option>
              </select>
            </div>

            <div className="filter-group">
              <label>Search:</label>
              <input
                type="text"
                placeholder="Search by email or name..."
                value={filters.search}
                onChange={(e) => setFilters({...filters, search: e.target.value})}
              />
            </div>
          </div>
        </div>

        {/* Invitations Table */}
        <div className="table-container">
          <table className="invitations-table">
            <thead>
              <tr>
                <th>Invitee</th>
                <th>Type</th>
                <th>Status</th>
                <th>Sent Date</th>
                <th>Expires</th>
                <th>Invited By</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {invitations.map(invitation => (
                <tr key={invitation._id}>
                  <td>
                    <div className="invitee-info">
                      <div className="invitee-name">
                        {invitation.inviteeDetails.firstName && invitation.inviteeDetails.lastName ? 
                          `${invitation.inviteeDetails.firstName} ${invitation.inviteeDetails.lastName}` :
                          'Name not provided'
                        }
                      </div>
                      <div className="invitee-email">{invitation.email}</div>
                      {invitation.inviteeDetails.companyName && (
                        <div className="invitee-company">{invitation.inviteeDetails.companyName}</div>
                      )}
                    </div>
                  </td>
                  <td>
                    <span className={`type-badge ${invitation.userType.toLowerCase()}`}>
                      {invitation.userType}
                    </span>
                  </td>
                  <td>
                    <span className={getStatusBadgeClass(invitation.status)}>
                      {invitation.status}
                    </span>
                  </td>
                  <td>{formatDate(invitation.createdAt)}</td>
                  <td>
                    <div className="expiry-info">
                      <div>{formatDate(invitation.expiresAt)}</div>
                      {invitation.status === 'Pending' && (
                        <div className={`days-remaining ${getDaysRemaining(invitation.expiresAt) <= 1 ? 'urgent' : ''}`}>
                          {getDaysRemaining(invitation.expiresAt)} days left
                        </div>
                      )}
                    </div>
                  </td>
                  <td>
                    {invitation.invitedBy ? 
                      `${invitation.invitedBy.firstName} ${invitation.invitedBy.lastName}` :
                      'Unknown'
                    }
                  </td>
                  <td>
                    <div className="action-buttons">
                      {invitation.status === 'Pending' && (
                        <>
                          <button
                            className="action-btn resend"
                            onClick={() => handleResendInvitation(invitation._id)}
                            title="Send Reminder"
                          >
                            <i className="fas fa-paper-plane"></i>
                          </button>
                          <button
                            className="action-btn revoke"
                            onClick={() => handleRevokeInvitation(invitation._id)}
                            title="Revoke Invitation"
                          >
                            <i className="fas fa-ban"></i>
                          </button>
                        </>
                      )}
                      {invitation.status === 'Accepted' && invitation.registeredUserId && (
                        <button
                          className="action-btn view"
                          onClick={() => navigate(`/admin/users/${invitation.registeredUserId._id}`)}
                          title="View User"
                        >
                          <i className="fas fa-eye"></i>
                        </button>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>

          {invitations.length === 0 && (
            <div className="empty-state">
              <i className="fas fa-envelope"></i>
              <h3>No invitations found</h3>
              <p>Start by sending invitations to brokers and suppliers</p>
            </div>
          )}
        </div>

        {/* Pagination */}
        {pagination.totalPages > 1 && (
          <div className="pagination">
            <button 
              disabled={!pagination.hasPrevPage}
              onClick={() => setFilters({...filters, page: pagination.currentPage - 1})}
            >
              Previous
            </button>
            <span>Page {pagination.currentPage} of {pagination.totalPages}</span>
            <button 
              disabled={!pagination.hasNextPage}
              onClick={() => setFilters({...filters, page: pagination.currentPage + 1})}
            >
              Next
            </button>
          </div>
        )}
      </div>

      {/* Broker Invitation Modal */}
      {showBrokerModal && (
        <BrokerInvitationModal
          onClose={() => setShowBrokerModal(false)}
          onSubmit={handleSendBrokerInvitation}
        />
      )}

      {/* Supplier Invitation Modal */}
      {showSupplierModal && (
        <SupplierInvitationModal
          onClose={() => setShowSupplierModal(false)}
          onSubmit={handleSendSupplierInvitation}
        />
      )}
    </DashboardLayout>
  );
};

// Broker Invitation Modal Component
const BrokerInvitationModal = ({ onClose, onSubmit }) => {
  const [formData, setFormData] = useState({
    email: '',
    firstName: '',
    lastName: '',
    phone: '',
    companyName: '',
    notes: ''
  });
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    try {
      await onSubmit(formData);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      isOpen={true}
      onClose={onClose}
      title="Invite Broker"
      subtitle="Send an invitation to a new broker to join the platform"
      size="medium"
      className="invitation-modal"
      footer={
        <ModalFooter
          leftContent={
            <Button variant="secondary" onClick={onClose}>
              Cancel
            </Button>
          }
          rightContent={
            <Button
              variant="primary"
              loading={loading}
              onClick={handleSubmit}
            >
              Send Invitation
            </Button>
          }
        />
      }
    >
      <FormSection title="Contact Information" icon="fas fa-user">
          <FormGroup label="Email Address" required>
            <input
              type="email"
              required
              value={formData.email}
              onChange={(e) => setFormData({...formData, email: e.target.value})}
              placeholder="<EMAIL>"
            />
          </FormGroup>

          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>
            <FormGroup label="First Name">
              <input
                type="text"
                value={formData.firstName}
                onChange={(e) => setFormData({...formData, firstName: e.target.value})}
                placeholder="John"
              />
            </FormGroup>
            <FormGroup label="Last Name">
              <input
                type="text"
                value={formData.lastName}
                onChange={(e) => setFormData({...formData, lastName: e.target.value})}
                placeholder="Doe"
              />
            </FormGroup>
          </div>

          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>
            <FormGroup label="Phone Number">
              <input
                type="tel"
                value={formData.phone}
                onChange={(e) => setFormData({...formData, phone: e.target.value})}
                placeholder="+33 1 23 45 67 89"
              />
            </FormGroup>
            <FormGroup label="Company Name">
              <input
                type="text"
                value={formData.companyName}
                onChange={(e) => setFormData({...formData, companyName: e.target.value})}
                placeholder="Energy Broker Ltd"
              />
            </FormGroup>
          </div>
        </FormSection>

        <FormSection title="Additional Information" icon="fas fa-sticky-note">
          <FormGroup label="Notes">
            <textarea
              rows="3"
              value={formData.notes}
              onChange={(e) => setFormData({...formData, notes: e.target.value})}
              placeholder="Additional notes about this broker..."
            />
          </FormGroup>
        </FormSection>
    </Modal>
  );
};

// Supplier Invitation Modal Component
const SupplierInvitationModal = ({ onClose, onSubmit }) => {
  const [formData, setFormData] = useState({
    email: '',
    firstName: '',
    lastName: '',
    phone: '',
    companyName: '',
    apiEndpoint: '',
    hasApiIntegration: false,
    notes: ''
  });
  const [tariffFile, setTariffFile] = useState(null);
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      const submitData = new FormData();

      // Append form fields
      Object.keys(formData).forEach(key => {
        submitData.append(key, formData[key]);
      });

      // Append file if selected
      if (tariffFile) {
        submitData.append('tariffFile', tariffFile);
      }

      await onSubmit(submitData);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      isOpen={true}
      onClose={onClose}
      title="Invite Supplier"
      subtitle="Send an invitation to a new energy supplier to join the platform"
      size="large"
      className="invitation-modal"
      footer={
        <ModalFooter
          leftContent={
            <Button variant="secondary" onClick={onClose}>
              Cancel
            </Button>
          }
          rightContent={
            <Button
              variant="primary"
              loading={loading}
              onClick={handleSubmit}
            >
              Send Invitation
            </Button>
          }
        />
      }
    >
      <FormSection title="Company Information" icon="fas fa-building">
        <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>
          <FormGroup label="Email Address" required>
            <input
              type="email"
              required
              value={formData.email}
              onChange={(e) => setFormData({...formData, email: e.target.value})}
              placeholder="<EMAIL>"
            />
          </FormGroup>
          <FormGroup label="Company Name" required>
            <input
              type="text"
              required
              value={formData.companyName}
              onChange={(e) => setFormData({...formData, companyName: e.target.value})}
              placeholder="Energy Supply Corp"
            />
          </FormGroup>
        </div>
      </FormSection>

      <FormSection title="Contact Information" icon="fas fa-user">
        <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: '16px' }}>
          <FormGroup label="First Name">
            <input
              type="text"
              value={formData.firstName}
              onChange={(e) => setFormData({...formData, firstName: e.target.value})}
              placeholder="Jane"
            />
          </FormGroup>
          <FormGroup label="Last Name">
            <input
              type="text"
              value={formData.lastName}
              onChange={(e) => setFormData({...formData, lastName: e.target.value})}
              placeholder="Smith"
            />
          </FormGroup>
          <FormGroup label="Phone Number">
            <input
              type="tel"
              value={formData.phone}
              onChange={(e) => setFormData({...formData, phone: e.target.value})}
              placeholder="+33 1 23 45 67 89"
            />
          </FormGroup>
        </div>
      </FormSection>

      <FormSection title="Technical Integration" icon="fas fa-cogs">
        <FormGroup>
          <label style={{ display: 'flex', alignItems: 'center', gap: '8px', cursor: 'pointer' }}>
            <input
              type="checkbox"
              checked={formData.hasApiIntegration}
              onChange={(e) => setFormData({...formData, hasApiIntegration: e.target.checked})}
              style={{ margin: 0 }}
            />
            <span>Has API Integration</span>
          </label>
        </FormGroup>

        {formData.hasApiIntegration && (
          <FormGroup label="API Endpoint">
            <input
              type="url"
              value={formData.apiEndpoint}
              onChange={(e) => setFormData({...formData, apiEndpoint: e.target.value})}
              placeholder="https://api.supplier.com/tariffs"
            />
          </FormGroup>
        )}
      </FormSection>

      <FormSection title="Initial Data" icon="fas fa-file-upload">
        <FormGroup label="Tariff File (Optional)">
          <FileUpload
            accept=".csv,.xlsx,.xls"
            onFileSelect={setTariffFile}
          >
            <div className="file-upload-icon">📊</div>
            <div className="file-upload-text">
              <strong>Upload tariff file</strong> or drag and drop
              <br />
              <small>Supported formats: CSV, Excel (.xlsx, .xls)</small>
              {tariffFile && (
                <div style={{ marginTop: '8px', color: '#10b981', fontWeight: '500' }}>
                  ✓ {tariffFile.name}
                </div>
              )}
            </div>
          </FileUpload>
        </FormGroup>
      </FormSection>

      <FormSection title="Additional Information" icon="fas fa-sticky-note">
        <FormGroup label="Notes">
          <textarea
            rows="3"
            value={formData.notes}
            onChange={(e) => setFormData({...formData, notes: e.target.value})}
            placeholder="Additional notes about this supplier..."
          />
        </FormGroup>
      </FormSection>
    </Modal>
  );
};

export default InvitationManagement;
