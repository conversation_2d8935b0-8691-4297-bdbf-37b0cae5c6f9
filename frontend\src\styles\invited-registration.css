.invited-registration-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.registration-card {
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  width: 100%;
  max-width: 700px;
  overflow: hidden;
  border: 1px solid #e0e0e0;
}

.card-header {
  background: linear-gradient(135deg, #000000 0%, #333333 100%);
  color: #ffffff;
  padding: 50px 40px;
  text-align: center;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.logo-row {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20px;
}

.registration-title {
  text-align: center;
  width: 100%;
}

.invitation-text {
  text-align: center;
  width: 100%;
}

.registration-logo {
  width: 70px;
  height: 70px;
  border-radius: 8px;
  object-fit: cover;
}

.app-title {
  font-family: 'Arial', sans-serif;
  font-weight: bold;
  font-size: 1.4rem;
  line-height: 1.2;
  text-align: left;
  color: #ffffff;
  letter-spacing: 1px;
  white-space: nowrap;
}

.card-header h1 {
  margin: 0 0 15px 0;
  font-size: 2rem;
  font-weight: 700;
  color: #ffffff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  line-height: 1.2;
}

.card-header p {
  margin: 0;
  font-size: 1.1rem;
  color: #e0e0e0;
  font-weight: 300;
  line-height: 1.3;
}

.invitation-info {
  padding: 40px;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-bottom: 1px solid #e0e0e0;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px 20px;
  background: #ffffff;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border-left: 4px solid #000000;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-item .label {
  font-weight: 600;
  color: #333333;
  font-size: 15px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.info-item .value {
  font-weight: 600;
  color: #000000;
  text-align: right;
  font-size: 15px;
}

.info-item .value.urgent {
  color: #dc3545;
  font-weight: 700;
}

.registration-form {
  padding: 40px;
  background: #ffffff;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 25px;
  margin-bottom: 25px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 25px;
}

.form-group label {
  font-weight: 600;
  color: #333333;
  font-size: 15px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.form-group input {
  padding: 15px 20px;
  border: 2px solid #e0e0e0;
  border-radius: 10px;
  font-size: 16px;
  transition: all 0.3s ease;
  background: #ffffff;
  color: #000000;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.form-group input:focus {
  outline: none;
  border-color: #000000;
  box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.form-group input.error {
  border-color: #dc3545;
  box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
}

.error-message {
  color: #dc3545;
  font-size: 13px;
  margin-top: 5px;
  font-weight: 500;
}

/* Password Strength Indicator */
.password-strength-container {
  margin-top: 15px;
  padding: 15px;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 8px;
  border: 1px solid #e0e0e0;
}

.password-strength-header {
  font-size: 0.9rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 10px;
}

.password-strength-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.password-strength-list li {
  font-size: 0.85rem;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 0;
  transition: all 0.2s ease;
}

.password-strength-list li.valid {
  color: #28a745;
  font-weight: 500;
}

.password-strength-list li.invalid {
  color: #6c757d;
}

.password-strength-list li.valid::before {
  content: '';
  width: 4px;
  height: 4px;
  background: #28a745;
  border-radius: 50%;
  margin-right: 4px;
}

.password-strength-list li.invalid::before {
  content: '';
  width: 4px;
  height: 4px;
  background: #6c757d;
  border-radius: 50%;
  margin-right: 4px;
}

.form-actions {
  margin-top: 40px;
}

.btn-primary {
  width: 100%;
  padding: 18px 25px;
  background: linear-gradient(135deg, #000000 0%, #333333 100%);
  color: #ffffff;
  border: none;
  border-radius: 12px;
  font-size: 17px;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  text-transform: uppercase;
  letter-spacing: 1px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.btn-primary:hover:not(:disabled) {
  background: linear-gradient(135deg, #333333 0%, #000000 100%);
  transform: translateY(-3px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.3);
}

.btn-primary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.registration-note {
  padding: 30px 40px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-top: 1px solid #e0e0e0;
  text-align: center;
}

.registration-note p {
  margin: 0;
  color: #333333;
  font-size: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  font-weight: 500;
  line-height: 1.6;
}

.registration-note i {
  font-size: 18px;
  color: #000000;
}

/* Error Page Styles */
.error-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 20px;
  background: #ffffff;
}

.error-page h2 {
  color: #dc3545;
  margin-bottom: 16px;
}

.error-page p {
  color: #000000;
  margin-bottom: 24px;
  max-width: 400px;
}

.error-page button {
  padding: 12px 24px;
  background: #000000;
  color: #ffffff;
  border: 2px solid #000000;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.error-page button:hover {
  background: #ffffff;
  color: #000000;
}

/* Password Strength Indicator */
.password-strength {
  margin-top: 8px;
  display: flex;
  gap: 4px;
}

.strength-bar {
  height: 4px;
  flex: 1;
  background: #e0e0e0;
  border-radius: 2px;
  transition: background 0.2s ease;
}

.strength-bar.active {
  background: #000000;
}

.strength-bar.medium {
  background: #666666;
}

.strength-bar.weak {
  background: #999999;
}

.strength-text {
  font-size: 12px;
  margin-top: 4px;
  color: #000000;
}

/* Loading States */
.form-loading {
  position: relative;
}

.form-loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.registration-card {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
}

.logo-row {
  animation: pulse 3s infinite;
}

/* Responsive Design */
@media (max-width: 768px) {
  .invited-registration-container {
    padding: 15px;
  }

  .registration-card {
    margin: 0;
    max-width: 100%;
  }

  .card-header {
    padding: 40px 25px;
  }

  .logo-row {
    flex-direction: row;
    gap: 15px;
    justify-content: center;
    align-items: center;
  }

  .app-title {
    text-align: left;
    font-size: 1.2rem;
  }

  .card-header h1 {
    font-size: 1.7rem;
    line-height: 1.2;
  }

  .invitation-info,
  .registration-form {
    padding: 30px 25px;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: 0;
  }

  .info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
    padding: 12px 15px;
  }

  .info-item .value {
    text-align: left;
  }

  .registration-note {
    padding: 25px 25px;
  }

  .registration-note p {
    font-size: 14px;
    flex-direction: column;
    gap: 8px;
  }
}

@media (max-width: 480px) {
  .card-header {
    padding: 30px 20px;
  }

  .card-header h1 {
    font-size: 1.5rem;
    line-height: 1.2;
  }

  .card-header p {
    font-size: 0.95rem;
    line-height: 1.3;
  }

  .registration-logo {
    width: 50px;
    height: 50px;
  }

  .app-title {
    font-size: 1rem;
  }

  .invitation-info,
  .registration-form {
    padding: 25px 20px;
  }

  .form-group input {
    padding: 12px 16px;
    font-size: 15px;
  }

  .btn-primary {
    padding: 15px 20px;
    font-size: 15px;
  }

  .info-item {
    padding: 10px 12px;
  }
}

/* Placeholder styles */
.form-group input::placeholder {
  color: #666666;
  opacity: 1;
}

.form-group input:focus::placeholder {
  color: #999999;
}
