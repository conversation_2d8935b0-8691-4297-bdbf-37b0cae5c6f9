.invited-registration-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.registration-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 600px;
  overflow: hidden;
}

.card-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 40px 30px;
  text-align: center;
  position: relative;
}

.invitation-badge {
  width: 80px;
  height: 80px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  font-size: 32px;
  backdrop-filter: blur(10px);
}

.card-header h1 {
  margin: 0 0 10px 0;
  font-size: 2rem;
  font-weight: 300;
}

.card-header p {
  margin: 0;
  opacity: 0.9;
  font-size: 1.1rem;
}

.invitation-info {
  padding: 30px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding: 8px 0;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-item .label {
  font-weight: 500;
  color: #6c757d;
  font-size: 14px;
}

.info-item .value {
  font-weight: 600;
  color: #2c3e50;
  text-align: right;
}

.info-item .value.urgent {
  color: #dc3545;
}

.registration-form {
  padding: 30px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 20px;
}

.form-group label {
  font-weight: 500;
  color: #495057;
  font-size: 14px;
}

.form-group input {
  padding: 12px 16px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 16px;
  transition: all 0.2s ease;
  background: #fff;
}

.form-group input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group input.error {
  border-color: #dc3545;
  box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
}

.error-message {
  color: #dc3545;
  font-size: 12px;
  margin-top: 4px;
}

.form-actions {
  margin-top: 30px;
}

.btn-primary {
  width: 100%;
  padding: 14px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.btn-primary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.registration-note {
  padding: 20px 30px;
  background: #e3f2fd;
  border-top: 1px solid #e9ecef;
  text-align: center;
}

.registration-note p {
  margin: 0;
  color: #1976d2;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.registration-note i {
  font-size: 16px;
}

/* Error Page Styles */
.error-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 20px;
  background: #f8f9fa;
}

.error-page h2 {
  color: #dc3545;
  margin-bottom: 16px;
}

.error-page p {
  color: #6c757d;
  margin-bottom: 24px;
  max-width: 400px;
}

.error-page button {
  padding: 12px 24px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: background 0.2s ease;
}

.error-page button:hover {
  background: #0056b3;
}

/* Password Strength Indicator */
.password-strength {
  margin-top: 8px;
  display: flex;
  gap: 4px;
}

.strength-bar {
  height: 4px;
  flex: 1;
  background: #e9ecef;
  border-radius: 2px;
  transition: background 0.2s ease;
}

.strength-bar.active {
  background: #28a745;
}

.strength-bar.medium {
  background: #ffc107;
}

.strength-bar.weak {
  background: #dc3545;
}

.strength-text {
  font-size: 12px;
  margin-top: 4px;
  color: #6c757d;
}

/* Loading States */
.form-loading {
  position: relative;
}

.form-loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.registration-card {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.invitation-badge {
  animation: pulse 2s infinite;
}

/* Responsive Design */
@media (max-width: 768px) {
  .invited-registration-container {
    padding: 10px;
  }

  .registration-card {
    margin: 10px;
  }

  .card-header {
    padding: 30px 20px;
  }

  .card-header h1 {
    font-size: 1.5rem;
  }

  .invitation-info,
  .registration-form {
    padding: 20px;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: 0;
  }

  .info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .info-item .value {
    text-align: left;
  }

  .registration-note {
    padding: 15px 20px;
  }

  .registration-note p {
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .card-header h1 {
    font-size: 1.3rem;
  }

  .card-header p {
    font-size: 1rem;
  }

  .invitation-badge {
    width: 60px;
    height: 60px;
    font-size: 24px;
  }

  .form-group input {
    padding: 10px 14px;
    font-size: 14px;
  }

  .btn-primary {
    padding: 12px 16px;
    font-size: 14px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .registration-card {
    background: #2c3e50;
    color: #ecf0f1;
  }

  .invitation-info {
    background: #34495e;
    border-bottom-color: #4a5f7a;
  }

  .info-item .label {
    color: #bdc3c7;
  }

  .info-item .value {
    color: #ecf0f1;
  }

  .form-group input {
    background: #34495e;
    border-color: #4a5f7a;
    color: #ecf0f1;
  }

  .form-group input:focus {
    border-color: #667eea;
  }

  .registration-note {
    background: #2980b9;
    border-top-color: #3498db;
  }

  .registration-note p {
    color: #ecf0f1;
  }
}
