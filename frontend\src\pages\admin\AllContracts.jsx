import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import DashboardLayout from '../../components/DashboardLayout';
import Spinner from '../../components/Spinner';
import { showSuccessMessage, showErrorMessage } from '../../utils/toastNotifications';
import logger from '../../utils/logger';
import '../../styles/admin-management.css';

const AllContracts = () => {
  const [loading, setLoading] = useState(true);
  const [contracts, setContracts] = useState([]);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalContracts: 0,
    hasNextPage: false,
    hasPrevPage: false
  });
  const [filters, setFilters] = useState({
    status: 'all',
    search: ''
  });
  const [selectedContract, setSelectedContract] = useState(null);
  const [showContractDetails, setShowContractDetails] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    fetchContracts();
  }, [filters, pagination.currentPage]);

  const fetchContracts = async () => {
    try {
      setLoading(true);
      logger.info('Fetching contracts with filters:', filters);

      // For now, we'll create mock data since contracts API might not be fully implemented
      // TODO: Replace with actual API call when contracts endpoint is ready
      setTimeout(() => {
        const mockContracts = [
          {
            _id: '1',
            contractNumber: 'CNT-2024-001',
            clientName: 'John Doe',
            clientEmail: '<EMAIL>',
            supplierName: 'Green Energy Co.',
            status: 'Active',
            startDate: new Date('2024-01-15'),
            endDate: new Date('2025-01-15'),
            monthlyAmount: 150.00,
            createdAt: new Date('2024-01-10')
          },
          {
            _id: '2',
            contractNumber: 'CNT-2024-002',
            clientName: 'Jane Smith',
            clientEmail: '<EMAIL>',
            supplierName: 'Solar Power Ltd.',
            status: 'Pending',
            startDate: new Date('2024-02-01'),
            endDate: new Date('2025-02-01'),
            monthlyAmount: 175.50,
            createdAt: new Date('2024-01-25')
          },
          {
            _id: '3',
            contractNumber: 'CNT-2024-003',
            clientName: 'Mike Johnson',
            clientEmail: '<EMAIL>',
            supplierName: 'Wind Energy Inc.',
            status: 'Expired',
            startDate: new Date('2023-03-01'),
            endDate: new Date('2024-03-01'),
            monthlyAmount: 200.00,
            createdAt: new Date('2023-02-20')
          }
        ];

        // Apply filters
        let filteredContracts = mockContracts;
        
        if (filters.status !== 'all') {
          filteredContracts = filteredContracts.filter(contract => 
            contract.status.toLowerCase() === filters.status.toLowerCase()
          );
        }

        if (filters.search) {
          filteredContracts = filteredContracts.filter(contract =>
            contract.clientName.toLowerCase().includes(filters.search.toLowerCase()) ||
            contract.clientEmail.toLowerCase().includes(filters.search.toLowerCase()) ||
            contract.contractNumber.toLowerCase().includes(filters.search.toLowerCase()) ||
            contract.supplierName.toLowerCase().includes(filters.search.toLowerCase())
          );
        }

        setContracts(filteredContracts);
        setPagination({
          currentPage: 1,
          totalPages: 1,
          totalContracts: filteredContracts.length,
          hasNextPage: false,
          hasPrevPage: false
        });
        setLoading(false);
      }, 1000);

    } catch (error) {
      logger.error('Error fetching contracts:', error);
      showErrorMessage('CONTRACTS_LOAD_FAILED', 'Failed to load contracts');
      setLoading(false);
    }
  };

  const handleFilterChange = (filterName, value) => {
    setFilters(prev => ({
      ...prev,
      [filterName]: value
    }));
    setPagination(prev => ({ ...prev, currentPage: 1 }));
  };

  const handleStatusUpdate = async (contractId, newStatus) => {
    try {
      // TODO: Replace with actual API call when contracts endpoint is ready
      // await api.patch(`/api/admin/contracts/${contractId}/status`, { status: newStatus });

      // For now, update the local state
      setContracts(prev => prev.map(contract =>
        contract._id === contractId
          ? { ...contract, status: newStatus }
          : contract
      ));

      showSuccessMessage('CONTRACT_STATUS_UPDATED', 'Contract status updated successfully');
    } catch (error) {
      logger.error('Error updating contract status:', error);
      showErrorMessage('STATUS_UPDATE_FAILED', 'Failed to update contract status');
    }
  };

  const handleViewContract = (contract) => {
    try {
      setSelectedContract(contract);
      setShowContractDetails(true);
    } catch (error) {
      logger.error('Error viewing contract details:', error);
      showErrorMessage('CONTRACT_DETAILS_FAILED', 'Failed to load contract details');
    }
  };

  const handleDownloadContract = (contract) => {
    try {
      // TODO: Implement actual contract download functionality
      // For now, show a success message
      showSuccessMessage('CONTRACT_DOWNLOAD', `Downloading contract ${contract.contractNumber}...`);

      // In a real implementation, this would trigger a file download
      // const downloadUrl = `/api/admin/contracts/${contract._id}/download`;
      // window.open(downloadUrl, '_blank');
    } catch (error) {
      logger.error('Error downloading contract:', error);
      showErrorMessage('DOWNLOAD_FAILED', 'Failed to download contract');
    }
  };

  const handleEditContract = (contract) => {
    try {
      // Navigate to edit contract page or open edit modal
      navigate(`/admin/contracts/${contract._id}/edit`);
    } catch (error) {
      logger.error('Error navigating to edit contract:', error);
      showErrorMessage('NAVIGATION_FAILED', 'Failed to open contract edit page');
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'EUR'
    }).format(amount);
  };

  const getStatusBadgeClass = (status) => {
    switch (status?.toLowerCase()) {
      case 'active': return 'status-badge active';
      case 'pending': return 'status-badge pending';
      case 'expired': return 'status-badge inactive';
      case 'cancelled': return 'status-badge suspended';
      default: return 'status-badge';
    }
  };

  if (loading) {
    return <Spinner fullScreen={true} message="Loading contracts..." size="large" />;
  }

  return (
    <DashboardLayout>
      <div className="admin-management-container">
        {/* Header */}
        <div className="management-header">
          <div className="header-content">
            <h1>All Contracts</h1>
            <p>View and manage all energy contracts in the system</p>
          </div>
          <div className="header-stats">
            <div className="stat-item">
              <span className="stat-value">{pagination.totalContracts}</span>
              <span className="stat-label">Total Contracts</span>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="management-filters">
          <div className="filter-group">
            <label>Status:</label>
            <select 
              value={filters.status} 
              onChange={(e) => handleFilterChange('status', e.target.value)}
            >
              <option value="all">All Status</option>
              <option value="Active">Active</option>
              <option value="Pending">Pending</option>
              <option value="Expired">Expired</option>
              <option value="Cancelled">Cancelled</option>
            </select>
          </div>

          <div className="filter-group search-group">
            <label>Search:</label>
            <input
              type="text"
              placeholder="Search by contract number, client, or supplier..."
              value={filters.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
            />
          </div>
        </div>

        {/* Contracts Table */}
        <div className="management-table-container">
          <table className="management-table contracts-table">
            <thead>
              <tr>
                <th>Contract</th>
                <th>Client</th>
                <th>Supplier</th>
                <th>Status</th>
                <th>Actions</th>
                <th>Manage</th>
              </tr>
            </thead>
            <tbody>
              {contracts.map(contract => (
                <tr key={contract._id}>
                  <td>
                    <div className="contract-info">
                      <div className="contract-icon">
                        <i className="fas fa-file-contract"></i>
                      </div>
                      <div className="contract-details">
                        <div className="contract-number">
                          {contract.contractNumber}
                        </div>
                        <div className="contract-amount">
                          {formatCurrency(contract.monthlyAmount)}/month
                        </div>
                      </div>
                    </div>
                  </td>
                  <td>
                    <div className="user-details">
                      <div className="user-name">{contract.clientName}</div>
                      <div className="user-email">{contract.clientEmail}</div>
                    </div>
                  </td>
                  <td>
                    <span className="supplier-name">
                      {contract.supplierName}
                    </span>
                  </td>
                  <td>
                    <span className={getStatusBadgeClass(contract.status)}>
                      {contract.status}
                    </span>
                  </td>
                  <td>
                    <div className="action-buttons">
                      <button
                        className="action-btn view"
                        onClick={() => handleViewContract(contract)}
                        title="View Contract"
                      >
                        <i className="fas fa-eye"></i>
                      </button>
                      <button
                        className="action-btn download"
                        onClick={() => handleDownloadContract(contract)}
                        title="Download Contract"
                      >
                        <i className="fas fa-download"></i>
                      </button>
                      <button
                        className="action-btn edit"
                        onClick={() => handleEditContract(contract)}
                        title="Edit Contract"
                      >
                        <i className="fas fa-edit"></i>
                      </button>
                    </div>
                  </td>
                  <td>
                    <select
                      value={contract.status || 'Active'}
                      onChange={(e) => handleStatusUpdate(contract._id, e.target.value)}
                      className="status-select"
                    >
                      <option value="Active">Active</option>
                      <option value="Pending">Pending</option>
                      <option value="Expired">Expired</option>
                      <option value="Cancelled">Cancelled</option>
                    </select>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>

          {contracts.length === 0 && (
            <div className="empty-state">
              <i className="fas fa-file-contract"></i>
              <h3>No contracts found</h3>
              <p>No contracts match your current filters.</p>
            </div>
          )}
        </div>

        {/* Pagination */}
        <div className="management-pagination">
          <button 
            disabled={!pagination.hasPrevPage}
            onClick={() => handlePageChange(pagination.currentPage - 1)}
            className="pagination-btn"
          >
            <i className="fas fa-chevron-left"></i>
            Previous
          </button>
          
          <span className="pagination-info">
            Page {pagination.currentPage} of {pagination.totalPages}
          </span>
          
          <button 
            disabled={!pagination.hasNextPage}
            onClick={() => handlePageChange(pagination.currentPage + 1)}
            className="pagination-btn"
          >
            Next
            <i className="fas fa-chevron-right"></i>
          </button>
        </div>

        {/* Contract Details Modal */}
        {showContractDetails && selectedContract && (
          <div className="modal-overlay" onClick={() => setShowContractDetails(false)}>
            <div className="modal-content" onClick={(e) => e.stopPropagation()}>
              <div className="modal-header">
                <h3>Contract Details</h3>
                <button
                  className="modal-close"
                  onClick={() => setShowContractDetails(false)}
                >
                  <i className="fas fa-times"></i>
                </button>
              </div>
              <div className="modal-body">
                <div className="contract-details">
                  <div className="detail-group">
                    <label>Contract Number:</label>
                    <span>{selectedContract.contractNumber}</span>
                  </div>
                  <div className="detail-group">
                    <label>Client:</label>
                    <span>{selectedContract.clientName}</span>
                  </div>
                  <div className="detail-group">
                    <label>Client Email:</label>
                    <span>{selectedContract.clientEmail}</span>
                  </div>
                  <div className="detail-group">
                    <label>Supplier:</label>
                    <span>{selectedContract.supplierName}</span>
                  </div>
                  <div className="detail-group">
                    <label>Status:</label>
                    <span className={getStatusBadgeClass(selectedContract.status)}>
                      {selectedContract.status}
                    </span>
                  </div>
                  <div className="detail-group">
                    <label>Monthly Amount:</label>
                    <span className="amount">
                      {formatCurrency(selectedContract.monthlyAmount)}
                    </span>
                  </div>
                  <div className="detail-group">
                    <label>Start Date:</label>
                    <span>{formatDate(selectedContract.startDate)}</span>
                  </div>
                  <div className="detail-group">
                    <label>End Date:</label>
                    <span>{formatDate(selectedContract.endDate)}</span>
                  </div>
                  <div className="detail-group">
                    <label>Created:</label>
                    <span>{formatDate(selectedContract.createdAt)}</span>
                  </div>
                </div>
              </div>
              <div className="modal-footer">
                <button
                  className="btn btn-secondary"
                  onClick={() => setShowContractDetails(false)}
                >
                  Close
                </button>
                <button
                  className="btn btn-primary"
                  onClick={() => handleDownloadContract(selectedContract)}
                >
                  <i className="fas fa-download"></i>
                  Download Contract
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </DashboardLayout>
  );
};

export default AllContracts;
