const express = require('express');
const router = express.Router();
const Invitation = require('../../models/Invitation');
const User = require('../../models/User');
const UserActivity = require('../../models/UserActivity');
const emailService = require('../../services/emailService');
const logger = require('../../utils/logger');
const { authMiddleware } = require('../../middleware/auth');
const multer = require('multer');
const path = require('path');

// Configure multer for tariff file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, 'uploads/tariffs/');
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, 'tariff-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({ 
  storage: storage,
  fileFilter: function (req, file, cb) {
    const allowedTypes = ['.csv', '.xlsx', '.xls'];
    const ext = path.extname(file.originalname).toLowerCase();
    if (allowedTypes.includes(ext)) {
      cb(null, true);
    } else {
      cb(new Error('Invalid file type. Only CSV and Excel files are allowed.'));
    }
  },
  limits: {
    fileSize: 5 * 1024 * 1024 // 5MB limit
  }
});

// Apply auth middleware to all admin routes
router.use(authMiddleware);

// Middleware to check if user is admin
const requireAdmin = async (req, res, next) => {
  try {
    const userEmail = req.user?.email;
    if (!userEmail) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    const user = await User.findOne({ email: userEmail });
    if (!user || user.userType !== 'Admin') {
      return res.status(403).json({
        success: false,
        message: 'Admin access required'
      });
    }

    req.adminUser = user;
    next();
  } catch (error) {
    logger.error('Admin middleware error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// Get all invitations with filtering and pagination
router.get('/', requireAdmin, async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      status,
      userType,
      search,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    const filter = {};
    
    if (status && status !== 'all') {
      filter.status = status;
    }
    
    if (userType && userType !== 'all') {
      filter.userType = userType;
    }
    
    if (search) {
      filter.$or = [
        { email: { $regex: search, $options: 'i' } },
        { 'inviteeDetails.firstName': { $regex: search, $options: 'i' } },
        { 'inviteeDetails.lastName': { $regex: search, $options: 'i' } },
        { 'inviteeDetails.companyName': { $regex: search, $options: 'i' } }
      ];
    }

    const skip = (parseInt(page) - 1) * parseInt(limit);
    const sortOptions = {};
    sortOptions[sortBy] = sortOrder === 'desc' ? -1 : 1;

    const invitations = await Invitation.find(filter)
      .populate('invitedBy', 'firstName lastName email')
      .populate('registeredUserId', 'firstName lastName email status')
      .sort(sortOptions)
      .skip(skip)
      .limit(parseInt(limit));

    const totalInvitations = await Invitation.countDocuments(filter);
    const totalPages = Math.ceil(totalInvitations / parseInt(limit));

    // Get invitation statistics
    const stats = await Invitation.getInvitationStats();

    res.json({
      success: true,
      data: {
        invitations,
        pagination: {
          currentPage: parseInt(page),
          totalPages,
          totalInvitations,
          hasNextPage: parseInt(page) < totalPages,
          hasPrevPage: parseInt(page) > 1
        },
        stats
      }
    });

  } catch (error) {
    logger.error('Error fetching invitations:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch invitations'
    });
  }
});

// Send broker invitation
router.post('/broker', requireAdmin, async (req, res) => {
  try {
    const {
      email,
      firstName,
      lastName,
      phone,
      companyName,
      notes
    } = req.body;

    // Validate required fields
    if (!email) {
      return res.status(400).json({
        success: false,
        message: 'Email is required'
      });
    }

    // Check if user already exists
    const existingUser = await User.findOne({ email: email.toLowerCase() });
    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: 'A user with this email already exists'
      });
    }

    // Check if there's already a pending invitation
    const existingInvitation = await Invitation.findOne({
      email: email.toLowerCase(),
      status: 'Pending',
      userType: 'Broker'
    });

    if (existingInvitation && !existingInvitation.isExpired()) {
      return res.status(400).json({
        success: false,
        message: 'A pending invitation already exists for this email'
      });
    }

    // Create new invitation
    const invitation = new Invitation({
      email: email.toLowerCase(),
      userType: 'Broker',
      invitedBy: req.adminUser._id,
      inviteeDetails: {
        firstName,
        lastName,
        phone,
        companyName,
        notes
      },
      metadata: {
        ipAddress: req.ip,
        userAgent: req.get('User-Agent'),
        adminNotes: notes
      }
    });

    await invitation.save();

    // Send invitation email
    try {
      const emailResult = await emailService.sendInvitationEmail(invitation);
      await invitation.markEmailSent(emailResult.messageId);
      
      logger.info(`Broker invitation sent successfully to ${email} by admin ${req.adminUser.email}`);
    } catch (emailError) {
      logger.error('Failed to send invitation email:', emailError);
      // Don't fail the invitation creation if email fails
    }

    // Log activity
    await UserActivity.logActivity({
      userId: req.adminUser._id,
      activityType: 'Other',
      description: `Sent broker invitation to ${email}`,
      details: {
        invitationId: invitation._id,
        email,
        inviteeDetails: invitation.inviteeDetails
      },
      severity: 'Normal',
      isSystemGenerated: true
    });

    res.json({
      success: true,
      message: 'Broker invitation sent successfully',
      data: {
        invitationId: invitation._id,
        registrationUrl: invitation.getRegistrationUrl()
      }
    });

  } catch (error) {
    logger.error('Error sending broker invitation:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to send broker invitation'
    });
  }
});

// Send supplier invitation
router.post('/supplier', requireAdmin, upload.single('tariffFile'), async (req, res) => {
  try {
    const {
      email,
      firstName,
      lastName,
      phone,
      companyName,
      apiEndpoint,
      hasApiIntegration,
      notes
    } = req.body;

    // Validate required fields
    if (!email || !companyName) {
      return res.status(400).json({
        success: false,
        message: 'Email and company name are required'
      });
    }

    // Check if user already exists
    const existingUser = await User.findOne({ email: email.toLowerCase() });
    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: 'A user with this email already exists'
      });
    }

    // Check if there's already a pending invitation
    const existingInvitation = await Invitation.findOne({
      email: email.toLowerCase(),
      status: 'Pending',
      userType: 'Supplier'
    });

    if (existingInvitation && !existingInvitation.isExpired()) {
      return res.status(400).json({
        success: false,
        message: 'A pending invitation already exists for this email'
      });
    }

    // Prepare supplier-specific data
    const supplierSpecific = {
      hasApiIntegration: hasApiIntegration === 'true',
      apiEndpoint: hasApiIntegration === 'true' ? apiEndpoint : undefined
    };

    // Handle tariff file upload
    if (req.file) {
      supplierSpecific.initialTariffData = {
        fileName: req.file.originalname,
        filePath: req.file.path,
        fileSize: req.file.size,
        uploadedAt: new Date()
      };
    }

    // Create new invitation
    const invitation = new Invitation({
      email: email.toLowerCase(),
      userType: 'Supplier',
      invitedBy: req.adminUser._id,
      inviteeDetails: {
        firstName,
        lastName,
        phone,
        companyName,
        notes
      },
      supplierSpecific,
      metadata: {
        ipAddress: req.ip,
        userAgent: req.get('User-Agent'),
        adminNotes: notes
      }
    });

    await invitation.save();

    // Send invitation email
    try {
      const emailResult = await emailService.sendInvitationEmail(invitation);
      await invitation.markEmailSent(emailResult.messageId);
      
      logger.info(`Supplier invitation sent successfully to ${email} by admin ${req.adminUser.email}`);
    } catch (emailError) {
      logger.error('Failed to send invitation email:', emailError);
      // Don't fail the invitation creation if email fails
    }

    // Log activity
    await UserActivity.logActivity({
      userId: req.adminUser._id,
      activityType: 'Other',
      description: `Sent supplier invitation to ${companyName} (${email})`,
      details: {
        invitationId: invitation._id,
        email,
        companyName,
        hasApiIntegration: supplierSpecific.hasApiIntegration,
        hasTariffFile: !!req.file
      },
      severity: 'Normal',
      isSystemGenerated: true
    });

    res.json({
      success: true,
      message: 'Supplier invitation sent successfully',
      data: {
        invitationId: invitation._id,
        registrationUrl: invitation.getRegistrationUrl()
      }
    });

  } catch (error) {
    logger.error('Error sending supplier invitation:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to send supplier invitation'
    });
  }
});

// Get specific invitation details
router.get('/:invitationId', requireAdmin, async (req, res) => {
  try {
    const { invitationId } = req.params;

    const invitation = await Invitation.findById(invitationId)
      .populate('invitedBy', 'firstName lastName email')
      .populate('registeredUserId', 'firstName lastName email status verificationStatus');

    if (!invitation) {
      return res.status(404).json({
        success: false,
        message: 'Invitation not found'
      });
    }

    res.json({
      success: true,
      data: invitation
    });

  } catch (error) {
    logger.error('Error fetching invitation details:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch invitation details'
    });
  }
});

// Resend invitation email
router.post('/:invitationId/resend', requireAdmin, async (req, res) => {
  try {
    const { invitationId } = req.params;

    const invitation = await Invitation.findById(invitationId);

    if (!invitation) {
      return res.status(404).json({
        success: false,
        message: 'Invitation not found'
      });
    }

    if (invitation.status !== 'Pending') {
      return res.status(400).json({
        success: false,
        message: 'Can only resend pending invitations'
      });
    }

    if (invitation.isExpired()) {
      // Extend the invitation by 7 days
      await invitation.extend(7);
    }

    // Send reminder email
    try {
      const emailResult = await emailService.sendReminderEmail(invitation);
      await invitation.sendReminder();

      logger.info(`Invitation reminder sent to ${invitation.email} by admin ${req.adminUser.email}`);
    } catch (emailError) {
      logger.error('Failed to send reminder email:', emailError);
      return res.status(500).json({
        success: false,
        message: 'Failed to send reminder email'
      });
    }

    // Log activity
    await UserActivity.logActivity({
      userId: req.adminUser._id,
      activityType: 'Other',
      description: `Sent reminder for ${invitation.userType} invitation to ${invitation.email}`,
      details: {
        invitationId: invitation._id,
        reminderNumber: invitation.remindersSent
      },
      severity: 'Normal',
      isSystemGenerated: true
    });

    res.json({
      success: true,
      message: 'Invitation reminder sent successfully'
    });

  } catch (error) {
    logger.error('Error resending invitation:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to resend invitation'
    });
  }
});

// Revoke invitation
router.delete('/:invitationId', requireAdmin, async (req, res) => {
  try {
    const { invitationId } = req.params;

    const invitation = await Invitation.findById(invitationId);

    if (!invitation) {
      return res.status(404).json({
        success: false,
        message: 'Invitation not found'
      });
    }

    if (invitation.status !== 'Pending') {
      return res.status(400).json({
        success: false,
        message: 'Can only revoke pending invitations'
      });
    }

    await invitation.revoke();

    // Log activity
    await UserActivity.logActivity({
      userId: req.adminUser._id,
      activityType: 'Other',
      description: `Revoked ${invitation.userType} invitation for ${invitation.email}`,
      details: {
        invitationId: invitation._id,
        email: invitation.email,
        userType: invitation.userType
      },
      severity: 'Normal',
      isSystemGenerated: true
    });

    logger.info(`Invitation ${invitationId} revoked by admin ${req.adminUser.email}`);

    res.json({
      success: true,
      message: 'Invitation revoked successfully'
    });

  } catch (error) {
    logger.error('Error revoking invitation:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to revoke invitation'
    });
  }
});

// Extend invitation expiry
router.patch('/:invitationId/extend', requireAdmin, async (req, res) => {
  try {
    const { invitationId } = req.params;
    const { days = 7 } = req.body;

    const invitation = await Invitation.findById(invitationId);

    if (!invitation) {
      return res.status(404).json({
        success: false,
        message: 'Invitation not found'
      });
    }

    if (invitation.status !== 'Pending') {
      return res.status(400).json({
        success: false,
        message: 'Can only extend pending invitations'
      });
    }

    await invitation.extend(parseInt(days));

    // Log activity
    await UserActivity.logActivity({
      userId: req.adminUser._id,
      activityType: 'Other',
      description: `Extended ${invitation.userType} invitation for ${invitation.email} by ${days} days`,
      details: {
        invitationId: invitation._id,
        extensionDays: days,
        newExpiryDate: invitation.expiresAt
      },
      severity: 'Normal',
      isSystemGenerated: true
    });

    logger.info(`Invitation ${invitationId} extended by ${days} days by admin ${req.adminUser.email}`);

    res.json({
      success: true,
      message: `Invitation extended by ${days} days`,
      data: {
        newExpiryDate: invitation.expiresAt
      }
    });

  } catch (error) {
    logger.error('Error extending invitation:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to extend invitation'
    });
  }
});

// Bulk operations
router.post('/bulk/remind', requireAdmin, async (req, res) => {
  try {
    const { invitationIds } = req.body;

    if (!Array.isArray(invitationIds) || invitationIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Invalid invitation IDs provided'
      });
    }

    const invitations = await Invitation.find({
      _id: { $in: invitationIds },
      status: 'Pending'
    });

    const results = {
      success: 0,
      failed: 0,
      errors: []
    };

    for (const invitation of invitations) {
      try {
        if (invitation.isExpired()) {
          await invitation.extend(7);
        }

        await emailService.sendReminderEmail(invitation);
        await invitation.sendReminder();
        results.success++;
      } catch (error) {
        results.failed++;
        results.errors.push({
          invitationId: invitation._id,
          email: invitation.email,
          error: error.message
        });
      }
    }

    // Log activity
    await UserActivity.logActivity({
      userId: req.adminUser._id,
      activityType: 'Other',
      description: `Sent bulk reminders: ${results.success} successful, ${results.failed} failed`,
      details: {
        totalInvitations: invitations.length,
        results
      },
      severity: 'Normal',
      isSystemGenerated: true
    });

    res.json({
      success: true,
      message: `Bulk reminder completed: ${results.success} sent, ${results.failed} failed`,
      data: results
    });

  } catch (error) {
    logger.error('Error sending bulk reminders:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to send bulk reminders'
    });
  }
});

// Clean up expired invitations (admin utility)
router.post('/cleanup/expired', requireAdmin, async (req, res) => {
  try {
    const expiredCount = await Invitation.markExpiredInvitations();

    // Log activity
    await UserActivity.logActivity({
      userId: req.adminUser._id,
      activityType: 'Other',
      description: `Marked ${expiredCount} expired invitations`,
      details: {
        expiredCount
      },
      severity: 'Normal',
      isSystemGenerated: true
    });

    logger.info(`${expiredCount} expired invitations marked by admin ${req.adminUser.email}`);

    res.json({
      success: true,
      message: `${expiredCount} expired invitations marked`,
      data: {
        expiredCount
      }
    });

  } catch (error) {
    logger.error('Error cleaning up expired invitations:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to cleanup expired invitations'
    });
  }
});

module.exports = router;
