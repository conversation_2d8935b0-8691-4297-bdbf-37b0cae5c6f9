# Energy Bill Application Database Guide

This guide explains how to set up and use the MongoDB database for the Energy Bill application.

## Database Setup Options

You have two options for setting up MongoDB:

1. **Docker Compose** (local development)
2. **MongoDB Atlas** (cloud-based)

## Option 1: Docker Compose Setup

### Prerequisites
- Docker and Docker Compose installed
- Docker Desktop running

### Steps

1. **Start MongoDB containers**

   From the project root directory:
   ```bash
   docker compose up -d
   ```

2. **Configure the application**

   In `backend/.env`, use:
   ```
   MONGODB_URI=mongodb://localhost:27017/energy_bill_db
   ENABLE_MONGODB=true
   ```

3. **Access MongoDB Admin UI**

   Open [http://localhost:8081](http://localhost:8081) in your browser
   - Username: `admin`
   - Password: `password`

## Option 2: MongoDB Atlas Setup

### Steps

1. Follow the instructions in `mongodb-atlas-setup.md` to create a MongoDB Atlas account and cluster

2. Update `backend/.env` with your MongoDB Atlas connection string:
   ```
   MONGODB_URI=mongodb+srv://<username>:<password>@<cluster-url>/energy_bill_db?retryWrites=true&w=majority
   ENABLE_MONGODB=true
   ```

## Database Management

### Check Database Connection

To verify your MongoDB connection:

```bash
cd backend
npm run check-db
```

### Seed the Database

To populate the database with sample data:

```bash
cd backend
npm run seed-db
```

This will create:
- Sample users (Individual, Professional, Broker, Supplier, Admin)
- Individual and Professional profiles
- Sample energy requests

### Database Models

The application uses the following MongoDB collections:

1. **Users**: Basic user information and authentication
2. **IndividualProfiles**: Details for individual users
3. **ProfessionalProfiles**: Details for business users
4. **BrokerProfiles**: Details for energy brokers
5. **SupplierProfiles**: Details for energy suppliers
6. **ReferrerProfiles**: Details for referral partners
7. **Documents**: Document metadata for uploaded files
8. **EnergyRequests**: Energy service requests from users
9. **Offers**: Energy offers from suppliers
10. **Contracts**: Finalized energy contracts
11. **Appointments**: Scheduled meetings
12. **SupportTickets**: Technical support issues
13. **Notifications**: System and user notifications
14. **Analytics**: Aggregated analytics data
15. **Transactions**: Financial transaction records

### Database Relationships

- Users → Various Profile Collections (1:1)
- Users → Documents (1:Many)
- Users → EnergyRequests (1:Many)
- EnergyRequests → Offers (1:Many)
- Offers → Contracts (1:1)
- Users → SupportTickets (1:Many)
- Users → Notifications (1:Many)

## Troubleshooting

### Connection Issues

If you can't connect to MongoDB:

1. **For Docker setup**:
   - Ensure Docker Desktop is running
   - Check container status with `docker compose ps`
   - View logs with `docker compose logs mongodb`

2. **For MongoDB Atlas**:
   - Verify your connection string is correct
   - Check that your IP address is in the allowed list
   - Ensure your database user has the correct permissions

### Data Issues

If you encounter data-related issues:

1. **Reset the database**:
   ```bash
   cd backend
   npm run seed-db
   ```

2. **Check schema validation**:
   - Ensure your data matches the schema definitions in the models
   - Check for required fields and data types

## Development Workflow

1. Start the MongoDB database (Docker or use Atlas)
2. Run the backend server: `npm run dev`
3. The server will connect to MongoDB automatically
4. Use the API endpoints to interact with the database

## Production Considerations

For production deployment:

1. Use MongoDB Atlas with proper security settings
2. Set up database backups
3. Configure proper authentication
4. Use environment variables for sensitive information
5. Implement proper indexing for performance
