import React from 'react';
import { showErrorMessage } from '../../utils/toastNotifications';

const SupplierServicesStep = ({ formData, onChange, onMultiSelectChange, onNext, onPrev }) => {
  const energyTypeOptions = ['Electricity', 'Gas', 'Both'];
  const contractTypeOptions = ['Fixed Rate', 'Variable Rate', 'Indexed', 'Green Energy'];
  const serviceAreaOptions = ['Residential', 'Commercial', 'Industrial', 'Municipal'];

  const handleNext = () => {
    // Validate required fields
    if (formData.energyTypesProvided.length === 0) {
      showErrorMessage('VALIDATION_FAILED', 'Please select at least one energy type you provide');
      return;
    }
    if (formData.serviceAreas.length === 0) {
      showErrorMessage('VALIDATION_FAILED', 'Please select at least one service area');
      return;
    }
    onNext();
  };

  return (
    <div className="step-content">
      <div className="stepper-header">
        <h2 className="page-title">Energy & Meter</h2>
        <p className="page-subtitle">Select the types of energy services and areas you provide</p>
      </div>

      <div className="stepper-form">
        <div className="form-row">
          <div className="form-group">
            <label className="form-label">Energy Types Provided <span className="required">*</span></label>
            <p className="hint-text">Select the types of energy you supply to customers</p>

            <div className="checkbox-group">
              {energyTypeOptions.map(type => (
                <div key={type} className="checkbox-item">
                  <input
                    type="checkbox"
                    id={`energy-${type}`}
                    checked={formData.energyTypesProvided.includes(type)}
                    onChange={() => onMultiSelectChange('energyTypesProvided', type)}
                  />
                  <label htmlFor={`energy-${type}`}>{type}</label>
                </div>
              ))}
            </div>
          </div>
        </div>

        <div className="form-row">
          <div className="form-group">
            <label className="form-label">Contract Types <span className="optional">(optional)</span></label>
            <p className="hint-text">Select the types of energy contracts you offer</p>

            <div className="checkbox-group">
              {contractTypeOptions.map(type => (
                <div key={type} className="checkbox-item">
                  <input
                    type="checkbox"
                    id={`contract-${type}`}
                    checked={formData.contractTypes.includes(type)}
                    onChange={() => onMultiSelectChange('contractTypes', type)}
                  />
                  <label htmlFor={`contract-${type}`}>{type}</label>
                </div>
              ))}
            </div>
          </div>
        </div>

        <div className="form-row">
          <div className="form-group">
            <label className="form-label">Service Areas <span className="required">*</span></label>
            <p className="hint-text">Select the customer segments you serve</p>

            <div className="checkbox-group">
              {serviceAreaOptions.map(area => (
                <div key={area} className="checkbox-item">
                  <input
                    type="checkbox"
                    id={`area-${area}`}
                    checked={formData.serviceAreas.includes(area)}
                    onChange={() => onMultiSelectChange('serviceAreas', area)}
                  />
                  <label htmlFor={`area-${area}`}>{area}</label>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      <div className="stepper-buttons">
        <button type="button" className="stepper-button stepper-button-prev" onClick={onPrev}>
          <i className="fas fa-arrow-left"></i> Previous
        </button>
        <button type="button" className="stepper-button stepper-button-next" onClick={handleNext}>
          Next <i className="fas fa-arrow-right"></i>
        </button>
      </div>
    </div>
  );
};

export default SupplierServicesStep;
