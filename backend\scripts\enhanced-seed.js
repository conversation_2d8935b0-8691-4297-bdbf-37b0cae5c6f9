/**
 * Enhanced script to seed the database with comprehensive sample data
 * Run with: node scripts/enhanced-seed.js
 */

const mongoose = require('mongoose');
const dotenv = require('dotenv');
const logger = require('../utils/logger');
const path = require('path');

const {
  User,
  IndividualProfile,
  ProfessionalProfile,
  BrokerProfile,
  SupplierProfile,
  ReferrerProfile,
  Document,
  EnergyRequest,
  Offer,
  Contract,
  Appointment,
  SupportTicket,
  Notification,
  Analytics,
  Transaction
} = require('../models');
const Invoice = require('../models/Invoice');

// Load environment variables
const cliEnv = process.argv[2];
process.env.NODE_ENV = cliEnv || process.env.NODE_ENV || 'local';

const envFile = `.env.${process.env.NODE_ENV}`;

dotenv.config({
  path: path.resolve(__dirname, `../${envFile}`),
  override: false,
});

console.log(`Loaded environment: ${envFile}`);

// Get MongoDB URI from environment variables
const MONGODB_URI = process.env.MONGODB_URI;

if (!MONGODB_URI) {
  logger.error('Error: MONGODB_URI is not defined in the environment variables');
  process.exit(1);
}

// Sample data
const sampleUsers = [
  {
    email: '<EMAIL>',
    firstName: 'John',
    lastName: 'Doe',
    phone: '+33123456789',
    userType: 'Individual',
    status: 'Active',
    profileComplete: true,
    verificationStatus: 'Verified'
  },
  {
    email: '<EMAIL>',
    firstName: 'Jane',
    lastName: 'Smith',
    phone: '+33987654321',
    userType: 'Professional',
    status: 'Active',
    profileComplete: true,
    verificationStatus: 'Verified'
  },
  {
    email: '<EMAIL>',
    firstName: 'Robert',
    lastName: 'Johnson',
    phone: '+33456789123',
    userType: 'Broker',
    status: 'Active',
    profileComplete: true,
    verificationStatus: 'Verified'
  },
  {
    email: '<EMAIL>',
    firstName: 'Emily',
    lastName: 'Brown',
    phone: '+33789123456',
    userType: 'Supplier',
    status: 'Active',
    profileComplete: true,
    verificationStatus: 'Verified'
  },
  {
    email: '<EMAIL>',
    firstName: 'Michael',
    lastName: 'Wilson',
    phone: '+33321654987',
    userType: 'Referrer',
    status: 'Active',
    profileComplete: true,
    verificationStatus: 'Verified'
  },
  {
    email: '<EMAIL>',
    firstName: 'Admin',
    lastName: 'User',
    phone: '+33654321987',
    userType: 'Admin',
    status: 'Active',
    profileComplete: true,
    verificationStatus: 'Verified'
  }
];

// Connect to MongoDB
logger.info('Connecting to MongoDB...');
mongoose.connect(MONGODB_URI)
  .then(async () => {
    logger.info('✅ MongoDB connection successful!');

    // Clear existing data
    logger.info('Clearing existing data...');
    await User.deleteMany({});
    await IndividualProfile.deleteMany({});
    await ProfessionalProfile.deleteMany({});
    await BrokerProfile.deleteMany({});
    await SupplierProfile.deleteMany({});
    await ReferrerProfile.deleteMany({});
    await Document.deleteMany({});
    await EnergyRequest.deleteMany({});
    await Offer.deleteMany({});
    await Contract.deleteMany({});
    await Appointment.deleteMany({});
    await SupportTicket.deleteMany({});
    await Notification.deleteMany({});
    await Analytics.deleteMany({});
    await Transaction.deleteMany({});

    // Create users
    logger.info('Creating sample users...');
    const createdUsers = await User.insertMany(sampleUsers);

    // Map users by email for easy reference
    const userMap = createdUsers.reduce((map, user) => {
      map[user.email] = user;
      return map;
    }, {});

    // Create individual profile
    logger.info('Creating individual profile...');
    const individualProfile = await IndividualProfile.create({
      userId: userMap['<EMAIL>']._id,
      address: {
        street: '123 Rue de Paris',
        city: 'Paris',
        postalCode: '75001',
        country: 'France'
      },
      energyIdentifiers: {
        pdl: 'PDL123456789',
        prm: 'PRM987654321'
      },
      currentSupplier: 'EDF',
      consumptionDetails: {
        annualConsumption: 3500,
        averageMonthlyBill: 85
      },
      preferredContactMethod: 'Email'
    });

    // Create professional profile
    logger.info('Creating professional profile...');
    const professionalProfile = await ProfessionalProfile.create({
      userId: userMap['<EMAIL>']._id,
      companyName: 'Tech Solutions SARL',
      companyAddress: {
        street: '456 Avenue des Champs-Élysées',
        city: 'Paris',
        postalCode: '75008',
        country: 'France'
      },
      siretNumber: '12345678901234',
      vatNumber: 'FR12345678901',
      companyRole: 'Technology',
      businessType: 'SARL',
      energyTypes: ['Electricity', 'Gas'],
      numberOfLocations: 3,
      annualConsumption: {
        electricity: 25000,
        gas: 15000
      },
      currentSuppliers: [
        {
          energyType: 'Electricity',
          supplierName: 'EDF',
          contractEndDate: new Date('2023-12-31')
        },
        {
          energyType: 'Gas',
          supplierName: 'Engie',
          contractEndDate: new Date('2023-10-15')
        }
      ]
    });

    // Create broker profile
    logger.info('Creating broker profile...');
    const brokerProfile = await BrokerProfile.create({
      userId: userMap['<EMAIL>']._id,
      companyName: 'Energy Broker Solutions',
      companyAddress: {
        street: '789 Boulevard Haussmann',
        city: 'Paris',
        postalCode: '75009',
        country: 'France'
      },
      licenseNumber: 'BRK123456',
      specializations: ['Residential', 'Commercial'],
      commissionStructure: {
        percentageRate: 5,
        fixedFee: 100,
        tieredRates: [
          { threshold: 10000, rate: 3 },
          { threshold: 50000, rate: 4 },
          { threshold: 100000, rate: 5 }
        ]
      }
    });

    // Create supplier profile
    logger.info('Creating supplier profile...');
    const supplierProfile = await SupplierProfile.create({
      userId: userMap['<EMAIL>']._id,
      companyName: 'Green Energy Suppliers',
      companyAddress: {
        street: '101 Rue de Rivoli',
        city: 'Paris',
        postalCode: '75001',
        country: 'France'
      },
      licenseNumber: 'SUP789012',
      energyTypes: ['Electricity', 'Gas'],
      serviceAreas: ['Île-de-France', 'Provence-Alpes-Côte d\'Azur'],
      offerTypes: ['Fixed', 'Variable', 'Green'],
      contractTerms: {
        minDuration: 12,
        maxDuration: 36,
        earlyTerminationFee: 50
      }
    });

    // Create referrer profile
    logger.info('Creating referrer profile...');
    const referrerProfile = await ReferrerProfile.create({
      userId: userMap['<EMAIL>']._id,
      companyName: 'Referral Partners',
      companyAddress: {
        street: '202 Avenue Montaigne',
        city: 'Paris',
        postalCode: '75008',
        country: 'France'
      },
      referralCode: 'REF2023',
      commissionRate: 3,
      paymentDetails: {
        bankName: 'BNP Paribas',
        accountNumber: '***************************',
        accountHolder: 'Referral Partners SARL'
      }
    });

    // Create documents
    logger.info('Creating sample documents...');
    const documents = await Document.insertMany([
      {
        userId: userMap['<EMAIL>']._id,
        type: 'EnergyBill',
        fileUrl: 'https://storage.example.com/documents/energy-bill-1.pdf',
        fileName: 'energy-bill-1.pdf',
        fileSize: 1024000,
        mimeType: 'application/pdf',
        status: 'Verified',
        uploadDate: new Date('2023-01-15')
      },
      {
        userId: userMap['<EMAIL>']._id,
        type: 'IdentityProof',
        fileUrl: 'https://storage.example.com/documents/id-proof-1.jpg',
        fileName: 'id-proof-1.jpg',
        fileSize: 512000,
        mimeType: 'image/jpeg',
        status: 'Verified',
        uploadDate: new Date('2023-01-10')
      },
      {
        userId: userMap['<EMAIL>']._id,
        type: 'BusinessRegistration',
        fileUrl: 'https://storage.example.com/documents/business-reg-1.pdf',
        fileName: 'business-reg-1.pdf',
        fileSize: 2048000,
        mimeType: 'application/pdf',
        status: 'Verified',
        uploadDate: new Date('2023-02-05')
      },
      {
        userId: userMap['<EMAIL>']._id,
        type: 'EnergyBill',
        fileUrl: 'https://storage.example.com/documents/energy-bill-2.pdf',
        fileName: 'energy-bill-2.pdf',
        fileSize: 1536000,
        mimeType: 'application/pdf',
        status: 'Verified',
        uploadDate: new Date('2023-02-10')
      }
    ]);

    // Map documents by user and type for easy reference
    const documentMap = documents.reduce((map, doc) => {
      const key = `${doc.userId}_${doc.type}`;
      map[key] = doc;
      return map;
    }, {});

    // Update profiles with document references
    await IndividualProfile.findByIdAndUpdate(
      individualProfile._id,
      {
        $push: {
          documents: [
            documentMap[`${userMap['<EMAIL>']._id}_EnergyBill`]._id,
            documentMap[`${userMap['<EMAIL>']._id}_IdentityProof`]._id
          ]
        }
      }
    );

    await ProfessionalProfile.findByIdAndUpdate(
      professionalProfile._id,
      {
        $push: {
          documents: [
            documentMap[`${userMap['<EMAIL>']._id}_BusinessRegistration`]._id,
            documentMap[`${userMap['<EMAIL>']._id}_EnergyBill`]._id
          ]
        }
      }
    );

    // Create energy requests
    logger.info('Creating energy requests...');
    const energyRequests = await EnergyRequest.insertMany([
      {
        userId: userMap['<EMAIL>']._id,
        userType: 'Individual',
        requestType: 'Electricity',
        status: 'Submitted',
        consumptionDetails: {
          annualConsumption: 3500,
          averageMonthlyBill: 85
        },
        currentSupplier: 'EDF',
        currentContractEndDate: new Date('2023-12-31'),
        preferredDuration: '12 months',
        additionalRequirements: 'Looking for green energy options',
        documents: [documentMap[`${userMap['<EMAIL>']._id}_EnergyBill`]._id]
      },
      {
        userId: userMap['<EMAIL>']._id,
        userType: 'Professional',
        requestType: 'Both',
        status: 'UnderReview',
        consumptionDetails: {
          annualConsumption: 25000,
          averageMonthlyBill: 450
        },
        currentSupplier: 'EDF & Engie',
        currentContractEndDate: new Date('2023-10-15'),
        preferredDuration: '24 months',
        additionalRequirements: 'Need competitive rates for multiple locations',
        assignedBroker: userMap['<EMAIL>']._id,
        documents: [documentMap[`${userMap['<EMAIL>']._id}_EnergyBill`]._id]
      },
      {
        userId: userMap['<EMAIL>']._id,
        userType: 'Individual',
        requestType: 'Gas',
        status: 'OffersAvailable',
        consumptionDetails: {
          annualConsumption: 12000,
          averageMonthlyBill: 120
        },
        currentSupplier: 'Engie',
        currentContractEndDate: new Date('2023-11-30'),
        preferredDuration: '24 months',
        additionalRequirements: 'Looking for fixed rate options',
        assignedBroker: userMap['<EMAIL>']._id
      }
    ]);

    // Map energy requests by index for easy reference
    const requestMap = {};
    energyRequests.forEach((req, index) => {
      requestMap[index] = req;
    });

    // Create offers
    logger.info('Creating offers...');
    const offers = await Offer.insertMany([
      {
        requestId: requestMap[2]._id, // For the individual gas request
        supplierId: userMap['<EMAIL>']._id,
        offerDetails: {
          name: 'Fixed Rate Gas Plan',
          description: 'Stable gas rates for 24 months',
          highlights: ['No price increases', 'No hidden fees', 'Easy billing']
        },
        energyType: 'Gas',
        rateType: 'Fixed',
        duration: 24,
        price: {
          baseRate: 0.065,
          standingCharge: 15,
          totalEstimatedAnnual: 780,
          currency: 'EUR'
        },
        estimatedSavings: {
          amount: 120,
          percentage: 10
        },
        additionalBenefits: ['Online account management', 'Monthly consumption reports'],
        termsUrl: 'https://example.com/terms/gas-fixed-24',
        validUntil: new Date('2023-09-30'),
        status: 'Active',
        reviewStatus: 'Approved',
        reviewedBy: userMap['<EMAIL>']._id,
        reviewDate: new Date('2023-08-15'),
        views: 156,
        applications: 8
      },
      {
        requestId: requestMap[2]._id, // For the individual gas request
        supplierId: userMap['<EMAIL>']._id,
        offerDetails: {
          name: 'Variable Rate Gas Plan',
          description: 'Flexible gas rates with market prices',
          highlights: ['Lower starting rate', 'No long-term commitment', 'Green gas option']
        },
        energyType: 'Gas',
        rateType: 'Variable',
        duration: 12,
        price: {
          baseRate: 0.058,
          standingCharge: 12,
          totalEstimatedAnnual: 720,
          currency: 'EUR'
        },
        estimatedSavings: {
          amount: 180,
          percentage: 15
        },
        additionalBenefits: ['No exit fees', 'Paperless billing discount'],
        termsUrl: 'https://example.com/terms/gas-variable-12',
        validUntil: new Date('2023-09-30'),
        status: 'Active',
        reviewStatus: 'Approved',
        reviewedBy: userMap['<EMAIL>']._id,
        reviewDate: new Date('2023-08-15'),
        views: 234,
        applications: 12
      }
    ]);

    // Map offers by index for easy reference
    const offerMap = {};
    offers.forEach((offer, index) => {
      offerMap[index] = offer;
    });

    // Create contracts
    logger.info('Creating contracts...');
    const contracts = await Contract.insertMany([
      {
        offerId: offerMap[0]._id,
        userId: userMap['<EMAIL>']._id,
        supplierId: userMap['<EMAIL>']._id,
        status: 'Active',
        startDate: new Date('2023-09-01'),
        endDate: new Date('2025-09-01'),
        contractDetails: {
          contractNumber: 'CNT-2023-001',
          energyType: 'Gas', // Required field
          rateType: 'Fixed', // Required field
          rate: 0.065,
          standingCharge: 15,
          estimatedAnnualCost: 780,
          currency: 'EUR',
          specialTerms: 'Price guaranteed for full term'
        },
        signatureStatus: {
          userSigned: true,
          userSignedDate: new Date('2023-08-20'),
          supplierSigned: true,
          supplierSignedDate: new Date('2023-08-22'),
          completed: true,
          completedDate: new Date('2023-08-22')
        },
        paymentDetails: {
          initialPayment: 65,
          paymentFrequency: 'Monthly',
          paymentMethod: 'Direct Debit',
          paymentStatus: 'Active'
        },
        monthlyValue: 65,
        commissions: {
          brokerCommission: 75.50,
          referrerCommission: 25.00,
          commissionPaid: false
        }
      },
      {
        offerId: offerMap[1]._id,
        userId: userMap['<EMAIL>']._id,
        supplierId: userMap['<EMAIL>']._id,
        brokerId: userMap['<EMAIL>']._id,
        status: 'Active',
        startDate: new Date('2023-10-01'),
        endDate: new Date('2025-10-01'),
        contractDetails: {
          contractNumber: 'CNT-2023-002',
          energyType: 'Electricity',
          rateType: 'Fixed',
          rate: 0.085,
          standingCharge: 20,
          estimatedAnnualCost: 1200,
          currency: 'EUR',
          specialTerms: 'Business rate with peak/off-peak pricing'
        },
        signatureStatus: {
          userSigned: true,
          userSignedDate: new Date('2023-09-25'),
          supplierSigned: true,
          supplierSignedDate: new Date('2023-09-26'),
          completed: true,
          completedDate: new Date('2023-09-26')
        },
        paymentDetails: {
          initialPayment: 100,
          paymentFrequency: 'Monthly',
          paymentMethod: 'Direct Debit',
          paymentStatus: 'Active'
        },
        monthlyValue: 100,
        commissions: {
          brokerCommission: 120.00,
          referrerCommission: 0,
          commissionPaid: true,
          commissionPaidDate: new Date('2023-10-01')
        }
      },
      {
        offerId: offerMap[0]._id,
        userId: userMap['<EMAIL>']._id,
        supplierId: userMap['<EMAIL>']._id,
        brokerId: userMap['<EMAIL>']._id,
        status: 'Pending',
        startDate: new Date('2024-01-01'),
        endDate: new Date('2026-01-01'),
        contractDetails: {
          contractNumber: 'CNT-2023-003',
          energyType: 'Both',
          rateType: 'Variable',
          rate: 0.075,
          standingCharge: 18,
          estimatedAnnualCost: 950,
          currency: 'EUR',
          specialTerms: 'Dual fuel package with green energy option'
        },
        signatureStatus: {
          userSigned: false,
          supplierSigned: false,
          completed: false
        },
        paymentDetails: {
          initialPayment: 80,
          paymentFrequency: 'Monthly',
          paymentMethod: 'Direct Debit',
          paymentStatus: 'Pending'
        },
        monthlyValue: 80,
        commissions: {
          brokerCommission: 95.00,
          referrerCommission: 30.00,
          commissionPaid: false
        }
      }
    ]);

    // Create sample invoices
    logger.info('Creating sample invoices...');
    const invoices = await Invoice.insertMany([
      {
        userId: userMap['<EMAIL>']._id,
        cognitoId: 'individual-cognito-id',
        userType: 'individual',
        originalFilename: 'electricity_bill_august_2023.pdf',
        s3Key: 'invoices/individual/electricity_bill_august_2023.pdf',
        s3Bucket: 'energy-bills-bucket',
        fileSize: 245760,
        mimeType: 'application/pdf',
        metadata: {
          invoiceDate: new Date('2023-08-15'),
          invoiceNumber: 'EDF-2023-08-001',
          provider: 'EDF',
          energyType: 'electricity',
          pointOfDelivery: 'PDL123456789',
          amount: 85.50,
          currency: 'EUR',
          consumption: 350
        },
        status: 'processed'
      },
      {
        userId: userMap['<EMAIL>']._id,
        cognitoId: 'individual-cognito-id',
        userType: 'individual',
        originalFilename: 'gas_bill_august_2023.pdf',
        s3Key: 'invoices/individual/gas_bill_august_2023.pdf',
        s3Bucket: 'energy-bills-bucket',
        fileSize: 198432,
        mimeType: 'application/pdf',
        metadata: {
          invoiceDate: new Date('2023-08-20'),
          invoiceNumber: 'ENGIE-2023-08-002',
          provider: 'Engie',
          energyType: 'gas',
          pointOfDelivery: 'PRM987654321',
          amount: 120.75,
          currency: 'EUR',
          consumption: 1200
        },
        status: 'processed'
      },
      {
        userId: userMap['<EMAIL>']._id,
        cognitoId: 'professional-cognito-id',
        userType: 'professional',
        originalFilename: 'business_electricity_july_2023.pdf',
        s3Key: 'invoices/professional/business_electricity_july_2023.pdf',
        s3Bucket: 'energy-bills-bucket',
        fileSize: 312456,
        mimeType: 'application/pdf',
        metadata: {
          invoiceDate: new Date('2023-07-31'),
          invoiceNumber: 'EDF-BUS-2023-07-001',
          provider: 'EDF Business',
          energyType: 'electricity',
          pointOfDelivery: 'PDL555666777',
          amount: 450.25,
          currency: 'EUR',
          consumption: 2500
        },
        status: 'processed'
      }
    ]);

    // Create appointments
    logger.info('Creating appointments...');
    const appointments = await Appointment.insertMany([
      {
        userId: userMap['<EMAIL>']._id,
        agentId: userMap['<EMAIL>']._id,
        agentType: 'Broker',
        requestId: requestMap[0]._id,
        type: 'Initial Consultation',
        scheduledTime: new Date('2023-09-15T10:00:00Z'),
        duration: 30, // minutes
        status: 'Scheduled',
        location: 'Virtual',
        meetingLink: 'https://zoom.us/j/123456789',
        notes: 'Initial consultation to discuss electricity options'
      },
      {
        userId: userMap['<EMAIL>']._id,
        agentId: userMap['<EMAIL>']._id,
        agentType: 'Broker',
        requestId: requestMap[1]._id,
        type: 'Offer Discussion',
        scheduledTime: new Date('2023-09-20T14:00:00Z'),
        duration: 45,
        status: 'Completed',
        location: 'In-Person',
        notes: 'Discussed business energy package options'
      },
      {
        userId: userMap['<EMAIL>']._id,
        agentId: userMap['<EMAIL>']._id,
        agentType: 'Supplier',
        requestId: requestMap[0]._id,
        type: 'Contract Signing',
        scheduledTime: new Date('2023-10-05T11:00:00Z'),
        duration: 30,
        status: 'Scheduled',
        location: 'Virtual',
        meetingLink: 'https://teams.microsoft.com/l/meetup-join/19%3a...',
        notes: 'Final contract review and signing'
      },
      {
        userId: userMap['<EMAIL>']._id,
        agentId: userMap['<EMAIL>']._id,
        agentType: 'Broker',
        requestId: requestMap[1]._id,
        type: 'Follow-up',
        scheduledTime: new Date('2023-08-10T14:00:00Z'),
        duration: 60, // minutes
        status: 'Completed',
        location: 'In-Person',
        notes: 'Site visit to assess energy needs for multiple locations',
        feedback: {
          rating: 5,
          comments: 'Very helpful visit, all questions answered',
          submittedAt: new Date('2023-08-11T10:00:00Z')
        }
      }
    ]);

    // Create support tickets
    logger.info('Creating support tickets...');
    const supportTickets = await SupportTicket.insertMany([
      {
        userId: userMap['<EMAIL>']._id,
        subject: 'Question about my electricity bill',
        description: 'I have a question about the calculation on my latest bill',
        type: 'Billing',
        priority: 'Medium',
        status: 'Open',
        assignedTo: userMap['<EMAIL>']._id,
        comments: [
          {
            text: 'Can you explain why my bill is higher this month?',
            createdBy: userMap['<EMAIL>']._id,
            isInternal: false,
            createdAt: new Date('2023-08-25T09:30:00Z')
          },
          {
            text: 'I\'ll look into this and get back to you shortly.',
            createdBy: userMap['<EMAIL>']._id,
            isInternal: false,
            createdAt: new Date('2023-08-25T10:15:00Z')
          }
        ]
      },
      {
        userId: userMap['<EMAIL>']._id,
        subject: 'Contract renewal options',
        description: 'I would like to discuss options for renewing our energy contract',
        type: 'Contract',
        priority: 'High',
        status: 'InProgress',
        assignedTo: userMap['<EMAIL>']._id,
        comments: [
          {
            text: 'Our contract expires in 2 months and we need to discuss renewal options.',
            createdBy: userMap['<EMAIL>']._id,
            isInternal: false,
            createdAt: new Date('2023-08-20T14:00:00Z')
          },
          {
            text: 'I\'ve reviewed your account and have several options to present. Can we schedule a call?',
            createdBy: userMap['<EMAIL>']._id,
            isInternal: false,
            createdAt: new Date('2023-08-21T09:45:00Z')
          }
        ]
      }
    ]);

    // Create notifications
    logger.info('Creating notifications...');
    const notifications = await Notification.insertMany([
      {
        userId: userMap['<EMAIL>']._id,
        type: 'OfferReceived',
        title: 'New Energy Offer Available',
        message: 'You have received a new energy offer for your gas request.',
        read: false,
        relatedEntity: {
          entityType: 'Offer',
          entityId: offerMap[0]._id
        },
        priority: 'Normal'
      },
      {
        userId: userMap['<EMAIL>']._id,
        type: 'AppointmentReminder',
        title: 'Upcoming Appointment',
        message: 'Reminder: You have a consultation with your energy broker tomorrow at 10:00 AM.',
        read: true,
        readAt: new Date('2023-09-14T10:00:00Z'),
        relatedEntity: {
          entityType: 'Appointment',
          entityId: appointments[0]._id
        },
        priority: 'High'
      },
      {
        userId: userMap['<EMAIL>']._id,
        type: 'RequestStatusUpdate',
        title: 'Request Status Updated',
        message: 'Your energy request is now under review by our broker team.',
        read: false,
        relatedEntity: {
          entityType: 'EnergyRequest',
          entityId: requestMap[1]._id
        },
        priority: 'Normal'
      }
    ]);

    // Create analytics
    logger.info('Creating analytics data...');
    const analyticsData = await Analytics.insertMany([
      {
        type: 'UserActivity',
        period: 'Monthly',
        startDate: new Date('2023-08-01'),
        endDate: new Date('2023-08-31'),
        metrics: {
          newUsers: 25,
          activeUsers: 120,
          requestsSubmitted: 18,
          offersGenerated: 42,
          contractsSigned: 12
        },
        isAutoGenerated: true
      },
      {
        type: 'SupplierPerformance',
        period: 'Quarterly',
        startDate: new Date('2023-07-01'),
        endDate: new Date('2023-09-30'),
        metrics: {
          totalOffers: 150,
          acceptedOffers: 65,
          averageSavings: 12.5,
          averageResponseTime: 1.8 // days
        },
        isAutoGenerated: true
      }
    ]);

    // Create transactions
    logger.info('Creating transactions...');
    const transactions = await Transaction.insertMany([
      {
        userId: userMap['<EMAIL>']._id,
        relatedEntity: {
          entityType: 'Contract',
          entityId: contracts[0]._id
        },
        type: 'Commission',
        amount: 75.50,
        currency: 'EUR',
        status: 'Completed',
        paymentMethod: 'Bank Transfer',
        description: 'Broker commission for contract CNT-2023-001',
        processedBy: userMap['<EMAIL>']._id,
        completedAt: new Date('2023-09-05')
      },
      {
        userId: userMap['<EMAIL>']._id,
        relatedEntity: {
          entityType: 'Contract',
          entityId: contracts[0]._id
        },
        type: 'Bonus',
        amount: 25.00,
        currency: 'EUR',
        status: 'Pending',
        paymentMethod: 'Bank Transfer',
        description: 'Referral bonus for new customer',
        processedBy: userMap['<EMAIL>']._id
      }
    ]);

    logger.info('✅ Database seeded successfully!');
    logger.info(`Created ${createdUsers.length} users`);
    logger.info(`Created ${documents.length} documents`);
    logger.info(`Created ${energyRequests.length} energy requests`);
    logger.info(`Created ${offers.length} offers`);
    logger.info(`Created ${contracts.length} contracts`);
    logger.info(`Created ${invoices.length} invoices`);
    logger.info(`Created ${appointments.length} appointments`);
    logger.info(`Created ${supportTickets.length} support tickets`);
    logger.info(`Created ${notifications.length} notifications`);
    logger.info(`Created ${analyticsData.length} analytics records`);
    logger.info(`Created ${transactions.length} transactions`);

    // Close the connection
    await mongoose.connection.close();
    logger.info('Connection closed');
    process.exit(0);
  })
  .catch(err => {
    logger.error('❌ Error seeding database:', err);
    process.exit(1);
  });
