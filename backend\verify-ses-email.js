const dotenv = require('dotenv');
const path = require('path');

// Load environment variables
dotenv.config({
  path: path.resolve(__dirname, '.env.uat'),
  override: false,
});

const emailService = require('./services/emailService');
const logger = require('./utils/logger');

async function verifySESEmail() {
  console.log('🔧 AWS SES Email Verification Tool\n');

  const emailToVerify = process.env.FROM_EMAIL || '<EMAIL>';

  console.log('📧 Configuration:');
  console.log(`FROM_EMAIL: ${emailToVerify}`);
  console.log(`AWS_REGION: ${process.env.AWS_REGION}`);
  console.log(`AWS_ACCESS_KEY_ID: ${process.env.AWS_ACCESS_KEY_ID ? 'Set' : 'Not Set'}`);
  console.log(`AWS_SECRET_ACCESS_KEY: ${process.env.AWS_SECRET_ACCESS_KEY ? 'Set' : 'Not Set'}\n`);

  // First, let's check what emails are already verified
  console.log('🔍 Checking currently verified email addresses...');
  try {
    const AWS = require('aws-sdk');
    const ses = new AWS.SES({
      region: process.env.AWS_REGION || 'eu-west-3',
      accessKeyId: process.env.AWS_ACCESS_KEY_ID,
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
    });

    const verifiedEmails = await ses.listVerifiedEmailAddresses().promise();
    console.log('✅ Currently verified emails:', verifiedEmails.VerifiedEmailAddresses);

    if (verifiedEmails.VerifiedEmailAddresses.includes(emailToVerify)) {
      console.log(`✅ Email ${emailToVerify} is already verified!`);
      console.log('🤔 The issue might be with AWS credentials or region mismatch.\n');
    } else {
      console.log(`❌ Email ${emailToVerify} is NOT verified in this region/account.\n`);
    }

    // Also check verified domains
    const verifiedDomains = await ses.listVerifiedEmailAddresses().promise();
    console.log('📧 Checking verified domains...');
    try {
      const domains = await ses.listIdentities({ IdentityType: 'Domain' }).promise();
      console.log('✅ Verified domains:', domains.Identities);
    } catch (domainError) {
      console.log('⚠️ Could not check domains:', domainError.message);
    }

  } catch (listError) {
    console.error('❌ Failed to list verified emails:', listError.message);
    console.log('🤔 This might indicate AWS credentials or region issues.\n');
  }

  try {
    console.log(`🔍 Attempting to verify email address: ${emailToVerify}`);
    
    const result = await emailService.verifyEmailAddress(emailToVerify);
    
    if (result) {
      console.log('✅ Email verification request sent successfully!');
      console.log('\n📝 Next Steps:');
      console.log('1. Check the email inbox for the verification email');
      console.log('2. Click the verification link in the email');
      console.log('3. Once verified, the email can be used to send notifications');
      console.log('\n⚠️  Note: It may take a few minutes for the verification email to arrive.');
    } else {
      console.log('❌ Failed to send email verification request');
    }

    // Also check current SES quota
    console.log('\n📊 Checking AWS SES Quota...');
    try {
      const quota = await emailService.getSendingQuota();
      console.log('✅ SES Quota Information:');
      console.log(`   Max24HourSend: ${quota.Max24HourSend}`);
      console.log(`   MaxSendRate: ${quota.MaxSendRate}`);
      console.log(`   SentLast24Hours: ${quota.SentLast24Hours}`);
    } catch (quotaError) {
      console.error('❌ Failed to get SES quota:', quotaError.message);
    }

  } catch (error) {
    console.error('💥 Error verifying email:', error.message);
    
    if (error.code === 'InvalidParameterValue') {
      console.log('\n💡 Troubleshooting:');
      console.log('- Make sure the email address format is correct');
      console.log('- Check that AWS credentials have SES permissions');
    } else if (error.code === 'MessageRejected') {
      console.log('\n💡 Troubleshooting:');
      console.log('- The email might already be verified');
      console.log('- Check AWS SES console for verification status');
    } else {
      console.log('\n💡 Troubleshooting:');
      console.log('- Check AWS credentials and permissions');
      console.log('- Verify AWS region is correct');
      console.log('- Ensure SES service is available in the region');
    }
  }
}

// Run the verification if this file is executed directly
if (require.main === module) {
  verifySESEmail()
    .then(() => {
      console.log('\n✨ Email verification process completed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Verification failed:', error);
      process.exit(1);
    });
}

module.exports = { verifySESEmail };
