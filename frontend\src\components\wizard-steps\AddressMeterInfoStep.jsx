import React from 'react';
import { showErrorMessage } from '../../utils/toastNotifications';
import { validateMeterNumber } from '../../utils/meterValidation';

const AddressMeterInfoStep = ({ formData, onChange, onNext, onPrev }) => {
  const handleChange = (e) => {
    const { name, value } = e.target;
    onChange(name, value);
  };

  const handleSubmit = (e) => {
    e.preventDefault();

    // Validate required fields
    if (!formData.streetAddress.trim()) {
      showErrorMessage('VALIDATION_FAILED', 'Street address is required');
      return;
    }

    if (!formData.city.trim()) {
      showErrorMessage('VALIDATION_FAILED', 'City is required');
      return;
    }

    if (!formData.postalCode.trim()) {
      showErrorMessage('VALIDATION_FAILED', 'Postal code is required');
      return;
    } else if (!/^\d{5}$/.test(formData.postalCode.replace(/\s/g, ''))) {
      showErrorMessage('VALIDATION_FAILED', 'Please enter a valid postal code (5 digits)');
      return;
    }

    // Validate meter number using utility function
    const meterValidation = validateMeterNumber(formData.meterNumber);
    if (!meterValidation.isValid) {
      showErrorMessage('VALIDATION_FAILED', meterValidation.errorMessage);
      return;
    }

    onNext();
  };

  return (
    <div>
      <div className="stepper-header">
        <h3 className="page-title">Address & Meter Information</h3>
        <p className="page-subtitle">
          Please provide your address and meter details. This information is required to
          identify your energy supply and provide accurate pricing.
        </p>
      </div>
      <form onSubmit={handleSubmit}>
        <div className="form-row">
          <div className="form-group">
            <label htmlFor="streetAddress">Street Address <span className="required">*</span></label>
            <div className="input-with-icon">
              <span className="input-icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                  <path fill-rule="evenodd" d="M4 4a4 4 0 1 1 4.5 3.969V13.5a.5.5 0 0 1-1 0V7.97A4 4 0 0 1 4 3.999zm2.493 8.574a.5.5 0 0 1-.411.575c-.712.118-1.28.295-1.655.493a1.319 1.319 0 0 0-.37.265.301.301 0 0 0-.057.09V14l.002.008a.147.147 0 0 0 .016.033.617.617 0 0 0 .145.15c.165.13.435.27.813.395.751.25 1.82.414 3.024.414s2.273-.163 3.024-.414c.378-.126.648-.265.813-.395a.619.619 0 0 0 .146-.15.148.148 0 0 0 .015-.033L12 14v-.004a.301.301 0 0 0-.057-.09 1.318 1.318 0 0 0-.37-.264c-.376-.198-.943-.375-1.655-.493a.5.5 0 1 1 .164-.986c.77.127 1.452.328 1.957.594C12.5 13 13 13.4 13 14c0 .426-.26.752-.544.977-.29.228-.68.413-1.116.558-.878.293-2.059.465-3.34.465-1.281 0-2.462-.172-3.34-.465-.436-.145-.826-.33-1.116-.558C3.26 14.752 3 14.426 3 14c0-.599.5-1 .961-1.243.505-.266 1.187-.467 1.957-.594a.5.5 0 0 1 .575.411z"/>
                </svg>
              </span>
              <input
                type="text"
                id="streetAddress"
                name="streetAddress"
                className="form-input"
                value={formData.streetAddress}
                onChange={handleChange}
                placeholder="Enter your street address"
                required
              />
            </div>
          </div>

          <div className="form-group">
            <label htmlFor="city">City <span className="required">*</span></label>
            <div className="input-with-icon">
              <span className="input-icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                  <path d="M8 16s6-5.686 6-10A6 6 0 0 0 2 6c0 4.314 6 10 6 10zm0-7a3 3 0 1 1 0-6 3 3 0 0 1 0 6z"/>
                </svg>
              </span>
              <input
                type="text"
                id="city"
                name="city"
                className="form-input"
                value={formData.city}
                onChange={handleChange}
                placeholder="Enter your city"
                required
              />
            </div>
          </div>

          <div className="form-group">
            <label htmlFor="postalCode">Postal Code <span className="required">*</span></label>
            <div className="input-with-icon">
              <span className="input-icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                  <path d="M0 4a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V4zm2-1a1 1 0 0 0-1 1v.217l7 4.2 7-4.2V4a1 1 0 0 0-1-1H2zm13 2.383-4.708 2.825L15 11.105V5.383zm-.034 6.876-5.64-3.471L8 9.583l-1.326-.795-5.64 3.47A1 1 0 0 0 2 13h12a1 1 0 0 0 .966-.741zM1 11.105l4.708-2.897L1 5.383v5.722z"/>
                </svg>
              </span>
              <input
                type="text"
                id="postalCode"
                name="postalCode"
                className="form-input"
                value={formData.postalCode}
                onChange={handleChange}
                placeholder="Enter postal code"
                required
                maxLength="5"
              />
            </div>
          </div>

          <div className="form-group">
            <label htmlFor="country">Country <span className="required">*</span></label>
            <div className="input-with-icon">
              <span className="input-icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                  <path d="M0 8a8 8 0 1 1 16 0A8 8 0 0 1 0 8zm7.5-6.923c-.67.204-1.335.82-1.887 1.855A7.97 7.97 0 0 0 5.145 4H7.5V1.077zM4.09 4a9.267 9.267 0 0 1 .64-1.539 6.7 6.7 0 0 1 .597-.933A7.025 7.025 0 0 0 2.255 4H4.09zm-.582 3.5c.03-.877.138-1.718.312-2.5H1.674a6.958 6.958 0 0 0-.656 2.5h2.49zM4.847 5a12.5 12.5 0 0 0-.338 2.5H7.5V5H4.847zM8.5 5v2.5h2.99a12.495 12.495 0 0 0-.337-2.5H8.5zM4.51 8.5a12.5 12.5 0 0 0 .337 2.5H7.5V8.5H4.51zm3.99 0V11h2.653c.187-.765.306-1.608.338-2.5H8.5zM5.145 12c.138.386.295.744.468 1.068.552 1.035 1.218 1.65 1.887 1.855V12H5.145zm.182 2.472a6.696 6.696 0 0 1-.597-.933A9.268 9.268 0 0 1 4.09 12H2.255a7.024 7.024 0 0 0 3.072 2.472zM3.82 11a13.652 13.652 0 0 1-.312-2.5h-2.49c.062.89.291 1.733.656 2.5H3.82zm6.853 3.472A7.024 7.024 0 0 0 13.745 12H11.91a9.27 9.27 0 0 1-.64 1.539 6.688 6.688 0 0 1-.597.933zM8.5 12v2.923c.67-.204 1.335-.82 1.887-1.855.173-.324.33-.682.468-1.068H8.5zm3.68-1h2.146c.365-.767.594-1.61.656-2.5h-2.49a13.65 13.65 0 0 1-.312 2.5zm2.802-3.5a6.959 6.959 0 0 0-.656-2.5H12.18c.174.782.282 1.623.312 2.5h2.49zM11.27 2.461c.247.464.462.98.64 1.539h1.835a7.024 7.024 0 0 0-3.072-2.472c.218.284.418.598.597.933zM10.855 4a7.966 7.966 0 0 0-.468-1.068C9.835 1.897 9.17 1.282 8.5 1.077V4h2.355z"/>
                </svg>
              </span>
              <select
                id="country"
                name="country"
                className="form-input"
                value={formData.country}
                onChange={handleChange}
                required
              >
                <option value="France">France</option>
                <option value="Belgium">Belgium</option>
                <option value="Switzerland">Switzerland</option>
                <option value="Germany">Germany</option>
                <option value="Italy">Italy</option>
                <option value="Spain">Spain</option>
                <option value="United Kingdom">United Kingdom</option>
              </select>
            </div>
          </div>

          <div className="form-group">
            <label htmlFor="meterNumber">PDL/PRM/RAE Number <span className="required">*</span></label>
            <div className="input-with-icon">
              <span className="input-icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                  <path d="M8 1a2 2 0 0 1 2 2v4H6V3a2 2 0 0 1 2-2zm3 6V3a3 3 0 0 0-6 0v4a2 2 0 0 0-2 2v5a2 2 0 0 0 2 2h6a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2z"/>
                </svg>
              </span>
              <input
                type="text"
                id="meterNumber"
                name="meterNumber"
                className="form-input"
                value={formData.meterNumber}
                onChange={handleChange}
                placeholder="Enter your 14-digit meter number"
                pattern="[0-9]{14}"
                maxLength="14"
                inputMode="numeric"
                required
              />
            </div>
            <div className="hint-text">Your meter identification number (exactly 14 digits)</div>
          </div>
        </div>

        <div className="stepper-buttons">
          <button type="button" className="stepper-button stepper-button-prev" onClick={onPrev}>
            Back
          </button>
          <button type="submit" className="stepper-button stepper-button-next">
            Next
          </button>
        </div>
      </form>
    </div>
  );
};

export default AddressMeterInfoStep;
