import { useState } from 'react';
import { Link } from 'react-router-dom';
import styled from 'styled-components';
import logoImage from '../assets/login-logo.png';
// import backgroundImage from '../assets/background.png';
import '../styles/password-input.css';
import logger from '../utils/logger';

const PageContainer = styled.div`
  position: relative;
  min-height: 100vh;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: 'Inter', sans-serif;
  overflow: auto;
  background-color: #f5f5f5;
  margin: 0;
  padding: 0;
  box-sizing: border-box;

  @media (max-width: 768px) {
    background-position: 65% top !important;
    background-size: cover !important;
  }
`;

const FormContainer = styled.div`
  max-width: 400px;
  width: 90%;
  z-index: 2;
  padding: 0;
  background-color: transparent;
  margin-top: 0;
  position: absolute;
  left: 5%;

  @media (max-width: 768px) {
    position: relative;
    left: 0;
    width: 90%;
    margin: 0 auto;
  }
`;

const InnerContainer = styled.div`
  width: 100%;
  background-color: rgba(245, 250, 255, 0.75);
  border-radius: 15px;
  padding: 15px;
  backdrop-filter: none;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);

  @media (max-width: 768px) {
    background-color: rgba(245, 250, 255, 0.85);
    padding: 20px;
  }
`;

const LogoContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15px;
  gap: 0;
  background-color: transparent;
  padding: 0;
  margin-top: -10px;
`;

const Logo = styled.img`
  width: 75px;
  height: 75px;
  margin-right: -5px;
  filter: brightness(0);

  @media (max-width: 768px) {
    width: 60px;
    height: 60px;
  }
`;

const AppTitle = styled.div`
  color: #000000;
  font-weight: 900;
  font-size: 26px;
  line-height: 1.2;
  text-align: left;
  text-shadow: 0px 0px 2px rgba(0, 0, 0, 0.3);
  margin-left: 0;
  padding-left: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;

  @media (max-width: 768px) {
    font-size: 22px;
  }
`;

const WelcomeText = styled.div`
  text-align: center;
  margin-bottom: 15px;
  margin-top: 0;
  color: #000000;
  background-color: transparent;
  padding: 0;
`;

const Title = styled.h1`
  font-size: 32px;
  font-weight: 900;
  margin-bottom: 5px;
  color: #000000;
  text-shadow: 1px 1px 3px rgba(255, 255, 255, 0.8);

  @media (max-width: 768px) {
    font-size: 28px;
  }
`;

const SubTitle = styled.p`
  font-size: 17px;
  font-weight: 600;
  color: #000000;
  text-shadow: 0.5px 0.5px 2px rgba(255, 255, 255, 0.8);
  margin: 0;

  @media (max-width: 768px) {
    font-size: 15px;
  }
`;

const FormElementsContainer = styled.div`
  padding: 0;
  margin-bottom: 10px;
  background-color: transparent;
`;

const InputWrapper = styled.div`
  margin-bottom: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
`;

const InputContainer = styled.div`
  background-color: white;
  border-radius: 10px;
  display: flex;
  align-items: center;
  padding: 14px 16px;
  border: none;
  margin-bottom: 5px;
  width: 100%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
`;

const IconSpan = styled.span`
  margin-right: 10px;
  color: #000;
`;

const StyledInput = styled.input`
  border: none;
  outline: none;
  width: 100%;
  background-color: transparent;
  font-size: 16px;
  color: #000;
  padding: 0;
  margin: 0;

  &::placeholder {
    color: rgba(0, 0, 0, 0.6);
  }

  /* Hide browser's password reveal icon */
  &::-ms-reveal,
  &::-ms-clear {
    display: none;
  }

  &[type="password"]::-webkit-contacts-auto-fill-button,
  &[type="password"]::-webkit-credentials-auto-fill-button {
    visibility: hidden;
    display: none !important;
    pointer-events: none;
    height: 0;
    width: 0;
    margin: 0;
  }
`;

const LoginButton = styled.button`
  background-color: #1E3D5C;
  color: white;
  border: none;
  border-radius: 10px;
  padding: 14px;
  width: 100%;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  margin-bottom: 10px;
  text-transform: uppercase;
  transition: all 0.3s;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);

  &:hover {
    background-color: #15304a;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.25);
  }
`;

const LinksContainer = styled.div`
  text-align: center;
  background-color: transparent;
  padding: 0;
  margin-top: 5px;
`;

const StyledLink = styled(Link)`
  color: #000000;
  text-decoration: none;
  font-size: 14px;
  display: inline-block;
  margin: 2px 0;
  transition: color 0.3s;
  font-weight: 600;

  &:hover {
    color: #333333;
    text-decoration: underline;
  }
`;

const ErrorMessage = styled.div`
  color: #ff6b6b;
  font-size: 12px;
  margin-top: 5px;
  font-weight: bold;
`;

const AuthForm = ({ type, onSubmit }) => {
  const [formData, setFormData] = useState({
    email: '', // Changed from identifier to email
    password: '',
  });
  const [errors, setErrors] = useState({});
  const [showPassword, setShowPassword] = useState(false);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });

    // Clear errors when user starts typing
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: null
      });
    }

    // Validate email as user types
    if (name === 'email' && value.trim() !== '') {
      const emailRegex = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;

      // Only show error if they've typed something that looks like an email but isn't valid
      if (value.includes('@') && value.includes('.') && !emailRegex.test(value.toLowerCase())) {
        setErrors({
          ...errors,
          email: 'Please enter a valid email address'
        });
      }
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.email) {
      newErrors.email = 'Email address is required';
    } else {
      // Validate email format
      const emailRegex = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
      if (!emailRegex.test(formData.email.toLowerCase())) {
        newErrors.email = 'Please enter a valid email address';
      }
    }

    if (!formData.password) {
      newErrors.password = 'Password is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e) => {
    e.preventDefault();

    if (validateForm()) {
      onSubmit(formData);
    }
  };

  return (
    <PageContainer style={{
      backgroundImage: 'url(/background.png)',
      backgroundPosition: 'center top',
      backgroundSize: '100% auto',
      backgroundRepeat: 'no-repeat',
      backgroundColor: '#f5f5f5',
      width: '100vw',
      minHeight: '100vh',
      maxWidth: '100%',
      margin: 0,
      padding: 0,
      overflow: 'auto',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      position: 'relative'
    }}>
      <FormContainer>
        <LogoContainer>
          <Logo src={logoImage} alt="MY ENERGY BILL Logo" />
          <AppTitle>
            <div>MY ENERGY</div>
            <div>BILL</div>
          </AppTitle>
        </LogoContainer>

        <InnerContainer>
          <form className="auth-form" onSubmit={handleSubmit}>
          <WelcomeText>
            <Title>Welcome Back!</Title>
            <SubTitle>Log in to access your energy insights</SubTitle>
          </WelcomeText>
          <FormElementsContainer>
            {/* Email input */}
            <InputWrapper>
              <InputContainer>
                <IconSpan>
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" className="bi bi-envelope" viewBox="0 0 16 16">
                    <path d="M0 4a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V4Zm2-1a1 1 0 0 0-1 1v.217l7 4.2 7-4.2V4a1 1 0 0 0-1-1H2Zm13 2.383-4.708 2.825L15 11.105V5.383Zm-.034 6.876-5.64-3.471L8 9.583l-1.326-.795-5.64 3.47A1 1 0 0 0 2 13h12a1 1 0 0 0 .966-.741ZM1 11.105l4.708-2.897L1 5.383v5.722Z"/>
                  </svg>
                </IconSpan>
                <StyledInput
                  type="email"
                  id="email"
                  name="email"
                  placeholder="Email or phone number"
                  value={formData.email}
                  onChange={handleChange}
                  required
                />
              </InputContainer>
              {errors.email && <ErrorMessage>{errors.email}</ErrorMessage>}
            </InputWrapper>

            {/* Password input */}
            <InputWrapper>
              <InputContainer>
                <IconSpan>
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" className="bi bi-lock" viewBox="0 0 16 16">
                    <path d="M8 1a2 2 0 0 1 2 2v4H6V3a2 2 0 0 1 2-2zm3 6V3a3 3 0 0 0-6 0v4a2 2 0 0 0-2 2v5a2 2 0 0 0 2 2h6a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2z"/>
                  </svg>
                </IconSpan>
                <StyledInput
                  type={showPassword ? "text" : "password"}
                  id="password"
                  name="password"
                  placeholder="Password"
                  value={formData.password}
                  onChange={handleChange}
                  required
                  autoComplete="current-password"
                  style={{
                    "&::-ms-reveal": { display: "none" },
                    "&::-webkit-credentials-auto-fill-button": { visibility: "hidden", display: "none" }
                  }}
                />
                <IconSpan
                  style={{ cursor: 'pointer' }}
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" className="bi bi-eye-slash" viewBox="0 0 16 16">
                      <path d="M13.359 11.238C15.06 9.72 16 8 16 8s-3-5.5-8-5.5a7.028 7.028 0 0 0-2.79.588l.77.771A5.944 5.944 0 0 1 8 3.5c2.12 0 3.879 1.168 5.168 2.457A13.134 13.134 0 0 1 14.828 8c-.058.087-.122.183-.195.288-.335.48-.83 1.12-1.465 1.755-.165.165-.337.328-.517.486l.708.709z"/>
                      <path d="M11.297 9.176a3.5 3.5 0 0 0-4.474-4.474l.823.823a2.5 2.5 0 0 1 2.829 2.829l.822.822zm-2.943 1.299.822.822a3.5 3.5 0 0 1-4.474-4.474l.823.823a2.5 2.5 0 0 0 2.829 2.829z"/>
                      <path d="M3.35 5.47c-.18.16-.353.322-.518.487A13.134 13.134 0 0 0 1.172 8l.195.288c.335.48.83 1.12 1.465 1.755C4.121 11.332 5.881 12.5 8 12.5c.716 0 1.39-.133 2.02-.36l.77.772A7.029 7.029 0 0 1 8 13.5C3 13.5 0 8 0 8s.939-1.721 2.641-3.238l.708.709zm10.296 8.884-12-12 .708-.708 12 12-.708.708z"/>
                    </svg>
                  ) : (
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" className="bi bi-eye" viewBox="0 0 16 16">
                      <path d="M16 8s-3-5.5-8-5.5S0 8 0 8s3 5.5 8 5.5S16 8 16 8zM1.173 8a13.133 13.133 0 0 1 1.66-2.043C4.12 4.668 5.88 3.5 8 3.5c2.12 0 3.879 1.168 5.168 2.457A13.133 13.133 0 0 1 14.828 8c-.058.087-.122.183-.195.288-.335.48-.83 1.12-1.465 1.755C11.879 11.332 10.119 12.5 8 12.5c-2.12 0-3.879-1.168-5.168-2.457A13.134 13.134 0 0 1 1.172 8z"/>
                      <path d="M8 5.5a2.5 2.5 0 1 0 0 5 2.5 2.5 0 0 0 0-5zM4.5 8a3.5 3.5 0 1 1 7 0 3.5 3.5 0 0 1-7 0z"/>
                    </svg>
                  )}
                </IconSpan>
              </InputContainer>
              {errors.password && <ErrorMessage>{errors.password}</ErrorMessage>}
            </InputWrapper>

            {/* Login button */}
            <LoginButton type="submit">
              LOG IN
            </LoginButton>
          </FormElementsContainer>

          {/* Links */}
          <LinksContainer>
            {type === 'login' ? (
              <>
                <div style={{ marginBottom: '5px' }}>
                  <StyledLink
                    to="/forgot-password"
                    onClick={() => {
                      // Clear any residual authentication state before navigating
                      localStorage.clear();
                      sessionStorage.clear();

                      // Remove any AWS Amplify related keys
                      try {
                        const allKeys = Object.keys(localStorage);
                        const amplifyKeys = allKeys.filter(key =>
                          key.startsWith('CognitoIdentityServiceProvider') ||
                          key.startsWith('amplify-') ||
                          key.startsWith('aws.') ||
                          key.startsWith('AWSCognito')
                        );

                        amplifyKeys.forEach(key => {
                          localStorage.removeItem(key);
                        });
                      } catch (e) {
                        // Silently fail
                      }
                    }}
                  >
                    Forgot password?
                  </StyledLink>
                </div>
                <div>
                  <span style={{ fontSize: '14px', color: '#000000' }}>
                    Don't have an account?{' '}
                  </span>
                  <StyledLink
                    to="/signup"
                    onClick={() => {
                      // Clear any residual authentication state before navigating
                      localStorage.clear();
                      sessionStorage.clear();

                      // Remove any AWS Amplify related keys
                      try {
                        const allKeys = Object.keys(localStorage);
                        const amplifyKeys = allKeys.filter(key =>
                          key.startsWith('CognitoIdentityServiceProvider') ||
                          key.startsWith('amplify-') ||
                          key.startsWith('aws.') ||
                          key.startsWith('AWSCognito')
                        );

                        amplifyKeys.forEach(key => {
                          localStorage.removeItem(key);
                        });
                      } catch (e) {
                        // Silently fail
                      }
                    }}
                  >
                    Create one
                  </StyledLink>
                </div>
              </>
            ) : (
              <div>
                <StyledLink
                  to="/login"
                  onClick={() => {
                    logger.info('Already have an account link clicked, performing cleanup');

                    // Force a complete logout before navigating
                    localStorage.clear();
                    sessionStorage.clear();

                    // Remove any AWS Amplify related keys
                    try {
                      const allKeys = Object.keys(localStorage);
                      const amplifyKeys = allKeys.filter(key =>
                        key.startsWith('CognitoIdentityServiceProvider') ||
                        key.startsWith('amplify-') ||
                        key.startsWith('aws.') ||
                        key.startsWith('AWSCognito')
                      );

                      amplifyKeys.forEach(key => {
                        localStorage.removeItem(key);
                      });

                      console.log(`Removed ${amplifyKeys.length} AWS Amplify related keys`);
                    } catch (e) {
                      console.error('Error removing Amplify keys:', e);
                    }

                    // Force Cognito signout
                    try {
                      const Auth = require('@aws-amplify/auth').default;
                      Auth.signOut({ global: true })
                        .catch(e => console.error('Error in forced signout:', e));
                    } catch (e) {
                      console.error('Error importing Auth:', e);
                    }

                    // Set a flag in sessionStorage to indicate we're coming from a clean logout
                    sessionStorage.setItem('clean_login', 'true');
                  }}
                >
                  Already have an account?
                </StyledLink>
              </div>
            )}
          </LinksContainer>
        </form>
        </InnerContainer>
      </FormContainer>
    </PageContainer>
  );
};

export default AuthForm;
