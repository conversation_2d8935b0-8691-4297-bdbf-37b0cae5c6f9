import { useState } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import logoImage from '../assets/logo.jpeg';
import { useAuth } from '../context/AuthContext';
import { showSuccessMessage, showErrorMessage } from '../utils/toastNotifications';
import '../styles/app.css';

const Navbar = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const [menuOpen, setMenuOpen] = useState(false);
  const [userMenuOpen, setUserMenuOpen] = useState(false);
  const [notificationsOpen, setNotificationsOpen] = useState(false);
  const [notifications, setNotifications] = useState([
    { id: 1, message: "Your energy bill request has been processed", read: false, date: "2023-05-01" },
    { id: 2, message: "New offer available for your electricity contract", read: false, date: "2023-04-28" },
    { id: 3, message: "Your account information has been updated", read: true, date: "2023-04-25" }
  ]);

  const toggleMenu = () => {
    setMenuOpen(!menuOpen);
  };

  const toggleUserMenu = () => {
    setUserMenuOpen(!userMenuOpen);
    if (notificationsOpen) setNotificationsOpen(false);
  };

  const toggleNotifications = () => {
    setNotificationsOpen(!notificationsOpen);
    if (userMenuOpen) setUserMenuOpen(false);
  };

  // Handle navigation with menu closing
  const handleNavigation = (path) => {
    // Close menus
    setUserMenuOpen(false);
    setNotificationsOpen(false);

    // Navigate to the path
    navigate(path);
  };

  const markAllAsRead = () => {
    setNotifications(notifications.map(notification => ({
      ...notification,
      read: true
    })));
  };

  const { signOut } = useAuth();

  const handleLogout = async () => {
    try {
      console.log('Navbar: Logging out user');

      // Use the signOut function from AuthContext
      await signOut();

      // Force clear all localStorage data to ensure clean logout
      localStorage.clear();

      console.log('Navbar: Successfully logged out, redirecting to login page');

      // Show success message
      showSuccessMessage('LOGOUT_SUCCESS');

      // Redirect to login page with a flag indicating we came from logout
      navigate('/login', { state: { fromLogout: true } });
    } catch (error) {
      console.error('Navbar: Error during logout:', error);

      // Show error message
      showErrorMessage('UNEXPECTED_ERROR', 'Logout completed, but there was an issue. You have been signed out.');

      // Even if there's an error with Cognito, clear localStorage
      localStorage.clear();

      // Redirect to login with the same flag
      navigate('/login', { state: { fromLogout: true } });
    }
  };

  // Check if we're on the registration flow or onboarding pages
  const isRegistrationOrOnboardingPage = [
    '/user-type',
    '/individual-info',
    '/professional-info',
    '/upload-first-invoice'  // Add the upload-first-invoice page
  ].includes(location.pathname);

  // For debugging
  console.log('Current path:', location.pathname, 'isRegistrationOrOnboardingPage:', isRegistrationOrOnboardingPage);

  return (
    <nav className="navbar">
      <div className="navbar-logo" style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', whiteSpace: 'nowrap' }}>
        {isRegistrationOrOnboardingPage ? (
          <>
            <img src={logoImage} alt="MY ENERGY BILL Logo" className="navbar-logo-img" />
            <span className="navbar-brand">MY ENERGY BILL</span>
          </>
        ) : (
          <span className="navbar-brand">Dashboard</span>
        )}
      </div>

      {/* Removed Dashboard and New Request links from top navigation */}

      <div className="navbar-right">
        {/* Notification Icon */}
        <div className="notification-container">
          <button
            className="notification-button"
            onClick={toggleNotifications}
            aria-label="Notifications"
          >
            <div className="notification-icon-wrapper">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 16 16">
                <path d="M8 16a2 2 0 0 0 2-2H6a2 2 0 0 0 2 2zm.995-14.901a1 1 0 1 0-1.99 0A5.002 5.002 0 0 0 3 6c0 1.098-.5 6-2 7h14c-1.5-1-2-5.902-2-7 0-2.42-1.72-4.44-4.005-4.901z"/>
              </svg>
              {notifications.some(n => !n.read) && (
                <span className="notification-badge"></span>
              )}
            </div>
          </button>

          {notificationsOpen && (
            <div className="notification-dropdown">
              <div className="notification-header">
                <h3>Notifications</h3>
                <button className="mark-read-button" onClick={markAllAsRead}>
                  Mark all as read
                </button>
              </div>

              <div className="notification-list">
                {notifications.length > 0 ? (
                  notifications.map(notification => (
                    <div
                      key={notification.id}
                      className={`notification-item ${notification.read ? 'read' : 'unread'}`}
                    >
                      <div className="notification-content">
                        <p>{notification.message}</p>
                        <span className="notification-date">{notification.date}</span>
                      </div>
                      {!notification.read && <div className="unread-indicator"></div>}
                    </div>
                  ))
                ) : (
                  <div className="no-notifications">
                    <p>No notifications</p>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* User Menu */}
        <div className="user-menu-container">
          <button
            className="user-menu-button"
            onClick={toggleUserMenu}
            aria-label="User menu"
          >
            <div className="user-icon-wrapper">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="#666" viewBox="0 0 16 16">
                <path d="M11 6a3 3 0 1 1-6 0 3 3 0 0 1 6 0z"/>
                <path fillRule="evenodd" d="M0 8a8 8 0 1 1 16 0A8 8 0 0 1 0 8zm8-7a7 7 0 0 0-5.468 11.37C3.242 11.226 4.805 10 8 10s4.757 1.225 5.468 2.37A7 7 0 0 0 8 1z"/>
              </svg>
            </div>
          </button>

          {userMenuOpen && (
            <div className="user-dropdown">
              <button className="dropdown-item" onClick={() => handleNavigation('/profile')}>
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="#666" viewBox="0 0 16 16">
                  <path d="M11 6a3 3 0 1 1-6 0 3 3 0 0 1 6 0z"/>
                  <path fillRule="evenodd" d="M0 8a8 8 0 1 1 16 0A8 8 0 0 1 0 8zm8-7a7 7 0 0 0-5.468 11.37C3.242 11.226 4.805 10 8 10s4.757 1.225 5.468 2.37A7 7 0 0 0 8 1z"/>
                </svg>
                <span>My Profile</span>
              </button>
              <button className="dropdown-item" onClick={() => handleNavigation('/settings')}>
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="#666" viewBox="0 0 16 16">
                  <path d="M8 4.754a3.246 3.246 0 1 0 0 6.492 3.246 3.246 0 0 0 0-6.492zM5.754 8a2.246 2.246 0 1 1 4.492 0 2.246 2.246 0 0 1-4.492 0z"/>
                  <path d="M9.796 1.343c-.527-1.79-3.065-1.79-3.592 0l-.094.319a.873.873 0 0 1-1.255.52l-.292-.16c-1.64-.892-3.433.902-2.54 2.541l.159.292a.873.873 0 0 1-.52 1.255l-.319.094c-1.79.527-1.79 3.065 0 3.592l.319.094a.873.873 0 0 1 .52 1.255l-.16.292c-.892 1.64.901 3.434 2.541 2.54l.292-.159a.873.873 0 0 1 1.255.52l.094.319c.527 1.79 3.065 1.79 3.592 0l.094-.319a.873.873 0 0 1 1.255-.52l.292.16c1.64.893 3.434-.902 2.54-2.541l-.159-.292a.873.873 0 0 1 .52-1.255l.319-.094c1.79-.527 1.79-3.065 0-3.592l-.319-.094a.873.873 0 0 1-.52-1.255l.16-.292c.893-1.64-.902-3.433-2.541-2.54l-.292.159a.873.873 0 0 1-1.255-.52l-.094-.319z"/>
                </svg>
                <span>Settings</span>
              </button>
              <div className="dropdown-divider"></div>
              <button className="dropdown-item" onClick={handleLogout}>
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                  <path fillRule="evenodd" d="M10 12.5a.5.5 0 0 1-.5.5h-8a.5.5 0 0 1-.5-.5v-9a.5.5 0 0 1 .5-.5h8a.5.5 0 0 1 .5.5v2a.5.5 0 0 0 1 0v-2A1.5 1.5 0 0 0 9.5 2h-8A1.5 1.5 0 0 0 0 3.5v9A1.5 1.5 0 0 0 1.5 14h8a1.5 1.5 0 0 0 1.5-1.5v-2a.5.5 0 0 0-1 0v2z"/>
                  <path fillRule="evenodd" d="M15.854 8.354a.5.5 0 0 0 0-.708l-3-3a.5.5 0 0 0-.708.708L14.293 7.5H5.5a.5.5 0 0 0 0 1h8.793l-2.147 2.146a.5.5 0 0 0 .708.708l3-3z"/>
                </svg>
                <span>Logout</span>
              </button>
            </div>
          )}
        </div>

        {/* Hamburger menu removed as we have profile and logout in user icon */}
      </div>

      {/* Mobile menu removed as we have profile and logout in user icon */}
    </nav>
  );
};

export default Navbar;
