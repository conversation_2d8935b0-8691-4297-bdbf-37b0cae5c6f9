const dotenv = require('dotenv');
const path = require('path');

// Load environment variables
dotenv.config({
  path: path.resolve(__dirname, '.env.uat'),
  override: false,
});

const AWS = require('aws-sdk');

async function checkSESInMultipleRegions() {
  console.log('🌍 Checking SES verified emails across multiple regions...\n');

  const regions = ['eu-west-3', 'eu-west-1', 'us-east-1', 'us-west-2'];
  const emailToCheck = process.env.FROM_EMAIL || '<EMAIL>';

  console.log(`🔍 Looking for: ${emailToCheck}\n`);

  for (const region of regions) {
    console.log(`📍 Checking region: ${region}`);
    
    try {
      const ses = new AWS.SES({
        region: region,
        accessKeyId: process.env.AWS_ACCESS_KEY_ID,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
      });

      // Check verified emails
      const verifiedEmails = await ses.listVerifiedEmailAddresses().promise();
      console.log(`   📧 Verified emails: ${verifiedEmails.VerifiedEmailAddresses.length}`);
      
      if (verifiedEmails.VerifiedEmailAddresses.includes(emailToCheck)) {
        console.log(`   ✅ FOUND! ${emailToCheck} is verified in ${region}`);
      } else {
        console.log(`   ❌ ${emailToCheck} not found in ${region}`);
      }

      // Check verified domains
      try {
        const identities = await ses.listIdentities({ IdentityType: 'Domain' }).promise();
        console.log(`   🌐 Verified domains: ${identities.Identities.join(', ') || 'None'}`);
        
        // Check if the domain of our email is verified
        const emailDomain = emailToCheck.split('@')[1];
        if (identities.Identities.includes(emailDomain)) {
          console.log(`   ✅ DOMAIN VERIFIED! ${emailDomain} is verified in ${region}`);
        }
      } catch (domainError) {
        console.log(`   ⚠️ Could not check domains: ${domainError.message}`);
      }

      // Check SES sending quota
      try {
        const quota = await ses.getSendQuota().promise();
        console.log(`   📊 Sending quota: ${quota.SentLast24Hours}/${quota.Max24HourSend} (Rate: ${quota.MaxSendRate}/sec)`);
      } catch (quotaError) {
        console.log(`   ⚠️ Could not check quota: ${quotaError.message}`);
      }

    } catch (error) {
      console.log(`   ❌ Error checking ${region}: ${error.message}`);
    }
    
    console.log(''); // Empty line for readability
  }

  console.log('🔧 Recommendations:');
  console.log('1. If email found in different region, update AWS_REGION in .env.uat');
  console.log('2. If domain is verified, you can use any email from that domain');
  console.log('3. If not found anywhere, the email needs to be verified');
  console.log('4. Check if you\'re using the correct AWS credentials');
}

// Run the check if this file is executed directly
if (require.main === module) {
  checkSESInMultipleRegions()
    .then(() => {
      console.log('\n✨ Region check completed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Check failed:', error);
      process.exit(1);
    });
}

module.exports = { checkSESInMultipleRegions };
