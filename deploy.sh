#!/bin/bash
# Linux-compatible deployment script

echo "Starting deployment process..."

# Build frontend
echo "Building frontend..."
cd frontend
export NODE_ENV=development
npm run build:linux

# Check if frontend build was successful
if [ $? -ne 0 ]; then
  echo "Frontend build failed!"
  exit 1
fi
cd ..

# Build backend
echo "Building backend..."
cd backend
npm run build

# Check if backend build was successful
if [ $? -ne 0 ]; then
  echo "Backend build failed!"
  exit 1
fi
cd ..

echo "Deployment build completed successfully!"
echo "You can now deploy the application to your hosting provider."

echo "Frontend files are in: frontend/dist"
echo "Backend files are in: backend/dist"
