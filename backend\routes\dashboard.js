const express = require('express');
const router = express.Router();
const mongoose = require('mongoose');
const Invoice = require('../models/Invoice');
const Offer = require('../models/Offer');
const Contract = require('../models/Contract');
const Appointment = require('../models/Appointment');
const User = require('../models/User');
const logger = require('../utils/logger');
const dashboardController = require('../controllers/dashboardController');

// Get dashboard statistics for a user
router.get('/stats/:cognitoId', dashboardController.getDashboardStats);

// Get invoice count for a user
router.get('/invoices/count/:cognitoId', dashboardController.getInvoiceCount);

// Get offer count for a user
router.get('/offers/count/:cognitoId', dashboardController.getOfferCount);

// Get contract count for a user
router.get('/contracts/count/:cognitoId', dashboardController.getContractCount);

// Get appointment count for a user
router.get('/appointments/count/:cognitoId', dashboardController.getAppointmentCount);

module.exports = router;
