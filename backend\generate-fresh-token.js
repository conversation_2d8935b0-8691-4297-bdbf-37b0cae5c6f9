const jwt = require('jsonwebtoken');
const dotenv = require('dotenv');
const path = require('path');

// Load environment variables
dotenv.config({
  path: path.resolve(__dirname, '.env.local'),
  override: false,
});

const jwtSecret = process.env.JWT_SECRET || 'temporary_jwt_secret_for_development';

// Generate a fresh token for the same user
const payload = {
  email: '<EMAIL>',
  userId: '685e6591080bb473ffee39ad',
  type: 'password_reset',
  timestamp: Date.now()
};

const freshToken = jwt.sign(payload, jwtSecret, {
  expiresIn: '1h',
  issuer: 'energy-platform',
  audience: 'password-reset'
});

console.log('Fresh password reset token generated:');
console.log('');
console.log(freshToken);
console.log('');
console.log('Test URL:');
console.log(`http://localhost:8080/reset-password?token=${freshToken}&email=${encodeURIComponent('<EMAIL>')}`);
console.log('');

// Verify the fresh token works
try {
  const verified = jwt.verify(freshToken, jwtSecret, {
    issuer: 'energy-platform',
    audience: 'password-reset'
  });
  console.log('✅ Fresh token verification successful!');
  console.log('Email:', verified.email);
  console.log('Expires:', new Date(verified.exp * 1000));
} catch (error) {
  console.log('❌ Fresh token verification failed:', error.message);
}
