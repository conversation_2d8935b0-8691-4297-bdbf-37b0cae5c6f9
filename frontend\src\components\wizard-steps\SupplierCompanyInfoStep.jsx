import React from 'react';
import { showErrorMessage } from '../../utils/toastNotifications';

const SupplierCompanyInfoStep = ({ formData, onChange, onNext, onCancel }) => {
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    onChange(name, value);
  };

  const handleNext = () => {
    // Validate required fields
    if (!formData.companyName || !formData.licenseNumber) {
      showErrorMessage('VALIDATION_FAILED', 'Please fill in all required fields');
      return;
    }
    onNext();
  };

  return (
    <div className="step-content">
      <div className="stepper-header">
        <h2 className="page-title">Company Information</h2>
        <p className="page-subtitle">Tell us about your energy supply company</p>
      </div>

      <div className="stepper-form">
        <div className="form-row">
          <div className="form-group">
            <label className="form-label" htmlFor="companyName">Company Name <span className="required">*</span></label>
            <input
              type="text"
              id="companyName"
              name="companyName"
              className="form-input"
              value={formData.companyName}
              onChange={handleInputChange}
              placeholder="Enter your company name"
              required
            />
          </div>
        </div>

        <div className="form-row">
          <div className="form-group">
            <label className="form-label" htmlFor="licenseNumber">SIRET Number <span className="required">*</span></label>
            <input
              type="text"
              id="licenseNumber"
              name="licenseNumber"
              className="form-input"
              value={formData.licenseNumber}
              onChange={handleInputChange}
              placeholder="Enter your SIRET number"
              required
            />
          </div>
        </div>

        <div className="form-row">
          <div className="form-group">
            <label className="form-label" htmlFor="website">Website <span className="optional">(optional)</span></label>
            <input
              type="url"
              id="website"
              name="website"
              className="form-input"
              value={formData.website}
              onChange={handleInputChange}
              placeholder="https://your-company.com"
            />
          </div>
        </div>

        <div className="form-row">
          <div className="form-group">
            <label className="form-label" htmlFor="phone">Business Phone <span className="optional">(optional)</span></label>
            <input
              type="tel"
              id="phone"
              name="phone"
              className="form-input"
              value={formData.phone}
              onChange={handleInputChange}
              placeholder="+33 1 23 45 67 89"
            />
          </div>
        </div>

        <div className="form-row">
          <div className="form-group">
            <label className="form-label" htmlFor="vatNumber">VAT Number <span className="optional">(optional)</span></label>
            <input
              type="text"
              id="vatNumber"
              name="vatNumber"
              className="form-input"
              value={formData.vatNumber}
              onChange={handleInputChange}
              placeholder="FR12345678901"
            />
          </div>
        </div>

        <div className="form-row">
          <div className="form-group">
            <label className="form-label" htmlFor="businessDescription">Business Description <span className="optional">(optional)</span></label>
            <textarea
              id="businessDescription"
              name="businessDescription"
              className="form-input"
              value={formData.businessDescription}
              onChange={handleInputChange}
              placeholder="Describe your energy supply business..."
              rows="3"
            />
          </div>
        </div>

        <div className="form-row">
          <div className="form-group">
            <label className="form-label" htmlFor="companyAddress.street">Street Address <span className="optional">(optional)</span></label>
            <input
              type="text"
              id="companyAddress.street"
              name="companyAddress.street"
              className="form-input"
              value={formData.companyAddress.street}
              onChange={handleInputChange}
              placeholder="Street address"
            />
          </div>
        </div>

        <div className="form-row">
          <div className="form-group">
            <label className="form-label" htmlFor="companyAddress.city">City <span className="optional">(optional)</span></label>
            <input
              type="text"
              id="companyAddress.city"
              name="companyAddress.city"
              className="form-input"
              value={formData.companyAddress.city}
              onChange={handleInputChange}
              placeholder="City"
            />
          </div>
        </div>

        <div className="form-row">
          <div className="form-group">
            <label className="form-label" htmlFor="companyAddress.postalCode">Postal Code <span className="optional">(optional)</span></label>
            <input
              type="text"
              id="companyAddress.postalCode"
              name="companyAddress.postalCode"
              className="form-input"
              value={formData.companyAddress.postalCode}
              onChange={handleInputChange}
              placeholder="75001"
            />
          </div>
        </div>
      </div>

      <div className="stepper-buttons">
        <button type="button" className="stepper-button stepper-button-prev" onClick={onCancel}>
          Cancel
        </button>
        <button type="button" className="stepper-button stepper-button-next" onClick={handleNext}>
          Next <i className="fas fa-arrow-right"></i>
        </button>
      </div>
    </div>
  );
};

export default SupplierCompanyInfoStep;
