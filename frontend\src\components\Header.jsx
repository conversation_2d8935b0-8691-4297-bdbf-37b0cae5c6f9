import { useState, useEffect, useRef } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Auth } from 'aws-amplify';
import { showSuccessMessage, showErrorMessage } from '../utils/toastNotifications';
import '../styles/header.css';

const Header = ({ toggleSidebar }) => {
  const [userMenuOpen, setUserMenuOpen] = useState(false);
  const [notificationsOpen, setNotificationsOpen] = useState(false);
  const [userInfo, setUserInfo] = useState({
    firstName: '',
    lastName: '',
    email: ''
  });
  const [pageTitle, setPageTitle] = useState('Dashboard');
  const [isMobile, setIsMobile] = useState(window.innerWidth < 992);
  const userMenuRef = useRef(null);
  const notificationsRef = useRef(null);
  const navigate = useNavigate();
  const location = useLocation();

  // Check if we're on mobile
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 992);
    };

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  useEffect(() => {
    // Get user info from localStorage
    const firstName = localStorage.getItem('firstName') || '';
    const lastName = localStorage.getItem('lastName') || '';
    const email = localStorage.getItem('email') || '';

    setUserInfo({
      firstName,
      lastName,
      email
    });

    // Close menus when clicking outside
    const handleClickOutside = (event) => {
      if (userMenuRef.current && !userMenuRef.current.contains(event.target)) {
        setUserMenuOpen(false);
      }

      if (notificationsRef.current && !notificationsRef.current.contains(event.target)) {
        setNotificationsOpen(false);
      }
    };

    const handleScroll = () => {
      if (userMenuOpen) {
        updateDropdownPosition();
      }
    };

    const handleResize = () => {
      if (userMenuOpen) {
        updateDropdownPosition();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    window.addEventListener('scroll', handleScroll, true);
    window.addEventListener('resize', handleResize);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      window.removeEventListener('scroll', handleScroll, true);
      window.removeEventListener('resize', handleResize);
    };
  }, [userMenuOpen]);

  // Update page title based on current route
  useEffect(() => {
    const path = location.pathname;

    if (path === '/dashboard') {
      setPageTitle('Dashboard');
    } else if (path === '/invoices') {
      setPageTitle('My Invoices');
    } else if (path === '/offers') {
      setPageTitle('Personalized Offers');
    } else if (path === '/contracts') {
      setPageTitle('My Contracts');
    } else if (path === '/appointments') {
      setPageTitle('My Appointments');
    } else if (path === '/profile') {
      setPageTitle('My Profile');
    } else if (path === '/settings') {
      setPageTitle('Settings');
    } else if (path === '/support') {
      setPageTitle('Help & Support');
    } else if (path === '/clients') {
      setPageTitle('My Clients');
    } else if (path === '/analytics') {
      setPageTitle('Analytics');
    }
  }, [location.pathname]);





  const toggleUserMenu = () => {
    setUserMenuOpen(!userMenuOpen);
    if (notificationsOpen) setNotificationsOpen(false);
  };

  const toggleNotifications = () => {
    setNotificationsOpen(!notificationsOpen);
    if (userMenuOpen) setUserMenuOpen(false);
  };

  // Handle navigation with menu closing
  const handleNavigation = (path) => {
    // Close menus
    setUserMenuOpen(false);
    setNotificationsOpen(false);

    // Navigate to the path
    navigate(path);
  };

  const handleLogout = async () => {
    try {
      // Sign out with Cognito
      await Auth.signOut();

      // Clear ALL localStorage data
      localStorage.clear();

      // Show success message
      showSuccessMessage('LOGOUT_SUCCESS');

      // Redirect to login page with a flag indicating we came from logout
      navigate('/login', { state: { fromLogout: true }, replace: true });
    } catch (error) {
      // Show error message
      showErrorMessage('UNEXPECTED_ERROR', 'Logout completed, but there was an issue. You have been signed out.');

      // Even if there's an error with Cognito, clear localStorage and redirect
      localStorage.clear();
      navigate('/login', { state: { fromLogout: true }, replace: true });
    }
  };

  return (
    <header className="dashboard-header">
      <div className="header-left">
        {/* Mobile sidebar toggle button */}
        {isMobile && (
          <button
            className="mobile-sidebar-toggle"
            onClick={toggleSidebar}
            aria-label="Toggle Sidebar"
          >
            <i className="fas fa-bars"></i>
          </button>
        )}

        {/* Page title */}
        <div className="header-title-container">
          <h1 className="page-title">{pageTitle}</h1>
        </div>
      </div>

      <div className="header-right">
        {/* Notifications */}
        <div className="notifications-container" ref={notificationsRef}>
          <button
            className={`notifications-button ${notificationsOpen ? 'active' : ''}`}
            onClick={toggleNotifications}
            aria-label="Notifications"
          >
            <div className="notification-icon-wrapper">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="#666" viewBox="0 0 16 16">
                <path d="M8 16a2 2 0 0 0 2-2H6a2 2 0 0 0 2 2zm.995-14.901a1 1 0 1 0-1.99 0A5.002 5.002 0 0 0 3 6c0 1.098-.5 6-2 7h14c-1.5-1-2-5.902-2-7 0-2.42-1.72-4.44-4.005-4.901z"/>
              </svg>
              <span className="notification-badge"></span>
            </div>
          </button>

          {notificationsOpen && (
            <div className="notifications-dropdown">
              <div className="notifications-header">
                <h3>Notifications</h3>
                <button className="mark-all-read">Mark all as read</button>
              </div>

              <div className="notifications-list">
                <div className="notification-item unread">
                  <div className="notification-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="#666" viewBox="0 0 16 16">
                      <path d="M6 4.5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0zm-1 0a.5.5 0 1 0-1 0 .5.5 0 0 0 1 0z"/>
                      <path d="M2 1h4.586a1 1 0 0 1 .707.293l7 7a1 1 0 0 1 0 1.414l-4.586 4.586a1 1 0 0 1-1.414 0l-7-7A1 1 0 0 1 1 6.586V2a1 1 0 0 1 1-1zm0 5.586 7 7L13.586 9l-7-7H2v4.586z"/>
                    </svg>
                  </div>
                  <div className="notification-content">
                    <p>New offer available for your energy needs!</p>
                    <span className="notification-time">2 hours ago</span>
                  </div>
                </div>

                <div className="notification-item unread">
                  <div className="notification-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="#666" viewBox="0 0 16 16">
                      <path d="M5.5 7a.5.5 0 0 0 0 1h5a.5.5 0 0 0 0-1h-5zM5 9.5a.5.5 0 0 1 .5-.5h5a.5.5 0 0 1 0 1h-5a.5.5 0 0 1-.5-.5zm0 2a.5.5 0 0 1 .5-.5h2a.5.5 0 0 1 0 1h-2a.5.5 0 0 1-.5-.5z"/>
                      <path d="M9.5 0H4a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V4.5L9.5 0zm0 1v2A1.5 1.5 0 0 0 11 4.5h2V14a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1h5.5z"/>
                    </svg>
                  </div>
                  <div className="notification-content">
                    <p>Your invoice has been processed successfully.</p>
                    <span className="notification-time">1 day ago</span>
                  </div>
                </div>

                <div className="notification-item">
                  <div className="notification-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="#666" viewBox="0 0 16 16">
                      <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
                      <path d="m8.93 6.588-2.29.287-.082.38.45.083c.294.07.352.176.288.469l-.738 3.468c-.194.897.105 1.319.808 1.319.545 0 1.178-.252 1.465-.598l.088-.416c-.2.176-.492.246-.686.246-.275 0-.375-.193-.304-.533L8.93 6.588zM9 4.5a1 1 0 1 1-2 0 1 1 0 0 1 2 0z"/>
                    </svg>
                  </div>
                  <div className="notification-content">
                    <p>Your profile information has been updated.</p>
                    <span className="notification-time">3 days ago</span>
                  </div>
                </div>
              </div>

              <div className="notifications-footer">
                <button onClick={() => handleNavigation('/notifications')}>
                  View all notifications
                </button>
              </div>
            </div>
          )}
        </div>

        {/* User menu */}
        <div className="user-menu-container" ref={userMenuRef}>
          <button
            className={`user-menu-button ${userMenuOpen ? 'active' : ''}`}
            onClick={toggleUserMenu}
          >
            <div className="user-icon-wrapper">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="#666" viewBox="0 0 16 16">
                <path d="M11 6a3 3 0 1 1-6 0 3 3 0 0 1 6 0z"/>
                <path fillRule="evenodd" d="M0 8a8 8 0 1 1 16 0A8 8 0 0 1 0 8zm8-7a7 7 0 0 0-5.468 11.37C3.242 11.226 4.805 10 8 10s4.757 1.225 5.468 2.37A7 7 0 0 0 8 1z"/>
              </svg>
            </div>
          </button>

          {userMenuOpen && (
            <div className="user-dropdown">
              <div className="user-info">
                <div className="user-name">
                  {userInfo.firstName} {userInfo.lastName}
                </div>
                <div className="user-email">{userInfo.email}</div>
              </div>

              <div className="user-menu-items">
                <button onClick={() => handleNavigation('/profile')}>
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="#666" viewBox="0 0 16 16">
                    <path d="M11 6a3 3 0 1 1-6 0 3 3 0 0 1 6 0z"/>
                    <path fillRule="evenodd" d="M0 8a8 8 0 1 1 16 0A8 8 0 0 1 0 8zm8-7a7 7 0 0 0-5.468 11.37C3.242 11.226 4.805 10 8 10s4.757 1.225 5.468 2.37A7 7 0 0 0 8 1z"/>
                  </svg> My Profile
                </button>
                <button onClick={() => handleNavigation('/settings')}>
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="#666" viewBox="0 0 16 16">
                    <path d="M8 4.754a3.246 3.246 0 1 0 0 6.492 3.246 3.246 0 0 0 0-6.492zM5.754 8a2.246 2.246 0 1 1 4.492 0 2.246 2.246 0 0 1-4.492 0z"/>
                    <path d="M9.796 1.343c-.527-1.79-3.065-1.79-3.592 0l-.094.319a.873.873 0 0 1-1.255.52l-.292-.16c-1.64-.892-3.433.902-2.54 2.541l.159.292a.873.873 0 0 1-.52 1.255l-.319.094c-1.79.527-1.79 3.065 0 3.592l.319.094a.873.873 0 0 1 .52 1.255l-.16.292c-.892 1.64.901 3.434 2.541 2.54l.292-.159a.873.873 0 0 1 1.255.52l.094.319c.527 1.79 3.065 1.79 3.592 0l.094-.319a.873.873 0 0 1 1.255-.52l.292.16c1.64.893 3.434-.902 2.54-2.541l-.159-.292a.873.873 0 0 1 .52-1.255l.319-.094c1.79-.527 1.79-3.065 0-3.592l-.319-.094a.873.873 0 0 1-.52-1.255l.16-.292c.893-1.64-.902-3.433-2.541-2.54l-.292.159a.873.873 0 0 1-1.255-.52l-.094-.319z"/>
                  </svg> Settings
                </button>
                <button onClick={handleLogout}>
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="#666" viewBox="0 0 16 16">
                    <path fillRule="evenodd" d="M10 12.5a.5.5 0 0 1-.5.5h-8a.5.5 0 0 1-.5-.5v-9a.5.5 0 0 1 .5-.5h8a.5.5 0 0 1 .5.5v2a.5.5 0 0 0 1 0v-2A1.5 1.5 0 0 0 9.5 2h-8A1.5 1.5 0 0 0 0 3.5v9A1.5 1.5 0 0 0 1.5 14h8a1.5 1.5 0 0 0 1.5-1.5v-2a.5.5 0 0 0-1 0v2z"/>
                    <path fillRule="evenodd" d="M15.854 8.354a.5.5 0 0 0 0-.708l-3-3a.5.5 0 0 0-.708.708L14.293 7.5H5.5a.5.5 0 0 0 0 1h8.793l-2.147 2.146a.5.5 0 0 0 .708.708l3-3z"/>
                  </svg> Logout
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </header>
  );
};

export default Header;
