.sidebar {
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  width: 250px;
  background-color: #fff;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
  will-change: transform, width;
}

.sidebar-closed .sidebar {
  width: 60px;
  overflow: visible; /* Allow the toggle button to overflow */
}

/* Overlay for mobile when sidebar is open */
.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
  display: none;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none; /* Ensure it doesn't block clicks when not active */
}

.sidebar-overlay.active {
  opacity: 1;
  pointer-events: auto; /* Allow clicks when active */
}

/* Sidebar header */
.sidebar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 15px;
  border-bottom: 1px solid #eee;
  height: 60px; /* Match dashboard header height */
  position: relative; /* For absolute positioning of toggle button */
  overflow: visible; /* Allow toggle button to overflow */
}

.sidebar-closed .sidebar-header {
  padding: 0;
  justify-content: center;
  height: 60px; /* Match dashboard header height */
  display: flex;
  align-items: center;
}

.sidebar-logo {
  display: flex;
  align-items: center;
  padding: 5px 0;
  transition: all 0.3s ease;
  margin-left: 5px;
}

.sidebar-logo img {
  width: 36px;
  height: 36px;
  margin-right: 6px;
  object-fit: contain;
  background-color: transparent;
}

.sidebar-logo span {
  font-weight: 600;
  font-size: 16px;
  color: #000;
  white-space: nowrap;
  opacity: 1;
  transition: opacity 0.3s ease;
}

.sidebar-closed .sidebar-logo {
  justify-content: center;
  margin: 0 auto;
  width: 60px;
  padding: 0;
  display: flex;
  align-items: center;
}

.sidebar-closed .sidebar-logo img {
  margin: 0 auto;
  width: 32px;
  height: 32px;
  display: block;
}

.sidebar-closed .sidebar-logo span {
  opacity: 0;
  width: 0;
  overflow: hidden;
  display: none;
}

.collapsed-toggle-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}

.sidebar-closed .sidebar-toggle-btn {
  position: static;
  background-color: transparent;
  border: none;
  z-index: 1001;
  color: #333;
  width: 28px;
  height: 28px;
  box-shadow: none;
  margin: 0;
  padding: 0;
}

.sidebar-toggle-btn {
  background: none;
  border: 1px solid #ddd;
  color: #666;
  font-size: 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 4px;
  transition: all 0.3s ease;
  margin-left: 10px;
}

.sidebar-toggle-btn:hover {
  color: #000;
  background-color: #f5f5f5;
  border-color: #ccc;
}

.sidebar-toggle-btn.collapsed {
  background-color: transparent;
  color: #333;
  border: none;
  box-shadow: none;
  font-size: 20px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Sidebar content */
.sidebar-content {
  flex: 1;
  overflow-y: auto;
  padding: 10px 0;
}

.sidebar-menu {
  list-style: none;
  padding: 0;
  margin: 0;
}

.sidebar-menu li {
  margin: 5px 0;
}

.sidebar-menu li a,
.sidebar-menu li .sidebar-nav-button,
.sidebar-menu li .sidebar-nav-link {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  color: #333;
  text-decoration: none;
  transition: all 0.2s ease;
  white-space: nowrap;
  overflow: hidden;
  background: none;
  border: none;
  width: 100%;
  text-align: left;
  cursor: pointer;
}

.sidebar-menu li a:hover,
.sidebar-menu li .sidebar-nav-button:hover,
.sidebar-menu li .sidebar-nav-link:hover {
  background-color: #f5f5f5;
}

.sidebar-menu li.active a,
.sidebar-menu li.active .sidebar-nav-button,
.sidebar-menu li.active .sidebar-nav-link {
  background: linear-gradient(to right, #000, #333);
  color: #fff;
  border-radius: 0 30px 30px 0;
}

.sidebar-menu li i,
.sidebar-menu li svg {
  margin-right: 10px;
  font-size: 18px;
  min-width: 20px;
  text-align: center;
  transition: margin 0.3s ease;
}

.sidebar-menu li span {
  opacity: 1;
  transition: opacity 0.3s ease;
}

/* Collapsed sidebar styles */
.sidebar-closed .sidebar-menu li a,
.sidebar-closed .sidebar-menu li .sidebar-nav-button,
.sidebar-closed .sidebar-menu li .sidebar-nav-link {
  padding: 12px 0;
  justify-content: center;
}

.sidebar-closed .sidebar-menu li i,
.sidebar-closed .sidebar-menu li svg {
  margin-right: 0;
  font-size: 20px;
}

.sidebar-closed .sidebar-menu li span {
  opacity: 0;
  width: 0;
  overflow: hidden;
}

.sidebar-closed .sidebar-menu li.active a,
.sidebar-closed .sidebar-menu li.active .sidebar-nav-button,
.sidebar-closed .sidebar-menu li.active .sidebar-nav-link {
  border-radius: 0;
}

/* Divider */
.sidebar-divider {
  height: 1px;
  background-color: #eee;
  margin: 10px 20px;
}

.sidebar-closed .sidebar-divider {
  margin: 10px 5px;
}

/* Sidebar footer */
.sidebar-footer {
  padding: 20px;
  border-top: 1px solid #eee;
}

.sidebar-closed .sidebar-footer {
  padding: 15px 5px;
}

.logout-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 12px;
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  overflow: hidden;
}

.logout-button:hover {
  background: rgba(255, 255, 255, 0.2);
}

.logout-button i,
.logout-button svg {
  margin-right: 10px;
  min-width: 20px;
  transition: margin 0.3s ease;
}

.logout-button span {
  opacity: 1;
  transition: opacity 0.3s ease;
}

.sidebar-closed .logout-button {
  padding: 12px 0;
  justify-content: center;
}

.sidebar-closed .logout-button i,
.sidebar-closed .logout-button svg {
  margin-right: 0;
}

.sidebar-closed .logout-button span {
  opacity: 0;
  width: 0;
  overflow: hidden;
}

/* Responsive styles */
@media (min-width: 992px) {
  /* For desktop */
  .sidebar {
    transform: translateX(0);
    z-index: 98;
  }

  .sidebar-close {
    display: none;
  }

  /* Adjust main content to make space for sidebar */
  .dashboard-container {
    margin-left: 250px;
    width: calc(100% - 250px);
  }
}

@media (max-width: 991px) {
  /* For mobile and tablet */
  .sidebar {
    width: 250px;
    transform: translateX(-100%); /* Ensure it's hidden by default on mobile */
  }

  .sidebar.open {
    transform: translateX(0); /* Show when open class is applied */
  }

  .sidebar-closed .sidebar {
    transform: translateX(-100%);
    width: 250px;
  }

  .sidebar-closed .main-content {
    margin-left: 0;
    width: 100%;
  }

  .sidebar-closed .content-wrapper {
    max-width: 100%;
  }
}

@media (max-width: 480px) {
  .sidebar-toggle-btn {
    width: 28px;
    height: 28px;
    font-size: 14px;
  }

  .sidebar-closed .sidebar-toggle-btn {
    position: static;
  }
}

@media (max-width: 320px) {
  .sidebar-toggle-btn {
    width: 26px;
    height: 26px;
    font-size: 12px;
  }

  .sidebar-closed .sidebar-toggle-btn {
    position: static;
  }
}
