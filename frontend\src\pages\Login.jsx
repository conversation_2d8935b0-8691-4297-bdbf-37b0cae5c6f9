import { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import LoginForm from '../components/LoginForm';
import { useAuth } from '../context/AuthContext';
import { Auth } from 'aws-amplify';
import { clearUserData, setItem, getItem, STORAGE_KEYS } from '../utils/localStorage';
import Spinner from '../components/Spinner';
import { getLoadingMessage } from '../utils/loadingMessages';
import { showSuccessMessage, showErrorMessage, showInfoMessage } from '../utils/toastNotifications';
import logger from '../utils/logger';
import { API_BASE_URL } from '../config/api-config';

const Login = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [successMessage, setSuccessMessage] = useState(null);
  const navigate = useNavigate();
  const location = useLocation();
  const { signIn, isAuthenticated, loading: authLoading } = useAuth();

  // Initial cleanup on component mount
  useEffect(() => {
    logger.debug('Login component mounted, performing initial cleanup');

    // Check if we're coming directly to the login page (not from another page)
    if (!location.state) {
      logger.info('Login - Direct access to login page detected, clearing storage');

      // Clear localStorage and sessionStorage to ensure a clean state
      localStorage.clear();
      sessionStorage.clear();

      // Remove any AWS Amplify related keys
      try {
        const allKeys = Object.keys(localStorage);
        const amplifyKeys = allKeys.filter(key =>
          key.startsWith('CognitoIdentityServiceProvider') ||
          key.startsWith('amplify-') ||
          key.startsWith('aws.') ||
          key.startsWith('AWSCognito')
        );

        amplifyKeys.forEach(key => {
          localStorage.removeItem(key);
        });

        logger.debug(`Login - Removed ${amplifyKeys.length} AWS Amplify related keys`);
      } catch (e) {
        logger.error('Login - Error removing Amplify keys:', e);
      }
    }
  }, []);

  // Check if user is already authenticated and redirect to dashboard
  useEffect(() => {
    // If we're coming from logout or clean login, don't redirect even if there's stale authentication data
    const isFromLogout = location.state?.fromLogout === true;
    const isCleanLogin = sessionStorage.getItem('clean_login') === 'true';

    if (isFromLogout || isCleanLogin) {
      // If coming from clean login, clear the flag
      if (isCleanLogin) {
        logger.info('Login - Detected clean_login flag, clearing it');
        sessionStorage.removeItem('clean_login');
      }
      return;
    }

    // Verify with Cognito if the user is actually authenticated
    const verifyAuthentication = async () => {
      if (!authLoading && isAuthenticated) {
        logger.debug('Login - Verifying authentication with Cognito');

        try {
          // Verify with Cognito if the user is actually authenticated
          const user = await Auth.currentAuthenticatedUser();
          logger.debug('Login - User is authenticated according to Cognito:', user.username);

          // Now that we've confirmed the user is actually authenticated, proceed with redirection
          logger.info('Login - Authenticated user, checking redirection path');

          // Check if user has a user type and profile completion status
          const userType = getItem(STORAGE_KEYS.USER_TYPE);
          const profileComplete = getItem(STORAGE_KEYS.PROFILE_COMPLETION) === 'true';

          if (!userType) {
            // If no user type, redirect to user type selection
            logger.info('Login - No user type found, redirecting to user type selection');
            navigate('/user-type');
          } else if (!profileComplete) {
            // If profile not complete, redirect to appropriate info page
            if (userType.toLowerCase() === 'individual') {
              logger.info('Login - Individual profile not complete, redirecting to individual info page');
              navigate('/individual-info');
            } else if (userType.toLowerCase() === 'broker') {
              logger.info('Login - Broker profile not complete, redirecting to broker info page');
              navigate('/broker-info');
            } else if (userType.toLowerCase() === 'supplier') {
              logger.info('Login - Supplier profile not complete, redirecting to supplier info page');
              navigate('/supplier-info');
            } else {
              logger.info('Login - Professional profile not complete, redirecting to professional info page');
              navigate('/professional-info');
            }
          } else {
            // If profile is complete, redirect to dashboard
            logger.info('Login - Profile complete, redirecting to dashboard');
            navigate('/dashboard');
          }
        } catch (error) {
          // User is not actually authenticated according to Cognito
          logger.warn('Login - User is NOT authenticated according to Cognito, clearing state');

          // Clear all localStorage and sessionStorage data to ensure clean state
          localStorage.clear();
          sessionStorage.clear();

          // Don't redirect - let the user stay on the login page
        }
      }
    };

    verifyAuthentication();
  }, [isAuthenticated, authLoading, navigate, location.state]);

  // Check for success message from verification step and handle logout cleanup
  useEffect(() => {
    // If coming from logout, ensure all authentication state is properly cleared
    if (location.state?.fromLogout) {
      try {
        logger.info('Login page detected fromLogout state, performing cleanup');

        // Import the logout handler dynamically to avoid circular dependencies
        import('../utils/logoutHandler').then(({ performLogout }) => {
          // Perform a comprehensive logout
          performLogout().then(() => {
            logger.info('Comprehensive logout completed on login page');

            // Force a refresh of the Auth state in the AuthContext
            Auth.currentAuthenticatedUser()
              .then(() => {
                // This should not happen after logout, but if it does, force sign out
                logger.warn('WARNING: User still authenticated after logout, forcing sign out');
                Auth.signOut({ global: true })
                  .catch(e => logger.error('Error in forced signout:', e));
              })
              .catch(() => {
                // This is expected - user should not be authenticated
                logger.debug('Confirmed user is not authenticated after logout');
              });
          });
        });

        // Clear ALL localStorage and sessionStorage data to ensure a clean state
        localStorage.clear();
        sessionStorage.clear();

        // Reset the fromLogout flag to prevent issues with browser back button
        // Use replaceState to avoid adding a new history entry
        window.history.replaceState(
          { ...location.state, fromLogout: false },
          document.title
        );
      } catch (error) {
        logger.error('Error during logout cleanup:', error);

        // If there's an error, still try to clear storage
        try {
          localStorage.clear();
          sessionStorage.clear();
        } catch (e) {
          // Silent fail
        }
      }
    }

    // Handle success message if present
    if (location.state?.message) {
      showSuccessMessage('LOGIN_SUCCESS', location.state.message);
      // Clear the message from location state after displaying it
      window.history.replaceState({}, document.title);
    }
  }, [location]);

  const handleLogin = async (formData) => {
    setLoading(true);
    setError(null);
    setSuccessMessage(null);

    try {
      // Use the email field for login
      const username = formData.email;
      const password = formData.password;

      logger.info('Attempting to sign in with Cognito:', username);

      // Sign in with Cognito
      await signIn(username, password);

      logger.info('Login successful');

      // Tokens are now stored in localStorage by the AuthContext

      // Check if user has a user type set in Cognito
      try {
        logger.debug('Checking for user type in Cognito');
        const currentUser = await Auth.currentAuthenticatedUser();

        // First check for custom:userType attribute
        let userType = currentUser.attributes['custom:userType'];
        logger.debug('User type from custom:userType:', userType);

        // PRIORITY CHECK: If user type is Admin, bypass all other checks and redirect immediately
        if (userType && userType.toLowerCase() === 'admin') {
          logger.info('🔑 ADMIN USER DETECTED - Bypassing all checks and redirecting to dashboard');

          // Store essential admin data in localStorage
          const adminUserData = {
            email: currentUser.attributes.email,
            firstName: currentUser.attributes.given_name || 'System',
            lastName: currentUser.attributes.family_name || 'Administrator',
            phoneNumber: currentUser.attributes.phone_number || '',
            cognitoId: currentUser.attributes.sub || '',
            userType: 'admin'
          };

          // Store all required localStorage items for admin
          setItem(STORAGE_KEYS.USER_DATA, adminUserData);
          setItem(STORAGE_KEYS.COGNITO_ID, currentUser.attributes.sub);
          setItem(STORAGE_KEYS.USER_TYPE, 'admin');
          setItem(STORAGE_KEYS.PROFILE_COMPLETION, 'true'); // Admin always has complete profile

          logger.info('✅ Admin localStorage data stored, redirecting to dashboard');
          navigate('/dashboard', { replace: true }); // Use replace to prevent back navigation issues
          return;
        }

        // If not found, check preferred_username for fallback format (e.g., "individual_user")
        if (!userType && currentUser.attributes.preferred_username) {
          const preferredUsername = currentUser.attributes.preferred_username;
          logger.debug('Checking preferred_username:', preferredUsername);
          if (preferredUsername.includes('_user')) {
            userType = preferredUsername.split('_')[0];
            logger.debug('Found user type in preferred_username:', userType);
          }
        }

        // Debug: Log all available attributes
        logger.debug('All Cognito attributes:', Object.keys(currentUser.attributes));
        logger.debug('Full Cognito attributes:', currentUser.attributes);

        // Specifically check for user type attributes
        logger.debug('Checking specific user type attributes:', {
          'custom:userType': currentUser.attributes['custom:userType'],
          'preferred_username': currentUser.attributes['preferred_username'],
          'custom:profileComplete': currentUser.attributes['custom:profileComplete'],
          'custom:profileCompletion': currentUser.attributes['custom:profileCompletion']
        });

        // Store user data in localStorage
        const userData = {
          email: currentUser.attributes.email,
          firstName: currentUser.attributes.given_name || '',
          lastName: currentUser.attributes.family_name || '',
          phoneNumber: currentUser.attributes.phone_number || '',
          cognitoId: currentUser.attributes.sub || ''
        };

        setItem(STORAGE_KEYS.USER_DATA, userData);

        if (!userType) {
          logger.warn('No user type found in Cognito attributes, checking MongoDB as fallback');

          // Try to get user type from MongoDB as fallback
          try {
            const response = await fetch(`${API_BASE_URL}/auth/get-user-type`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                email: currentUser.attributes.email,
                cognitoId: currentUser.attributes.sub
              })
            });

            if (response.ok) {
              const data = await response.json();
              if (data.success && data.userType) {
                userType = data.userType.toLowerCase();
                logger.info('Found user type in MongoDB:', userType);

                // PRIORITY CHECK: If MongoDB returns admin, handle immediately
                if (userType === 'admin') {
                  logger.info('🔑 ADMIN USER FOUND IN MONGODB - Redirecting immediately');

                  // Store essential admin data
                  const adminUserData = {
                    email: currentUser.attributes.email,
                    firstName: currentUser.attributes.given_name || 'System',
                    lastName: currentUser.attributes.family_name || 'Administrator',
                    phoneNumber: currentUser.attributes.phone_number || '',
                    cognitoId: currentUser.attributes.sub || '',
                    userType: 'admin'
                  };

                  setItem(STORAGE_KEYS.USER_DATA, adminUserData);
                  setItem(STORAGE_KEYS.COGNITO_ID, currentUser.attributes.sub);
                  setItem(STORAGE_KEYS.USER_TYPE, 'admin');
                  setItem(STORAGE_KEYS.PROFILE_COMPLETION, 'true');

                  // Update Cognito for future logins
                  try {
                    await Auth.updateUserAttributes(currentUser, {
                      'custom:userType': 'admin',
                      'custom:profileComplete': 'true'
                    });
                    logger.info('Updated Cognito with admin user type');
                  } catch (updateError) {
                    logger.warn('Could not update Cognito with admin user type:', updateError);
                  }

                  navigate('/dashboard', { replace: true });
                  return;
                }

                // Update Cognito with the found user type for future logins
                try {
                  const { Auth } = await import('aws-amplify');
                  const currentUser = await Auth.currentAuthenticatedUser();
                  await Auth.updateUserAttributes(currentUser, {
                    'custom:userType': userType
                  });
                  logger.info('Updated Cognito with user type from MongoDB');
                } catch (updateError) {
                  logger.warn('Could not update Cognito with user type:', updateError);
                }
              }
            }
          } catch (mongoError) {
            logger.error('Error checking MongoDB for user type:', mongoError);
          }

          // If still no user type found, redirect to user type selection
          if (!userType) {
            logger.warn('No user type found in Cognito or MongoDB, redirecting to user type selection');
            navigate('/user-type', { state: { userData } });
            return;
          }
        }

        logger.debug('User type found in Cognito:', userType);

        // Check if user has completed their profile by checking both profileCompletion and profileComplete attributes in Cognito
        const profileCompletion = currentUser.attributes['custom:profileCompletion'] === 'true';
        const profileComplete = currentUser.attributes['custom:profileComplete'] === 'true';
        const isProfileComplete = profileCompletion || profileComplete;

        logger.debug('Profile completion status from Cognito:', {
          profileCompletion,
          profileComplete,
          isProfileComplete
        });

        // Store essential user data in localStorage
        // Store the Cognito ID
        if (currentUser.attributes.sub) {
          setItem(STORAGE_KEYS.COGNITO_ID, currentUser.attributes.sub);
          logger.debug('Cognito ID stored in localStorage:', currentUser.attributes.sub);
        }

        // Store user type
        setItem(STORAGE_KEYS.USER_TYPE, userType);
        logger.debug('User type stored in localStorage:', userType);

        // Store profile completion status
        setItem(STORAGE_KEYS.PROFILE_COMPLETION, isProfileComplete.toString());
        logger.debug('Profile completion status stored in localStorage:', isProfileComplete);

        // Update user data with any additional information and store it again
        userData.userType = userType;
        setItem(STORAGE_KEYS.USER_DATA, userData);
        logger.debug('User data updated and stored in localStorage for form prepopulation');

        // Note: Admin users are handled earlier in the flow and should never reach this point

        if (!isProfileComplete) {
          // If profile is not complete, redirect to the appropriate info page based on user type
          if (userType.toLowerCase() === 'individual') {
            logger.info('Individual profile not complete, redirecting to individual info page');
            navigate('/individual-info', { state: { userData: { ...userData, userType } } });
            return;
          } else if (userType.toLowerCase() === 'supplier') {
            logger.info('Supplier profile not complete, redirecting to supplier info page');
            navigate('/supplier-info', { state: { userData: { ...userData, userType } } });
            return;
          } else if (userType.toLowerCase() === 'broker') {
            logger.info('Broker profile not complete, redirecting to broker info page');
            navigate('/broker-info', { state: { userData: { ...userData, userType } } });
            return;
          } else if (['professional', 'referrer'].includes(userType.toLowerCase())) {
            logger.info('Professional profile not complete, redirecting to professional info page');
            navigate('/professional-info', { state: { userData: { ...userData, userType } } });
            return;
          }
        }

        // If user has completed their profile, redirect to dashboard
        logger.info('User has completed profile, redirecting to dashboard');
        navigate('/dashboard');
      } catch (userTypeError) {
        logger.error('Error checking user type:', userTypeError);
        // If there's an error checking user type, just redirect to dashboard
        navigate('/dashboard');
      }
    } catch (err) {
      logger.error('Login error:', err);

      // Check if this is an account status error from our backend
      if (err.response && err.response.data &&
          ['ACCOUNT_SUSPENDED', 'ACCOUNT_INACTIVE', 'ACCOUNT_PENDING'].includes(err.response.data.error)) {

        const { error, message, status, supportEmail } = err.response.data;

        // Redirect to AccountStatus page with error details
        navigate('/account-status', {
          state: {
            status: error,
            message: message,
            supportEmail: supportEmail || '<EMAIL>'
          }
        });
        return; // Exit early to prevent other error handling
      } else if (err.code === 'UserNotConfirmedException') {
        // User exists but is not confirmed
        showErrorMessage('VERIFICATION_FAILED', 'Your account is not verified. Please check your email for a verification code.');
        // Redirect to verification page
        navigate('/verify', { state: { email: formData.email } });
      } else if (err.code === 'NotAuthorizedException') {
        showErrorMessage('LOGIN_FAILED', 'Incorrect username or password. Please try again.');
      } else if (err.code === 'UserNotFoundException') {
        showErrorMessage('LOGIN_FAILED', 'User does not exist. Please check your email or sign up.');
      } else {
        showErrorMessage('LOGIN_FAILED', err.message || 'Failed to login. Please check your credentials and try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      {error && (
        <div style={{
          position: 'fixed',
          top: '20px',
          left: '50%',
          transform: 'translateX(-50%)',
          backgroundColor: '#fff',
          color: '#ff3b30',
          padding: '10px 20px',
          borderRadius: '8px',
          border: '1px solid #e0e0e0',
          boxShadow: '0 2px 10px rgba(0, 0, 0, 0.1)',
          zIndex: 100
        }}>
          {error}
        </div>
      )}

      {successMessage && (
        <div style={{
          position: 'fixed',
          top: '20px',
          left: '50%',
          transform: 'translateX(-50%)',
          backgroundColor: '#fff',
          color: '#34c759',
          padding: '10px 20px',
          borderRadius: '8px',
          border: '1px solid #e0e0e0',
          boxShadow: '0 2px 10px rgba(0, 0, 0, 0.1)',
          zIndex: 100
        }}>
          {successMessage}
        </div>
      )}

      <LoginForm
        onSubmit={handleLogin}
      />

      {loading && <Spinner fullScreen={true} message={getLoadingMessage('LOGIN')} size="large" />}
    </>
  );
};

export default Login;
