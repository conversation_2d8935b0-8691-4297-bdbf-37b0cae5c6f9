/**
 * Data Synchronization Manager
 * Handles sync between localStorage, Cognito, and Database
 * Provides single source of truth with smart caching
 */

import { Auth } from 'aws-amplify';
import { STORAGE_KEYS, setItem, getItem, removeItem } from './localStorage';
import profileService from '../services/profile.service';
import logger from './logger';

// Cache timestamps to track data freshness
const CACHE_KEYS = {
  USER_DATA_TIMESTAMP: 'userDataTimestamp',
  PROFILE_DATA_TIMESTAMP: 'profileDataTimestamp',
  COGNITO_SYNC_TIMESTAMP: 'cognitoSyncTimestamp'
};

// Cache duration in milliseconds (5 minutes)
const CACHE_DURATION = 5 * 60 * 1000;

/**
 * Check if cached data is still fresh
 * @param {string} timestampKey - The timestamp key to check
 * @returns {boolean} - True if data is fresh, false if stale
 */
const isCacheFresh = (timestampKey) => {
  const timestamp = getItem(timestampKey);
  if (!timestamp) return false;
  
  const now = Date.now();
  const cacheTime = parseInt(timestamp);
  return (now - cacheTime) < CACHE_DURATION;
};

/**
 * Update cache timestamp
 * @param {string} timestampKey - The timestamp key to update
 */
const updateCacheTimestamp = (timestampKey) => {
  setItem(timestampKey, Date.now().toString());
};

/**
 * Get user authentication status with smart sync
 * Priority: Cognito → localStorage (if fresh) → Force re-auth
 */
export const getAuthenticationStatus = async () => {
  try {
    logger.debug('DataSync: Checking authentication status');
    
    // First, always check Cognito as the authoritative source
    try {
      const user = await Auth.currentAuthenticatedUser();
      if (user) {
        // User is authenticated in Cognito
        setItem(STORAGE_KEYS.IS_AUTHENTICATED, 'true');
        updateCacheTimestamp(CACHE_KEYS.COGNITO_SYNC_TIMESTAMP);
        
        logger.debug('DataSync: User authenticated in Cognito');
        return {
          isAuthenticated: true,
          user: user,
          source: 'cognito'
        };
      }
    } catch (cognitoError) {
      logger.warn('DataSync: Cognito authentication check failed:', cognitoError);
      
      // If Cognito fails, check localStorage only if cache is fresh
      if (isCacheFresh(CACHE_KEYS.COGNITO_SYNC_TIMESTAMP)) {
        const isAuthInStorage = getItem(STORAGE_KEYS.IS_AUTHENTICATED) === 'true';
        if (isAuthInStorage) {
          logger.debug('DataSync: Using fresh localStorage auth status');
          return {
            isAuthenticated: true,
            user: null,
            source: 'localStorage-cache'
          };
        }
      }
      
      // Clear stale auth data
      removeItem(STORAGE_KEYS.IS_AUTHENTICATED);
      return {
        isAuthenticated: false,
        user: null,
        source: 'none'
      };
    }
  } catch (error) {
    logger.error('DataSync: Error checking authentication:', error);
    return {
      isAuthenticated: false,
      user: null,
      source: 'error'
    };
  }
};

/**
 * Get user type with smart sync
 * Priority: Cognito → Database → localStorage (if fresh) → null
 */
export const getUserType = async () => {
  try {
    logger.debug('DataSync: Getting user type');
    
    // First try Cognito (authoritative source)
    try {
      const user = await Auth.currentAuthenticatedUser();
      const cognitoUserType = user.attributes['custom:userType'];
      
      if (cognitoUserType) {
        // Update localStorage with fresh data
        setItem(STORAGE_KEYS.USER_TYPE, cognitoUserType);
        updateCacheTimestamp(CACHE_KEYS.USER_DATA_TIMESTAMP);
        
        logger.debug('DataSync: User type from Cognito:', cognitoUserType);
        return {
          userType: cognitoUserType,
          source: 'cognito'
        };
      }
    } catch (cognitoError) {
      logger.warn('DataSync: Could not get user type from Cognito:', cognitoError);
    }
    
    // Try database as fallback
    try {
      const userData = await profileService.fetchUserProfile();
      if (userData.data?.userType) {
        const dbUserType = userData.data.userType;
        
        // Update localStorage and Cognito
        setItem(STORAGE_KEYS.USER_TYPE, dbUserType);
        updateCacheTimestamp(CACHE_KEYS.USER_DATA_TIMESTAMP);
        
        // Try to sync back to Cognito
        try {
          const user = await Auth.currentAuthenticatedUser();
          await Auth.updateUserAttributes(user, {
            'custom:userType': dbUserType
          });
          logger.debug('DataSync: Synced user type back to Cognito');
        } catch (syncError) {
          logger.warn('DataSync: Could not sync user type to Cognito:', syncError);
        }
        
        logger.debug('DataSync: User type from database:', dbUserType);
        return {
          userType: dbUserType,
          source: 'database'
        };
      }
    } catch (dbError) {
      logger.warn('DataSync: Could not get user type from database:', dbError);
    }
    
    // Use localStorage only if cache is fresh
    if (isCacheFresh(CACHE_KEYS.USER_DATA_TIMESTAMP)) {
      const cachedUserType = getItem(STORAGE_KEYS.USER_TYPE);
      if (cachedUserType) {
        logger.debug('DataSync: User type from fresh cache:', cachedUserType);
        return {
          userType: cachedUserType,
          source: 'localStorage-cache'
        };
      }
    }
    
    // No user type found anywhere
    logger.warn('DataSync: No user type found in any source');
    return {
      userType: null,
      source: 'none'
    };
    
  } catch (error) {
    logger.error('DataSync: Error getting user type:', error);
    return {
      userType: null,
      source: 'error'
    };
  }
};

/**
 * Get profile completion status with smart sync
 * Priority: Cognito → Database → localStorage (if fresh) → false
 */
export const getProfileCompletionStatus = async () => {
  try {
    logger.debug('DataSync: Getting profile completion status');
    
    // First try Cognito (authoritative source)
    try {
      const user = await Auth.currentAuthenticatedUser();
      const cognitoProfileCompletion = user.attributes['custom:profileCompletion'];
      
      if (cognitoProfileCompletion !== undefined) {
        const isComplete = cognitoProfileCompletion === 'true';
        
        // Update localStorage with fresh data
        setItem(STORAGE_KEYS.PROFILE_COMPLETION, isComplete.toString());
        updateCacheTimestamp(CACHE_KEYS.PROFILE_DATA_TIMESTAMP);
        
        logger.debug('DataSync: Profile completion from Cognito:', isComplete);
        return {
          isComplete: isComplete,
          source: 'cognito'
        };
      }
    } catch (cognitoError) {
      logger.warn('DataSync: Could not get profile completion from Cognito:', cognitoError);
    }
    
    // Try database as fallback
    try {
      const userData = await profileService.fetchUserProfile();
      if (userData.data?.profileComplete !== undefined) {
        const isComplete = userData.data.profileComplete;
        
        // Update localStorage and Cognito
        setItem(STORAGE_KEYS.PROFILE_COMPLETION, isComplete.toString());
        updateCacheTimestamp(CACHE_KEYS.PROFILE_DATA_TIMESTAMP);
        
        // Try to sync back to Cognito
        try {
          const user = await Auth.currentAuthenticatedUser();
          await Auth.updateUserAttributes(user, {
            'custom:profileCompletion': isComplete.toString()
          });
          logger.debug('DataSync: Synced profile completion back to Cognito');
        } catch (syncError) {
          logger.warn('DataSync: Could not sync profile completion to Cognito:', syncError);
        }
        
        logger.debug('DataSync: Profile completion from database:', isComplete);
        return {
          isComplete: isComplete,
          source: 'database'
        };
      }
    } catch (dbError) {
      logger.warn('DataSync: Could not get profile completion from database:', dbError);
    }
    
    // Use localStorage only if cache is fresh
    if (isCacheFresh(CACHE_KEYS.PROFILE_DATA_TIMESTAMP)) {
      const cachedCompletion = getItem(STORAGE_KEYS.PROFILE_COMPLETION) === 'true';
      logger.debug('DataSync: Profile completion from fresh cache:', cachedCompletion);
      return {
        isComplete: cachedCompletion,
        source: 'localStorage-cache'
      };
    }
    
    // Default to false if no data found
    logger.debug('DataSync: No profile completion data found, defaulting to false');
    return {
      isComplete: false,
      source: 'default'
    };
    
  } catch (error) {
    logger.error('DataSync: Error getting profile completion status:', error);
    return {
      isComplete: false,
      source: 'error'
    };
  }
};

/**
 * Force refresh all cached data
 * Clears cache timestamps to force fresh data fetch
 */
export const forceRefreshCache = () => {
  logger.debug('DataSync: Force refreshing all cached data');
  removeItem(CACHE_KEYS.USER_DATA_TIMESTAMP);
  removeItem(CACHE_KEYS.PROFILE_DATA_TIMESTAMP);
  removeItem(CACHE_KEYS.COGNITO_SYNC_TIMESTAMP);
};

/**
 * Sync data after profile updates
 * Call this after any profile completion or user type changes
 */
export const syncAfterProfileUpdate = async (userType, profileComplete) => {
  try {
    logger.debug('DataSync: Syncing after profile update', { userType, profileComplete });
    
    // Update Cognito first
    try {
      const user = await Auth.currentAuthenticatedUser();
      await Auth.updateUserAttributes(user, {
        'custom:userType': userType,
        'custom:profileCompletion': profileComplete.toString()
      });
      logger.debug('DataSync: Updated Cognito attributes');
    } catch (cognitoError) {
      logger.error('DataSync: Failed to update Cognito attributes:', cognitoError);
    }
    
    // Update localStorage
    setItem(STORAGE_KEYS.USER_TYPE, userType);
    setItem(STORAGE_KEYS.PROFILE_COMPLETION, profileComplete.toString());
    
    // Update cache timestamps
    updateCacheTimestamp(CACHE_KEYS.USER_DATA_TIMESTAMP);
    updateCacheTimestamp(CACHE_KEYS.PROFILE_DATA_TIMESTAMP);
    updateCacheTimestamp(CACHE_KEYS.COGNITO_SYNC_TIMESTAMP);
    
    logger.debug('DataSync: Profile update sync completed');
    return true;
  } catch (error) {
    logger.error('DataSync: Error syncing after profile update:', error);
    return false;
  }
};

export default {
  getAuthenticationStatus,
  getUserType,
  getProfileCompletionStatus,
  forceRefreshCache,
  syncAfterProfileUpdate,
  isCacheFresh,
  CACHE_DURATION
};
