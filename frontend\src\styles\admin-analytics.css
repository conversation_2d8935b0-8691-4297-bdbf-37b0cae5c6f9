.analytics-container {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.analytics-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e0e0e0;
}

.header-content h1 {
  margin: 0 0 5px 0;
  color: #2c3e50;
  font-size: 2rem;
}

.header-content p {
  margin: 0;
  color: #7f8c8d;
  font-size: 1rem;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  color: #495057;
  text-decoration: none;
  font-size: 14px;
  transition: all 0.2s ease;
  margin-bottom: 15px;
}

.back-button:hover {
  background: #e9ecef;
  color: #343a40;
}

.period-selector {
  display: flex;
  align-items: center;
  gap: 10px;
}

.period-selector label {
  font-weight: 500;
  color: #495057;
}

.period-selector select {
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 6px;
  background: white;
  font-size: 14px;
}

.analytics-tabs {
  display: flex;
  gap: 5px;
  margin-bottom: 30px;
  border-bottom: 1px solid #e0e0e0;
}

.tab-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: none;
  border: none;
  border-bottom: 3px solid transparent;
  color: #6c757d;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.tab-button:hover {
  color: #495057;
  background: #f8f9fa;
}

.tab-button.active {
  color: #007bff;
  border-bottom-color: #007bff;
  background: #f8f9fa;
}

.analytics-content {
  min-height: 400px;
}

/* Overview Tab Styles */
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.metric-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #007bff;
  transition: transform 0.2s ease;
}

.metric-card:hover {
  transform: translateY(-2px);
}

.metric-card.users {
  border-left-color: #28a745;
}

.metric-card.contracts {
  border-left-color: #17a2b8;
}

.metric-card.revenue {
  border-left-color: #ffc107;
}

.metric-card.savings {
  border-left-color: #28a745;
}

.metric-card.conversion {
  border-left-color: #6f42c1;
}

.metric-card.active-users {
  border-left-color: #fd7e14;
}

.metric-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
  background: #f8f9fa;
  color: #6c757d;
}

.metric-content h3 {
  margin: 0 0 8px 0;
  font-size: 2rem;
  font-weight: 700;
  color: #2c3e50;
}

.metric-content p {
  margin: 0 0 8px 0;
  color: #6c757d;
  font-size: 14px;
  font-weight: 500;
}

.metric-change {
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 4px;
  background: #e9ecef;
  color: #6c757d;
}

.metric-change.positive {
  background: #d4edda;
  color: #155724;
}

.quick-stats {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stat-section h4 {
  margin: 0 0 16px 0;
  color: #2c3e50;
  font-size: 1.2rem;
}

.stat-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f1f3f4;
}

.stat-label {
  color: #6c757d;
  font-size: 14px;
}

.stat-value {
  font-weight: 600;
  color: #2c3e50;
}

/* Analytics Sections */
.analytics-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.analytics-section h4 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 1.3rem;
  border-bottom: 2px solid #f1f3f4;
  padding-bottom: 10px;
}

/* User Type Grid */
.user-type-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
}

.user-type-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  text-align: center;
}

.type-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #007bff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12px;
}

.type-content h5 {
  margin: 0 0 4px 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: #2c3e50;
}

.type-content p {
  margin: 0;
  color: #6c757d;
  font-size: 14px;
}

/* Activity Stats */
.activity-stats {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.activity-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.activity-type {
  color: #495057;
  font-size: 14px;
}

.activity-count {
  font-weight: 600;
  color: #007bff;
}

/* Retention Stats */
.retention-stats {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.retention-metric {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.retention-label {
  color: #6c757d;
  font-size: 14px;
}

.retention-value {
  font-weight: 600;
  color: #28a745;
}

/* Contract Stats */
.contract-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.stat-card {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  text-align: center;
}

.stat-card h5 {
  margin: 0 0 8px 0;
  color: #6c757d;
  font-size: 14px;
  font-weight: 500;
}

.stat-card p {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: #2c3e50;
}

/* Energy Type Breakdown */
.energy-type-breakdown {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.energy-type-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.energy-type-label {
  color: #495057;
  font-weight: 500;
}

.energy-type-count {
  font-weight: 600;
  color: #007bff;
}

/* Rankings */
.supplier-ranking,
.broker-ranking {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.supplier-item,
.broker-item {
  display: grid;
  grid-template-columns: 40px 1fr auto auto auto;
  gap: 12px;
  align-items: center;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.supplier-rank,
.broker-rank {
  font-weight: 700;
  color: #007bff;
  text-align: center;
}

.supplier-name,
.broker-name {
  font-weight: 500;
  color: #495057;
}

.supplier-contracts,
.broker-contracts,
.supplier-value,
.broker-value,
.broker-commission {
  font-size: 14px;
  color: #6c757d;
  text-align: right;
}

/* Commission Stats */
.commission-stats,
.offer-stats {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.commission-metric,
.offer-metric {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.commission-label,
.offer-label {
  color: #6c757d;
  font-size: 14px;
}

.commission-value,
.offer-value {
  font-weight: 600;
  color: #28a745;
}

/* KPI Grid */
.kpi-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
}

.kpi-card {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  text-align: center;
}

.kpi-card .kpi-value {
  display: block;
  font-size: 1.5rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 4px;
}

.kpi-card .kpi-label {
  font-size: 12px;
  color: #6c757d;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .analytics-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .analytics-tabs {
    flex-wrap: wrap;
  }

  .metrics-grid {
    grid-template-columns: 1fr;
  }

  .supplier-item,
  .broker-item {
    grid-template-columns: 1fr;
    gap: 8px;
    text-align: center;
  }
}
