import axios from 'axios';
import logger from '../utils/logger';
import { API_BASE_URL } from '../config/api-config';

class OfferService {
  constructor() {
    this.api = axios.create({
      baseURL: `${API_BASE_URL}/api/offers`,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Add request interceptor to include auth token
    this.api.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        logger.error('Request interceptor error:', error);
        return Promise.reject(error);
      }
    );

    // Add response interceptor for error handling
    this.api.interceptors.response.use(
      (response) => response,
      (error) => {
        logger.error('API Error:', error.response?.data || error.message);

        if (error.response?.status === 401) {
          // Token expired or invalid
          localStorage.removeItem('token');
          window.location.href = '/login';
        }

        return Promise.reject(error);
      }
    );
  }

  /**
   * Get all offers for the current user
   */
  async getUserOffers() {
    try {
      logger.info('Fetching user offers');
      const response = await this.api.get('/user');
      logger.info('Offers fetched successfully:', response.data.data?.length || 0);
      return response.data;
    } catch (error) {
      logger.error('Error fetching user offers:', error);
      throw this.handleError(error);
    }
  }

  /**
   * Get a specific offer by ID
   */
  async getOfferById(offerId) {
    try {
      logger.info('Fetching offer by ID:', offerId);
      const response = await this.api.get(`/${offerId}`);
      logger.info('Offer fetched successfully');
      return response.data;
    } catch (error) {
      logger.error('Error fetching offer:', error);
      throw this.handleError(error);
    }
  }

  /**
   * Accept an offer
   */
  async acceptOffer(offerId) {
    try {
      logger.info('Accepting offer:', offerId);
      const response = await this.api.post(`/${offerId}/accept`);
      logger.info('Offer accepted successfully');
      return response.data;
    } catch (error) {
      logger.error('Error accepting offer:', error);
      throw this.handleError(error);
    }
  }

  /**
   * Get offer status
   */
  async getOfferStatus(offerId) {
    try {
      logger.info('Getting offer status:', offerId);
      const response = await this.api.get(`/${offerId}/status`);
      return response.data;
    } catch (error) {
      logger.error('Error getting offer status:', error);
      throw this.handleError(error);
    }
  }

  /**
   * Handle API errors
   */
  handleError(error) {
    if (error.response) {
      // Server responded with error status
      const message = error.response.data?.message || 'An error occurred';
      return new Error(message);
    } else if (error.request) {
      // Request was made but no response received
      return new Error('Network error - please check your connection');
    } else {
      // Something else happened
      return new Error(error.message || 'An unexpected error occurred');
    }
  }
}

// Export singleton instance
export default new OfferService();
