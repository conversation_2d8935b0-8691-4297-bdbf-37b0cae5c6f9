const fs = require('fs').promises;
const path = require('path');
const logger = require('../utils/logger');

class EmailTemplateService {
  constructor() {
    this.templatesPath = path.join(__dirname, '../templates/emails');
    this.baseTemplatePath = path.join(this.templatesPath, 'base.html');
    this.templateCache = new Map();
    this.cacheEnabled = process.env.NODE_ENV === 'production';
  }

  /**
   * Load and cache email template
   * @param {string} templateName - Name of the template file (without .html extension)
   * @returns {Promise<string>} Template content
   */
  async loadTemplate(templateName) {
    const cacheKey = templateName;
    
    // Return cached template if available and caching is enabled
    if (this.cacheEnabled && this.templateCache.has(cacheKey)) {
      return this.templateCache.get(cacheKey);
    }

    try {
      const templatePath = path.join(this.templatesPath, `${templateName}.html`);
      const templateContent = await fs.readFile(templatePath, 'utf8');
      
      // Cache the template if caching is enabled
      if (this.cacheEnabled) {
        this.templateCache.set(cacheKey, templateContent);
      }
      
      return templateContent;
    } catch (error) {
      logger.error(`Failed to load email template: ${templateName}`, error);
      throw new Error(`Email template not found: ${templateName}`);
    }
  }

  /**
   * Load base template
   * @returns {Promise<string>} Base template content
   */
  async loadBaseTemplate() {
    const cacheKey = 'base';
    
    if (this.cacheEnabled && this.templateCache.has(cacheKey)) {
      return this.templateCache.get(cacheKey);
    }

    try {
      const baseTemplate = await fs.readFile(this.baseTemplatePath, 'utf8');
      
      if (this.cacheEnabled) {
        this.templateCache.set(cacheKey, baseTemplate);
      }
      
      return baseTemplate;
    } catch (error) {
      logger.error('Failed to load base email template', error);
      throw new Error('Base email template not found');
    }
  }

  /**
   * Simple template variable replacement
   * @param {string} template - Template content
   * @param {Object} variables - Variables to replace
   * @returns {string} Rendered template
   */
  renderTemplate(template, variables = {}) {
    let rendered = template;
    
    // Replace all {{variable}} placeholders
    for (const [key, value] = Object.entries(variables)) {
      const regex = new RegExp(`{{${key}}}`, 'g');
      rendered = rendered.replace(regex, value || '');
    }
    
    // Handle conditional blocks {{#if variable}}...{{/if}}
    rendered = this.handleConditionals(rendered, variables);
    
    // Clean up any remaining unreplaced variables
    rendered = rendered.replace(/{{[^}]+}}/g, '');
    
    return rendered;
  }

  /**
   * Handle conditional blocks in templates
   * @param {string} template - Template content
   * @param {Object} variables - Variables for conditions
   * @returns {string} Template with conditionals processed
   */
  handleConditionals(template, variables) {
    // Handle {{#if variable}}...{{/if}} blocks
    const ifRegex = /{{#if\s+(\w+)}}([\s\S]*?){{\/if}}/g;
    
    return template.replace(ifRegex, (match, variable, content) => {
      const value = variables[variable];
      // Show content if variable exists and is truthy
      return (value && value !== '' && value !== null && value !== undefined) ? content : '';
    });
  }

  /**
   * Render complete email with base template
   * @param {string} templateName - Name of the content template
   * @param {Object} variables - Variables for template rendering
   * @returns {Promise<Object>} Rendered email with HTML and text versions
   */
  async renderEmail(templateName, variables = {}) {
    try {
      // Load templates
      const [baseTemplate, contentTemplate] = await Promise.all([
        this.loadBaseTemplate(),
        this.loadTemplate(templateName)
      ]);

      // Add default variables
      const defaultVariables = {
        companyName: process.env.COMPANY_NAME || 'My Energy Bill',
        currentYear: new Date().getFullYear(),
        supportEmail: '<EMAIL>',
        ...variables
      };

      // Render content template
      const renderedContent = this.renderTemplate(contentTemplate, defaultVariables);
      
      // Insert content into base template
      const finalVariables = {
        ...defaultVariables,
        content: renderedContent
      };
      
      const htmlBody = this.renderTemplate(baseTemplate, finalVariables);
      
      // Generate text version (simple HTML to text conversion)
      const textBody = this.htmlToText(htmlBody);
      
      return {
        htmlBody,
        textBody,
        subject: variables.subject || `${defaultVariables.companyName} Notification`
      };
    } catch (error) {
      logger.error(`Failed to render email template: ${templateName}`, error);
      throw error;
    }
  }

  /**
   * Convert HTML to plain text (basic implementation)
   * @param {string} html - HTML content
   * @returns {string} Plain text version
   */
  htmlToText(html) {
    return html
      // Remove HTML tags
      .replace(/<[^>]*>/g, '')
      // Convert HTML entities
      .replace(/&nbsp;/g, ' ')
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      // Clean up whitespace
      .replace(/\s+/g, ' ')
      .replace(/\n\s+/g, '\n')
      .trim();
  }

  /**
   * Clear template cache (useful for development)
   */
  clearCache() {
    this.templateCache.clear();
    logger.info('Email template cache cleared');
  }

  /**
   * Get list of available templates
   * @returns {Promise<Array<string>>} List of template names
   */
  async getAvailableTemplates() {
    try {
      const files = await fs.readdir(this.templatesPath);
      return files
        .filter(file => file.endsWith('.html') && file !== 'base.html')
        .map(file => file.replace('.html', ''));
    } catch (error) {
      logger.error('Failed to get available templates', error);
      return [];
    }
  }
}

module.exports = new EmailTemplateService();
