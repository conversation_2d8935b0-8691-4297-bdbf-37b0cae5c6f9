const express = require('express');
const router = express.Router();
// Import MongoDB models
const { EnergyRequest, User, Document } = require('../models');
const { verifyToken } = require('../middleware/auth');
const logger = require('../utils/logger');

// Get all energy requests for the authenticated user
router.get('/', verifyToken, async (req, res) => {
  try {
    // Find user by email
    const user = await User.findOne({ email: req.user.email });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Get all energy requests for this user
    const requests = await EnergyRequest.find({ userId: user._id })
      .sort({ createdAt: -1 }) // Sort by creation date, newest first
      .populate('assignedBroker', 'firstName lastName email');

    res.status(200).json({
      success: true,
      count: requests.length,
      data: requests
    });
  } catch (error) {
    logger.error('Error fetching energy requests:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching energy requests',
      error: error.message
    });
  }
});

// Get a single energy request by ID
router.get('/:id', verifyToken, async (req, res) => {
  try {
    // Find user by email
    const user = await User.findOne({ email: req.user.email });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Find the energy request by ID
    const request = await EnergyRequest.findById(req.params.id)
      .populate('assignedBroker', 'firstName lastName email')
      .populate('documents');

    if (!request) {
      return res.status(404).json({
        success: false,
        message: 'Energy request not found'
      });
    }

    // Check if the request belongs to the user (unless admin or broker)
    if (user.userType !== 'Admin' && user.userType !== 'Broker' &&
        request.userId.toString() !== user._id.toString()) {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to access this request'
      });
    }

    res.status(200).json({
      success: true,
      data: request
    });
  } catch (error) {
    logger.error('Error fetching energy request:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching energy request',
      error: error.message
    });
  }
});

// Create a new energy request
router.post('/', verifyToken, async (req, res) => {
  try {
    // Find user by email
    const user = await User.findOne({ email: req.user.email });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    const {
      requestType,
      consumptionDetails,
      currentSupplier,
      currentContractEndDate,
      preferredDuration,
      additionalRequirements
    } = req.body;

    // Validate required fields
    if (!requestType) {
      return res.status(400).json({
        success: false,
        message: 'Request type is required'
      });
    }

    // Create a new energy request
    const newRequest = await EnergyRequest.create({
      userId: user._id,
      userType: user.userType,
      requestType,
      status: 'Submitted',
      consumptionDetails,
      currentSupplier,
      currentContractEndDate: currentContractEndDate ? new Date(currentContractEndDate) : undefined,
      preferredDuration,
      additionalRequirements
    });

    logger.info('Created energy request:', newRequest._id);

    // Populate the request with user data for the response
    const populatedRequest = await EnergyRequest.findById(newRequest._id)
      .populate('userId', 'firstName lastName email');

    res.status(201).json({
      success: true,
      message: 'Energy request created successfully',
      data: populatedRequest
    });
  } catch (error) {
    logger.error('Error creating energy request:', error);
    res.status(500).json({
      success: false,
      message: 'Error creating energy request',
      error: error.message
    });
  }
});

// Update an energy request
router.put('/:id', verifyToken, async (req, res) => {
  try {
    // Find user by email
    const user = await User.findOne({ email: req.user.email });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Find the energy request
    const request = await EnergyRequest.findById(req.params.id);

    if (!request) {
      return res.status(404).json({
        success: false,
        message: 'Energy request not found'
      });
    }

    // Check if the request belongs to the user (unless admin or broker)
    if (user.userType !== 'Admin' && user.userType !== 'Broker' &&
        request.userId.toString() !== user._id.toString()) {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to update this request'
      });
    }

    const {
      requestType,
      status,
      consumptionDetails,
      currentSupplier,
      currentContractEndDate,
      preferredDuration,
      additionalRequirements
    } = req.body;

    // Update fields
    if (requestType) request.requestType = requestType;
    if (status && (user.userType === 'Admin' || user.userType === 'Broker')) {
      request.status = status;
    }
    if (consumptionDetails) request.consumptionDetails = consumptionDetails;
    if (currentSupplier) request.currentSupplier = currentSupplier;
    if (currentContractEndDate) request.currentContractEndDate = new Date(currentContractEndDate);
    if (preferredDuration) request.preferredDuration = preferredDuration;
    if (additionalRequirements) request.additionalRequirements = additionalRequirements;

    // Save the updated request
    await request.save();
    logger.info('Updated energy request:', request._id);

    res.status(200).json({
      success: true,
      message: 'Energy request updated successfully',
      data: request
    });
  } catch (error) {
    logger.error('Error updating energy request:', error);
    res.status(500).json({
      success: false,
      message: 'Error updating energy request',
      error: error.message
    });
  }
});

// Add a note to an energy request
router.post('/:id/notes', verifyToken, async (req, res) => {
  try {
    const { text, isInternal = false } = req.body;

    if (!text) {
      return res.status(400).json({
        success: false,
        message: 'Note text is required'
      });
    }

    // Find user by email
    const user = await User.findOne({ email: req.user.email });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Find the energy request
    const request = await EnergyRequest.findById(req.params.id);

    if (!request) {
      return res.status(404).json({
        success: false,
        message: 'Energy request not found'
      });
    }

    // Check if the request belongs to the user (unless admin or broker)
    if (user.userType !== 'Admin' && user.userType !== 'Broker' &&
        request.userId.toString() !== user._id.toString()) {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to add notes to this request'
      });
    }

    // Create a new note
    const newNote = {
      text,
      createdBy: user._id,
      createdAt: new Date(),
      isInternal: isInternal && (user.userType === 'Admin' || user.userType === 'Broker')
    };

    // Add the note to the request
    request.notes.push(newNote);

    // Save the updated request
    await request.save();
    logger.info('Added note to energy request:', request._id);

    res.status(201).json({
      success: true,
      message: 'Note added successfully',
      data: newNote
    });
  } catch (error) {
    logger.error('Error adding note:', error);
    res.status(500).json({
      success: false,
      message: 'Error adding note',
      error: error.message
    });
  }
});

// Delete an energy request
router.delete('/:id', verifyToken, async (req, res) => {
  try {
    // Find user by email
    const user = await User.findOne({ email: req.user.email });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Find the energy request
    const request = await EnergyRequest.findById(req.params.id);

    if (!request) {
      return res.status(404).json({
        success: false,
        message: 'Energy request not found'
      });
    }

    // Check if the request belongs to the user (unless admin)
    if (user.userType !== 'Admin' && request.userId.toString() !== user._id.toString()) {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to delete this request'
      });
    }

    // Only allow deletion if the request is in Draft or Submitted status
    if (!['Draft', 'Submitted'].includes(request.status) && user.userType !== 'Admin') {
      return res.status(400).json({
        success: false,
        message: 'Cannot delete a request that is already being processed'
      });
    }

    // Delete the request
    await EnergyRequest.findByIdAndDelete(req.params.id);
    logger.info('Deleted energy request:', req.params.id);

    res.status(200).json({
      success: true,
      message: 'Energy request deleted successfully'
    });
  } catch (error) {
    logger.error('Error deleting energy request:', error);
    res.status(500).json({
      success: false,
      message: 'Error deleting energy request',
      error: error.message
    });
  }
});

module.exports = router;
