import { useState } from 'react';
import { Link } from 'react-router-dom';
import styled from 'styled-components';
import AuthPageContainer from './AuthPageContainer';

const Title = styled.h1`
  font-size: 32px;
  font-weight: 900;
  margin-bottom: 5px;
  color: #000000;
  text-shadow: 1px 1px 3px rgba(255, 255, 255, 0.8);
  text-align: center;

  @media (max-width: 768px) {
    font-size: 28px;
    margin-bottom: 5px;
  }
`;

const SubTitle = styled.p`
  font-size: 17px;
  font-weight: 600;
  color: #000000;
  text-shadow: 0.5px 0.5px 2px rgba(255, 255, 255, 0.8);
  margin: 0 0 20px 0;
  text-align: center;

  @media (max-width: 768px) {
    font-size: 16px;
    margin: 0 0 15px 0;
  }
`;

const FormElementsContainer = styled.div`
  padding: 0;
  margin-bottom: 5px;
  background: none;
`;

const InputWrapper = styled.div`
  margin-bottom: 15px;
  display: flex;
  flex-direction: column;
  width: 100%;
`;

const InputContainer = styled.div`
  background-color: white;
  border-radius: 10px;
  display: flex;
  align-items: center;
  padding: 16px 18px;
  border: none;
  margin-bottom: 8px;
  width: 100%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
`;

const IconSpan = styled.span`
  margin-right: 12px;
  color: #000;

  svg {
    width: 20px !important;
    height: 20px !important;
  }
`;

const StyledInput = styled.input`
  border: none;
  outline: none;
  width: 100%;
  background-color: transparent;
  font-size: 16px;
  color: #000;
  padding: 0;
  margin: 0;

  &::placeholder {
    color: rgba(0, 0, 0, 0.6);
  }
`;

const ErrorMessage = styled.div`
  color: #ff6b6b;
  font-size: 12px;
  margin-top: 5px;
  font-weight: bold;
`;

const PasswordStrengthContainer = styled.div`
  margin-bottom: 15px;
  background: none;
  border-radius: 0;
  padding: 0;
  position: relative;
  z-index: 2;

  ul {
    list-style-type: none;
    padding: 0;
    margin: 0;

    li {
      font-size: 12px;
      margin-bottom: 5px;
      text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.7); /* Dark shadow for better readability */

      &.valid {
        color: #2dce89;
        font-weight: bold;
      }

      &.invalid {
        color: #ffffff;
        font-weight: 500;
      }
    }
  }
`;

const SignupButton = styled.button`
  background-color: #1E3D5C;
  color: white;
  border: none;
  border-radius: 10px;
  padding: 18px;
  width: 100%;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  margin-bottom: 15px;
  margin-top: 10px;
  text-transform: uppercase;
  transition: all 0.3s;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);

  &:hover {
    background-color: #15304a;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.25);
  }
`;

const LinksContainer = styled.div`
  text-align: center;
  background-color: transparent;
  padding: 0;
  margin-top: 5px;
  margin-bottom: 5px;
`;

const StyledLink = styled(Link)`
  color: #000000;
  text-decoration: none;
  font-size: 14px;
  display: inline-block;
  margin: 5px 0;
  transition: color 0.3s;
  font-weight: 600;

  &:hover {
    color: #333333;
    text-decoration: underline;
  }
`;

const RowContainer = styled.div`
  display: flex;
  gap: 15px;
  width: 100%;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 10px;
  }
`;

const HintText = styled.div`
  font-size: 12px;
  color: #666;
  margin-top: 5px;
`;

const SignupForm = ({ onSubmit }) => {
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phoneNumber: '',
    password: '',
    confirmPassword: ''
  });

  const [errors, setErrors] = useState({});
  const [passwordStrength, setPasswordStrength] = useState({
    hasMinLength: false,
    hasUppercase: false,
    hasLowercase: false,
    hasNumber: false,
    hasSpecialChar: false
  });

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });

    // Clear error for this field when user starts typing
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: null
      });
    }

    // Check password strength if password field is being updated
    if (name === 'password') {
      checkPasswordStrength(value);
    }

    // Validate email as user types
    if (name === 'email' && value.trim() !== '') {
      const emailRegex = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
      if (!emailRegex.test(value.toLowerCase())) {
        setErrors({
          ...errors,
          email: 'Please enter a valid email address'
        });
      }
    }

    // Validate phone number as user types (French format)
    if (name === 'phoneNumber' && value.trim() !== '') {
      // Remove any non-digit characters
      let formattedValue = value.replace(/\D/g, '');

      // French mobile numbers start with 06 or 07
      // Remove leading 0 if present (since we'll add +33 later)
      if (formattedValue.startsWith('0')) {
        formattedValue = formattedValue.substring(1);
      }

      // Format as XX XX XX XX XX (French format)
      if (formattedValue.length > 2 && formattedValue.length <= 4) {
        formattedValue = formattedValue.slice(0, 2) + ' ' + formattedValue.slice(2);
      } else if (formattedValue.length > 4 && formattedValue.length <= 6) {
        formattedValue = formattedValue.slice(0, 2) + ' ' + formattedValue.slice(2, 4) + ' ' + formattedValue.slice(4);
      } else if (formattedValue.length > 6 && formattedValue.length <= 8) {
        formattedValue = formattedValue.slice(0, 2) + ' ' + formattedValue.slice(2, 4) + ' ' +
                         formattedValue.slice(4, 6) + ' ' + formattedValue.slice(6);
      } else if (formattedValue.length > 8) {
        formattedValue = formattedValue.slice(0, 2) + ' ' + formattedValue.slice(2, 4) + ' ' +
                         formattedValue.slice(4, 6) + ' ' + formattedValue.slice(6, 8) + ' ' +
                         formattedValue.slice(8, 10);
      }

      // Only update if the formatting changed the value and it's not too long
      if (formattedValue !== value && formattedValue.length <= 14) {
        setFormData({
          ...formData,
          phoneNumber: formattedValue
        });
      }
    }

    // Validate confirm password as user types
    if (name === 'confirmPassword' && value !== '' && formData.password !== value) {
      setErrors({
        ...errors,
        confirmPassword: 'Passwords do not match'
      });
    } else if (name === 'confirmPassword' && formData.password === value) {
      setErrors({
        ...errors,
        confirmPassword: null
      });
    }
  };

  const checkPasswordStrength = (password) => {
    setPasswordStrength({
      hasMinLength: password.length >= 8,
      hasUppercase: /[A-Z]/.test(password),
      hasLowercase: /[a-z]/.test(password),
      hasNumber: /[0-9]/.test(password),
      hasSpecialChar: /[!@#$%^&*(),.?":{}|<>]/.test(password)
    });
  };

  const validateForm = () => {
    const newErrors = {};

    // First name validation
    if (!formData.firstName.trim()) {
      newErrors.firstName = 'First name is required';
    } else if (formData.firstName.trim().length < 2) {
      newErrors.firstName = 'First name must be at least 2 characters';
    }

    // Last name validation
    if (!formData.lastName.trim()) {
      newErrors.lastName = 'Last name is required';
    } else if (formData.lastName.trim().length < 2) {
      newErrors.lastName = 'Last name must be at least 2 characters';
    }

    // Email validation
    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else {
      // More comprehensive email validation
      const emailRegex = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
      if (!emailRegex.test(formData.email.toLowerCase())) {
        newErrors.email = 'Please enter a valid email address';
      }
    }

    // Phone number validation (French format)
    if (!formData.phoneNumber.trim()) {
      newErrors.phoneNumber = 'Phone number is required';
    } else {
      // Remove all non-digit characters for validation
      const digitsOnly = formData.phoneNumber.replace(/\D/g, '');

      // French mobile numbers are 9 digits (excluding the leading 0)
      // We're expecting the format to be 6 XX XX XX XX or 7 XX XX XX XX
      if (digitsOnly.length !== 9) {
        newErrors.phoneNumber = 'Please enter a valid French mobile number (9 digits)';
      } else if (!digitsOnly.match(/^[67]\d{8}$/)) {
        // French mobile numbers start with 6 or 7 (after removing the leading 0)
        newErrors.phoneNumber = 'French mobile numbers should start with 06 or 07';
      }
    }

    // Password validation
    if (!formData.password) {
      newErrors.password = 'Password is required';
    } else {
      // Check if password meets all criteria
      const { hasMinLength, hasUppercase, hasLowercase, hasNumber, hasSpecialChar } = passwordStrength;

      if (!hasMinLength) {
        newErrors.password = 'Password must be at least 8 characters long';
      } else if (!hasUppercase) {
        newErrors.password = 'Password must include at least one uppercase letter';
      } else if (!hasLowercase) {
        newErrors.password = 'Password must include at least one lowercase letter';
      } else if (!hasNumber) {
        newErrors.password = 'Password must include at least one number';
      } else if (!hasSpecialChar) {
        newErrors.password = 'Password must include at least one special character';
      }
    }

    // Confirm password validation
    if (!formData.confirmPassword) {
      newErrors.confirmPassword = 'Please confirm your password';
    } else if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    console.log('Form submitted', formData);

    // Check if form is valid
    const isValid = validateForm();
    console.log('Form validation result:', isValid);

    if (isValid) {
      console.log('Calling onSubmit with form data:', formData);
      onSubmit(formData);
    } else {
      console.error('Form validation failed with errors:', errors);
    }
  };

  return (
    <AuthPageContainer>
      <form onSubmit={handleSubmit}>
        <Title>Create Account</Title>
        <SubTitle>Sign up to access energy deals in France</SubTitle>

        <FormElementsContainer>
          <RowContainer>
            <InputWrapper>
              <InputContainer>
                <IconSpan>
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" viewBox="0 0 16 16">
                    <path d="M3 14s-1 0-1-1 1-4 6-4 6 3 6 4-1 1-1 1H3zm5-6a3 3 0 1 0 0-6 3 3 0 0 0 0 6z"/>
                  </svg>
                </IconSpan>
                <StyledInput
                  type="text"
                  id="firstName"
                  name="firstName"
                  placeholder="First Name"
                  value={formData.firstName}
                  onChange={handleChange}
                  required
                />
              </InputContainer>
              {errors.firstName && <ErrorMessage>{errors.firstName}</ErrorMessage>}
            </InputWrapper>

            <InputWrapper>
              <InputContainer>
                <IconSpan>
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" viewBox="0 0 16 16">
                    <path d="M3 14s-1 0-1-1 1-4 6-4 6 3 6 4-1 1-1 1H3zm5-6a3 3 0 1 0 0-6 3 3 0 0 0 0 6z"/>
                  </svg>
                </IconSpan>
                <StyledInput
                  type="text"
                  id="lastName"
                  name="lastName"
                  placeholder="Last Name"
                  value={formData.lastName}
                  onChange={handleChange}
                  required
                />
              </InputContainer>
              {errors.lastName && <ErrorMessage>{errors.lastName}</ErrorMessage>}
            </InputWrapper>
          </RowContainer>

          <InputWrapper>
            <InputContainer>
              <IconSpan>
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" viewBox="0 0 16 16">
                  <path d="M0 4a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V4zm2-1a1 1 0 0 0-1 1v.217l7 4.2 7-4.2V4a1 1 0 0 0-1-1H2zm13 2.383-4.758 2.855L15 11.114v-5.73zm-.034 6.878L9.271 8.82 8 9.583 6.728 8.82l-5.694 3.44A1 1 0 0 0 2 13h12a1 1 0 0 0 .966-.739zM1 11.114l4.758-2.876L1 5.383v5.73z"/>
                </svg>
              </IconSpan>
              <StyledInput
                type="email"
                id="email"
                name="email"
                placeholder="Email Address"
                value={formData.email}
                onChange={handleChange}
                required
              />
            </InputContainer>
            {errors.email && <ErrorMessage>{errors.email}</ErrorMessage>}
          </InputWrapper>

          <InputWrapper>
            <InputContainer>
              <IconSpan>
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" viewBox="0 0 16 16">
                  <path fill-rule="evenodd" d="M1.885.511a1.745 1.745 0 0 1 2.61.163L6.29 2.98c.329.423.445.974.315 1.494l-.547 2.19a.678.678 0 0 0 .178.643l2.457 2.457a.678.678 0 0 0 .644.178l2.189-.547a1.745 1.745 0 0 1 1.494.315l2.306 1.794c.829.645.905 1.87.163 2.611l-1.034 1.034c-.74.74-1.846 1.065-2.877.702a18.634 18.634 0 0 1-7.01-4.42 18.634 18.634 0 0 1-4.42-7.009c-.362-1.03-.037-2.137.703-2.877L1.885.511z"/>
                </svg>
              </IconSpan>
              <StyledInput
                type="text"
                id="phoneNumber"
                name="phoneNumber"
                placeholder="06 XX XX XX XX"
                value={formData.phoneNumber}
                onChange={handleChange}
                required
              />
            </InputContainer>
            {errors.phoneNumber && <ErrorMessage>{errors.phoneNumber}</ErrorMessage>}
            {!errors.phoneNumber && formData.phoneNumber && (
              <HintText>
                Format: 06 XX XX XX XX or 07 XX XX XX XX (French mobile number)
              </HintText>
            )}
            <HintText>
              <strong>Note:</strong> Country code (+33) will be added automatically
            </HintText>
          </InputWrapper>

          <InputWrapper>
            <InputContainer>
              <IconSpan>
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" viewBox="0 0 16 16">
                  <path d="M8 1a2 2 0 0 1 2 2v4H6V3a2 2 0 0 1 2-2zm3 6V3a3 3 0 0 0-6 0v4a2 2 0 0 0-2 2v5a2 2 0 0 0 2 2h6a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2z"/>
                </svg>
              </IconSpan>
              <StyledInput
                type="password"
                id="password"
                name="password"
                placeholder="Password"
                value={formData.password}
                onChange={handleChange}
                required
              />
            </InputContainer>
            {errors.password && <ErrorMessage>{errors.password}</ErrorMessage>}
          </InputWrapper>

          {formData.password && (
            <PasswordStrengthContainer>
              <ul style={{ backgroundColor: 'rgba(0, 0, 0, 0.6)', padding: '10px', borderRadius: '8px' }}>
                <li className={passwordStrength.hasMinLength ? 'valid' : 'invalid'}>
                  {passwordStrength.hasMinLength ? '✓' : '○'} At least 8 characters
                </li>
                <li className={passwordStrength.hasUppercase ? 'valid' : 'invalid'}>
                  {passwordStrength.hasUppercase ? '✓' : '○'} At least one uppercase letter
                </li>
                <li className={passwordStrength.hasLowercase ? 'valid' : 'invalid'}>
                  {passwordStrength.hasLowercase ? '✓' : '○'} At least one lowercase letter
                </li>
                <li className={passwordStrength.hasNumber ? 'valid' : 'invalid'}>
                  {passwordStrength.hasNumber ? '✓' : '○'} At least one number
                </li>
                <li className={passwordStrength.hasSpecialChar ? 'valid' : 'invalid'}>
                  {passwordStrength.hasSpecialChar ? '✓' : '○'} At least one special character
                </li>
              </ul>
            </PasswordStrengthContainer>
          )}

          <InputWrapper>
            <InputContainer>
              <IconSpan>
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" viewBox="0 0 16 16">
                  <path d="M8 1a2 2 0 0 1 2 2v4H6V3a2 2 0 0 1 2-2zm3 6V3a3 3 0 0 0-6 0v4a2 2 0 0 0-2 2v5a2 2 0 0 0 2 2h6a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2z"/>
                </svg>
              </IconSpan>
              <StyledInput
                type="password"
                id="confirmPassword"
                name="confirmPassword"
                placeholder="Confirm Password"
                value={formData.confirmPassword}
                onChange={handleChange}
                required
              />
            </InputContainer>
            {errors.confirmPassword && <ErrorMessage>{errors.confirmPassword}</ErrorMessage>}
          </InputWrapper>

          <SignupButton type="submit">
            Create Account
          </SignupButton>
        </FormElementsContainer>

        <LinksContainer>
          <StyledLink to="/login">
            Already have an account?
          </StyledLink>
        </LinksContainer>
      </form>
    </AuthPageContainer>
  );
};

export default SignupForm;
