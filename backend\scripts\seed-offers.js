const mongoose = require('mongoose');
const User = require('../models/User');
const EnergyRequest = require('../models/EnergyRequest');
const Offer = require('../models/Offer');
const logger = require('../utils/logger');

// MongoDB connection
const connectDB = async () => {
  try {
    const mongoURI = process.env.MONGODB_URI || 'mongodb://localhost:27017/energy-app';
    await mongoose.connect(mongoURI);
    logger.info('Connected to MongoDB for seeding offers');
  } catch (error) {
    logger.error('MongoDB connection error:', error);
    process.exit(1);
  }
};

// Sample offers data
const createSampleOffers = async () => {
  try {
    // Check if offers already exist
    const existingOffers = await Offer.countDocuments({});
    if (existingOffers > 0) {
      logger.info(`Found ${existingOffers} existing offers, skipping creation`);
      return;
    }

    // Find existing users and energy requests
    const supplierUser = await User.findOne({ userType: 'Supplier' });
    const energyRequests = await EnergyRequest.find({ status: { $in: ['Submitted', 'UnderReview', 'OffersAvailable'] } });

    if (!supplierUser) {
      logger.error('No supplier user found. Please run the enhanced-seed script first.');
      return;
    }

    if (energyRequests.length === 0) {
      logger.error('No energy requests found. Please run the enhanced-seed script first.');
      return;
    }

    logger.info(`Found supplier: ${supplierUser.email}`);
    logger.info(`Found ${energyRequests.length} energy requests`);

    // Create sample offers
    const sampleOffers = [
      {
        requestId: energyRequests[0]._id,
        supplierId: supplierUser._id,
        offerDetails: {
          name: 'EDF Fixed Rate Electricity Plan',
          description: 'Lock in your electricity rates for 24 months with our best fixed rate plan. Perfect for households looking for price stability.',
          highlights: [
            'No price increases for 24 months',
            'No exit fees',
            '100% renewable electricity',
            '24/7 customer support',
            'Smart meter included'
          ]
        },
        energyType: 'Electricity',
        rateType: 'Fixed',
        duration: 24,
        price: {
          baseRate: 0.145,
          standingCharge: 25.5,
          totalEstimatedAnnual: 950,
          currency: 'EUR'
        },
        estimatedSavings: {
          amount: 120,
          percentage: 12
        },
        additionalBenefits: [
          'Green energy certificate',
          'Monthly usage reports',
          'Mobile app access'
        ],
        validUntil: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000), // 60 days from now
        status: 'Active',
        reviewStatus: 'Approved',
        reviewedBy: supplierUser._id,
        reviewDate: new Date(),
        views: 45,
        applications: 3
      },
      {
        requestId: energyRequests[0]._id,
        supplierId: supplierUser._id,
        offerDetails: {
          name: 'Total Energies Green Energy Plan',
          description: 'Eco-friendly energy plan with 100% renewable sources. Support sustainable energy while saving money.',
          highlights: [
            'Carbon neutral energy',
            'Monthly billing',
            'Smart meter included',
            'Green energy certificate',
            'Competitive rates'
          ]
        },
        energyType: 'Both',
        rateType: 'Variable',
        duration: 12,
        price: {
          baseRate: 0.138,
          standingCharge: 22.8,
          totalEstimatedAnnual: 880,
          currency: 'EUR'
        },
        estimatedSavings: {
          amount: 150,
          percentage: 15
        },
        additionalBenefits: [
          'Carbon offset program',
          'Renewable energy tracking',
          'Environmental impact reports'
        ],
        validUntil: new Date(Date.now() + 45 * 24 * 60 * 60 * 1000), // 45 days from now
        status: 'Active',
        reviewStatus: 'Approved',
        reviewedBy: supplierUser._id,
        reviewDate: new Date(),
        views: 32,
        applications: 5
      },
      {
        requestId: energyRequests.length > 1 ? energyRequests[1]._id : energyRequests[0]._id,
        supplierId: supplierUser._id,
        offerDetails: {
          name: 'Engie Economy Gas Plan',
          description: 'Affordable gas rates with flexible payment options. Perfect for cost-conscious consumers.',
          highlights: [
            'Low standing charge',
            'Price match guarantee',
            'Online account management',
            'Flexible payment terms',
            'No hidden fees'
          ]
        },
        energyType: 'Gas',
        rateType: 'Fixed',
        duration: 18,
        price: {
          baseRate: 0.065,
          standingCharge: 18.5,
          totalEstimatedAnnual: 720,
          currency: 'EUR'
        },
        estimatedSavings: {
          amount: 85,
          percentage: 10
        },
        additionalBenefits: [
          'Price protection guarantee',
          'Online billing',
          'Customer loyalty rewards'
        ],
        validUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
        status: 'Active',
        reviewStatus: 'Approved',
        reviewedBy: supplierUser._id,
        reviewDate: new Date(),
        views: 28,
        applications: 2
      }
    ];

    // Add more offers if we have more energy requests
    if (energyRequests.length > 2) {
      sampleOffers.push({
        requestId: energyRequests[2]._id,
        supplierId: supplierUser._id,
        offerDetails: {
          name: 'Premium Business Energy Package',
          description: 'Comprehensive energy solution for businesses with competitive rates and dedicated support.',
          highlights: [
            'Dedicated account manager',
            'Flexible contract terms',
            'Volume discounts available',
            'Priority customer support',
            'Energy efficiency consulting'
          ]
        },
        energyType: 'Both',
        rateType: 'Indexed',
        duration: 36,
        price: {
          baseRate: 0.132,
          standingCharge: 35.0,
          totalEstimatedAnnual: 1250,
          currency: 'EUR'
        },
        estimatedSavings: {
          amount: 200,
          percentage: 14
        },
        additionalBenefits: [
          'Business energy audit',
          'Quarterly reviews',
          'Energy management tools'
        ],
        validUntil: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000), // 90 days from now
        status: 'Active',
        reviewStatus: 'Approved',
        reviewedBy: supplierUser._id,
        reviewDate: new Date(),
        views: 15,
        applications: 1
      });
    }

    // Insert offers into database
    const createdOffers = await Offer.insertMany(sampleOffers);
    logger.info(`✅ Created ${createdOffers.length} sample offers`);

    // Update energy request statuses to show offers are available
    await EnergyRequest.updateMany(
      { _id: { $in: energyRequests.map(req => req._id) } },
      { status: 'OffersAvailable' }
    );
    logger.info('✅ Updated energy request statuses to OffersAvailable');

    return createdOffers;
  } catch (error) {
    logger.error('Error creating sample offers:', error);
    throw error;
  }
};

// Main execution
const main = async () => {
  try {
    await connectDB();
    await createSampleOffers();
    logger.info('✅ Offers seeding completed successfully!');
  } catch (error) {
    logger.error('❌ Offers seeding failed:', error);
  } finally {
    await mongoose.connection.close();
    logger.info('Connection closed');
    process.exit(0);
  }
};

// Run the script
if (require.main === module) {
  main();
}

module.exports = { createSampleOffers };
