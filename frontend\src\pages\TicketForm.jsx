import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { priorityOptions } from '../mockData';
import DashboardLayout from '../components/DashboardLayout';
import { showSuccessMessage, showErrorMessage } from '../utils/toastNotifications';
import '../styles/app.css';

const TicketForm = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    priority: 'Medium',
    meterNumber: '',
    address: '',
    contactPhone: ''
  });
  const [errors, setErrors] = useState({});

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.title.trim()) {
      newErrors.title = 'Title is required';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Description is required';
    }

    if (!formData.meterNumber.trim()) {
      newErrors.meterNumber = 'Meter number is required';
    } else if (!/^\d{14}$/.test(formData.meterNumber.replace(/\s/g, ''))) {
      newErrors.meterNumber = 'Meter number must be exactly 14 digits';
    }

    if (!formData.address.trim()) {
      newErrors.address = 'Address is required';
    }

    if (!formData.contactPhone.trim()) {
      newErrors.contactPhone = 'Contact phone is required';
    } else if (!/^\d{3}-\d{3}-\d{4}$/.test(formData.contactPhone)) {
      newErrors.contactPhone = 'Phone format should be XXX-XXX-XXXX';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));

      // In a real app, you would send the form data to your backend
      console.log('Submitting ticket:', formData);

      // Show success message
      showSuccessMessage('OPERATION_COMPLETED', 'Support ticket submitted successfully! We will get back to you soon.');
      setSuccess(true);

      // Reset form after 2 seconds and redirect to dashboard
      setTimeout(() => {
        navigate('/dashboard');
      }, 2000);
    } catch (err) {
      console.error('Error submitting ticket:', err);
      showErrorMessage('OPERATION_FAILED', 'Failed to submit support ticket. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <DashboardLayout>
      <div className="dashboard-content">
        <div className="card" style={{ maxWidth: '800px', margin: '0 auto' }}>
          <h1 className="card-title">New Electricity Request</h1>

          {success ? (
            <div className="success-message" style={{ textAlign: 'center', padding: '2rem' }}>
              <div style={{ fontSize: '3rem', color: 'var(--success-color)', marginBottom: '1rem' }}>✓</div>
              <p>Your request has been submitted successfully!</p>
              <p>Redirecting to dashboard...</p>
            </div>
          ) : (
            <form onSubmit={handleSubmit}>
              <div className="form-group">
                <label htmlFor="title" style={{ fontWeight: 'bold', display: 'block', marginBottom: '0.5rem' }}>Request Title</label>
                <input
                  type="text"
                  id="title"
                  name="title"
                  className="form-input"
                  placeholder="e.g., Power Outage, New Connection"
                  value={formData.title}
                  onChange={handleChange}
                  style={{ paddingLeft: '15px' }}
                />
                {errors.title && <div className="error-message">{errors.title}</div>}
              </div>

              <div className="form-group">
                <label htmlFor="description" style={{ fontWeight: 'bold', display: 'block', marginBottom: '0.5rem' }}>Description</label>
                <textarea
                  id="description"
                  name="description"
                  className="form-input"
                  placeholder="Please provide details about your electricity issue"
                  value={formData.description}
                  onChange={handleChange}
                  rows={4}
                  style={{ height: 'auto', paddingLeft: '15px' }}
                />
                {errors.description && <div className="error-message">{errors.description}</div>}
              </div>

              <div style={{ display: 'flex', gap: '1rem', flexWrap: 'wrap' }}>
                <div className="form-group" style={{ flex: '1', minWidth: '200px' }}>
                  <label htmlFor="priority" style={{ fontWeight: 'bold', display: 'block', marginBottom: '0.5rem' }}>Priority</label>
                  <select
                    id="priority"
                    name="priority"
                    className="form-input"
                    value={formData.priority}
                    onChange={handleChange}
                    style={{ paddingLeft: '15px' }}
                  >
                    {priorityOptions.map(option => (
                      <option key={option} value={option}>{option}</option>
                    ))}
                  </select>
                </div>

                <div className="form-group" style={{ flex: '1', minWidth: '200px' }}>
                  <label htmlFor="meterNumber" style={{ fontWeight: 'bold', display: 'block', marginBottom: '0.5rem' }}>PDL/PRM/RAE Number</label>
                  <input
                    type="text"
                    id="meterNumber"
                    name="meterNumber"
                    className="form-input"
                    placeholder="Enter your 14-digit meter number"
                    pattern="[0-9]{14}"
                    maxLength="14"
                    inputMode="numeric"
                    value={formData.meterNumber}
                    onChange={handleChange}
                    style={{ paddingLeft: '15px' }}
                  />
                  {errors.meterNumber && <div className="error-message">{errors.meterNumber}</div>}
                </div>
              </div>

              <div className="form-group">
                <label htmlFor="address" style={{ fontWeight: 'bold', display: 'block', marginBottom: '0.5rem' }}>Address</label>
                <input
                  type="text"
                  id="address"
                  name="address"
                  className="form-input"
                  placeholder="Your full address"
                  value={formData.address}
                  onChange={handleChange}
                  style={{ paddingLeft: '15px' }}
                />
                {errors.address && <div className="error-message">{errors.address}</div>}
              </div>

              <div className="form-group">
                <label htmlFor="contactPhone" style={{ fontWeight: 'bold', display: 'block', marginBottom: '0.5rem' }}>Contact Phone</label>
                <input
                  type="text"
                  id="contactPhone"
                  name="contactPhone"
                  className="form-input"
                  placeholder="XXX-XXX-XXXX"
                  value={formData.contactPhone}
                  onChange={handleChange}
                  style={{ paddingLeft: '15px' }}
                />
                {errors.contactPhone && <div className="error-message">{errors.contactPhone}</div>}
              </div>

              <button
                type="submit"
                className="btn btn-primary"
                disabled={loading}
                style={{ maxWidth: '200px' }}
              >
                {loading ? 'Submitting...' : 'Submit Request'}
              </button>
            </form>
          )}
        </div>
      </div>

      {loading && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(255, 255, 255, 0.8)',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          zIndex: 100
        }}>
          <div style={{
            backgroundColor: 'white',
            padding: '2rem',
            borderRadius: '8px',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
            textAlign: 'center'
          }}>
            <div style={{ fontSize: '1.2rem', fontWeight: '500' }}>Processing your request...</div>
          </div>
        </div>
      )}
    </DashboardLayout>
  );
};

export default TicketForm;
