const express = require('express');
const router = express.Router();
const supplierController = require('../controllers/supplierController');
const { verifyToken } = require('../middleware/auth');

// Apply authentication middleware to all routes
router.use(verifyToken);

// Dashboard routes
router.get('/dashboard/stats/:cognitoId', supplierController.getDashboardStats);

// Offers routes
router.get('/offers/active', supplierController.getActiveOffers);
router.post('/offers', supplierController.createOffer);

// Contracts routes
router.get('/contracts/recent', supplierController.getRecentContracts);

// Profile routes
router.get('/profile', supplierController.getProfile);
router.put('/profile', supplierController.updateProfile);

// Analytics routes
router.get('/analytics', supplierController.getAnalytics);

module.exports = router;
