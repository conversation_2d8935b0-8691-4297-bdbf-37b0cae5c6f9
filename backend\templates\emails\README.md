# Email Templates System

This directory contains HTML email templates for the My Energy Bill platform. The template system provides a clean, maintainable way to manage email content without hardcoding HTML in the application code.

## 📁 Directory Structure

```
backend/templates/emails/
├── README.md                           # This documentation
├── base.html                          # Base template with common styling
├── invitation.html                    # User invitation emails
├── invitation-reminder.html           # Invitation reminder emails
├── password-reset.html               # Password reset emails
├── password-reset-confirmation.html  # Password reset confirmation
├── admin-password-reset-notification.html # Admin password reset notifications
├── user-suspended.html               # User suspension notifications
├── user-inactive.html                # User inactive notifications
├── profile-submitted.html            # Profile submission confirmations
├── profile-approved.html             # Profile approval notifications
├── changes-requested.html            # Profile change requests
├── broker-registration-admin.html    # Admin notifications for broker registrations
└── broker-approval.html              # Broker approval notifications
```

## 🎨 Template System Features

### Base Template (`base.html`)
- Professional responsive design
- Company branding and colors
- Consistent styling across all emails
- Mobile-friendly layout
- Common footer with company information

### Template Variables
All templates support variable substitution using `{{variableName}}` syntax:

#### Common Variables (available in all templates)
- `{{companyName}}` - Company name
- `{{recipientEmail}}` - Re<PERSON>pient's email address
- `{{currentYear}}` - Current year
- `{{frontendUrl}}` - Frontend application URL

#### Template-Specific Variables
Each template has its own set of variables. See individual template files for specific variables.

### Conditional Content
Templates support conditional blocks:
```html
{{#if variableName}}
  This content shows only if variableName is truthy
{{/if}}
```

## 🚀 Usage

### Using the Email Template Service

```javascript
const emailTemplateService = require('./services/emailTemplateService');

// Render a template
const renderedEmail = await emailTemplateService.renderEmail('invitation', {
  subject: 'Welcome to My Energy Bill!',
  inviteeName: 'John Doe',
  userTypeDisplay: 'Energy Broker',
  registrationUrl: 'https://example.com/register'
});

// renderedEmail contains: { htmlBody, textBody, subject }
```

### Using the Email Service

```javascript
const emailService = require('./services/emailService');

// Send templated email
await emailService.sendTemplatedEmail('invitation', '<EMAIL>', {
  subject: 'Welcome!',
  inviteeName: 'John Doe',
  userTypeDisplay: 'Energy Broker',
  registrationUrl: 'https://example.com/register'
});

// Or use specific methods
await emailService.sendInvitationEmail(invitationData);
await emailService.sendPasswordResetEmail(email, token, userName);
```

## 📝 Creating New Templates

### 1. Create Template File
Create a new `.html` file in this directory with your email content:

```html
<h2>{{subject}}</h2>
<p>Hello {{userName}},</p>
<p>Your custom email content here...</p>

{{#if conditionalContent}}
<div class="highlight-box">
  <p>{{conditionalContent}}</p>
</div>
{{/if}}
```

### 2. Add Service Method
Add a method to the email service to use your template:

```javascript
async sendCustomEmail(userEmail, customData) {
  const variables = {
    subject: 'Custom Email Subject',
    userName: customData.name,
    conditionalContent: customData.message
  };

  const tags = [
    { Name: 'EmailType', Value: 'Custom' }
  ];

  return await this.sendTemplatedEmail('custom-template', userEmail, variables, tags);
}
```

## 🎨 Styling Guidelines

### CSS Classes Available
The base template provides these CSS classes:

- `.highlight-box` - Highlighted content box with border
- `.warning-box` - Warning/alert box with yellow background
- `.action-button` - Styled button for CTAs
- `.info-grid` - Two-column grid for information display
- `.info-label` / `.info-value` - Labels and values in info grid
- `.steps-list` - Numbered list with styled counters

### Color Scheme
- Primary: `#10b981` (Green)
- Secondary: `#059669` (Dark Green)
- Text: `#1f2937` (Dark Gray)
- Light Text: `#6b7280` (Gray)
- Background: `#f4f4f4` (Light Gray)

### Responsive Design
All templates are mobile-responsive with:
- Max width of 600px
- Flexible grid layouts
- Readable font sizes on mobile
- Touch-friendly button sizes

## 🔧 Development

### Template Caching
- Templates are cached in production for performance
- Cache is disabled in development for easier testing
- Use `emailTemplateService.clearCache()` to clear cache manually

### Testing Templates
```javascript
// Test template rendering
const testEmail = await emailTemplateService.renderEmail('template-name', {
  // test variables
});

console.log('HTML:', testEmail.htmlBody);
console.log('Text:', testEmail.textBody);
```

### Available Templates List
```javascript
const templates = await emailTemplateService.getAvailableTemplates();
console.log('Available templates:', templates);
```

## 🚀 Migration from Old System

To migrate from the old hardcoded email system:

1. **Run Migration Script**:
   ```bash
   node backend/scripts/migrate-email-service.js
   ```

2. **Verify Migration**:
   - Check that all email functionality works
   - Test sending emails with new templates
   - Verify template rendering

3. **Rollback if Needed**:
   ```bash
   node backend/scripts/migrate-email-service.js rollback
   ```

## 📊 Benefits of Template System

### ✅ Advantages
- **Maintainable**: Easy to update email content without code changes
- **Consistent**: Unified styling across all emails
- **Flexible**: Support for variables and conditional content
- **Professional**: Responsive design with company branding
- **Scalable**: Easy to add new email types
- **Testable**: Templates can be tested independently

### 🔄 Migration Benefits
- **Separation of Concerns**: Content separated from logic
- **Designer Friendly**: Non-developers can update email content
- **Version Control**: Email content changes are tracked
- **Reusable**: Common elements shared across templates
- **Performance**: Template caching in production

## 🆘 Troubleshooting

### Common Issues

1. **Template Not Found**
   - Check file exists in `backend/templates/emails/`
   - Verify filename matches template name (without .html)

2. **Variables Not Replaced**
   - Check variable names match exactly (case-sensitive)
   - Ensure variables are passed to renderEmail()

3. **Styling Issues**
   - Test in multiple email clients
   - Use inline styles for better compatibility
   - Check base.html for available CSS classes

4. **Conditional Content Not Working**
   - Verify `{{#if variable}}...{{/if}}` syntax
   - Check that variable is truthy (not null, undefined, or empty string)

### Debug Mode
Set `NODE_ENV=development` to disable template caching and enable detailed logging.

## 📞 Support

For questions or issues with the email template system:
1. Check this documentation
2. Review template files for examples
3. Test with the email template service directly
4. Contact the development team
