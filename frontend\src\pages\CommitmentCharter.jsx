import React, { useEffect } from 'react';
import { Link } from 'react-router-dom';
import HomeHeader from '../components/HomeHeader';
import Footer from '../components/Footer';
import { useForceScrollToTop } from '../utils/scrollToTop';
import '../styles/app.css';
import '../styles/pages.css';

const CommitmentCharter = () => {
  // Force scroll to top when page loads
  useEffect(() => {
    useForceScrollToTop();
  }, []);

  return (
    <div className="home-container">
      {/* Header */}
      <HomeHeader />

      {/* Hero Section */}
      <section className="page-hero">
        <div className="page-hero-content">
          <h1 className="page-hero-title">🛡️ Our Commitment Charter</h1>
          <p className="page-hero-subtitle">
            At My Energy Bill, we believe that trust and ethics should be at the heart of every energy-related decision. Our commitment charter reflects our values and the way we work — transparently, independently, and respectfully.
          </p>
        </div>
      </section>

      {/* Charter Content Section */}
      <section className="charter-section">
        <div className="charter-container">
          <div className="charter-content">
            <div className="charter-principles">
              <div className="principle-item">
                <div className="principle-icon">
                  🔍
                </div>
                <div className="principle-content">
                  <h3 className="principle-title">Transparency</h3>
                  <p className="principle-description">
                    Every offer we present is clear, detailed, and comparable — with no hidden costs or confusing terms. You'll always know what you're signing and why.
                  </p>
                </div>
              </div>

              <div className="principle-item">
                <div className="principle-icon">
                  ⚖️
                </div>
                <div className="principle-content">
                  <h3 className="principle-title">Neutrality</h3>
                  <p className="principle-description">
                    We are 100% independent from suppliers. We don't push any offer over another based on commissions. The best solution is the one that fits your needs — period.
                  </p>
                </div>
              </div>

              <div className="principle-item">
                <div className="principle-icon">
                  ⏱️
                </div>
                <div className="principle-content">
                  <h3 className="principle-title">Responsiveness</h3>
                  <p className="principle-description">
                    We respond to every request within 72 hours and ensure that every client receives support tailored to their situation and priorities.
                  </p>
                </div>
              </div>

              <div className="principle-item">
                <div className="principle-icon">
                  👤
                </div>
                <div className="principle-content">
                  <h3 className="principle-title">Human-first approach</h3>
                  <p className="principle-description">
                    No bots, no automated pressure. Each client is supported by a real advisor who takes the time to explain, validate, and follow up — before and after signing.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="cta-section">
        <div className="cta-content">
          <h2 className="cta-title">Experience Our Commitment</h2>
          <p className="cta-description">
            Join thousands who have chosen transparency, fairness, and expert guidance for their energy needs.
          </p>
          <Link to="/contact" className="btn btn-primary btn-large">Contact Us Today</Link>
        </div>
      </section>

      {/* Footer */}
      <Footer />
    </div>
  );
};

export default CommitmentCharter;
