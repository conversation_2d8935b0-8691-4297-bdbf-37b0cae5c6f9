const BrokerProfile = require('../models/BrokerProfile');
const User = require('../models/User');
const Offer = require('../models/Offer');
const Contract = require('../models/Contract');
const Appointment = require('../models/Appointment');

/**
 * Get broker dashboard statistics
 */
const getDashboardStats = async (req, res) => {
  try {
    const { cognitoId } = req.params;
    console.log('Fetching broker dashboard stats for user:', cognitoId);

    // Find the user first
    const user = await User.findOne({ cognitoId });
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Get real data from database
    const [
      totalClientsArray,
      activeDeals,
      totalAppointments,
      recentClients,
      recentDeals,
      recentAppointments
    ] = await Promise.all([
      // Count unique clients (users who have contracts through this broker)
      Contract.distinct('userId', { brokerId: user._id }),
      // Count active deals/contracts
      Contract.countDocuments({ brokerId: user._id, status: 'Active' }),
      // Count appointments for this broker
      Appointment.countDocuments({ agentId: user._id, agentType: 'Broker' }),
      // Get recent clients
      Contract.find({ brokerId: user._id })
        .sort({ createdAt: -1 })
        .limit(5)
        .populate('userId', 'firstName lastName userType')
        .populate('supplierId', 'companyName')
        .select('userId supplierId contractDetails startDate status monthlyValue'),
      // Get recent deals
      Contract.find({ brokerId: user._id })
        .sort({ createdAt: -1 })
        .limit(5)
        .populate('userId', 'firstName lastName')
        .populate('supplierId', 'companyName')
        .select('contractDetails startDate endDate status monthlyValue commission'),
      // Get recent appointments
      Appointment.find({ agentId: user._id, agentType: 'Broker' })
        .sort({ scheduledTime: 1 })
        .limit(5)
        .populate('userId', 'firstName lastName')
        .select('type scheduledTime status location notes')
    ]);

    // Calculate monthly commission from active contracts
    const activeContractsList = await Contract.find({
      brokerId: user._id,
      status: 'Active'
    }).select('commission monthlyValue');

    const monthlyCommission = activeContractsList.reduce((sum, contract) => {
      // Assume commission is a percentage of monthly value
      const commissionAmount = contract.commission
        ? (contract.monthlyValue * contract.commission / 100)
        : (contract.monthlyValue * 0.05); // Default 5% commission
      return sum + commissionAmount;
    }, 0);

    // Calculate conversion rate (deals closed vs total prospects)
    const totalProspects = await Appointment.countDocuments({
      agentId: user._id,
      agentType: 'Broker'
    });
    const closedDeals = await Contract.countDocuments({
      brokerId: user._id,
      status: 'Active'
    });
    const conversionRate = totalProspects > 0 ? (closedDeals / totalProspects * 100) : 0;

    // Get pipeline data
    const pipelineData = {
      prospects: await Appointment.countDocuments({
        agentId: user._id,
        agentType: 'Broker',
        type: 'Initial Consultation'
      }),
      qualified: await Appointment.countDocuments({
        agentId: user._id,
        agentType: 'Broker',
        type: 'Offer Discussion'
      }),
      proposal: await Contract.countDocuments({
        brokerId: user._id,
        status: 'Pending'
      }),
      closing: await Contract.countDocuments({
        brokerId: user._id,
        status: 'Active',
        createdAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) } // Last 30 days
      })
    };

    const stats = {
      totalClients: totalClientsArray.length,
      activeDeals,
      monthlyCommission: Math.round(monthlyCommission),
      conversionRate: Math.round(conversionRate * 100) / 100,
      totalAppointments,
      recentClients,
      recentDeals,
      recentAppointments,
      pipelineData
    };

    console.log('Broker dashboard stats retrieved successfully:', {
      totalClients: totalClientsArray.length,
      activeDeals,
      monthlyCommission: Math.round(monthlyCommission),
      conversionRate: Math.round(conversionRate * 100) / 100
    });

    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('Error fetching broker dashboard stats:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch dashboard statistics',
      error: error.message
    });
  }
};

/**
 * Get active clients for broker
 */
const getActiveClients = async (req, res) => {
  try {
    console.log('Fetching active clients for broker:', req.user.sub);

    // Mock data for development
    const clients = [
      {
        id: '1',
        name: 'Acme Corporation',
        type: 'Business',
        location: 'Paris',
        activeContracts: 3,
        monthlyValue: 1250,
        status: 'Active'
      },
      {
        id: '2',
        name: 'Green Manufacturing',
        type: 'Industrial',
        location: 'Lyon',
        activeContracts: 2,
        monthlyValue: 2100,
        status: 'Active'
      },
      {
        id: '3',
        name: 'Tech Solutions Ltd',
        type: 'Business',
        location: 'Marseille',
        activeContracts: 1,
        monthlyValue: 850,
        status: 'Active'
      }
    ];

    console.log('Active clients retrieved successfully');

    res.json({
      success: true,
      data: clients
    });
  } catch (error) {
    console.error('Error fetching active clients:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch active clients'
    });
  }
};

/**
 * Get recent deals for broker
 */
const getRecentDeals = async (req, res) => {
  try {
    console.log('Fetching recent deals for broker:', req.user.sub);

    // Mock data for development
    const deals = [
      {
        id: '1',
        clientName: 'Acme Corporation',
        supplier: 'EDF Energy',
        energyType: 'Electricity',
        commission: 450,
        closedDate: '2024-01-15',
        status: 'Completed'
      },
      {
        id: '2',
        clientName: 'Tech Solutions Ltd',
        supplier: 'Total Energies',
        energyType: 'Gas',
        commission: 320,
        closedDate: '2024-01-20',
        status: 'Pending'
      },
      {
        id: '3',
        clientName: 'Green Manufacturing',
        supplier: 'Engie',
        energyType: 'Both',
        commission: 680,
        closedDate: '2024-01-25',
        status: 'Completed'
      }
    ];

    console.log('Recent deals retrieved successfully');

    res.json({
      success: true,
      data: deals
    });
  } catch (error) {
    console.error('Error fetching recent deals:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch recent deals'
    });
  }
};

/**
 * Get all clients for broker
 */
const getAllClients = async (req, res) => {
  try {
    console.log('Fetching all clients for broker:', req.user.sub);

    // Mock data for development
    const clients = [
      {
        id: '1',
        name: 'Acme Corporation',
        type: 'Business',
        location: 'Paris',
        email: '<EMAIL>',
        phone: '+33 1 23 45 67 89',
        activeContracts: 3,
        monthlyValue: 1250,
        status: 'Active',
        joinDate: '2023-06-15',
        lastContact: '2024-01-20'
      },
      {
        id: '2',
        name: 'Green Manufacturing',
        type: 'Industrial',
        location: 'Lyon',
        email: '<EMAIL>',
        phone: '+33 4 78 90 12 34',
        activeContracts: 2,
        monthlyValue: 2100,
        status: 'Active',
        joinDate: '2023-08-22',
        lastContact: '2024-01-18'
      },
      {
        id: '3',
        name: 'Tech Solutions Ltd',
        type: 'Business',
        location: 'Marseille',
        email: '<EMAIL>',
        phone: '+33 4 91 23 45 67',
        activeContracts: 1,
        monthlyValue: 850,
        status: 'Active',
        joinDate: '2023-11-10',
        lastContact: '2024-01-15'
      },
      {
        id: '4',
        name: 'Retail Chain SA',
        type: 'Commercial',
        location: 'Toulouse',
        email: '<EMAIL>',
        phone: '+33 5 61 12 34 56',
        activeContracts: 0,
        monthlyValue: 0,
        status: 'Prospect',
        joinDate: '2024-01-05',
        lastContact: '2024-01-22'
      }
    ];

    console.log('All clients retrieved successfully');

    res.json({
      success: true,
      data: clients
    });
  } catch (error) {
    console.error('Error fetching all clients:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch clients'
    });
  }
};

/**
 * Add a new client
 */
const addClient = async (req, res) => {
  try {
    console.log('Adding new client for broker:', req.user.sub);
    console.log('Client data:', req.body);

    const {
      name,
      type,
      location,
      email,
      phone,
      notes
    } = req.body;

    // Validate required fields
    if (!name || !email) {
      return res.status(400).json({
        success: false,
        message: 'Name and email are required'
      });
    }

    // In a real implementation, you would save this to the database
    // For now, return a mock response
    const newClient = {
      id: Date.now().toString(),
      name,
      type: type || 'Business',
      location: location || 'France',
      email,
      phone: phone || '',
      activeContracts: 0,
      monthlyValue: 0,
      status: 'Prospect',
      joinDate: new Date().toISOString(),
      lastContact: new Date().toISOString(),
      notes: notes || '',
      brokerId: req.user.sub
    };

    console.log('Client added successfully:', newClient.id);

    res.status(201).json({
      success: true,
      message: 'Client added successfully',
      data: newClient
    });
  } catch (error) {
    console.error('Error adding client:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to add client'
    });
  }
};

/**
 * Get broker profile
 */
const getProfile = async (req, res) => {
  try {
    console.log('Fetching broker profile for user:', req.user.sub);

    // Find user by Cognito ID
    const user = await User.findOne({ cognitoId: req.user.sub });
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Find broker profile
    const brokerProfile = await BrokerProfile.findOne({ userId: user._id });
    if (!brokerProfile) {
      return res.status(404).json({
        success: false,
        message: 'Broker profile not found'
      });
    }

    console.log('Broker profile retrieved successfully');

    res.json({
      success: true,
      data: {
        user: {
          firstName: user.firstName,
          lastName: user.lastName,
          email: user.email,
          userType: user.userType
        },
        profile: brokerProfile
      }
    });
  } catch (error) {
    console.error('Error fetching broker profile:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch broker profile'
    });
  }
};

/**
 * Update broker profile
 */
const updateProfile = async (req, res) => {
  try {
    console.log('Updating broker profile for user:', req.user.sub);
    console.log('Profile data:', req.body);

    // Find user by Cognito ID
    const user = await User.findOne({ cognitoId: req.user.sub });
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Update or create broker profile
    const brokerProfile = await BrokerProfile.findOneAndUpdate(
      { userId: user._id },
      { ...req.body, userId: user._id },
      { new: true, upsert: true }
    );

    console.log('Broker profile updated successfully');

    res.json({
      success: true,
      message: 'Broker profile updated successfully',
      data: brokerProfile
    });
  } catch (error) {
    console.error('Error updating broker profile:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update broker profile'
    });
  }
};

/**
 * Get broker analytics
 */
const getAnalytics = async (req, res) => {
  try {
    console.log('Fetching broker analytics for user:', req.user.sub);
    const { period = '30d' } = req.query;

    // Mock analytics data
    const analytics = {
      commission: {
        current: 3250,
        previous: 2890,
        growth: 12.5
      },
      clients: {
        total: 28,
        new: 5,
        retention: 92.8
      },
      deals: {
        total: 12,
        completed: 8,
        pending: 4,
        conversionRate: 15.5
      }
    };

    console.log('Broker analytics retrieved successfully');

    res.json({
      success: true,
      data: analytics
    });
  } catch (error) {
    console.error('Error fetching broker analytics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch analytics'
    });
  }
};

module.exports = {
  getDashboardStats,
  getActiveClients,
  getRecentDeals,
  getAllClients,
  addClient,
  getProfile,
  updateProfile,
  getAnalytics
};
