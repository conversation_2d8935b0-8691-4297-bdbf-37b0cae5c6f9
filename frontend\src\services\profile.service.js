import { Auth } from 'aws-amplify';
import { API_BASE_URL } from '../config/api-config';
import { getItem, STORAGE_KEYS } from '../utils/localStorage';

/**
 * Update the profileComplete attribute in Cognito
 * @param {boolean} isComplete - Whether the profile is complete
 * @returns {Promise} Promise object representing the update result
 */
const updateProfileCompletionInCognito = async (isComplete) => {
  try {
    console.log('Updating profileComplete in Cognito:', isComplete);

    // Get the current authenticated user
    const user = await Auth.currentAuthenticatedUser();
    console.log('Current user:', user.username);

    // Log the current user attributes for debugging
    console.log('Current user attributes:', user.attributes);

    // Update the custom:profileComplete attribute directly
    try {
      // Make sure we're using the correct attribute name with the 'custom:' prefix
      // Try with profileCompletion first (the likely correct name)
      const result = await Auth.updateUserAttributes(user, {
        'custom:profileCompletion': isComplete.toString()
      });

      console.log('Cognito profileComplete update result:', result);
      return { success: true, result };
    } catch (directUpdateError) {
      console.error('Error directly updating profileComplete in Cognito:', directUpdateError);
      console.error('Error details:', directUpdateError.code, directUpdateError.message);

      // Try again with a different approach
      try {
        // Try with profileComplete as a fallback
        const alternativeResult = await Auth.updateUserAttributes(user, {
          'custom:profileComplete': isComplete.toString()
        });

        console.log('Alternative Cognito update result:', alternativeResult);
        return { success: true, result: alternativeResult };
      } catch (alternativeError) {
        console.error('Alternative update also failed:', alternativeError);

        // Try one more approach with the attribute without prefix
        try {
          const lastAttemptResult = await Auth.updateUserAttributes(user, {
            'profileCompletion': isComplete.toString()
          });

          console.log('Last attempt Cognito update result:', lastAttemptResult);
          return { success: true, result: lastAttemptResult };
        } catch (lastAttemptError) {
          console.error('Last attempt also failed:', lastAttemptError);

          // Fallback to API method if direct update fails
          try {
            const username = user.username;

            // Get the token from the current session
            const session = await Auth.currentSession();
            const token = session.getIdToken().getJwtToken();

            // Call the API to update the profileComplete attribute
            const response = await fetch(`${API_BASE_URL}/api/users/update-profile-completion`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
              },
              body: JSON.stringify({
                email: username,
                profileComplete: isComplete,
                userType: 'individual'
              })
            });

            if (!response.ok) {
              const errorText = await response.text();
              console.error('Error response from server:', errorText);
              throw new Error(errorText || 'Failed to update profileComplete in Cognito');
            }

            const result = await response.json();
            console.log('Cognito profileComplete update result (via API):', result);
            return { success: true, result };
          } catch (apiError) {
            console.error('API fallback also failed:', apiError);
            return { success: false, error: apiError.message };
          }
        }
      }
    }
  } catch (error) {
    console.error('Error updating profileComplete in Cognito:', error);
    // Don't throw the error, just log it and return a default response
    // This allows the flow to continue even if this update fails
    return { success: false, error: error.message };
  }
};

/**
 * Update the profileComplete field in MongoDB
 * @param {boolean} isComplete - Whether the profile is complete
 * @param {string} userType - The user type (optional)
 * @returns {Promise} Promise object representing the update result
 */
const updateProfileCompletionInMongoDB = async (isComplete, userType) => {
  try {
    console.log('Updating profileComplete in MongoDB:', isComplete, 'userType:', userType);

    // Get the current authenticated user
    const user = await Auth.currentAuthenticatedUser();

    // Get user type from Cognito if not provided
    let effectiveUserType = userType;
    if (!effectiveUserType && user.attributes && user.attributes['custom:userType']) {
      effectiveUserType = user.attributes['custom:userType'];
    }

    // If still no user type, try localStorage
    if (!effectiveUserType) {
      effectiveUserType = getItem(STORAGE_KEYS.USER_TYPE);
    }

    console.log('Effective user type for MongoDB update:', effectiveUserType);

    // Call the API to update the profileComplete field
    const url = `${API_BASE_URL}/api/users/update-profile-completion`;
    console.log('Making request to:', url);

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          email: user.username,
          cognitoId: user.attributes.sub,
          profileComplete: isComplete,
          userType: effectiveUserType
        })
      });

      console.log('Response status:', response.status);

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Error response from server:', errorData);
        throw new Error(errorData.message || 'Failed to update profile completion status');
      }

      const result = await response.json();
      console.log('Profile completion update result:', result);
      return result;
    } catch (error) {
      console.error('Error updating profile completion status:', error);
      throw error;
    }
  } catch (error) {
    console.error('Error updating profileComplete in MongoDB:', error);
    // Don't throw the error, just log it and return a default response
    // This allows the flow to continue even if this update fails
    return { success: false, error: error.message };
  }
};

/**
 * Update profile completion status in both Cognito and MongoDB
 * @param {boolean} isComplete - Whether the profile is complete
 * @param {string} userType - The user type (optional)
 * @returns {Promise} Promise object representing the update result
 */
const updateProfileCompletion = async (isComplete, userType) => {
  try {
    console.log('Updating profile completion status in both systems:', isComplete, 'userType:', userType);

    // Get user type from localStorage if not provided
    let effectiveUserType = userType;
    if (!effectiveUserType) {
      effectiveUserType = getItem(STORAGE_KEYS.USER_TYPE);
    }

    console.log('Effective user type for updates:', effectiveUserType);

    // Update in Cognito - don't await to allow parallel execution
    const cognitoPromise = updateProfileCompletionInCognito(isComplete);

    // Update in MongoDB - don't await to allow parallel execution
    const mongoPromise = updateProfileCompletionInMongoDB(isComplete, effectiveUserType);

    // Wait for both promises to resolve
    const [cognitoResult, mongoResult] = await Promise.allSettled([cognitoPromise, mongoPromise]);

    console.log('Profile completion update results:', {
      cognito: cognitoResult,
      mongodb: mongoResult
    });

    // Return success if either one succeeded
    return {
      success: cognitoResult.status === 'fulfilled' || mongoResult.status === 'fulfilled',
      cognito: cognitoResult.status === 'fulfilled' ? cognitoResult.value : { error: cognitoResult.reason },
      mongodb: mongoResult.status === 'fulfilled' ? mongoResult.value : { error: mongoResult.reason }
    };
  } catch (error) {
    console.error('Error updating profile completion status:', error);
    // Don't throw the error, just return a failure result
    return { success: false, error: error.message };
  }
};



/**
 * Fetch user profile data from the backend
 * @returns {Promise} Promise object representing the user profile data
 */
const fetchUserProfile = async () => {
  try {
    // First try to get token from localStorage
    let token = getItem(STORAGE_KEYS.ACCESS_TOKEN);

    // If no token in localStorage, try to get it from the current session
    if (!token) {
      try {
        const session = await Auth.currentSession();
        token = session.getAccessToken().getJwtToken();
      } catch (sessionError) {
        console.error('Error getting current session:', sessionError);
        throw new Error('No access token found. Please log in again.');
      }
    }

    // Get the Cognito ID from localStorage
    const cognitoId = getItem(STORAGE_KEYS.COGNITO_ID);

    if (!cognitoId) {
      throw new Error('No Cognito ID found. Please log in again.');
    }

    console.log('Fetching user profile for Cognito ID:', cognitoId);

    // Fetch user data from API
    const response = await fetch(`${API_BASE_URL}/api/users/cognito/${cognitoId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      }
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to fetch user profile');
    }

    const userData = await response.json();
    console.log('User profile fetched successfully:', userData);
    return userData;
  } catch (error) {
    console.error('Error fetching user profile:', error);
    throw error;
  }
};

/**
 * Fetch individual profile data from the backend
 * @returns {Promise} Promise object representing the individual profile data
 */
const fetchIndividualProfile = async () => {
  try {
    const userData = await fetchUserProfile();
    return userData.data?.profile || null;
  } catch (error) {
    console.error('Error fetching individual profile:', error);
    throw error;
  }
};

/**
 * Fetch professional profile data from the backend
 * @returns {Promise} Promise object representing the professional profile data
 */
const fetchProfessionalProfile = async () => {
  try {
    const userData = await fetchUserProfile();
    return userData.data?.profile || null;
  } catch (error) {
    console.error('Error fetching professional profile:', error);
    throw error;
  }
};

/**
 * Fetch broker profile data from the backend
 * @returns {Promise} Promise object representing the broker profile data
 */
const fetchBrokerProfile = async () => {
  try {
    // First try to get token from localStorage
    let token = getItem(STORAGE_KEYS.ACCESS_TOKEN);

    // If no token in localStorage, try to get it from the current session
    if (!token) {
      try {
        const session = await Auth.currentSession();
        token = session.getAccessToken().getJwtToken();
      } catch (sessionError) {
        console.error('Error getting current session:', sessionError);
        throw new Error('No access token found. Please log in again.');
      }
    }

    console.log('Fetching broker profile from API');

    // Fetch broker profile from API
    const response = await fetch(`${API_BASE_URL}/api/broker/profile`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      }
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to fetch broker profile');
    }

    const brokerData = await response.json();
    console.log('Broker profile fetched successfully:', brokerData);
    return brokerData.data || null;
  } catch (error) {
    console.error('Error fetching broker profile:', error);
    throw error;
  }
};

/**
 * Fetch supplier profile data from the backend
 * @returns {Promise} Promise object representing the supplier profile data
 */
const fetchSupplierProfile = async () => {
  try {
    // First try to get token from localStorage
    let token = getItem(STORAGE_KEYS.ACCESS_TOKEN);

    // If no token in localStorage, try to get it from the current session
    if (!token) {
      try {
        const session = await Auth.currentSession();
        token = session.getAccessToken().getJwtToken();
      } catch (sessionError) {
        console.error('Error getting current session:', sessionError);
        throw new Error('No access token found. Please log in again.');
      }
    }

    console.log('Fetching supplier profile from API');

    // Fetch supplier profile from API
    const response = await fetch(`${API_BASE_URL}/api/supplier/profile`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      }
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to fetch supplier profile');
    }

    const supplierData = await response.json();
    console.log('Supplier profile fetched successfully:', supplierData);
    return supplierData.data || null;
  } catch (error) {
    console.error('Error fetching supplier profile:', error);
    throw error;
  }
};

/**
 * Generic function to fetch user data with profile based on user type
 * @param {string} userType - The type of user (individual, professional, broker, supplier)
 * @returns {Promise} Promise object representing the complete user data with profile
 */
const fetchUserDataByType = async (userType) => {
  try {
    console.log(`Fetching user data for type: ${userType}`);

    // Get basic user data from Cognito
    const user = await Auth.currentAuthenticatedUser();
    const basicUserData = {
      firstName: user.attributes.given_name || '',
      lastName: user.attributes.family_name || '',
      email: user.attributes.email || '',
      phoneNumber: user.attributes.phone_number || '',
      cognitoId: user.attributes.sub || '',
      userType: userType
    };

    // Fetch profile data based on user type
    let profileData = null;
    try {
      switch (userType.toLowerCase()) {
        case 'individual':
          profileData = await fetchIndividualProfile();
          break;
        case 'professional':
          profileData = await fetchProfessionalProfile();
          break;
        case 'broker':
          profileData = await fetchBrokerProfile();
          break;
        case 'supplier':
          profileData = await fetchSupplierProfile();
          break;
        default:
          console.warn(`Unknown user type: ${userType}`);
      }
    } catch (profileError) {
      console.warn(`Error fetching ${userType} profile:`, profileError);
      // Continue with basic user data if profile fetch fails
    }

    // Merge basic user data with profile data
    const mergedData = { ...basicUserData };

    if (profileData) {
      // Add profile-specific data based on user type
      if (userType.toLowerCase() === 'individual' && profileData.profile) {
        mergedData.streetAddress = profileData.profile.address?.street || '';
        mergedData.city = profileData.profile.address?.city || '';
        mergedData.postalCode = profileData.profile.address?.postalCode || '';
        mergedData.country = profileData.profile.address?.country || 'France';
        mergedData.meterNumber = profileData.profile.energyIdentifiers?.pdl || '';
        mergedData.authorizeDataAccess = profileData.profile.authorizeDataAccess || false;
      } else if (userType.toLowerCase() === 'professional' && profileData.profile) {
        mergedData.companyName = profileData.profile.companyName || '';
        mergedData.streetAddress = profileData.profile.companyAddress?.street || '';
        mergedData.city = profileData.profile.companyAddress?.city || '';
        mergedData.postalCode = profileData.profile.companyAddress?.postalCode || '';
        mergedData.country = profileData.profile.companyAddress?.country || 'France';
        mergedData.siretNumber = profileData.profile.siretNumber || '';
        mergedData.vatNumber = profileData.profile.vatNumber || '';
        mergedData.companyRole = profileData.profile.companyRole || '';
        mergedData.businessType = profileData.profile.businessType || '';
      } else if (userType.toLowerCase() === 'broker' && profileData.profile) {
        mergedData.companyName = profileData.profile.companyName || '';
        mergedData.streetAddress = profileData.profile.companyAddress?.street || '';
        mergedData.city = profileData.profile.companyAddress?.city || '';
        mergedData.postalCode = profileData.profile.companyAddress?.postalCode || '';
        mergedData.country = profileData.profile.companyAddress?.country || 'France';
        mergedData.licenseNumber = profileData.profile.licenseNumber || '';
        mergedData.specializations = profileData.profile.specializations || [];
      } else if (userType.toLowerCase() === 'supplier' && profileData.profile) {
        mergedData.companyName = profileData.profile.companyName || '';
        mergedData.streetAddress = profileData.profile.companyAddress?.street || '';
        mergedData.city = profileData.profile.companyAddress?.city || '';
        mergedData.postalCode = profileData.profile.companyAddress?.postalCode || '';
        mergedData.country = profileData.profile.companyAddress?.country || 'France';
        mergedData.licenseNumber = profileData.profile.licenseNumber || '';
        mergedData.energyTypes = profileData.profile.energyTypes || [];
      }
    }

    console.log(`Merged ${userType} user data:`, mergedData);
    return mergedData;
  } catch (error) {
    console.error(`Error fetching ${userType} user data:`, error);
    throw error;
  }
};

const profileService = {
  updateProfileCompletion,
  updateProfileCompletionInCognito,
  updateProfileCompletionInMongoDB,
  fetchUserProfile,
  fetchIndividualProfile,
  fetchProfessionalProfile,
  fetchBrokerProfile,
  fetchSupplierProfile,
  fetchUserDataByType
};

export default profileService;
