import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { showSuccessMessage, showErrorMessage, showInfoMessage } from '../utils/toastNotifications';
import DashboardLayout from '../components/DashboardLayout';
import supplierService from '../services/supplier.service';
import logger from '../utils/logger';
import '../styles/create-offer.css';

const CreateOffer = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    energyType: 'Electricity',
    rateType: 'Fixed',
    baseRate: '',
    standingCharge: '',
    duration: '12',
    serviceAreas: [],
    contractTypes: [],
    additionalBenefits: [],
    validUntil: '',
    isActive: true
  });

  const energyTypes = ['Electricity', 'Gas', 'Both'];
  const rateTypes = ['Fixed', 'Variable', 'Indexed', 'Green'];
  const durations = ['6', '12', '18', '24', '36'];
  const serviceAreaOptions = ['Paris', 'Lyon', 'Marseille', 'Toulouse', 'Nice', 'Nantes', 'Strasbourg', 'Montpellier'];
  const contractTypeOptions = ['Residential', 'Commercial', 'Industrial', 'Small Business'];
  const benefitOptions = ['24/7 Support', 'Green Energy', 'Smart Meter', 'Online Management', 'Price Guarantee', 'No Exit Fees'];

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleMultiSelectChange = (name, value) => {
    setFormData(prev => ({
      ...prev,
      [name]: prev[name].includes(value)
        ? prev[name].filter(item => item !== value)
        : [...prev[name], value]
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    try {
      setLoading(true);
      logger.info('Creating new offer:', formData);

      // Validate required fields
      if (!formData.name || !formData.baseRate || !formData.standingCharge) {
        showErrorMessage('VALIDATION_FAILED', 'Please fill in all required fields');
        return;
      }

      // Create the offer
      const response = await supplierService.createOffer({
        ...formData,
        baseRate: parseFloat(formData.baseRate),
        standingCharge: parseFloat(formData.standingCharge),
        duration: parseInt(formData.duration)
      });

      if (response.success) {
        logger.info('Offer created successfully:', response.data);
        showSuccessMessage('OFFER_CREATED');
        navigate('/my-offers');
      } else {
        throw new Error(response.message || 'Failed to create offer');
      }
    } catch (error) {
      logger.error('Error creating offer:', error);
      showErrorMessage('OFFER_CREATION_FAILED', `Failed to create offer: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    navigate('/dashboard');
  };

  return (
    <DashboardLayout>
      <div className="create-offer-container create-offer-page">
        <div className="create-offer-header">
          <h1>
            <i className="fas fa-plus"></i>
            Create New Energy Offer
          </h1>
          <p>Create a new energy offer for your customers</p>
        </div>

        <form onSubmit={handleSubmit} className="create-offer-form">
          {/* Basic Information */}
          <div className="form-section">
            <h3>Basic Information</h3>

            <div className="form-row">
              <div className="form-group">
                <label htmlFor="name">Offer Name *</label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  placeholder="e.g., Green Energy Fixed Rate Plan"
                  required
                />
              </div>

              <div className="form-group">
                <label htmlFor="energyType">Energy Type *</label>
                <select
                  id="energyType"
                  name="energyType"
                  value={formData.energyType}
                  onChange={handleInputChange}
                  required
                >
                  {energyTypes.map(type => (
                    <option key={type} value={type}>{type}</option>
                  ))}
                </select>
              </div>
            </div>

            <div className="form-group">
              <label htmlFor="description">Description</label>
              <textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                placeholder="Describe your energy offer..."
                rows="3"
              />
            </div>
          </div>

          {/* Pricing */}
          <div className="form-section">
            <h3>Pricing</h3>

            <div className="form-row">
              <div className="form-group">
                <label htmlFor="rateType">Rate Type *</label>
                <select
                  id="rateType"
                  name="rateType"
                  value={formData.rateType}
                  onChange={handleInputChange}
                  required
                >
                  {rateTypes.map(type => (
                    <option key={type} value={type}>{type}</option>
                  ))}
                </select>
              </div>

              <div className="form-group">
                <label htmlFor="baseRate">Base Rate (€/kWh) *</label>
                <input
                  type="number"
                  id="baseRate"
                  name="baseRate"
                  value={formData.baseRate}
                  onChange={handleInputChange}
                  placeholder="0.145"
                  step="0.001"
                  min="0"
                  required
                />
              </div>

              <div className="form-group">
                <label htmlFor="standingCharge">Standing Charge (€/month) *</label>
                <input
                  type="number"
                  id="standingCharge"
                  name="standingCharge"
                  value={formData.standingCharge}
                  onChange={handleInputChange}
                  placeholder="25.50"
                  step="0.01"
                  min="0"
                  required
                />
              </div>
            </div>
          </div>

          {/* Contract Details */}
          <div className="form-section">
            <h3>Contract Details</h3>

            <div className="form-row">
              <div className="form-group">
                <label htmlFor="duration">Duration (months) *</label>
                <select
                  id="duration"
                  name="duration"
                  value={formData.duration}
                  onChange={handleInputChange}
                  required
                >
                  {durations.map(duration => (
                    <option key={duration} value={duration}>{duration} months</option>
                  ))}
                </select>
              </div>

              <div className="form-group">
                <label htmlFor="validUntil">Valid Until</label>
                <input
                  type="date"
                  id="validUntil"
                  name="validUntil"
                  value={formData.validUntil}
                  onChange={handleInputChange}
                  min={new Date().toISOString().split('T')[0]}
                />
              </div>
            </div>
          </div>

          {/* Service Areas */}
          <div className="form-section">
            <h3>Service Areas</h3>
            <div className="checkbox-grid">
              {serviceAreaOptions.map(area => (
                <label key={area} className="checkbox-item">
                  <input
                    type="checkbox"
                    checked={formData.serviceAreas.includes(area)}
                    onChange={() => handleMultiSelectChange('serviceAreas', area)}
                  />
                  <span>{area}</span>
                </label>
              ))}
            </div>
          </div>

          {/* Contract Types */}
          <div className="form-section">
            <h3>Contract Types</h3>
            <div className="checkbox-grid">
              {contractTypeOptions.map(type => (
                <label key={type} className="checkbox-item">
                  <input
                    type="checkbox"
                    checked={formData.contractTypes.includes(type)}
                    onChange={() => handleMultiSelectChange('contractTypes', type)}
                  />
                  <span>{type}</span>
                </label>
              ))}
            </div>
          </div>

          {/* Additional Benefits */}
          <div className="form-section">
            <h3>Additional Benefits</h3>
            <div className="checkbox-grid">
              {benefitOptions.map(benefit => (
                <label key={benefit} className="checkbox-item">
                  <input
                    type="checkbox"
                    checked={formData.additionalBenefits.includes(benefit)}
                    onChange={() => handleMultiSelectChange('additionalBenefits', benefit)}
                  />
                  <span>{benefit}</span>
                </label>
              ))}
            </div>
          </div>

          {/* Status */}
          <div className="form-section">
            <div className="form-group">
              <label className="checkbox-item">
                <input
                  type="checkbox"
                  name="isActive"
                  checked={formData.isActive}
                  onChange={handleInputChange}
                />
                <span>Make this offer active immediately</span>
              </label>
            </div>
          </div>

          {/* Form Actions */}
          <div className="form-actions">
            <button
              type="button"
              className="btn-secondary"
              onClick={handleCancel}
              disabled={loading}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="btn-primary"
              disabled={loading}
            >
              {loading ? (
                <>
                  <i className="fas fa-spinner fa-spin"></i>
                  Creating...
                </>
              ) : (
                <>
                  <i className="fas fa-plus"></i>
                  Create Offer
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </DashboardLayout>
  );
};

export default CreateOffer;
