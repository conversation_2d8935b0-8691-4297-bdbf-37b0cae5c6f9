.settings-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.settings-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 30px;
  color: #000;
}

.settings-content {
  display: flex;
  gap: 30px;
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  border: 1px solid #f0f0f0;
}

.settings-tabs {
  width: 250px;
  background-color: #f9f9f9;
  padding: 20px 0;
  border-right: 1px solid #f0f0f0;
}

.tab-button {
  width: 100%;
  padding: 15px 20px;
  text-align: left;
  background: none;
  border: none;
  font-size: 16px;
  font-weight: 500;
  color: #666;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
}

.tab-button i {
  margin-right: 10px;
  width: 20px;
  text-align: center;
}

.tab-button:hover {
  background-color: #f0f0f0;
  color: #333;
}

.tab-button.active {
  background-color: #000;
  color: #fff;
}

.settings-panel {
  flex: 1;
  padding: 30px;
}

.settings-section {
  margin-bottom: 30px;
}

.settings-section h2 {
  font-size: 20px;
  font-weight: 600;
  color: #000;
  margin: 0 0 20px 0;
  padding-bottom: 10px;
  border-bottom: 1px solid #f0f0f0;
}

.settings-group {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 20px;
  border-bottom: 1px solid #f5f5f5;
}

.setting-item:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.setting-info {
  flex: 1;
}

.setting-info label {
  display: block;
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 5px;
}

.setting-description {
  font-size: 14px;
  color: #666;
  margin: 0;
}

.setting-control {
  margin-left: 20px;
}

/* Toggle Switch */
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
  border-radius: 24px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

input:checked + .toggle-slider {
  background: linear-gradient(to right, #000, #333);
}

input:checked + .toggle-slider:before {
  transform: translateX(26px);
}

/* Select */
.settings-select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  min-width: 150px;
}

/* Buttons */
.change-password-btn,
.view-history-btn {
  padding: 8px 16px;
  background-color: #f5f5f5;
  color: #333;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.change-password-btn:hover,
.view-history-btn:hover {
  background-color: #eee;
}

.delete-account-btn {
  padding: 8px 16px;
  background-color: #fff;
  color: #ff3b30;
  border: 1px solid #ff3b30;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.delete-account-btn:hover {
  background-color: #fff5f5;
}

.danger-zone {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px dashed #ff3b30;
}

.settings-actions {
  margin-top: 30px;
  display: flex;
  justify-content: flex-end;
}

.save-settings-btn {
  padding: 10px 20px;
  background: linear-gradient(to right, #000, #333);
  color: #fff;
  border: none;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.save-settings-btn:hover {
  opacity: 0.9;
  transform: translateY(-2px);
}

/* Responsive styles */
@media (max-width: 992px) {
  .settings-content {
    flex-direction: column;
  }
  
  .settings-tabs {
    width: 100%;
    display: flex;
    overflow-x: auto;
    padding: 15px;
    border-right: none;
    border-bottom: 1px solid #f0f0f0;
  }
  
  .tab-button {
    width: auto;
    white-space: nowrap;
    padding: 10px 15px;
  }
}

@media (max-width: 768px) {
  .setting-item {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .setting-control {
    margin-left: 0;
    margin-top: 10px;
    width: 100%;
  }
  
  .settings-select {
    width: 100%;
  }
}
