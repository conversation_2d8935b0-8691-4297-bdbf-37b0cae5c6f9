const express = require('express');
const router = express.Router();
const { verifyToken } = require('../middleware/auth');

// Protected route example
router.get('/protected', verifyToken, (req, res) => {
  res.json({
    message: 'This is a protected route',
    data: 'Secret data',
    user: {
      username: req.user.username,
      email: req.user.email
    }
  });
});

// Public route example
router.get('/public', (req, res) => {
  res.json({ message: 'This is a public route', data: 'Public data' });
});

module.exports = router;
