const API_BASE_URL = import.meta.env.VITE_APP_API_URL || 'http://localhost:3000';

// Submit contact form
export const submitContactForm = async (formData) => {
  try {
    const response = await fetch(`${API_BASE_URL}/api/contact/contact-form`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(formData),
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.message || 'Failed to submit contact form');
    }

    return data;
  } catch (error) {
    console.error('Error submitting contact form:', error);
    throw error;
  }
};

// Submit appointment booking
export const submitAppointmentBooking = async (appointmentData) => {
  try {
    const response = await fetch(`${API_BASE_URL}/api/contact/appointment`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(appointmentData),
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.message || 'Failed to book appointment');
    }

    return data;
  } catch (error) {
    console.error('Error booking appointment:', error);
    throw error;
  }
};

// Get contact forms (admin only)
export const getContactForms = async (params = {}) => {
  try {
    const token = localStorage.getItem('token');
    const queryParams = new URLSearchParams(params).toString();
    const url = `${API_BASE_URL}/api/contact/contact-forms${queryParams ? `?${queryParams}` : ''}`;

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.message || 'Failed to fetch contact forms');
    }

    return data;
  } catch (error) {
    console.error('Error fetching contact forms:', error);
    throw error;
  }
};

// Get appointment bookings (admin only)
export const getAppointmentBookings = async (params = {}) => {
  try {
    const token = localStorage.getItem('token');
    const queryParams = new URLSearchParams(params).toString();
    const url = `${API_BASE_URL}/api/contact/appointments${queryParams ? `?${queryParams}` : ''}`;

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.message || 'Failed to fetch appointment bookings');
    }

    return data;
  } catch (error) {
    console.error('Error fetching appointment bookings:', error);
    throw error;
  }
};
