import React, { useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { trackNavigation } from '../utils/navigationTracker';
import { useSmartNavigation } from '../hooks/useSmartNavigation';
import Spinner from './Spinner';

/**
 * ProtectedRoute component with smart navigation and data synchronization
 *
 * This component now uses the smart navigation system that:
 * - Syncs data between Cognito, Database, and localStorage
 * - Provides fallbacks when data sources are unavailable
 * - <PERSON><PERSON> cache freshness and automatic refresh
 * - Eliminates sync issues and data staleness
 */
const ProtectedRoute = ({ children }) => {
  const location = useLocation();

  // Use smart navigation hook for synchronized data and navigation decisions
  const {
    isLoading,
    isAuthenticated,
    userType,
    profileComplete,
    error,
    dataSource,
    navigationState
  } = useSmartNavigation({
    skipAuthCheck: false,
    skipProfileCheck: false
  });

  // Track navigation for analytics
  useEffect(() => {
    if (!isLoading && userType) {
      trackNavigation(
        'previous-page',
        location.pathname,
        'protected-route-access',
        {
          userType: userType,
          profileComplete: profileComplete,
          dataSource: dataSource,
          component: 'ProtectedRoute'
        }
      );
    }
  }, [isLoading, userType, profileComplete, dataSource, location.pathname]);

  // Show loading spinner while checking navigation state
  if (isLoading) {
    return <Spinner fullScreen={true} message="Loading your account..." size="large" />;
  }

  // Show error state if there's an error
  if (error) {
    console.error('ProtectedRoute - Navigation error:', error);
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        flexDirection: 'column',
        gap: '1rem'
      }}>
        <h2>Something went wrong</h2>
        <p>Please try refreshing the page or contact support if the problem persists.</p>
        <button onClick={() => window.location.reload()}>Refresh Page</button>
      </div>
    );
  }

  // The smart navigation hook handles all the complex logic:
  // - Authentication checks with Cognito
  // - Data sync between Cognito, Database, and localStorage
  // - Automatic redirects based on user type and profile completion
  // - Cache management and freshness checks
  // - Error handling and fallbacks

  // If navigation is handled by the hook (redirects), we don't render children
  // The hook will automatically navigate to the correct page

  // Render the protected content - smart navigation has already handled redirects
  return children;
};

export default ProtectedRoute;
