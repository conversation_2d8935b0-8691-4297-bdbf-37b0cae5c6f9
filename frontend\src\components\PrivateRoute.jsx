import React, { useEffect, useState } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import Spinner from './Spinner';
import { trackNavigation } from '../utils/navigationTracker';
import { getLoadingMessage } from '../utils/loadingMessages';

/**
 * PrivateRoute component that checks if the user is authenticated
 * and redirects to login if not.
 *
 * This is different from ProtectedRoute which also checks for user type
 * and profile completion. PrivateRoute only checks if the user is logged in.
 */
const PrivateRoute = ({ children }) => {
  const { isAuthenticated, loading } = useAuth();
  const [isChecking, setIsChecking] = useState(true);
  const location = useLocation();

  useEffect(() => {
    // Check authentication status
    const checkAuth = async () => {
      try {
        // Wait for the auth context to finish loading
        if (!loading) {
          // Double-check localStorage for authentication status
          const isAuthInStorage = localStorage.getItem('isAuthenticated') === 'true';

          // If auth context says not authenticated or localStorage says not authenticated
          if (!isAuthenticated || !isAuthInStorage) {
            console.log('PrivateRoute - Not authenticated according to context or localStorage');

            // Clear all localStorage data to ensure clean state
            localStorage.clear();
          }

          setIsChecking(false);
        }
      } catch (error) {
        console.error('PrivateRoute - Error checking authentication:', error);
        setIsChecking(false);
      }
    };

    checkAuth();
  }, [loading, isAuthenticated]);

  // Show loading spinner while checking authentication
  if (loading || isChecking) {
    return <Spinner fullScreen={true} message={getLoadingMessage('CHECKING_AUTH')} size="large" />;
  }

  // If not authenticated, redirect to login
  if (!isAuthenticated) {
    console.log('PrivateRoute - Not authenticated, redirecting to login');

    // Track this navigation event
    trackNavigation(
      location.pathname,
      '/login',
      'private-route-redirect',
      {
        reason: 'not-authenticated'
      }
    );

    // Redirect to login and save the location they were trying to access
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // If authenticated, render the children
  return children;
};

export default PrivateRoute;
