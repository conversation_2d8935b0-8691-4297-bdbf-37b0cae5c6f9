/* Dashboard Overview Styles */
.dashboard-overview {
  display: flex;
  flex-direction: column;
  gap: 24px;
  margin-top: 24px;
}

.overview-section {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f3f4f6;
}

/* Make dashboard section title more specific to avoid conflicts with home page */
.dashboard-overview .section-title,
.overview-section .section-title {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin: 0;
  text-align: left; /* Explicitly set for dashboard */
}

.section-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.refresh-btn {
  background: none;
  border: 1px solid #d1d5db;
  color: #6b7280;
  padding: 6px 8px;
  border-radius: 4px;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.refresh-btn:hover {
  background-color: #f9fafb;
  border-color: #9ca3af;
  color: #374151;
}

.refresh-btn i {
  transition: transform 0.3s ease;
}

.refresh-btn:active i {
  transform: rotate(180deg);
}

.view-all-btn {
  background: none;
  border: none;
  color: #3b82f6;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.view-all-btn:hover {
  background-color: #eff6ff;
  color: #2563eb;
}

.overview-items {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.overview-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border: 1px solid #f3f4f6;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  background: #fafafa;
}

.overview-item:hover {
  border-color: #d1d5db;
  background: #f9fafb;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.item-icon {
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  background: #f3f4f6;
}

.item-icon i {
  font-size: 18px;
  color: #6b7280;
}

/* Energy type specific colors for invoice icons */
.item-icon .electricity {
  color: #3b82f6;
}

.item-icon .gas {
  color: #f59e0b;
}

.item-details {
  flex: 1;
  min-width: 0;
}

.item-title {
  font-size: 14px;
  font-weight: 500;
  color: #111827;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.item-subtitle {
  font-size: 13px;
  color: #6b7280;
  margin-bottom: 2px;
}

.item-meta {
  font-size: 12px;
  color: #9ca3af;
}

.item-status {
  flex-shrink: 0;
}

.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Status badge colors */
.status-processed {
  background-color: #d1fae5;
  color: #065f46;
}

.status-pending {
  background-color: #fef3c7;
  color: #92400e;
}

.status-processing {
  background-color: #dbeafe;
  color: #1e40af;
}

.status-failed {
  background-color: #fee2e2;
  color: #dc2626;
}

.status-active {
  background-color: #dbeafe;
  color: #1e40af;
}

.status-completed {
  background-color: #e5e7eb;
  color: #374151;
}

.status-upcoming {
  background-color: #fef3c7;
  color: #92400e;
}

.status-scheduled {
  background-color: #e0e7ff;
  color: #3730a3;
}

.no-items {
  text-align: center;
  padding: 32px 24px;
  color: #6b7280;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.no-items i {
  font-size: 32px;
  color: #d1d5db;
  margin-bottom: 8px;
}

.no-items p {
  margin: 0;
  font-size: 14px;
  color: #6b7280;
}

.upload-first-btn {
  background: linear-gradient(135deg, #000 0%, #333 100%);
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 13px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: 4px;
}

.upload-first-btn:hover {
  background: linear-gradient(135deg, #333 0%, #555 100%);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.overview-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #6b7280;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f4f6;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 12px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .dashboard-overview {
    gap: 16px;
    margin-top: 16px;
  }

  .overview-section {
    padding: 16px;
  }

  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .section-actions {
    align-self: flex-end;
  }

  .overview-item {
    padding: 10px;
    gap: 10px;
  }

  .item-icon {
    width: 36px;
    height: 36px;
  }

  .item-icon i {
    font-size: 16px;
  }

  .item-title {
    font-size: 13px;
  }

  .item-subtitle {
    font-size: 12px;
  }

  .item-meta {
    font-size: 11px;
  }
}

@media (max-width: 480px) {
  .overview-item {
    flex-direction: column;
    align-items: flex-start;
    text-align: left;
  }

  .item-icon {
    align-self: center;
  }

  .item-details {
    width: 100%;
    text-align: center;
  }

  .item-status {
    align-self: center;
  }
}

/* Dark theme support (if needed) */
@media (prefers-color-scheme: dark) {
  .overview-section {
    background: #1f2937;
    border-color: #374151;
  }

  .section-title {
    color: #f9fafb;
  }

  .overview-item {
    background: #111827;
    border-color: #374151;
  }

  .overview-item:hover {
    background: #1f2937;
    border-color: #4b5563;
  }

  .item-icon {
    background: #374151;
  }

  .item-title {
    color: #f9fafb;
  }

  .item-subtitle {
    color: #d1d5db;
  }

  .item-meta {
    color: #9ca3af;
  }
}
