import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { showSuccessMessage, showErrorMessage, showInfoMessage } from '../utils/toastNotifications';
import DashboardLayout from '../components/DashboardLayout';
import Spinner from '../components/Spinner';
import OfferModal from '../components/OfferModal';
import offerService from '../services/offer.service';
import logger from '../utils/logger';
import '../styles/offers.css';

const Offers = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [offers, setOffers] = useState([]);
  const [selectedOffer, setSelectedOffer] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [acceptingOffer, setAcceptingOffer] = useState(null);



  useEffect(() => {
    fetchOffers();
  }, []);

  const fetchOffers = async () => {
    try {
      setLoading(true);
      logger.info('Fetching offers from API');

      const response = await offerService.getUserOffers();

      // If we get offers from the API, use them
      if (response.data && response.data.length > 0) {
        setOffers(response.data);
        logger.info('Offers loaded successfully from API:', response.data.length);
      } else {
        // If no offers from API, use mock data for better UX
        logger.info('No offers from API, using mock data');
        const mockOffers = getMockOffers();
        setOffers(mockOffers);
        logger.info('Mock offers loaded:', mockOffers.length);
      }
    } catch (error) {
      logger.error('Error fetching offers from API:', error);

      // Fallback to mock data instead of showing error
      logger.info('API failed, falling back to mock data');
      const mockOffers = getMockOffers();
      setOffers(mockOffers);

      // Show info message instead of error
      showInfoMessage('OFFERS_MOCK_DATA', 'Showing sample offers. Real offers will be available soon!');
    } finally {
      setLoading(false);
    }
  };

  // Keep mock data as fallback for development
  const getMockOffers = () => {
    return [
            {
              id: 1,
              provider: 'EDF Energy',
              name: 'Fixed Rate Electricity Plan',
              description: 'Lock in your electricity rates for 24 months with our best fixed rate plan.',
              energyType: 'Electricity',
              rateType: 'Fixed',
              duration: 24,
              price: {
                baseRate: 0.145,
                standingCharge: 25.5,
                totalEstimatedAnnual: 950,
                currency: 'EUR'
              },
              estimatedSavings: {
                amount: 120,
                percentage: 12
              },
              validUntil: '2023-12-31',
              status: 'Active',
              highlights: [
                'No price increases for 24 months',
                'No exit fees',
                '100% renewable electricity'
              ]
            },
            {
              id: 2,
              provider: 'Total Energies',
              name: 'Green Energy Plan',
              description: 'Eco-friendly energy plan with 100% renewable sources.',
              energyType: 'Both',
              rateType: 'Variable',
              duration: 12,
              price: {
                baseRate: 0.138,
                standingCharge: 22.8,
                totalEstimatedAnnual: 880,
                currency: 'EUR'
              },
              estimatedSavings: {
                amount: 150,
                percentage: 15
              },
              validUntil: '2023-11-30',
              status: 'Active',
              highlights: [
                'Carbon neutral energy',
                'Monthly billing',
                'Smart meter included'
              ]
            },
            {
              id: 3,
              provider: 'Engie',
              name: 'Economy Gas Plan',
              description: 'Affordable gas rates with flexible payment options.',
              energyType: 'Gas',
              rateType: 'Fixed',
              duration: 18,
              price: {
                baseRate: 0.065,
                standingCharge: 18.5,
                totalEstimatedAnnual: 720,
                currency: 'EUR'
              },
              estimatedSavings: {
                amount: 85,
                percentage: 10
              },
              validUntil: '2023-10-15',
              status: 'Active',
              highlights: [
                'Low standing charge',
                'Price match guarantee',
                'Online account management'
              ]
            }
    ];
  };

  const handleViewDetails = (offer) => {
    setSelectedOffer(offer);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedOffer(null);
  };

  const handleAcceptOffer = async (offerId) => {
    try {
      setAcceptingOffer(offerId);
      logger.info('Accepting offer:', offerId);

      const response = await offerService.acceptOffer(offerId);

      if (response.success) {
        logger.info('Offer accepted successfully:', response.data);

        // Show success message
        showSuccessMessage('OFFER_ACCEPTED', `Offer accepted! Contract ${response.data.contractNumber} has been created. You will be redirected to sign the contract.`);

        // Redirect to contract signing
        if (response.data.signingUrl) {
          window.open(response.data.signingUrl, '_blank');
        }

        // Refresh offers to show updated status
        await fetchOffers();

        // Close modal if open
        setIsModalOpen(false);
        setSelectedOffer(null);
      }
    } catch (error) {
      logger.error('Error accepting offer:', error);
      showErrorMessage('OFFER_ACCEPTANCE_FAILED', `Failed to accept offer: ${error.message}`);
    } finally {
      setAcceptingOffer(null);
    }
  };

  return (
    <>
      <DashboardLayout>
        <div className="offers-container">
          <div className="offers-header">
            <h1>Personalized Energy Offers</h1>
            <p>Based on your energy consumption and preferences, we've found these offers for you.</p>
          </div>

          {loading ? (
            <div className="offers-loading">
              <Spinner size="medium" message="Loading your personalized offers..." />
            </div>
          ) : (
            <div className="offers-grid">
              {offers.length > 0 ? (
                offers.map(offer => (
                  <div key={offer.id} className="offer-card-compact" onClick={() => handleViewDetails(offer)}>
                    <div className="offer-header-compact">
                      <div className="offer-provider-compact">
                        <span className="provider-name-compact">{offer.provider}</span>
                        <span className={`offer-type-compact ${offer.energyType.toLowerCase()}`}>
                          {offer.energyType}
                        </span>
                      </div>
                      <h3 className="offer-name-compact">{offer.name}</h3>
                    </div>

                    <div className="offer-content-compact">
                      <div className="offer-pricing-compact">
                        <div className="price-item-compact">
                          <span className="price-label-compact">Base Rate</span>
                          <span className="price-value-compact">{offer.price.baseRate} €/kWh</span>
                        </div>
                        <div className="price-item-compact">
                          <span className="price-label-compact">Duration</span>
                          <span className="price-value-compact">{offer.duration} months</span>
                        </div>
                      </div>

                      <div className="offer-additional-details">
                        <div className="detail-row-compact">
                          <span className="detail-label-compact">Rate Type:</span>
                          <span className="detail-value-compact">{offer.rateType}</span>
                        </div>
                        <div className="detail-row-compact">
                          <span className="detail-label-compact">Standing Charge:</span>
                          <span className="detail-value-compact">{offer.price.standingCharge} €/month</span>
                        </div>
                        <div className="detail-row-compact">
                          <span className="detail-label-compact">Annual Cost:</span>
                          <span className="detail-value-compact">{offer.price.totalEstimatedAnnual} €</span>
                        </div>
                      </div>

                      <div className="offer-savings-compact">
                        <div className="savings-compact">
                          <span className="savings-label-compact">Annual Savings</span>
                          <div className="savings-values-compact">
                            <span className="savings-amount-compact">{offer.estimatedSavings.amount} €</span>
                            <span className="savings-percentage-compact">({offer.estimatedSavings.percentage}%)</span>
                          </div>
                        </div>
                      </div>

                      <div className="offer-highlights-compact">
                        <div className="highlights-list-compact">
                          {offer.highlights.slice(0, 2).map((highlight, index) => (
                            <div key={index} className="highlight-item-compact">
                              <i className="fas fa-check-circle"></i>
                              <span>{highlight}</span>
                            </div>
                          ))}
                        </div>
                      </div>

                      <div className="offer-validity-compact">
                        <i className="fas fa-calendar-alt"></i>
                        <span>Valid until: {new Date(offer.validUntil).toLocaleDateString()}</span>
                      </div>
                    </div>

                    <div className="offer-footer-compact">
                      <button
                        className="btn-details-compact"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleViewDetails(offer);
                        }}
                      >
                        View Details
                      </button>
                      <button
                        className="btn-accept-compact"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleAcceptOffer(offer.id);
                        }}
                        disabled={acceptingOffer === offer.id}
                      >
                        {acceptingOffer === offer.id ? (
                          <>
                            <i className="fas fa-spinner fa-spin"></i> Accepting...
                          </>
                        ) : (
                          'Accept Offer'
                        )}
                      </button>
                    </div>
                  </div>
                ))
              ) : (
                <div className="no-offers">
                  <i className="fas fa-search"></i>
                  <h3>No offers available</h3>
                  <p>We're currently looking for the best energy deals for you. Check back soon!</p>
                </div>
              )}
            </div>
          )}
        </div>
      </DashboardLayout>

      {/* Offer Details Modal - Outside DashboardLayout */}
      {isModalOpen && selectedOffer && (
        <OfferModal
          offer={selectedOffer}
          isOpen={isModalOpen}
          onClose={handleCloseModal}
          onAccept={() => handleAcceptOffer(selectedOffer.id)}
        />
      )}
    </>
  );
};

export default Offers;
