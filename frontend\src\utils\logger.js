/**
 * Logger utility for the frontend
 * Provides consistent logging with different levels and formats
 * Can be configured to send critical logs to the backend
 */

// Define log levels
const LOG_LEVELS = {
  ERROR: 0,
  WARN: 1,
  INFO: 2,
  DEBUG: 3,
};

// Get current environment
const isDevelopment = process.env.NODE_ENV === 'development';

// Default log level based on environment
const defaultLogLevel = isDevelopment ? LOG_LEVELS.DEBUG : LOG_LEVELS.INFO;

// Current log level - can be changed at runtime
let currentLogLevel = defaultLogLevel;

// Sensitive data patterns to mask
const sensitivePatterns = [
  { regex: /(password["']?\s*:\s*["']?)([^"',\s]+)(["',])/gi, replacement: '$1********$3' },
  { regex: /(Authorization["']?\s*:\s*["']?Bearer\s+)([^"',\s]+)(["',])/gi, replacement: '$1********$3' },
  { regex: /(accessKeyId["']?\s*:\s*["']?)([^"',\s]+)(["',])/gi, replacement: '$1********$3' },
  { regex: /(secretAccessKey["']?\s*:\s*["']?)([^"',\s]+)(["',])/gi, replacement: '$1********$3' },
  { regex: /(sessionToken["']?\s*:\s*["']?)([^"',\s]+)(["',])/gi, replacement: '$1********$3' },
  { regex: /(apiKey["']?\s*:\s*["']?)([^"',\s]+)(["',])/gi, replacement: '$1********$3' },
  { regex: /(token["']?\s*:\s*["']?)([^"',\s]+)(["',])/gi, replacement: '$1********$3' },
  { regex: /(jwt["']?\s*:\s*["']?)([^"',\s]+)(["',])/gi, replacement: '$1********$3' },
  { regex: /(refresh_token["']?\s*:\s*["']?)([^"',\s]+)(["',])/gi, replacement: '$1********$3' },
  { regex: /(id_token["']?\s*:\s*["']?)([^"',\s]+)(["',])/gi, replacement: '$1********$3' },
  { regex: /(access_token["']?\s*:\s*["']?)([^"',\s]+)(["',])/gi, replacement: '$1********$3' },
  { regex: /([A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,})/gi, replacement: '****@****.com' },
  { regex: /(\d{3}[-\s]?\d{3}[-\s]?\d{4})/g, replacement: '***-***-****' },
  { regex: /(\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4})/g, replacement: '****-****-****-****' },
];

// Function to mask sensitive data
const maskSensitiveData = (data) => {
  if (typeof data === 'string') {
    let maskedData = data;
    sensitivePatterns.forEach(pattern => {
      maskedData = maskedData.replace(pattern.regex, pattern.replacement);
    });
    return maskedData;
  } else if (typeof data === 'object' && data !== null) {
    // Handle objects (including arrays)
    const maskedObj = Array.isArray(data) ? [...data] : { ...data };
    
    Object.keys(maskedObj).forEach(key => {
      if (typeof maskedObj[key] === 'string') {
        maskedObj[key] = maskSensitiveData(maskedObj[key]);
      } else if (typeof maskedObj[key] === 'object' && maskedObj[key] !== null) {
        maskedObj[key] = maskSensitiveData(maskedObj[key]);
      }
    });
    
    return maskedObj;
  }
  
  // Return unchanged for other types
  return data;
};

// Format log message
const formatLogMessage = (level, message, ...args) => {
  const timestamp = new Date().toISOString();
  let formattedArgs = args.map(arg => {
    if (typeof arg === 'object' && arg !== null) {
      return maskSensitiveData(arg);
    }
    return arg;
  });
  
  return {
    timestamp,
    level,
    message: maskSensitiveData(message),
    args: formattedArgs
  };
};

// Log to console with appropriate styling
const logToConsole = (level, formattedLog) => {
  const { timestamp, message, args } = formattedLog;
  
  const prefix = `[${timestamp}] [${level}]`;
  
  switch (level) {
    case 'ERROR':
      console.error(prefix, message, ...args);
      break;
    case 'WARN':
      console.warn(prefix, message, ...args);
      break;
    case 'INFO':
      console.info(prefix, message, ...args);
      break;
    case 'DEBUG':
      console.debug(prefix, message, ...args);
      break;
    default:
      console.log(prefix, message, ...args);
  }
};

// Send critical logs to backend
const sendLogToBackend = async (formattedLog) => {
  try {
    // Only send ERROR logs to backend
    if (formattedLog.level !== 'ERROR') return;
    
    // Don't send logs to backend in development
    if (isDevelopment) return;
    
    const response = await fetch('/api/logs', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        level: formattedLog.level,
        message: formattedLog.message,
        timestamp: formattedLog.timestamp,
        data: formattedLog.args,
        source: 'frontend',
        userAgent: navigator.userAgent,
        url: window.location.href
      }),
    });
    
    if (!response.ok) {
      // Don't use logger here to avoid infinite loop
      console.error('Failed to send log to backend:', response.statusText);
    }
  } catch (error) {
    // Don't use logger here to avoid infinite loop
    console.error('Error sending log to backend:', error);
  }
};

// Main logging function
const log = (level, message, ...args) => {
  // Check if we should log based on current level
  if (LOG_LEVELS[level] > currentLogLevel) {
    return;
  }
  
  const formattedLog = formatLogMessage(level, message, ...args);
  
  // Log to console
  logToConsole(level, formattedLog);
  
  // Send to backend if needed
  if (level === 'ERROR') {
    sendLogToBackend(formattedLog);
  }
};

// Public API
const logger = {
  error: (message, ...args) => log('ERROR', message, ...args),
  warn: (message, ...args) => log('WARN', message, ...args),
  info: (message, ...args) => log('INFO', message, ...args),
  debug: (message, ...args) => log('DEBUG', message, ...args),
  
  // Set log level
  setLevel: (level) => {
    if (LOG_LEVELS[level] !== undefined) {
      currentLogLevel = LOG_LEVELS[level];
    }
  },
  
  // Get current log level
  getLevel: () => {
    return Object.keys(LOG_LEVELS).find(key => LOG_LEVELS[key] === currentLogLevel);
  }
};

export default logger;
