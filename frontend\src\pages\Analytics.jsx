import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import DashboardLayout from '../components/DashboardLayout';
import Spinner from '../components/Spinner';
import '../styles/analytics.css';

const Analytics = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [analyticsData, setAnalyticsData] = useState(null);
  const [userType, setUserType] = useState(null);
  const [timeRange, setTimeRange] = useState('month');

  useEffect(() => {
    // Check if user is broker or supplier
    const storedUserType = localStorage.getItem('userType');
    
    if (storedUserType !== 'broker' && storedUserType !== 'supplier') {
      navigate('/dashboard');
      return;
    }
    
    setUserType(storedUserType);
    
    // Fetch analytics data
    const fetchAnalyticsData = async () => {
      try {
        setLoading(true);
        
        // In a real app, this would be an API call
        // For now, we'll use mock data
        setTimeout(() => {
          const mockAnalyticsData = {
            summary: {
              totalClients: 48,
              activeContracts: 35,
              pendingOffers: 12,
              totalRevenue: '€24,850'
            },
            clientAcquisition: {
              labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May'],
              data: [5, 8, 12, 10, 13]
            },
            contractsValue: {
              labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May'],
              data: [4200, 5800, 6500, 3900, 4450]
            },
            clientDistribution: {
              individual: 65,
              business: 35
            },
            topClients: [
              {
                id: 1,
                name: 'Tech Solutions Inc.',
                contracts: 5,
                value: '€8,450'
              },
              {
                id: 2,
                name: 'Acme Corporation',
                contracts: 3,
                value: '€6,200'
              },
              {
                id: 3,
                name: 'Global Enterprises',
                contracts: 2,
                value: '€4,800'
              },
              {
                id: 4,
                name: 'John Smith',
                contracts: 2,
                value: '€3,200'
              },
              {
                id: 5,
                name: 'Marie Dupont',
                contracts: 1,
                value: '€2,200'
              }
            ]
          };
          
          setAnalyticsData(mockAnalyticsData);
          setLoading(false);
        }, 1000);
      } catch (error) {
        console.error('Error fetching analytics data:', error);
        setLoading(false);
      }
    };
    
    fetchAnalyticsData();
  }, [navigate]);

  const handleTimeRangeChange = (range) => {
    setTimeRange(range);
    // In a real app, this would trigger a new data fetch with the selected time range
  };

  return (
    <DashboardLayout>
      <div className="analytics-container">
        <div className="analytics-header">
          <h1>Analytics Dashboard</h1>
          <div className="time-range-selector">
            <button
              className={`time-range-btn ${timeRange === 'week' ? 'active' : ''}`}
              onClick={() => handleTimeRangeChange('week')}
            >
              Week
            </button>
            <button
              className={`time-range-btn ${timeRange === 'month' ? 'active' : ''}`}
              onClick={() => handleTimeRangeChange('month')}
            >
              Month
            </button>
            <button
              className={`time-range-btn ${timeRange === 'quarter' ? 'active' : ''}`}
              onClick={() => handleTimeRangeChange('quarter')}
            >
              Quarter
            </button>
            <button
              className={`time-range-btn ${timeRange === 'year' ? 'active' : ''}`}
              onClick={() => handleTimeRangeChange('year')}
            >
              Year
            </button>
          </div>
        </div>

        {loading ? (
          <Spinner message="Loading analytics data..." />
        ) : (
          <div className="analytics-content">
            {/* Summary Cards */}
            <div className="analytics-summary">
              <div className="summary-card">
                <div className="summary-icon">
                  <i className="fas fa-users"></i>
                </div>
                <div className="summary-details">
                  <h3 className="summary-title">Total Clients</h3>
                  <p className="summary-value">{analyticsData.summary.totalClients}</p>
                </div>
              </div>
              
              <div className="summary-card">
                <div className="summary-icon">
                  <i className="fas fa-file-signature"></i>
                </div>
                <div className="summary-details">
                  <h3 className="summary-title">Active Contracts</h3>
                  <p className="summary-value">{analyticsData.summary.activeContracts}</p>
                </div>
              </div>
              
              <div className="summary-card">
                <div className="summary-icon">
                  <i className="fas fa-tags"></i>
                </div>
                <div className="summary-details">
                  <h3 className="summary-title">Pending Offers</h3>
                  <p className="summary-value">{analyticsData.summary.pendingOffers}</p>
                </div>
              </div>
              
              <div className="summary-card">
                <div className="summary-icon">
                  <i className="fas fa-euro-sign"></i>
                </div>
                <div className="summary-details">
                  <h3 className="summary-title">Total Revenue</h3>
                  <p className="summary-value">{analyticsData.summary.totalRevenue}</p>
                </div>
              </div>
            </div>

            {/* Charts Section */}
            <div className="analytics-charts">
              <div className="chart-card">
                <div className="chart-header">
                  <h2>Client Acquisition</h2>
                </div>
                <div className="chart-content">
                  <div className="chart-placeholder">
                    <p>Client acquisition chart will be displayed here</p>
                    <div className="mock-bar-chart">
                      {analyticsData.clientAcquisition.data.map((value, index) => (
                        <div key={index} className="mock-bar" style={{ height: `${value * 5}px` }}>
                          <span className="mock-bar-value">{value}</span>
                        </div>
                      ))}
                    </div>
                    <div className="mock-chart-labels">
                      {analyticsData.clientAcquisition.labels.map((label, index) => (
                        <div key={index} className="mock-label">{label}</div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              <div className="chart-card">
                <div className="chart-header">
                  <h2>Contracts Value</h2>
                </div>
                <div className="chart-content">
                  <div className="chart-placeholder">
                    <p>Contracts value chart will be displayed here</p>
                    <div className="mock-line-chart">
                      <div className="mock-line"></div>
                      <div className="mock-points">
                        {analyticsData.contractsValue.data.map((value, index) => (
                          <div 
                            key={index} 
                            className="mock-point" 
                            style={{ 
                              left: `${index * 25}%`, 
                              bottom: `${(value / 10000) * 100}%` 
                            }}
                          ></div>
                        ))}
                      </div>
                    </div>
                    <div className="mock-chart-labels">
                      {analyticsData.contractsValue.labels.map((label, index) => (
                        <div key={index} className="mock-label">{label}</div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Additional Analytics */}
            <div className="analytics-additional">
              <div className="chart-card">
                <div className="chart-header">
                  <h2>Client Distribution</h2>
                </div>
                <div className="chart-content">
                  <div className="distribution-chart">
                    <div className="distribution-item">
                      <div className="distribution-label">Individual</div>
                      <div className="distribution-bar-container">
                        <div 
                          className="distribution-bar individual"
                          style={{ width: `${analyticsData.clientDistribution.individual}%` }}
                        ></div>
                        <span className="distribution-value">
                          {analyticsData.clientDistribution.individual}%
                        </span>
                      </div>
                    </div>
                    <div className="distribution-item">
                      <div className="distribution-label">Business</div>
                      <div className="distribution-bar-container">
                        <div 
                          className="distribution-bar business"
                          style={{ width: `${analyticsData.clientDistribution.business}%` }}
                        ></div>
                        <span className="distribution-value">
                          {analyticsData.clientDistribution.business}%
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="chart-card">
                <div className="chart-header">
                  <h2>Top Clients</h2>
                </div>
                <div className="chart-content">
                  <div className="top-clients-list">
                    {analyticsData.topClients.map((client, index) => (
                      <div key={client.id} className="top-client-item">
                        <div className="client-rank">{index + 1}</div>
                        <div className="client-info">
                          <div className="client-name">{client.name}</div>
                          <div className="client-details">
                            {client.contracts} contracts | {client.value}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </DashboardLayout>
  );
};

export default Analytics;
