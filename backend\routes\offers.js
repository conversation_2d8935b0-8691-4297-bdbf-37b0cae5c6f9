const express = require('express');
const router = express.Router();
const offerController = require('../controllers/offerController');
// const { verifyToken } = require('../middleware/auth');
// const logger = require('../utils/logger');

// Temporarily disable authentication for demo purposes
// router.use(verifyToken);

// Get all offers for a user
router.get('/user', offerController.getUserOffers);

// Get a specific offer by ID
router.get('/:id', offerController.getOfferById);

// Accept an offer (creates contract and initiates DocuSign)
router.post('/:id/accept', offerController.acceptOffer);

// Get offer acceptance status
router.get('/:id/status', offerController.getOfferStatus);

module.exports = router;
