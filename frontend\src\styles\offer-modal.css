/* Modal Overlay */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 99999;
  padding: 20px;
}

/* Modal Content */
.modal-content {
  background-color: #fff;
  border-radius: 12px;
  max-width: 800px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Modal Header */
.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 25px 30px 20px;
  border-bottom: 1px solid #eee;
}

.modal-title-section {
  flex: 1;
}

.modal-provider {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.modal-provider-name {
  font-weight: 600;
  color: #555;
  font-size: 14px;
}

.modal-offer-type {
  padding: 4px 10px;
  border-radius: 20px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
}

.modal-offer-type.electricity {
  background-color: #e6f7ff;
  color: #0070f3;
}

.modal-offer-type.gas {
  background-color: #fff7e6;
  color: #fa8c16;
}

.modal-offer-type.both {
  background-color: #f6ffed;
  color: #52c41a;
}

.modal-offer-name {
  font-size: 24px;
  font-weight: 700;
  margin: 0;
  color: #000;
  line-height: 1.3;
}

.modal-close-btn {
  background: none;
  border: none;
  font-size: 20px;
  color: #666;
  cursor: pointer;
  padding: 5px;
  border-radius: 50%;
  width: 35px;
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.modal-close-btn:hover {
  background-color: #f5f5f5;
  color: #333;
}

/* Modal Body */
.modal-body {
  padding: 25px 30px;
}

.modal-description {
  margin-bottom: 25px;
}

.modal-description p {
  font-size: 16px;
  color: #555;
  line-height: 1.6;
  margin: 0;
}

.modal-details-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  margin-bottom: 25px;
}

.modal-section {
  background-color: #f9f9f9;
  padding: 20px;
  border-radius: 8px;
}

.modal-section h3 {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 15px 0;
  color: #333;
}

.modal-detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  font-size: 14px;
}

.modal-detail-item:last-child {
  margin-bottom: 0;
}

.modal-detail-label {
  color: #777;
}

.modal-detail-value {
  font-weight: 600;
  color: #333;
}

.modal-savings-highlight {
  background: linear-gradient(to right, #000, #333);
  color: #fff;
  padding: 20px;
  border-radius: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-savings-amount {
  display: flex;
  flex-direction: column;
}

.modal-savings-label {
  font-size: 12px;
  opacity: 0.8;
  margin-bottom: 5px;
}

.modal-savings-value {
  font-size: 24px;
  font-weight: 700;
}

.modal-savings-percentage {
  display: flex;
  flex-direction: column;
  text-align: right;
}

.modal-percentage-value {
  font-size: 20px;
  font-weight: 700;
}

.modal-percentage-label {
  font-size: 12px;
  opacity: 0.8;
}

.modal-highlights-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.modal-highlight-item {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 12px;
  font-size: 14px;
  color: #555;
}

.modal-highlight-item:last-child {
  margin-bottom: 0;
}

.modal-highlight-item i {
  color: #52c41a;
  font-size: 16px;
}

.modal-validity {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: #f0f8ff;
  padding: 12px 15px;
  border-radius: 6px;
  font-size: 14px;
  color: #0070f3;
  margin-top: 20px;
}

.modal-validity i {
  font-size: 16px;
}

/* Modal Footer */
.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  padding: 20px 30px 25px;
  border-top: 1px solid #eee;
}

.modal-btn-cancel,
.modal-btn-accept {
  padding: 12px 24px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  border: none;
}

.modal-btn-cancel {
  background-color: transparent;
  border: 1px solid #ddd;
  color: #666;
}

.modal-btn-cancel:hover {
  background-color: #f5f5f5;
  border-color: #ccc;
}

.modal-btn-accept {
  background: linear-gradient(to right, #000, #333);
  color: #fff;
}

.modal-btn-accept:hover {
  opacity: 0.9;
  transform: translateY(-1px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .modal-overlay {
    padding: 10px;
  }

  .modal-content {
    max-height: 95vh;
  }

  .modal-header {
    padding: 20px 20px 15px;
  }

  .modal-offer-name {
    font-size: 20px;
  }

  .modal-body {
    padding: 20px;
  }

  .modal-details-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .modal-savings-highlight {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .modal-savings-percentage {
    text-align: left;
  }

  .modal-footer {
    padding: 15px 20px 20px;
    flex-direction: column;
  }

  .modal-btn-cancel,
  .modal-btn-accept {
    width: 100%;
    padding: 14px;
  }
}
