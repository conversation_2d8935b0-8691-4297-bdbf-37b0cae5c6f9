<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Support Ticket API Integration Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f7fa;
        }
        
        .test-container {
            background: white;
            border-radius: 12px;
            padding: 32px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2d3748;
        }
        
        input, textarea, select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.2s ease;
        }
        
        input:focus, textarea:focus, select:focus {
            outline: none;
            border-color: #007aff;
            box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
        }
        
        button {
            background: linear-gradient(135deg, #007aff 0%, #0056cc 100%);
            color: white;
            border: none;
            padding: 16px 32px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        button:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 122, 255, 0.4);
        }
        
        button:disabled {
            background: #a0aec0;
            cursor: not-allowed;
            transform: none;
        }
        
        .loading-spinner {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: #ffffff;
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .result {
            margin-top: 20px;
            padding: 16px;
            border-radius: 8px;
            border: 2px solid;
        }
        
        .result.success {
            background: #f0fff4;
            border-color: #38a169;
            color: #2d3748;
        }
        
        .result.error {
            background: #fff5f5;
            border-color: #e53e3e;
            color: #2d3748;
        }
        
        .result pre {
            background: rgba(0, 0, 0, 0.05);
            padding: 12px;
            border-radius: 6px;
            overflow-x: auto;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎫 Support Ticket API Integration Test</h1>
        <p>Test the support ticket creation API directly from the frontend.</p>
        
        <form id="ticketForm">
            <div class="form-group">
                <label for="email">Email Address *</label>
                <input type="email" id="email" name="email" value="<EMAIL>" required>
            </div>
            
            <div class="form-group">
                <label for="subject">Subject *</label>
                <input type="text" id="subject" name="subject" value="Account Status Issue - ACCOUNT_SUSPENDED" required>
            </div>
            
            <div class="form-group">
                <label for="message">Message *</label>
                <textarea id="message" name="message" rows="4" required>My account has been suspended and I need assistance to resolve this issue. Please help me understand why this happened and how to restore access.</textarea>
            </div>
            
            <div class="form-group">
                <label for="priority">Priority</label>
                <select id="priority" name="priority">
                    <option value="high">🔴 High - Account Access Issue</option>
                    <option value="medium">🟡 Medium - General Inquiry</option>
                    <option value="low">🟢 Low - Information Request</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="status">Account Status</label>
                <input type="text" id="status" name="status" value="ACCOUNT_SUSPENDED">
            </div>
            
            <div class="form-group">
                <label for="errorCode">Error Code</label>
                <input type="text" id="errorCode" name="errorCode" value="ACCOUNT_SUSPENDED">
            </div>
            
            <button type="submit" id="submitBtn">
                🎫 Create Support Ticket
            </button>
        </form>
        
        <div id="result" style="display: none;"></div>
    </div>

    <script>
        const form = document.getElementById('ticketForm');
        const submitBtn = document.getElementById('submitBtn');
        const resultDiv = document.getElementById('result');

        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            // Show loading state
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<span class="loading-spinner"></span>Creating Ticket...';
            resultDiv.style.display = 'none';
            
            try {
                const formData = new FormData(form);
                const ticketData = {
                    email: formData.get('email'),
                    subject: formData.get('subject'),
                    message: formData.get('message'),
                    priority: formData.get('priority'),
                    status: formData.get('status'),
                    errorCode: formData.get('errorCode')
                };
                
                console.log('Sending ticket data:', ticketData);
                
                const response = await fetch('http://localhost:3000/auth/create-account-status-ticket', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(ticketData)
                });
                
                const data = await response.json();
                
                if (!response.ok) {
                    throw new Error(data.message || 'Failed to create support ticket');
                }
                
                // Show success
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `
                    <h3>✅ Success!</h3>
                    <p><strong>Ticket Number:</strong> ${data.ticketNumber}</p>
                    <p><strong>Ticket ID:</strong> ${data.ticketId}</p>
                    <p><strong>Message:</strong> ${data.message}</p>
                    <details>
                        <summary>Full Response</summary>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    </details>
                `;
                resultDiv.style.display = 'block';
                
                submitBtn.innerHTML = '✅ Ticket Created Successfully!';
                
            } catch (error) {
                console.error('Error creating support ticket:', error);
                
                // Show error
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <h3>❌ Error</h3>
                    <p><strong>Error:</strong> ${error.message}</p>
                    <details>
                        <summary>Error Details</summary>
                        <pre>${error.stack || error.toString()}</pre>
                    </details>
                `;
                resultDiv.style.display = 'block';
                
                submitBtn.innerHTML = '🎫 Create Support Ticket';
                submitBtn.disabled = false;
            }
        });
    </script>
</body>
</html>
