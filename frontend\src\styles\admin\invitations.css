/*
 * INVITATION MODAL DIALOG STYLES ONLY
 * These styles ONLY affect modal dialogs with class "invitation-modal"
 * They do NOT affect the main invitation management page layout
 */

/* Modal Container - Beautiful rounded corners and shadow */
.modal-container.invitation-modal {
  background: #ffffff !important;
  border-radius: 16px !important;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;
  border: none !important;
  overflow: hidden !important;
  max-width: 800px !important;
}

/* Modal Header - Beautiful gradient background */
.modal-container.invitation-modal .modal-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  color: white !important;
  padding: 24px 32px !important;
  border-bottom: none !important;
  position: relative !important;
}

/* Modal Header Title */
.modal-container.invitation-modal .modal-header h2,
.modal-container.invitation-modal .modal-header h3 {
  font-size: 24px !important;
  font-weight: 600 !important;
  margin: 0 !important;
  color: white !important;
}

/* Modal Header Subtitle */
.modal-container.invitation-modal .modal-header p {
  color: rgba(255, 255, 255, 0.9) !important;
  margin: 8px 0 0 0 !important;
  font-size: 14px !important;
}

/* Modal Close Button */
.modal-container.invitation-modal .modal-close {
  background: rgba(255, 255, 255, 0.2) !important;
  border: none !important;
  color: white !important;
  width: 32px !important;
  height: 32px !important;
  border-radius: 8px !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
  font-size: 18px !important;
}

.modal-container.invitation-modal .modal-close:hover {
  background: rgba(255, 255, 255, 0.3) !important;
  transform: scale(1.05) !important;
}

/* Modal Body - Clean background */
.modal-container.invitation-modal .modal-body {
  padding: 32px !important;
  background: #fafafa !important;
  max-height: 70vh !important;
  overflow-y: auto !important;
}

/* Form Sections - Beautiful cards */
.modal-container.invitation-modal .form-section {
  background: white !important;
  border-radius: 12px !important;
  padding: 24px !important;
  margin-bottom: 24px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
  border: 1px solid #e5e7eb !important;
  transition: all 0.2s ease !important;
}

.modal-container.invitation-modal .form-section:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
  transform: translateY(-1px) !important;
}

.modal-container.invitation-modal .form-section:last-child {
  margin-bottom: 0 !important;
}

/* Form Section Headers */
.modal-container.invitation-modal .form-section-header {
  display: flex !important;
  align-items: center !important;
  gap: 12px !important;
  margin-bottom: 20px !important;
  padding-bottom: 16px !important;
  border-bottom: 2px solid #f3f4f6 !important;
}

.modal-container.invitation-modal .form-section-icon {
  width: 40px !important;
  height: 40px !important;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border-radius: 10px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  color: white !important;
  font-size: 16px !important;
}

.modal-container.invitation-modal .form-section-title {
  font-size: 18px !important;
  font-weight: 600 !important;
  color: #1f2937 !important;
  margin: 0 !important;
}

/* Form Groups */
.modal-container.invitation-modal .form-group {
  margin-bottom: 20px !important;
}

.modal-container.invitation-modal .form-group:last-child {
  margin-bottom: 0 !important;
}

.modal-container.invitation-modal .form-group label {
  display: block !important;
  font-weight: 500 !important;
  color: #374151 !important;
  margin-bottom: 8px !important;
  font-size: 14px !important;
}

.modal-container.invitation-modal .form-group label.required::after {
  content: ' *' !important;
  color: #ef4444 !important;
}

/* Form Inputs - Modern styling */
.modal-container.invitation-modal .form-group input,
.modal-container.invitation-modal .form-group textarea,
.modal-container.invitation-modal .form-group select {
  width: 100% !important;
  padding: 12px 16px !important;
  border: 2px solid #e5e7eb !important;
  border-radius: 8px !important;
  font-size: 14px !important;
  transition: all 0.2s ease !important;
  background: white !important;
  color: #1f2937 !important;
}

.modal-container.invitation-modal .form-group input:focus,
.modal-container.invitation-modal .form-group textarea:focus,
.modal-container.invitation-modal .form-group select:focus {
  outline: none !important;
  border-color: #667eea !important;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
}

.modal-container.invitation-modal .form-group input::placeholder,
.modal-container.invitation-modal .form-group textarea::placeholder {
  color: #9ca3af !important;
}

.modal-container.invitation-modal .form-group textarea {
  resize: vertical !important;
  min-height: 80px !important;
}

/* Grid Layouts */
.modal-container.invitation-modal .grid-2 {
  display: grid !important;
  grid-template-columns: 1fr 1fr !important;
  gap: 16px !important;
}

.modal-container.invitation-modal .grid-3 {
  display: grid !important;
  grid-template-columns: 1fr 1fr 1fr !important;
  gap: 16px !important;
}

/* Modal Footer Styling */
.modal-container.invitation-modal .modal-footer {
  background: #f9fafb !important;
  padding: 20px 32px !important;
  border-top: 1px solid #e5e7eb !important;
}

/* Button Styling in Modal */
.modal-container.invitation-modal .modal-footer .btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border: none !important;
  color: white !important;
  padding: 12px 24px !important;
  border-radius: 8px !important;
  font-weight: 500 !important;
  transition: all 0.2s ease !important;
}

.modal-container.invitation-modal .modal-footer .btn-primary:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3) !important;
}

.modal-container.invitation-modal .modal-footer .btn-secondary {
  background: #f3f4f6 !important;
  border: 1px solid #d1d5db !important;
  color: #374151 !important;
  padding: 12px 24px !important;
  border-radius: 8px !important;
  font-weight: 500 !important;
  transition: all 0.2s ease !important;
}

.modal-container.invitation-modal .modal-footer .btn-secondary:hover {
  background: #e5e7eb !important;
  transform: translateY(-1px) !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .modal-container.invitation-modal .grid-2,
  .modal-container.invitation-modal .grid-3 {
    grid-template-columns: 1fr !important;
  }

  .modal-container.invitation-modal .modal-header,
  .modal-container.invitation-modal .modal-body,
  .modal-container.invitation-modal .modal-footer {
    padding-left: 20px !important;
    padding-right: 20px !important;
  }
}
