/* Modern Invitation Modal Styles - ONLY affects modal dialogs, NOT the main page */

/* ONLY target elements inside a modal with invitation-modal class */
div[class*="invitation-modal"] .modal-container {
  background: #ffffff !important;
  border-radius: 16px !important;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;
  border: none !important;
  overflow: hidden !important;
}

/* Modal Header with Gradient - ONLY inside invitation modals */
div[class*="invitation-modal"] .modal-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  color: white !important;
  padding: 24px 32px !important;
  border-bottom: none !important;
  position: relative !important;
  border-radius: 16px 16px 0 0 !important;
}

div[class*="invitation-modal"] .modal-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.9) 0%, rgba(118, 75, 162, 0.9) 100%);
  z-index: 1;
}

div[class*="invitation-modal"] .modal-header > * {
  position: relative;
  z-index: 2;
}

div[class*="invitation-modal"] .modal-header h2,
div[class*="invitation-modal"] .modal-header h3 {
  font-size: 24px !important;
  font-weight: 600 !important;
  margin: 0 !important;
  display: flex !important;
  align-items: center !important;
  gap: 12px !important;
  color: white !important;
}

div[class*="invitation-modal"] .modal-header h2 i,
div[class*="invitation-modal"] .modal-header h3 i {
  font-size: 20px !important;
  opacity: 0.9 !important;
}

div[class*="invitation-modal"] .modal-close {
  background: rgba(255, 255, 255, 0.2) !important;
  border: none !important;
  color: white !important;
  width: 32px !important;
  height: 32px !important;
  border-radius: 8px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
  position: absolute !important;
  top: 20px !important;
  right: 24px !important;
  z-index: 2 !important;
}

div[class*="invitation-modal"] .modal-close:hover {
  background: rgba(255, 255, 255, 0.3) !important;
  transform: scale(1.05) !important;
}

/* Modal Body - ONLY inside invitation modals */
div[class*="invitation-modal"] .modal-body {
  padding: 32px !important;
  background: #fafafa !important;
  max-height: 70vh !important;
  overflow-y: auto !important;
}

/* Form Sections - ONLY inside invitation modals */
div[class*="invitation-modal"] .form-section {
  background: white !important;
  border-radius: 12px !important;
  padding: 24px !important;
  margin-bottom: 24px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
  border: 1px solid #e5e7eb !important;
  transition: all 0.2s ease !important;
}

div[class*="invitation-modal"] .form-section:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
  transform: translateY(-1px) !important;
}

div[class*="invitation-modal"] .form-section:last-child {
  margin-bottom: 0 !important;
}

div[class*="invitation-modal"] .form-section-header {
  display: flex !important;
  align-items: center !important;
  gap: 12px !important;
  margin-bottom: 20px !important;
  padding-bottom: 16px !important;
  border-bottom: 2px solid #f3f4f6 !important;
}

div[class*="invitation-modal"] .form-section-icon {
  width: 40px !important;
  height: 40px !important;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border-radius: 10px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  color: white !important;
  font-size: 16px !important;
}

div[class*="invitation-modal"] .form-section-title {
  font-size: 18px !important;
  font-weight: 600 !important;
  color: #1f2937 !important;
  margin: 0 !important;
}

/* Form Groups - ONLY inside invitation modals */
div[class*="invitation-modal"] .form-group {
  margin-bottom: 20px !important;
}

div[class*="invitation-modal"] .form-group:last-child {
  margin-bottom: 0 !important;
}

div[class*="invitation-modal"] .form-group label {
  display: block !important;
  font-weight: 500 !important;
  color: #374151 !important;
  margin-bottom: 8px !important;
  font-size: 14px !important;
}

div[class*="invitation-modal"] .form-group label.required::after {
  content: ' *' !important;
  color: #ef4444 !important;
}

/* Form Inputs - ONLY inside invitation modals */
div[class*="invitation-modal"] .form-group input,
div[class*="invitation-modal"] .form-group textarea,
div[class*="invitation-modal"] .form-group select {
  width: 100% !important;
  padding: 12px 16px !important;
  border: 2px solid #e5e7eb !important;
  border-radius: 8px !important;
  font-size: 14px !important;
  transition: all 0.2s ease !important;
  background: white !important;
  color: #1f2937 !important;
}

div[class*="invitation-modal"] .form-group input:focus,
div[class*="invitation-modal"] .form-group textarea:focus,
div[class*="invitation-modal"] .form-group select:focus {
  outline: none !important;
  border-color: #667eea !important;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
}

div[class*="invitation-modal"] .form-group input::placeholder,
div[class*="invitation-modal"] .form-group textarea::placeholder {
  color: #9ca3af !important;
}

div[class*="invitation-modal"] .form-group textarea {
  resize: vertical !important;
  min-height: 80px !important;
}

/* Checkbox Styling - ONLY inside invitation modals */
div[class*="invitation-modal"] .form-group input[type="checkbox"] {
  width: auto !important;
  margin-right: 8px !important;
  accent-color: #667eea !important;
}

/* File Upload */
.file-upload {
  border: 2px dashed #d1d5db;
  border-radius: 12px;
  padding: 32px;
  text-align: center;
  background: #f9fafb;
  transition: all 0.2s ease;
  cursor: pointer;
}

.file-upload:hover {
  border-color: #667eea;
  background: #f0f4ff;
}

.file-upload-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.file-upload-text {
  color: #6b7280;
  line-height: 1.5;
}

.file-upload-text strong {
  color: #374151;
}

/* Modal Footer */
.modal-footer {
  background: white;
  padding: 24px 32px;
  border-top: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* Button Enhancements */
.btn {
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 500;
  font-size: 14px;
  transition: all 0.2s ease;
  border: none;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.btn-secondary:hover {
  background: #e5e7eb;
  transform: translateY(-1px);
}

/* Grid Layouts - ONLY inside invitation modals */
div[class*="invitation-modal"] .grid-2 {
  display: grid !important;
  grid-template-columns: 1fr 1fr !important;
  gap: 16px !important;
}

div[class*="invitation-modal"] .grid-3 {
  display: grid !important;
  grid-template-columns: 1fr 1fr 1fr !important;
  gap: 16px !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .modal-content {
    margin: 20px;
    width: calc(100% - 40px);
  }
  
  .modal-body {
    padding: 20px;
  }
  
  .grid-2,
  .grid-3 {
    grid-template-columns: 1fr;
  }
  
  .form-section {
    padding: 16px;
  }
}

/* Loading States */
.btn.loading {
  opacity: 0.7;
  cursor: not-allowed;
}

.btn.loading::after {
  content: '';
  width: 16px;
  height: 16px;
  border: 2px solid currentColor;
  border-top-color: transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-left: 8px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Success States */
.form-group.success input {
  border-color: #10b981;
}

.form-group.error input {
  border-color: #ef4444;
}

/* Animation for modal entrance */
.modal-content {
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}
