/**
 * CSS Loader Utility
 * Dynamically loads and unloads CSS files to prevent global style conflicts
 */

const loadedCSS = new Set();

/**
 * Load a CSS file dynamically
 * @param {string} cssPath - Path to the CSS file
 * @param {string} id - Unique ID for the CSS link element
 */
export const loadCSS = (cssPath, id) => {
  // Check if CSS is already loaded
  if (loadedCSS.has(id) || document.getElementById(id)) {
    return;
  }

  const link = document.createElement('link');
  link.rel = 'stylesheet';
  link.href = cssPath;
  link.id = id;
  
  // Add to document head
  document.head.appendChild(link);
  loadedCSS.add(id);
};

/**
 * Unload a CSS file
 * @param {string} id - Unique ID of the CSS link element to remove
 */
export const unloadCSS = (id) => {
  const link = document.getElementById(id);
  if (link) {
    document.head.removeChild(link);
    loadedCSS.delete(id);
  }
};

/**
 * React hook for loading CSS files in components
 * @param {string} cssPath - Path to the CSS file
 * @param {string} id - Unique ID for the CSS link element
 */
export const useCSS = (cssPath, id) => {
  const { useEffect } = require('react');
  
  useEffect(() => {
    // Load CSS when component mounts
    loadCSS(cssPath, id);
    
    // Cleanup function to unload CSS when component unmounts
    return () => {
      unloadCSS(id);
    };
  }, [cssPath, id]);
};

/**
 * Predefined CSS paths for components
 */
export const COMPONENT_CSS = {
  // Admin components
  ADMIN_DASHBOARD: '/src/styles/admin-dashboard.css',
  ADMIN_INVITATIONS: '/src/styles/admin/invitations.css',
  ADMIN_MANAGEMENT: '/src/styles/admin-management.css',
  ADMIN_ANALYTICS: '/src/styles/admin-analytics.css',
  // Other components
  CONTRACT_SIGNING: '/src/styles/contract-signing.css'
};

// Legacy export for backward compatibility
export const ADMIN_CSS = {
  DASHBOARD: COMPONENT_CSS.ADMIN_DASHBOARD,
  INVITATIONS: COMPONENT_CSS.ADMIN_INVITATIONS,
  MANAGEMENT: COMPONENT_CSS.ADMIN_MANAGEMENT,
  ANALYTICS: COMPONENT_CSS.ADMIN_ANALYTICS
};

/**
 * Predefined CSS IDs for components
 */
export const CSS_IDS = {
  ADMIN_DASHBOARD: 'admin-dashboard-css',
  ADMIN_INVITATIONS: 'admin-invitations-css',
  ADMIN_MANAGEMENT: 'admin-management-css',
  ADMIN_ANALYTICS: 'admin-analytics-css',
  CONTRACT_SIGNING: 'contract-signing-css'
};
