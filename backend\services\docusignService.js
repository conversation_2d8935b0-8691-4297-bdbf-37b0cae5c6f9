const docusign = require('docusign-esign');
const fs = require('fs');
const path = require('path');
// const logger = require('../utils/logger');

class DocuSignService {
  constructor() {
    this.apiClient = new docusign.ApiClient();
    this.basePath = process.env.DOCUSIGN_BASE_PATH || 'https://demo.docusign.net/restapi';
    this.integrationKey = process.env.DOCUSIGN_INTEGRATION_KEY;
    this.userId = process.env.DOCUSIGN_USER_ID;
    this.accountId = process.env.DOCUSIGN_ACCOUNT_ID;
    this.privateKey = process.env.DOCUSIGN_PRIVATE_KEY;
    this.redirectUrl = process.env.DOCUSIGN_REDIRECT_URL || 'http://localhost:3000/contract-signed';

    this.apiClient.setBasePath(this.basePath);

    // Initialize authentication
    this.initializeAuth();
  }

  async initializeAuth() {
    try {
      if (!this.integrationKey || !this.userId || !this.privateKey) {
        console.warn('DocuSign credentials not configured. Using mock mode.');
        this.mockMode = true;
        return;
      }

      // Configure JWT authentication
      this.apiClient.setOAuthBasePath(this.basePath.replace('/restapi', ''));

      // Get access token using JWT
      const results = await this.apiClient.requestJWTUserToken(
        this.integrationKey,
        this.userId,
        'signature impersonation',
        Buffer.from(this.privateKey, 'utf8'),
        3600 // 1 hour
      );

      const accessToken = results.body.access_token;
      this.apiClient.addDefaultHeader('Authorization', 'Bearer ' + accessToken);

      console.log('DocuSign authentication successful');
      this.mockMode = false;

    } catch (error) {
      console.error('DocuSign authentication failed:', error);
      console.warn('Falling back to mock mode');
      this.mockMode = true;
    }
  }

  async createSigningEnvelope({ contractId, userEmail, userName, contractDetails }) {
    try {
      if (this.mockMode) {
        return this.createMockSigningUrl(contractId);
      }

      // Create envelope definition
      const envelopeDefinition = this.createEnvelopeDefinition({
        userEmail,
        userName,
        contractDetails,
        contractId
      });

      // Create the envelope
      const envelopesApi = new docusign.EnvelopesApi(this.apiClient);
      const results = await envelopesApi.createEnvelope(this.accountId, {
        envelopeDefinition: envelopeDefinition
      });

      const envelopeId = results.envelopeId;
      console.log(`DocuSign envelope created: ${envelopeId}`);

      // Create recipient view (signing URL)
      const viewRequest = new docusign.RecipientViewRequest();
      viewRequest.returnUrl = `${this.redirectUrl}?contractId=${contractId}&envelopeId=${envelopeId}`;
      viewRequest.authenticationMethod = 'none';
      viewRequest.email = userEmail;
      viewRequest.userName = userName;
      viewRequest.clientUserId = contractId.toString();

      const viewResults = await envelopesApi.createRecipientView(this.accountId, envelopeId, {
        recipientViewRequest: viewRequest
      });

      return {
        envelopeId: envelopeId,
        url: viewResults.url
      };

    } catch (error) {
      console.error('Error creating DocuSign envelope:', error);
      throw new Error('Failed to create signing envelope');
    }
  }

  createEnvelopeDefinition({ userEmail, userName, contractDetails, contractId }) {
    // Create the document
    const document = new docusign.Document();
    document.documentBase64 = this.generateContractPDF(contractDetails);
    document.name = `Energy Contract - ${contractDetails.name}`;
    document.fileExtension = 'pdf';
    document.documentId = '1';

    // Create the signer
    const signer = new docusign.Signer();
    signer.email = userEmail;
    signer.name = userName;
    signer.recipientId = '1';
    signer.routingOrder = '1';
    signer.clientUserId = contractId.toString();

    // Create signature tab
    const signHere = new docusign.SignHere();
    signHere.documentId = '1';
    signHere.pageNumber = '1';
    signHere.recipientId = '1';
    signHere.tabLabel = 'SignHereTab';
    signHere.xPosition = '191';
    signHere.yPosition = '148';

    // Create date tab
    const dateTab = new docusign.DateSigned();
    dateTab.documentId = '1';
    dateTab.pageNumber = '1';
    dateTab.recipientId = '1';
    dateTab.tabLabel = 'DateSignedTab';
    dateTab.xPosition = '191';
    dateTab.yPosition = '200';

    // Add tabs to signer
    const tabs = new docusign.Tabs();
    tabs.signHereTabs = [signHere];
    tabs.dateSignedTabs = [dateTab];
    signer.tabs = tabs;

    // Create recipients
    const recipients = new docusign.Recipients();
    recipients.signers = [signer];

    // Create envelope definition
    const envelopeDefinition = new docusign.EnvelopeDefinition();
    envelopeDefinition.emailSubject = `Please sign your energy contract - ${contractDetails.name}`;
    envelopeDefinition.documents = [document];
    envelopeDefinition.recipients = recipients;
    envelopeDefinition.status = 'sent';

    return envelopeDefinition;
  }

  generateContractPDF(contractDetails) {
    // In a real implementation, you would generate a proper PDF
    // For now, we'll create a simple base64 encoded PDF content
    const contractContent = `
      ENERGY SUPPLY CONTRACT

      Contract Details:
      - Provider: ${contractDetails.provider || 'Energy Provider'}
      - Plan: ${contractDetails.name || 'Energy Plan'}
      - Energy Type: ${contractDetails.energyType || 'Electricity'}
      - Rate Type: ${contractDetails.rateType || 'Fixed'}
      - Duration: ${contractDetails.duration || 12} months
      - Base Rate: ${contractDetails.price?.baseRate || 0.15} €/kWh
      - Standing Charge: ${contractDetails.price?.standingCharge || 25} €/month

      By signing below, you agree to the terms and conditions of this contract.

      Signature: _____________________ Date: _____________________
    `;

    // Convert to base64 (this is a simplified version)
    // In production, use a proper PDF generation library like PDFKit or jsPDF
    return Buffer.from(contractContent).toString('base64');
  }

  createMockSigningUrl(contractId) {
    console.log('Creating mock signing URL for contract:', contractId);

    // Return a mock signing URL that redirects to our frontend
    const mockEnvelopeId = `mock-envelope-${Date.now()}`;
    const mockSigningUrl = `${this.redirectUrl}?contractId=${contractId}&envelopeId=${mockEnvelopeId}&mock=true`;

    return {
      envelopeId: mockEnvelopeId,
      url: mockSigningUrl
    };
  }

  async getEnvelopeStatus(envelopeId) {
    try {
      if (this.mockMode) {
        return {
          status: 'completed',
          completed: true
        };
      }

      const envelopesApi = new docusign.EnvelopesApi(this.apiClient);
      const envelope = await envelopesApi.getEnvelope(this.accountId, envelopeId);

      return {
        status: envelope.status,
        completed: envelope.status === 'completed'
      };

    } catch (error) {
      console.error('Error getting envelope status:', error);
      throw new Error('Failed to get signing status');
    }
  }

  async downloadSignedDocument(envelopeId, documentId = '1') {
    try {
      if (this.mockMode) {
        return Buffer.from('Mock signed document content');
      }

      const envelopesApi = new docusign.EnvelopesApi(this.apiClient);
      const document = await envelopesApi.getDocument(this.accountId, envelopeId, documentId);

      return document;

    } catch (error) {
      console.error('Error downloading signed document:', error);
      throw new Error('Failed to download signed document');
    }
  }
}

// Export singleton instance
module.exports = new DocuSignService();
