/* Authenticated Layout Styles */
.authenticated-layout {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.authenticated-content {
  flex: 1;
  padding-top: 70px; /* Height of the navbar */
  background-color: #f5f5f5;
  width: 100%;
  position: relative;
  z-index: 1;
}

/* Special case for upload invoice page to remove extra space */
.upload-first-invoice-page .authenticated-content {
  padding-bottom: 0;
}

/* Footer Styles for Authenticated Pages */
.authenticated-layout .home-footer {
  margin-top: auto;
}

.authenticated-footer {
  padding: 1rem 0 0.5rem !important;
  margin-top: 0 !important;
  background-color: #f5f5f5 !important;
}

/* Special case for upload invoice page to remove extra space */
.upload-first-invoice-page .authenticated-footer {
  padding-top: 0 !important;
  margin-top: 0 !important;
}

.authenticated-footer .footer-content {
  margin-bottom: 0.75rem;
  padding: 0 1rem;
}

.authenticated-footer .footer-bottom {
  border-top: none;
  padding: 0.5rem 1rem;
  font-size: 0.8rem;
  color: #777;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .authenticated-content {
    padding-top: 70px;
  }
}
