const jwt = require('jsonwebtoken');

// Get token from command line argument
const token = process.argv[2];

if (!token) {
  console.log('Usage: node debug-token.js <your-jwt-token>');
  console.log('Copy your access token from localStorage and paste it here');
  process.exit(1);
}

try {
  // Decode without verification (just to see the payload)
  const decoded = jwt.decode(token);
  
  console.log('🔍 JWT Token Analysis:');
  console.log('='.repeat(50));
  console.log('📧 Email:', decoded.email);
  console.log('👤 Username:', decoded.username);
  console.log('🆔 Cognito ID (sub):', decoded.sub);
  console.log('🏷️  Token Use:', decoded.token_use);
  console.log('⏰ Issued At:', new Date(decoded.iat * 1000));
  console.log('⏰ Expires At:', new Date(decoded.exp * 1000));
  console.log('');
  console.log('📋 Full Payload:');
  console.log(JSON.stringify(decoded, null, 2));
  
} catch (error) {
  console.error('❌ Error decoding token:', error.message);
  console.log('Make sure you copied the full token correctly');
}
