/* Wizard Progress Styles */
.wizard-progress-container {
  padding: 2.5rem 1.5rem 1.5rem;
  background-color: #f8f8f8;
  border-bottom: 1px solid #eaeaea;
}

.wizard-progress {
  max-width: 600px;
  margin: 0 auto;
  position: relative;
}

.step-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  position: relative;
}

/* Progress line that connects steps */
.step-wrapper::before {
  content: '';
  position: absolute;
  top: 18px; /* Center of the step circles */
  left: 18%; /* Start at first step */
  right: 18%; /* End at last step */
  height: 3px;
  background-color: #e0e0e0;
  z-index: 1;
}

/* Active progress line */
.step-wrapper::after {
  content: '';
  position: absolute;
  top: 18px; /* Center of the step circles */
  left: 18%; /* Start at first step */
  height: 3px;
  background: linear-gradient(90deg, #000000, #2ecc71);
  z-index: 1;
  transition: width 0.3s ease;
}

/* Progress line width based on current step */
.step-wrapper[data-step="1"]::after {
  width: 0%;
}

.step-wrapper[data-step="2"]::after {
  width: 50%;
}

.step-wrapper[data-step="3"]::after {
  width: 100%;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 2;
  flex: 1;
  padding: 0 10px;
}

.step-circle {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: #fff;
  border: 2px solid #e0e0e0;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  font-weight: 600;
  margin: 0 auto 0.75rem;
  position: relative;
  z-index: 5;
  transition: all 0.3s ease;
}

.step.active .step-circle {
  background-color: #000;
  color: #fff;
  border-color: #000;
  transform: scale(1.15);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.step.completed .step-circle {
  background-color: #2ecc71;
  color: #fff;
  border-color: #2ecc71;
}

.step-label {
  font-size: 0.8rem;
  color: #666;
  text-align: center;
}

.step.active .step-label {
  color: #000;
  font-weight: 600;
}

/* Responsive styles */
@media (max-width: 768px) {
  .wizard-progress-container {
    padding: 2rem 1rem 1rem;
  }

  .step-label {
    font-size: 0.75rem;
  }
}

@media (max-width: 480px) {
  .wizard-progress-container {
    padding: 1.75rem 0.75rem 0.75rem;
  }

  .step-circle {
    width: 30px;
    height: 30px;
    font-size: 0.9rem;
  }

  .step-label {
    font-size: 0.7rem;
  }
}
