/**
 * Centralized toast notification utility
 */
import { toast } from 'react-toastify';

// Default toast configuration
const DEFAULT_CONFIG = {
  position: "top-right",
  autoClose: 5000,
  hideProgressBar: false,
  closeOnClick: true,
  pauseOnHover: true,
  draggable: true,
  theme: "light"
};

// Quick toast configurations
const QUICK_CONFIG = {
  ...DEFAULT_CONFIG,
  autoClose: 2000
};

const LONG_CONFIG = {
  ...DEFAULT_CONFIG,
  autoClose: 8000
};

/**
 * Success notifications
 */
export const showSuccess = (message, config = {}) => {
  return toast.success(message, { ...DEFAULT_CONFIG, ...config });
};

export const showQuickSuccess = (message, config = {}) => {
  return toast.success(message, { ...QUICK_CONFIG, ...config });
};

/**
 * Error notifications
 */
export const showError = (message, config = {}) => {
  return toast.error(message, { ...LONG_CONFIG, ...config });
};

export const showQuickError = (message, config = {}) => {
  return toast.error(message, { ...DEFAULT_CONFIG, ...config });
};

/**
 * Info notifications
 */
export const showInfo = (message, config = {}) => {
  return toast.info(message, { ...DEFAULT_CONFIG, ...config });
};

export const showQuickInfo = (message, config = {}) => {
  return toast.info(message, { ...QUICK_CONFIG, ...config });
};

/**
 * Warning notifications
 */
export const showWarning = (message, config = {}) => {
  return toast.warning(message, { ...DEFAULT_CONFIG, ...config });
};

/**
 * Predefined success messages
 */
export const SUCCESS_MESSAGES = {
  // Authentication
  LOGIN_SUCCESS: 'Welcome back! You have been logged in successfully.',
  SIGNUP_SUCCESS: 'Account created successfully! Please check your email for verification.',
  VERIFICATION_SUCCESS: 'Email verified successfully! You can now log in.',
  LOGOUT_SUCCESS: 'You have been logged out successfully.',
  PASSWORD_RESET_SUCCESS: 'Password reset instructions sent to your email.',
  PASSWORD_UPDATE_SUCCESS: 'Password updated successfully.',

  // Profile & User Info
  PROFILE_SAVED: 'Profile saved successfully!',
  PROFILE_UPDATED: 'Profile updated successfully!',
  INDIVIDUAL_INFO_SAVED: 'Personal information saved successfully!',
  PROFESSIONAL_INFO_SAVED: 'Professional information saved successfully!',
  BROKER_INFO_SAVED: 'Broker information saved successfully!',
  SUPPLIER_INFO_SAVED: 'Supplier information saved successfully!',

  // Invoice & File Upload
  INVOICE_UPLOADED: 'Energy bill uploaded successfully!',
  INVOICE_PROCESSED: 'Energy bill processed successfully!',
  FILE_UPLOADED: 'File uploaded successfully!',
  DATA_EXTRACTED: 'Bill information extracted successfully!',

  // Offers & Contracts
  OFFER_CREATED: 'Offer created successfully!',
  OFFER_ACCEPTED: 'Offer accepted successfully! Contract will be generated.',
  OFFER_UPDATED: 'Offer updated successfully!',
  CONTRACT_GENERATED: 'Contract generated successfully!',

  // Clients & Broker Operations
  CLIENT_ADDED: 'Client added successfully!',
  CLIENT_UPDATED: 'Client information updated successfully!',
  DEAL_PROCESSED: 'Deal processed successfully!',

  // General
  SAVED_SUCCESSFULLY: 'Saved successfully!',
  UPDATED_SUCCESSFULLY: 'Updated successfully!',
  DELETED_SUCCESSFULLY: 'Deleted successfully!',
  OPERATION_COMPLETED: 'Operation completed successfully!'
};

/**
 * Predefined error messages
 */
export const ERROR_MESSAGES = {
  // Authentication
  LOGIN_FAILED: 'Login failed. Please check your credentials and try again.',
  SIGNUP_FAILED: 'Account creation failed. Please try again.',
  VERIFICATION_FAILED: 'Email verification failed. Please try again.',
  PASSWORD_RESET_FAILED: 'Failed to send password reset instructions. Please try again.',
  SESSION_EXPIRED: 'Your session has expired. Please log in again.',
  UNAUTHORIZED: 'You are not authorized to perform this action.',

  // Profile & User Info
  PROFILE_SAVE_FAILED: 'Failed to save profile. Please try again.',
  PROFILE_LOAD_FAILED: 'Failed to load profile information.',
  VALIDATION_FAILED: 'Please check your information and try again.',

  // Invoice & File Upload
  UPLOAD_FAILED: 'File upload failed. Please try again.',
  PROCESSING_FAILED: 'File processing failed. Please try again.',
  INVALID_FILE_TYPE: 'Invalid file type. Please upload a valid file.',
  FILE_TOO_LARGE: 'File is too large. Please upload a smaller file.',
  EXTRACTION_FAILED: 'Failed to extract information from the file.',

  // Offers & Contracts
  OFFER_CREATION_FAILED: 'Failed to create offer. Please try again.',
  OFFER_ACCEPTANCE_FAILED: 'Failed to accept offer. Please try again.',
  OFFERS_LOAD_FAILED: 'Failed to load offers. Please refresh the page.',

  // Network & General
  NETWORK_ERROR: 'Network error. Please check your connection and try again.',
  SERVER_ERROR: 'Server error. Please try again later.',
  UNEXPECTED_ERROR: 'An unexpected error occurred. Please try again.',
  OPERATION_FAILED: 'Operation failed. Please try again.',
  DATA_LOAD_FAILED: 'Failed to load data. Please refresh the page.'
};

/**
 * Predefined info messages
 */
export const INFO_MESSAGES = {
  FEATURE_COMING_SOON: 'This feature is coming soon!',
  REDIRECTING: 'Redirecting you now...',
  PROCESSING: 'Processing your request...',
  PLEASE_WAIT: 'Please wait while we process your request.',
  CHECK_EMAIL: 'Please check your email for further instructions.',
  FORM_SAVED_DRAFT: 'Your form has been saved as a draft.',
  AUTO_SAVE_ENABLED: 'Auto-save is enabled for this form.',
  SESSION_WARNING: 'Your session will expire soon. Please save your work.',
  MAINTENANCE_MODE: 'The system is currently under maintenance. Some features may be unavailable.'
};

/**
 * Show predefined success message
 */
export const showSuccessMessage = (key, customMessage = null, config = {}) => {
  const message = customMessage || SUCCESS_MESSAGES[key] || 'Operation completed successfully!';
  return showSuccess(message, config);
};

/**
 * Show predefined error message
 */
export const showErrorMessage = (key, customMessage = null, config = {}) => {
  const message = customMessage || ERROR_MESSAGES[key] || 'An error occurred. Please try again.';
  return showError(message, config);
};

/**
 * Show predefined info message
 */
export const showInfoMessage = (key, customMessage = null, config = {}) => {
  const message = customMessage || INFO_MESSAGES[key] || 'Information';
  return showInfo(message, config);
};

/**
 * Dismiss all toasts
 */
export const dismissAll = () => {
  toast.dismiss();
};

/**
 * Show loading toast (returns toast ID for manual dismissal)
 */
export const showLoadingToast = (message = 'Loading...', config = {}) => {
  return toast.loading(message, { ...DEFAULT_CONFIG, ...config });
};

/**
 * Update existing toast
 */
export const updateToast = (toastId, message, type = 'default', config = {}) => {
  return toast.update(toastId, {
    render: message,
    type: type,
    isLoading: false,
    ...DEFAULT_CONFIG,
    ...config
  });
};

/**
 * Promise-based toast for async operations
 */
export const showPromiseToast = (promise, messages = {}, config = {}) => {
  const defaultMessages = {
    pending: 'Processing...',
    success: 'Operation completed successfully!',
    error: 'Operation failed. Please try again.'
  };

  return toast.promise(
    promise,
    { ...defaultMessages, ...messages },
    { ...DEFAULT_CONFIG, ...config }
  );
};

export default {
  success: showSuccess,
  error: showError,
  info: showInfo,
  warning: showWarning,
  successMessage: showSuccessMessage,
  errorMessage: showErrorMessage,
  infoMessage: showInfoMessage,
  dismiss: dismissAll,
  loading: showLoadingToast,
  update: updateToast,
  promise: showPromiseToast
};
