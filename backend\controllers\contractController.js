// const Contract = require('../models/Contract');
// const User = require('../models/User');
// const docusignService = require('../services/docusignService');
// const logger = require('../utils/logger');

/**
 * Get all contracts for a user
 */
const getUserContracts = async (req, res) => {
  try {
    console.log('Getting user contracts - using mock data for demo');

    // For demo purposes, return mock contracts
    const mockContracts = [
      {
        _id: 'contract-1',
        contractDetails: {
          contractNumber: 'CNT-2024-001',
          energyType: 'Electricity',
          rateType: 'Fixed',
          rate: 0.145,
          standingCharge: 25.5,
          estimatedAnnualCost: 950,
          currency: 'EUR'
        },
        startDate: new Date('2024-01-01'),
        endDate: new Date('2026-01-01'),
        status: 'Active',
        signatureStatus: {
          userSigned: true,
          userSignedDate: new Date('2024-01-01'),
          completed: true,
          completedDate: new Date('2024-01-01')
        },
        createdAt: new Date('2024-01-01')
      }
    ];

    res.json({
      success: true,
      data: mockContracts
    });

  } catch (error) {
    console.error('Error fetching user contracts:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch contracts',
      error: error.message
    });
  }
};

/**
 * Get a specific contract by ID
 */
const getContractById = async (req, res) => {
  try {
    const { id } = req.params;
    console.log(`Getting contract ${id} - using mock data for demo`);

    // For demo purposes, return mock contract
    const mockContract = {
      _id: id,
      contractDetails: {
        contractNumber: 'CNT-2024-001',
        energyType: 'Electricity',
        rateType: 'Fixed',
        rate: 0.145,
        standingCharge: 25.5,
        estimatedAnnualCost: 950,
        currency: 'EUR'
      },
      startDate: new Date('2024-01-01'),
      endDate: new Date('2026-01-01'),
      status: 'Active',
      signatureStatus: {
        userSigned: true,
        userSignedDate: new Date('2024-01-01'),
        completed: true,
        completedDate: new Date('2024-01-01')
      },
      createdAt: new Date('2024-01-01')
    };

    res.json({
      success: true,
      data: mockContract
    });

  } catch (error) {
    console.error('Error fetching contract:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch contract',
      error: error.message
    });
  }
};

/**
 * Update contract signature status (called by DocuSign webhook or manual update)
 */
const updateSignatureStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;
    console.log(`Updating contract ${id} signature status to ${status} - using mock data for demo`);

    res.json({
      success: true,
      message: 'Contract signature status updated',
      data: {
        contractId: id,
        status: 'Active',
        signatureStatus: {
          userSigned: true,
          userSignedDate: new Date(),
          completed: true,
          completedDate: new Date()
        }
      }
    });

  } catch (error) {
    console.error('Error updating signature status:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update signature status',
      error: error.message
    });
  }
};

/**
 * Get contract signing status
 */
const getSigningStatus = async (req, res) => {
  try {
    const { id } = req.params;
    console.log(`Getting signing status for contract ${id} - using mock data for demo`);

    res.json({
      success: true,
      data: {
        contractId: id,
        status: 'Active',
        signatureStatus: {
          userSigned: true,
          userSignedDate: new Date(),
          completed: true,
          completedDate: new Date()
        },
        docusignStatus: {
          status: 'completed',
          completed: true
        },
        envelopeId: 'mock-envelope-123'
      }
    });

  } catch (error) {
    console.error('Error getting signing status:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get signing status',
      error: error.message
    });
  }
};

/**
 * Download signed contract
 */
const downloadContract = async (req, res) => {
  try {
    const { id } = req.params;
    console.log(`Downloading contract ${id} - using mock data for demo`);

    // Return mock PDF content
    const mockPdfContent = Buffer.from('Mock signed contract PDF content');

    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename="contract-CNT-2024-001.pdf"`);
    res.send(mockPdfContent);

  } catch (error) {
    console.error('Error downloading contract:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to download contract',
      error: error.message
    });
  }
};

/**
 * Resend signing invitation
 */
const resendSigning = async (req, res) => {
  try {
    const { id } = req.params;
    console.log(`Resending signing invitation for contract ${id} - using mock data for demo`);

    const mockEnvelopeId = `mock-envelope-${Date.now()}`;
    const mockSigningUrl = `http://localhost:8080/contract-signed?contractId=${id}&envelopeId=${mockEnvelopeId}&mock=true`;

    res.json({
      success: true,
      message: 'Signing invitation sent',
      data: {
        contractId: id,
        signingUrl: mockSigningUrl,
        envelopeId: mockEnvelopeId
      }
    });

  } catch (error) {
    console.error('Error resending signing invitation:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to resend signing invitation',
      error: error.message
    });
  }
};

module.exports = {
  getUserContracts,
  getContractById,
  updateSignatureStatus,
  getSigningStatus,
  downloadContract,
  resendSigning
};
