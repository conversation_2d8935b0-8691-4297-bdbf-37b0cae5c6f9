import { useState, useEffect } from 'react';
import DashboardLayout from '../components/DashboardLayout';
import Spinner from '../components/Spinner';
import '../styles/appointments.css';

const Appointments = () => {
  const [loading, setLoading] = useState(true);
  const [appointments, setAppointments] = useState([]);

  useEffect(() => {
    // Simulate loading appointments data
    const fetchAppointments = async () => {
      try {
        setLoading(true);
        // In a real app, this would be an API call
        // For now, we'll use mock data
        setTimeout(() => {
          const mockAppointments = [
            {
              id: 1,
              title: 'Energy Consultation',
              date: '2023-12-15',
              time: '10:00 AM',
              duration: 60,
              type: 'Video Call',
              status: 'Upcoming',
              with: 'Energy Advisor',
              notes: 'Discuss energy saving options for your home'
            },
            {
              id: 2,
              title: 'Contract Review',
              date: '2023-12-20',
              time: '2:30 PM',
              duration: 45,
              type: 'Phone Call',
              status: 'Upcoming',
              with: 'Legal Advisor',
              notes: 'Review new energy contract terms'
            },
            {
              id: 3,
              title: 'Installation Planning',
              date: '2024-01-05',
              time: '9:00 AM',
              duration: 90,
              type: 'In-Person',
              status: 'Scheduled',
              with: 'Technical Team',
              notes: 'Plan installation of new energy-efficient equipment'
            }
          ];
          setAppointments(mockAppointments);
          setLoading(false);
        }, 1000);
      } catch (error) {
        console.error('Error fetching appointments:', error);
        setLoading(false);
      }
    };

    fetchAppointments();
  }, []);

  const formatDate = (dateString) => {
    const options = { year: 'numeric', month: 'long', day: 'numeric' };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  return (
    <DashboardLayout>
      <div className="appointments-container">
        <div className="appointments-header">
          <h1>My Appointments</h1>
          <button className="new-appointment-btn">
            <i className="fas fa-plus"></i> New Appointment
          </button>
        </div>

        {loading ? (
          <Spinner message="Loading appointments..." />
        ) : (
          <>
            {appointments.length === 0 ? (
              <div className="no-appointments">
                <p>You don't have any appointments scheduled.</p>
                <button className="schedule-btn">Schedule Your First Appointment</button>
              </div>
            ) : (
              <div className="appointments-list">
                {appointments.map((appointment) => (
                  <div key={appointment.id} className="appointment-card">
                    <div className="appointment-date">
                      <div className="date-badge">
                        <span className="month">{new Date(appointment.date).toLocaleString('default', { month: 'short' })}</span>
                        <span className="day">{new Date(appointment.date).getDate()}</span>
                      </div>
                      <span className="time">{appointment.time}</span>
                    </div>
                    <div className="appointment-details">
                      <h3 className="appointment-title">{appointment.title}</h3>
                      <div className="appointment-info">
                        <span className="appointment-type">
                          <i className={`fas ${
                            appointment.type === 'Video Call' ? 'fa-video' : 
                            appointment.type === 'Phone Call' ? 'fa-phone' : 'fa-user'
                          }`}></i>
                          {appointment.type}
                        </span>
                        <span className="appointment-duration">
                          <i className="fas fa-clock"></i>
                          {appointment.duration} min
                        </span>
                        <span className="appointment-with">
                          <i className="fas fa-user-tie"></i>
                          {appointment.with}
                        </span>
                      </div>
                      <p className="appointment-notes">{appointment.notes}</p>
                      <div className="appointment-actions">
                        <button className="reschedule-btn">Reschedule</button>
                        <button className="cancel-btn">Cancel</button>
                      </div>
                    </div>
                    <div className={`appointment-status ${appointment.status.toLowerCase()}`}>
                      {appointment.status}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </>
        )}
      </div>
    </DashboardLayout>
  );
};

export default Appointments;
