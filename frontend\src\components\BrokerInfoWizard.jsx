import React, { useState, useEffect } from 'react';
import BrokerCompanyInfoStep from './wizard-steps/BrokerCompanyInfoStep';
import BrokerSpecializationStep from './wizard-steps/BrokerSpecializationStep';
import BrokerCommissionStep from './wizard-steps/BrokerCommissionStep';
import BrokerConfirmationStep from './wizard-steps/BrokerConfirmationStep';
import StepperProgress from './StepperProgress';
import { useForceScrollToTopAuthenticated } from '../utils/scrollToTop';
import '../styles/stepper.css';

const BrokerInfoWizard = ({ onSubmit, onCancel, userData }) => {
  const [currentStep, setCurrentStep] = useState(1);

  // Initialize form data with default values
  const [formData, setFormData] = useState({
    companyName: '',
    licenseNumber: '',
    website: '',
    phone: '',
    yearsOfExperience: '',
    vatNumber: '',
    businessDescription: '',
    companyAddress: {
      street: '',
      city: '',
      postalCode: '',
      country: 'France'
    },
    specializations: [],
    serviceAreas: [],
    commissionStructure: {
      type: 'percentage',
      rate: ''
    }
  });

  const totalSteps = 4;
  const stepLabels = ['Company Info', 'Specializations', 'Commission', 'Confirmation'];

  // Pre-populate form with user data if available
  useEffect(() => {
    if (userData) {
      setFormData(prev => ({
        ...prev,
        // Pre-populate any available data from userData
        companyName: userData.companyName || '',
        phone: userData.phoneNumber || '',
        // Add other mappings as needed
      }));
    }
  }, [userData]);

  // Force scroll to top whenever step changes
  useEffect(() => {
    console.log('🔄 BrokerInfoWizard: Step changed to:', currentStep);
    useForceScrollToTopAuthenticated();
  }, [currentStep]);

  const handleNextStep = () => {
    console.log('🔄 BrokerInfoWizard: Moving to next step');
    setCurrentStep(prev => Math.min(prev + 1, totalSteps));

    // Force scroll to top when moving to next step
    setTimeout(() => {
      useForceScrollToTopAuthenticated();
    }, 50); // Small delay to ensure state update completes
  };

  const handlePrevStep = () => {
    console.log('🔄 BrokerInfoWizard: Moving to previous step');
    setCurrentStep(prev => Math.max(prev - 1, 1));

    // Force scroll to top when moving to previous step
    setTimeout(() => {
      useForceScrollToTopAuthenticated();
    }, 50); // Small delay to ensure state update completes
  };

  const handleChange = (name, value) => {
    if (name.includes('.')) {
      // Handle nested object properties
      const [parent, child] = name.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  const handleMultiSelectChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: prev[field].includes(value)
        ? prev[field].filter(item => item !== value)
        : [...prev[field], value]
    }));
  };

  const handleSubmit = () => {
    onSubmit(formData);
  };

  return (
    <div className="stepper-container">
      <div className="stepper-content">
        <StepperProgress
          currentStep={currentStep}
          steps={stepLabels}
        />

        <div className="stepper-form">
        {currentStep === 1 && (
          <BrokerCompanyInfoStep
            formData={formData}
            onChange={handleChange}
            onNext={handleNextStep}
            onCancel={onCancel}
          />
        )}

        {currentStep === 2 && (
          <BrokerSpecializationStep
            formData={formData}
            onChange={handleChange}
            onMultiSelectChange={handleMultiSelectChange}
            onNext={handleNextStep}
            onPrev={handlePrevStep}
          />
        )}

        {currentStep === 3 && (
          <BrokerCommissionStep
            formData={formData}
            onChange={handleChange}
            onNext={handleNextStep}
            onPrev={handlePrevStep}
          />
        )}

        {currentStep === 4 && (
          <BrokerConfirmationStep
            formData={formData}
            onChange={handleChange}
            onSubmit={handleSubmit}
            onPrev={handlePrevStep}
            onCancel={onCancel}
          />
        )}
        </div>
      </div>
    </div>
  );
};

export default BrokerInfoWizard;
