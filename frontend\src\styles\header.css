.dashboard-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 60px;
  padding: 0 20px 0 15px;
  background-color: #fff;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  position: relative; /* Changed from sticky to relative */
  z-index: 99997 !important;
  margin: 0;
  overflow: visible !important;
}

/* Specific override for broker dashboard header */
.broker-dashboard-container .dashboard-header {
  z-index: 99997 !important;
  position: relative !important;
  overflow: visible !important;
}

.header-left {
  display: flex;
  align-items: center;
  margin-left: 0;
  height: 100%;
}

.header-right {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 1rem;
  height: 100%;
  min-width: 120px;
}

/* Mobile sidebar toggle button in header */
.mobile-sidebar-toggle {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  font-size: 18px;
  margin-right: 1rem;
  width: 42px;
  height: 42px;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.mobile-sidebar-toggle:hover {
  background-color: #f5f5f5;
  color: #000;
}

/* Sidebar toggle button styles moved to sidebar.css */

/* Header title container - ensures no icons appear */
.header-title-container {
  display: flex;
  align-items: center;
  position: relative;
  min-width: 150px;
  margin-left: 0;
  padding-right: 10px;
  overflow: visible;
  height: 100%;
}

/* Clean page title styling with no icons */
.page-title {
  font-size: 18px;
  font-weight: 600;
  color: #000;
  margin: 0;
  padding: 0;
  background: transparent;
  border: none;
  box-shadow: none;
  display: inline-block;
  position: relative;
  white-space: nowrap;
  min-width: 120px;
}

/* Dashboard header notifications */
.dashboard-header .notifications-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dashboard-header .notifications-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  position: relative;
  transition: color 0.2s ease;
  width: 42px;
  height: 42px;
  border-radius: 50%;
}

.dashboard-header .notifications-button:hover {
  color: #000;
  background-color: #f5f5f5;
}

.dashboard-header .notification-icon-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dashboard-header .notification-badge {
  position: absolute;
  top: -2px;
  right: -2px;
  width: 8px;
  height: 8px;
  background-color: #ff3b30;
  border-radius: 50%;
  border: 2px solid #fff;
}

/* Dashboard header user menu */
.dashboard-header .user-menu-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dashboard-header .user-menu-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  position: relative;
  transition: color 0.2s ease;
  width: 42px;
  height: 42px;
  border-radius: 50%;
}

.dashboard-header .user-menu-button:hover {
  color: #000;
  background-color: #f5f5f5;
}

/* Ensure dropdowns appear above other content */
.dashboard-header .notifications-dropdown,
.dashboard-header .user-dropdown {
  position: absolute;
  top: calc(100% + 8px);
  right: 0;
  z-index: 99999;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 1px solid #e0e0e0;
}

/* Explicitly prevent any pseudo-elements */
.header-title-container::before,
.header-title-container::after,
.page-title::before,
.page-title::after,
.header-title-container *::before,
.header-title-container *::after {
  display: none !important;
  content: none !important;
  background: none !important;
  width: 0 !important;
  height: 0 !important;
}

.header-right {
  display: flex;
  align-items: center;
  height: 100%;
}

/* Notifications */
.notifications-container {
  position: relative;
  margin-right: 15px;
  z-index: 1000;
}

.notifications-button {
  background: none;
  border: none;
  cursor: pointer;
  position: relative;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
  color: #666;
}

.notifications-button:hover,
.notifications-button.active {
  background-color: #f0f0f0;
}

.notification-icon-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}

.notification-icon-wrapper svg {
  width: 24px;
  height: 24px;
  color: inherit;
}

.notification-badge {
  position: absolute;
  top: -2px;
  right: -2px;
  background-color: #ff3b30;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  border: 2px solid white;
}

.notifications-dropdown {
  position: absolute !important;
  top: calc(100% + 8px) !important;
  right: 0 !important;
  width: 320px;
  max-width: calc(100vw - 20px);
  max-height: 400px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  z-index: 9999;
  border: 1px solid #e0e0e0;
  transform: none !important;
  margin-top: 0 !important;
}

.notifications-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px;
  border-bottom: 1px solid #eee;
}

.notifications-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.mark-all-read {
  background: none;
  border: none;
  color: #000;
  font-size: 12px;
  cursor: pointer;
  text-decoration: underline;
}

.notifications-list {
  max-height: 300px;
  overflow-y: auto;
}

.notification-item {
  display: flex;
  padding: 15px;
  border-bottom: 1px solid #eee;
  transition: background-color 0.2s;
}

.notification-item:hover {
  background-color: #f9f9f9;
}

.notification-item.unread {
  background-color: #f0f7ff;
}

.notification-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  flex-shrink: 0;
}

.notification-icon i {
  font-size: 16px;
  color: #333;
}

.notification-content {
  flex: 1;
}

.notification-content p {
  margin: 0 0 5px 0;
  font-size: 14px;
}

.notification-time {
  font-size: 12px;
  color: #777;
}

.notifications-footer {
  padding: 15px;
  text-align: center;
  border-top: 1px solid #eee;
}

.notifications-footer button {
  background: none;
  border: none;
  color: #000;
  font-size: 14px;
  cursor: pointer;
  text-decoration: underline;
}

/* User menu */
.user-menu-container {
  position: relative;
  z-index: 2147483646 !important; /* One less than dropdown */
}

/* Specific override for broker dashboard */
.broker-dashboard-container .user-menu-container {
  z-index: 2147483646 !important;
  position: relative !important;
}

/* Ensure header doesn't clip dropdowns */
.header,
.header-right,
.dashboard-header,
.main-content {
  overflow: visible !important;
}

.user-menu-button {
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  width: 45px;
  height: 45px;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.user-menu-button:hover {
  background-color: #f0f0f0;
}

.user-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.user-avatar {
  width: 45px;
  height: 45px;
  border-radius: 50%;
  background: linear-gradient(135deg, #4a4a4a, #7a7a7a);
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 16px;
  border: 2px solid #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.user-dropdown {
  position: absolute;
  top: calc(100% + 8px);
  right: 0;
  width: 250px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  z-index: 2147483647;
  border: 1px solid #e0e0e0;
  overflow: visible;
  max-height: none;
  height: auto;
  max-width: calc(100vw - 20px);
  min-width: 200px;
}

/* Ensure parent containers don't clip the dropdown */
.header,
.header-right,
.notifications-container,
.user-menu-container,
.dashboard-header,
.main-content,
.dashboard-layout,
.content-wrapper,
.broker-dashboard-container,
.supplier-dashboard-container {
  overflow: visible !important;
}

/* High specificity override for notifications dropdown positioning */
.header-right .notifications-container .notifications-dropdown,
.dashboard-header .header-right .notifications-container .notifications-dropdown {
  position: absolute !important;
  top: calc(100% + 8px) !important;
  right: 0 !important;
  left: auto !important;
  transform: none !important;
  margin: 0 !important;
}

.user-info {
  padding: 15px;
  border-bottom: 1px solid #eee;
}

.user-name {
  font-weight: 600;
  font-size: 16px;
  margin-bottom: 5px;
}

.user-email {
  font-size: 14px;
  color: #777;
}

.user-menu-items {
  padding: 10px 0;
  overflow: visible;
  max-height: none;
  height: auto;
}

.user-menu-items button {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 10px 15px;
  background: none;
  border: none;
  text-align: left;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.user-menu-items button:hover {
  background-color: #f5f5f5;
}

.user-menu-items button i,
.user-menu-items button svg {
  margin-right: 10px;
  width: 20px;
  text-align: center;
  flex-shrink: 0;
}

/* Mobile sidebar toggle button */
.mobile-sidebar-toggle {
  display: none;
  background: none;
  border: none;
  cursor: pointer;
  width: 40px;
  height: 40px;
  border-radius: 4px;
  margin-right: 10px;
  color: #333;
  font-size: 20px;
  transition: background-color 0.2s;
  align-items: center;
  justify-content: center;
  padding: 0;
  margin-top: 0;
  margin-bottom: 0;
}

.mobile-sidebar-toggle:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.mobile-sidebar-toggle i {
  font-size: 22px;
}

/* Responsive styles */
@media (max-width: 991px) {
  .mobile-sidebar-toggle {
    display: flex;
  }
}

@media (max-width: 768px) {
  .page-title {
    font-size: 16px;
  }

  .notifications-dropdown {
    width: 290px;
  }

  .user-dropdown {
    width: 220px;
    max-width: calc(100vw - 20px);
  }

  .header-right {
    display: flex;
    align-items: center;
    gap: 5px;
  }

  .notifications-container {
    margin-right: 5px;
  }

  .notifications-button,
  .user-menu-button {
    width: 38px;
    height: 38px;
  }

  .notifications-button svg,
  .user-menu-button svg {
    width: 20px;
    height: 20px;
  }

  .mobile-sidebar-toggle {
    width: 36px;
    height: 36px;
    margin-right: 8px;
  }

  .mobile-sidebar-toggle i {
    font-size: 20px;
  }
}

@media (max-width: 480px) {
  .notifications-dropdown {
    width: 260px;
  }

  .user-dropdown {
    width: 200px;
    max-width: calc(100vw - 20px);
  }

  .notifications-button,
  .user-menu-button {
    width: 36px;
    height: 36px;
  }

  .notifications-button svg,
  .user-menu-button svg {
    width: 18px;
    height: 18px;
  }

  .mobile-sidebar-toggle {
    width: 34px;
    height: 34px;
    margin-right: 6px;
  }

  .mobile-sidebar-toggle i {
    font-size: 18px;
  }

  /* Make page title smaller on mobile */
  .page-title {
    font-size: 15px;
    min-width: 100px;
  }

  /* Header left margin is consistent across screen sizes */
}

@media (max-width: 320px) {
  .notifications-dropdown {
    width: 240px;
  }

  .user-dropdown {
    width: 180px;
    max-width: calc(100vw - 20px);
  }

  .notifications-button,
  .user-menu-button {
    width: 32px;
    height: 32px;
  }

  .notifications-button svg,
  .user-menu-button svg {
    width: 16px;
    height: 16px;
  }

  .mobile-sidebar-toggle {
    width: 32px;
    height: 32px;
    margin-right: 5px;
  }

  .mobile-sidebar-toggle i {
    font-size: 16px;
  }

  /* Header left margin is consistent across screen sizes */

  .page-title {
    font-size: 14px;
    min-width: 90px;
  }
}
