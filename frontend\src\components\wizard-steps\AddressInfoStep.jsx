import React from 'react';

const AddressInfoStep = ({ formData, onChange, onNext, onPrev }) => {
  const handleChange = (e) => {
    const { name, value } = e.target;
    onChange(name, value);
  };
  
  const handleSubmit = (e) => {
    e.preventDefault();
    onNext();
  };
  
  return (
    <div className="wizard-step">
      <h3 className="wizard-title">Address Information</h3>
      <form onSubmit={handleSubmit}>
        <div className="form-group">
          <label htmlFor="address">Address <span className="required">*</span></label>
          <input
            type="text"
            id="address"
            name="address"
            className="form-input"
            value={formData.address}
            onChange={handleChange}
            placeholder="Enter your full address"
            required
          />
        </div>
        
        <div className="wizard-buttons">
          <button type="button" className="btn btn-secondary" onClick={onPrev}>
            Back
          </button>
          <button type="submit" className="btn btn-primary">
            Next
          </button>
        </div>
      </form>
    </div>
  );
};

export default AddressInfoStep;
