@echo off
echo Starting Energy Bill Application in development mode...

echo Killing all Node.js processes...
taskkill /F /IM node.exe /T 2>nul

echo Starting MongoDB...
echo Note: Make sure MongoDB is installed or running in Docker

echo Starting backend...
start cmd /k "cd backend && npm run dev"

echo Waiting for backend to start...
timeout /t 5

echo Starting frontend...
start cmd /k "cd frontend && npm run dev"

echo Application started!
echo Backend: http://localhost:3000
echo Frontend: http://localhost:8080

echo Press any key to stop all processes...
pause

echo Stopping all Node.js processes...
taskkill /F /IM node.exe /T 2>nul

echo Application stopped!
