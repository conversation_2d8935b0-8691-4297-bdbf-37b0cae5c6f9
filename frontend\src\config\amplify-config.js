// AWS Amplify configuration
const amplifyConfig = {
  Auth: {
    // Amazon Cognito Region
    region: import.meta.env.VITE_AWS_REGION || 'eu-west-3',

    // Amazon Cognito User Pool ID
    userPoolId: import.meta.env.VITE_COGNITO_USER_POOL_ID || 'eu-west-3_k20MQLDSo',

    // Amazon Cognito Web Client ID
    userPoolWebClientId: import.meta.env.VITE_COGNITO_CLIENT_ID || '21ic56sj0uqlcck5jsjlq2m1ud',

    // Use USER_SRP_AUTH to avoid needing SECRET_HASH
    authenticationFlowType: 'USER_SRP_AUTH',

    // Optional - Enforces user authentication prior to accessing AWS resources
    mandatorySignIn: true,

    // Optional - Configuration for cookie storage
    cookieStorage: {
      // REQUIRED - Cookie domain (only required if cookieStorage is provided)
      domain: window.location.hostname,
      // OPTIONAL - Cookie path
      path: '/',
      // OPTIONAL - <PERSON>ie expiration in days
      expires: 7,
      // OPTIONAL - <PERSON>ie secure flag
      secure: process.env.NODE_ENV === 'production',
    },

    // Enable debug logs
    oauth: {
      scope: ['email', 'profile', 'openid'],
    }
  }
};

// Configuration is ready for use

export default amplifyConfig;
