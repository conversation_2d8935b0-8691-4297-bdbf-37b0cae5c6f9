import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import dashboardService from '../services/dashboard.service';
import '../styles/dashboard-stats.css';

const DashboardStats = () => {
  const [stats, setStats] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const navigate = useNavigate();

  useEffect(() => {
    const fetchStats = async () => {
      try {
        setLoading(true);
        const response = await dashboardService.getDashboardStats();
        if (response && response.data) {
          setStats(response.data);
        } else {
          setError('Failed to load dashboard statistics');
        }
      } catch (err) {
        console.error('Error fetching dashboard stats:', err);
        setError('Failed to load dashboard statistics');
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, []);

  const handleCardClick = (type) => {
    switch (type) {
      case 'invoices':
        navigate('/invoices');
        break;
      case 'offers':
        navigate('/offers');
        break;
      case 'contracts':
        navigate('/contracts');
        break;
      case 'appointments':
        navigate('/appointments');
        break;
      default:
        break;
    }
  };

  if (loading) {
    return (
      <div className="dashboard-stats-container loading">
        <div className="dashboard-stats-loading">
          <div className="loading-spinner"></div>
          <p>Loading statistics...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="dashboard-stats-container error">
        <div className="dashboard-stats-error">
          <p>{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="dashboard-stats-container">
      <div className="dashboard-stats-grid">
        {/* Invoices Card */}
        <div
          className="dashboard-stat-card"
          onClick={() => handleCardClick('invoices')}
        >
          <div className="stat-card-content">
            <div className="stat-card-icon invoices">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" width="30" height="30">
                <path d="M14 2H6c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V8l-6-6zm-2 16H8v-2h4v2zm6 0h-4v-2h4v2zm0-4H8v-2h10v2zm-6-4V3.5L18.5 9H12z"/>
              </svg>
            </div>
            <h3 className="stat-card-title">Invoices</h3>
            <p className="stat-card-description">
              {stats?.invoices?.count === 1 ? 'Uploaded invoice' : 'Uploaded invoices'}
            </p>
          </div>
          <p className="stat-card-value">{stats?.invoices?.count || 0}</p>
        </div>

        {/* Offers Card */}
        <div
          className="dashboard-stat-card"
          onClick={() => handleCardClick('offers')}
        >
          <div className="stat-card-content">
            <div className="stat-card-icon offers">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" width="30" height="30">
                <path d="M20 4H4c-1.11 0-1.99.89-1.99 2L2 18c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V6c0-1.11-.89-2-2-2zm0 14H4v-6h16v6zm0-10H4V6h16v2z"/>
              </svg>
            </div>
            <h3 className="stat-card-title">Offers</h3>
            <p className="stat-card-description">
              {stats?.offers?.count === 1 ? 'Available offer' : 'Available offers'}
            </p>
          </div>
          <p className="stat-card-value">{stats?.offers?.count || 0}</p>
        </div>

        {/* Contracts Card */}
        <div
          className="dashboard-stat-card"
          onClick={() => handleCardClick('contracts')}
        >
          <div className="stat-card-content">
            <div className="stat-card-icon contracts">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" width="30" height="30">
                <path d="M18 2H8c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h10c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 10H8V9h8v3zm0-5H8V4h8v3zm-8 10h8v3H8v-3z"/>
                <path d="M4 6H2v14c0 1.1.9 2 2 2h14v-2H4V6z"/>
              </svg>
            </div>
            <h3 className="stat-card-title">Contracts</h3>
            <p className="stat-card-description">
              {stats?.contracts?.count === 1 ? 'Active contract' : 'Active contracts'}
            </p>
          </div>
          <p className="stat-card-value">{stats?.contracts?.count || 0}</p>
        </div>

        {/* Appointments Card */}
        <div
          className="dashboard-stat-card"
          onClick={() => handleCardClick('appointments')}
        >
          <div className="stat-card-content">
            <div className="stat-card-icon appointments">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" width="30" height="30">
                <path d="M17 12h-5v5h5v-5zM16 1v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2h-1V1h-2zm3 18H5V8h14v11z"/>
              </svg>
            </div>
            <h3 className="stat-card-title">Appointments</h3>
            <p className="stat-card-description">
              {stats?.appointments?.count === 1 ? 'Upcoming appointment' : 'Upcoming appointments'}
            </p>
          </div>
          <p className="stat-card-value">{stats?.appointments?.count || 0}</p>
        </div>
      </div>
    </div>
  );
};

export default DashboardStats;
