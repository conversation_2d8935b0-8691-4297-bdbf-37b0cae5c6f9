const mongoose = require('mongoose');

const appointmentSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  agentId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  agentType: {
    type: String,
    enum: ['Broker', 'Supplier', 'Admin'],
    required: true
  },
  requestId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'EnergyRequest'
  },
  type: {
    type: String,
    enum: [
      'Initial Consultation', 
      'Offer Discussion', 
      'Contract Signing', 
      'Support', 
      'Follow-up',
      'Other'
    ],
    required: true
  },
  scheduledTime: {
    type: Date,
    required: true
  },
  duration: {
    type: Number,
    required: true,
    min: 5,
    default: 30
  },
  status: {
    type: String,
    enum: ['Scheduled', 'Completed', 'Cancelled', 'Rescheduled', 'Missed'],
    default: 'Scheduled'
  },
  notes: {
    type: String
  },
  location: {
    type: String,
    enum: ['Virtual', 'In-Person', 'Phone'],
    default: 'Virtual'
  },
  meetingLink: {
    type: String
  },
  reminderSent: {
    type: Boolean,
    default: false
  },
  reminderSentAt: {
    type: Date
  },
  feedback: {
    rating: {
      type: Number,
      min: 1,
      max: 5
    },
    comments: String,
    submittedAt: Date
  }
}, {
  timestamps: true
});

// Create indexes
appointmentSchema.index({ userId: 1 });
appointmentSchema.index({ agentId: 1 });
appointmentSchema.index({ requestId: 1 });
appointmentSchema.index({ scheduledTime: 1 });
appointmentSchema.index({ status: 1 });

const Appointment = mongoose.model('Appointment', appointmentSchema);

module.exports = Appointment;
