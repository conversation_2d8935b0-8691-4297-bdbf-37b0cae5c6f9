/**
 * Service for handling user verification status updates
 */

import { API_BASE_URL } from '../config/api-config';

class VerificationService {
  /**
   * Update user verification status in the backend
   * @param {Object} userData - User data including email, cognitoId, status, and verificationStatus
   * @returns {Promise} Promise object representing the update result
   */
  async updateVerificationStatus(userData) {
    try {
      console.log('Updating user verification status:', userData);

      // Make the API call to update verification status
      const response = await fetch(`${API_BASE_URL}/auth/update-verification-status`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(userData)
      });

      console.log('Verification status update response status:', response.status);

      const responseData = await response.json();
      console.log('Verification status update response data:', responseData);

      if (!response.ok) {
        throw new Error(responseData.message || 'Failed to update verification status');
      }

      return responseData;
    } catch (error) {
      console.error('Error updating verification status:', error);
      return { success: false, error: error.message };
    }
  }
}

export default new VerificationService();
