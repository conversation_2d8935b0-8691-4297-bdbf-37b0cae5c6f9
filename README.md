# My Energy Bill Application

## Project Overview
This application helps users manage and optimize their energy bills in France.

## Getting Started

### Prerequisites
- Node.js (v14 or higher)
- npm (v6 or higher)
- Docker and Docker Compose (for containerized deployment)

### Installation
1. Clone the repository
2. Install dependencies for both frontend and backend:
   ```
   cd frontend && npm install
   cd backend && npm install
   ```

## Running the Application

### Development Mode (Windows)
To run the application in development mode on Windows:
```
start-dev.bat
```
This will start both the frontend and backend in development mode with hot reloading.

### Using Docker (Windows)
To run the application using Docker on Windows:
```
start-docker.bat
```
This will build and start all containers (MongoDB, backend, and frontend).

### Development Mode with Docker (Windows)
To run the application in development mode using Docker on Windows:
```
start-docker-dev.bat
```
This will build and start all containers in development mode with volume mounting for hot reloading.

### MongoDB Only (Windows)
To run only MongoDB using Docker on Windows:
```
start-mongodb.bat
```
This will start only the MongoDB container.

### Linux Environment
For Linux environments, use the following scripts:
- `./build-and-run.sh` - Build and run the application using Docker
- `./dev.sh` - Run the application in development mode using Docker
- `./deploy.sh` - Build the application for deployment

## Deployment
To build the application for deployment:
```
deploy.bat  # Windows
./deploy.sh # Linux
```

This will build both the frontend and backend for production deployment.

## Project Structure
- `frontend/` - React.js frontend application
- `backend/` - Express.js backend API
- `docker-compose.yml` - Docker Compose configuration for production
- `docker-compose-dev.yml` - Docker Compose configuration for development

## Environment Variables
See `.env.example` files in both frontend and backend directories for required environment variables.

## API Documentation
The backend API is available at `http://localhost:3000` when running locally.
- Authentication: `/auth/*`
- API endpoints: `/api/*`
