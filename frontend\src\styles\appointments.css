.appointments-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.appointments-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.appointments-header h1 {
  font-size: 24px;
  font-weight: 600;
  color: #000;
  margin: 0;
}

.new-appointment-btn {
  background: linear-gradient(to right, #000, #333);
  color: #fff;
  border: none;
  border-radius: 6px;
  padding: 10px 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: all 0.2s ease;
}

.new-appointment-btn i {
  margin-right: 8px;
}

.new-appointment-btn:hover {
  opacity: 0.9;
  transform: translateY(-2px);
}

.no-appointments {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 40px;
  text-align: center;
}

.no-appointments p {
  font-size: 16px;
  color: #666;
  margin-bottom: 20px;
}

.schedule-btn {
  background: linear-gradient(to right, #000, #333);
  color: #fff;
  border: none;
  border-radius: 6px;
  padding: 12px 24px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.schedule-btn:hover {
  opacity: 0.9;
  transform: translateY(-2px);
}

.appointments-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.appointment-card {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  padding: 20px;
  display: flex;
  position: relative;
  border-left: 4px solid #000;
  transition: transform 0.2s ease;
}

.appointment-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
}

.appointment-date {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 20px;
  min-width: 80px;
}

.date-badge {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 8px;
  padding: 8px;
  width: 70px;
  margin-bottom: 10px;
}

.month {
  font-size: 14px;
  font-weight: 600;
  color: #666;
  text-transform: uppercase;
}

.day {
  font-size: 24px;
  font-weight: 700;
  color: #000;
}

.time {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.appointment-details {
  flex: 1;
}

.appointment-title {
  font-size: 18px;
  font-weight: 600;
  color: #000;
  margin: 0 0 10px 0;
}

.appointment-info {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 10px;
}

.appointment-type,
.appointment-duration,
.appointment-with {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #555;
}

.appointment-type i,
.appointment-duration i,
.appointment-with i {
  margin-right: 5px;
  color: #666;
}

.appointment-notes {
  font-size: 14px;
  color: #666;
  margin: 0 0 15px 0;
  line-height: 1.5;
}

.appointment-actions {
  display: flex;
  gap: 10px;
}

.reschedule-btn,
.cancel-btn {
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.reschedule-btn {
  background-color: #f5f5f5;
  color: #333;
  border: 1px solid #ddd;
}

.cancel-btn {
  background-color: #fff;
  color: #ff3b30;
  border: 1px solid #ff3b30;
}

.reschedule-btn:hover {
  background-color: #eee;
}

.cancel-btn:hover {
  background-color: #fff5f5;
}

.appointment-status {
  position: absolute;
  top: 20px;
  right: 20px;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.appointment-status.upcoming {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.appointment-status.scheduled {
  background-color: #e3f2fd;
  color: #1565c0;
}

.appointment-status.completed {
  background-color: #f5f5f5;
  color: #616161;
}

.appointment-status.cancelled {
  background-color: #ffebee;
  color: #c62828;
}

/* Responsive styles */
@media (max-width: 768px) {
  .appointments-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
  
  .appointment-card {
    flex-direction: column;
  }
  
  .appointment-date {
    flex-direction: row;
    margin-right: 0;
    margin-bottom: 15px;
    width: 100%;
    justify-content: flex-start;
  }
  
  .date-badge {
    margin-bottom: 0;
    margin-right: 15px;
  }
  
  .appointment-status {
    position: relative;
    top: auto;
    right: auto;
    margin-top: 15px;
    align-self: flex-start;
  }
}
