const mongoose = require('mongoose');

const offerSchema = new mongoose.Schema({
  requestId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'EnergyRequest',
    required: true
  },
  supplierId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  offerDetails: {
    name: String,
    description: String,
    highlights: [String]
  },
  energyType: {
    type: String,
    enum: ['Electricity', 'Gas', 'Both'],
    required: true
  },
  rateType: {
    type: String,
    enum: ['Fixed', 'Variable', 'Indexed', 'Green'],
    required: true
  },
  duration: {
    type: Number,
    required: true,
    min: 1
  },
  price: {
    baseRate: Number,
    standingCharge: Number,
    totalEstimatedAnnual: Number,
    currency: {
      type: String,
      default: 'EUR'
    }
  },
  estimatedSavings: {
    amount: Number,
    percentage: Number
  },
  additionalBenefits: [String],
  termsUrl: {
    type: String
  },
  validUntil: {
    type: Date,
    required: true
  },
  status: {
    type: String,
    enum: ['Draft', 'Pending', 'Active', 'Accepted', 'Rejected', 'Expired'],
    default: 'Draft'
  },
  reviewStatus: {
    type: String,
    enum: ['PendingReview', 'Approved', 'NeedsRevision', 'Rejected'],
    default: 'PendingReview'
  },
  reviewNotes: {
    type: String
  },
  reviewedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  reviewDate: {
    type: Date
  },
  views: {
    type: Number,
    default: 0
  },
  applications: {
    type: Number,
    default: 0
  }
}, {
  timestamps: true
});

// Create indexes
offerSchema.index({ requestId: 1 });
offerSchema.index({ supplierId: 1 });
offerSchema.index({ status: 1 });
offerSchema.index({ reviewStatus: 1 });
offerSchema.index({ validUntil: 1 });

const Offer = mongoose.model('Offer', offerSchema);

module.exports = Offer;
