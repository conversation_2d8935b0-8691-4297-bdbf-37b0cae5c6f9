const AWS = require('aws-sdk');
const logger = require('../utils/logger');
const emailTemplateService = require('./emailTemplateService');
const dotenv = require('dotenv');
const path = require('path');

// Load environment variables if not already loaded
if (!process.env.FROM_EMAIL) {
  const cliEnv = process.argv[2];
  const nodeEnv = cliEnv || process.env.NODE_ENV || 'local';
  const envFile = `.env.${nodeEnv}`;

  dotenv.config({
    path: path.resolve(__dirname, `../${envFile}`),
    override: false,
  });

  logger.info(`EmailService: Loaded environment from ${envFile}`);
}

// Configure AWS SES
const ses = new AWS.SES({
  region: process.env.AWS_REGION || 'eu-west-3',
  accessKeyId: process.env.AWS_ACCESS_KEY_ID,
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
});

class EmailService {
  constructor() {
    this.fromEmail = process.env.FROM_EMAIL || '<EMAIL>';
    this.companyName = process.env.COMPANY_NAME || 'Energy Platform';
    this.frontendUrl = process.env.FRONTEND_URL || 'http://localhost:8080';
  }

  /**
   * Send email using template
   * @param {string} templateName - Name of the email template
   * @param {string} recipientEmail - Recipient email address
   * @param {Object} templateVariables - Variables for template rendering
   * @param {Array} tags - Optional SES tags
   * @returns {Promise<Object>} Send result
   */
  async sendTemplatedEmail(templateName, recipientEmail, templateVariables = {}, tags = []) {
    try {
      // Add common variables
      const variables = {
        recipientEmail,
        companyName: this.companyName,
        frontendUrl: this.frontendUrl,
        currentYear: new Date().getFullYear(),
        ...templateVariables
      };

      // Render email template
      const renderedEmail = await emailTemplateService.renderEmail(templateName, variables);

      // Prepare SES parameters
      const params = {
        Source: this.fromEmail,
        Destination: {
          ToAddresses: [recipientEmail]
        },
        Message: {
          Subject: {
            Data: renderedEmail.subject,
            Charset: 'UTF-8'
          },
          Body: {
            Html: {
              Data: renderedEmail.htmlBody,
              Charset: 'UTF-8'
            },
            Text: {
              Data: renderedEmail.textBody,
              Charset: 'UTF-8'
            }
          }
        },
        Tags: tags
      };

      // Send email
      const result = await ses.sendEmail(params).promise();

      logger.info(`Email sent successfully using template: ${templateName}`, {
        messageId: result.MessageId,
        recipient: recipientEmail,
        template: templateName
      });

      return {
        success: true,
        messageId: result.MessageId,
        template: templateName
      };
    } catch (error) {
      logger.error(`Failed to send templated email: ${templateName}`, {
        error: error.message,
        recipient: recipientEmail,
        template: templateName
      });
      throw error;
    }
  }

  // ===== INVITATION EMAILS =====

  async sendInvitationEmail(invitation) {
    const { userType, email, inviteeDetails, invitationToken } = invitation;
    
    const registrationUrl = `${this.frontendUrl}/register/invited?token=${invitationToken}&type=${userType.toLowerCase()}`;
    const userTypeDisplay = userType === 'Broker' ? 'Energy Broker' : 'Energy Supplier';
    
    const variables = {
      subject: `You're Invited to Join ${this.companyName}!`,
      inviteeName: inviteeDetails.name || 'there',
      userTypeDisplay,
      registrationUrl,
      inviteeEmail: inviteeDetails.email || email
    };

    const tags = [
      { Name: 'EmailType', Value: 'Invitation' },
      { Name: 'UserType', Value: userType }
    ];

    return await this.sendTemplatedEmail('invitation', email, variables, tags);
  }

  async sendInvitationReminderEmail(invitation, reminderNumber) {
    const { userType, email, inviteeDetails, invitationToken } = invitation;
    
    const registrationUrl = `${this.frontendUrl}/register/invited?token=${invitationToken}&type=${userType.toLowerCase()}`;
    const userTypeDisplay = userType === 'Broker' ? 'Energy Broker' : 'Energy Supplier';
    
    const variables = {
      subject: `Reminder: Your ${this.companyName} Invitation is Expiring Soon`,
      inviteeName: inviteeDetails.name || 'there',
      userTypeDisplay,
      registrationUrl,
      reminderNumber: reminderNumber + 1,
      inviteeEmail: inviteeDetails.email || email
    };

    const tags = [
      { Name: 'EmailType', Value: 'InvitationReminder' },
      { Name: 'UserType', Value: userType },
      { Name: 'ReminderNumber', Value: (reminderNumber + 1).toString() }
    ];

    return await this.sendTemplatedEmail('invitation-reminder', email, variables, tags);
  }

  // ===== PASSWORD RESET EMAILS =====

  async sendPasswordResetEmail(userEmail, resetToken, userName = '') {
    const resetUrl = `${this.frontendUrl}/reset-password?token=${resetToken}`;
    
    const variables = {
      subject: `Reset Your ${this.companyName} Password`,
      userName: userName || 'User',
      resetUrl,
      userEmail,
      requestTime: new Date().toLocaleString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      })
    };

    const tags = [
      { Name: 'EmailType', Value: 'PasswordReset' }
    ];

    return await this.sendTemplatedEmail('password-reset', userEmail, variables, tags);
  }

  async sendPasswordResetConfirmationEmail(userEmail, userName = '') {
    const variables = {
      subject: `Password Reset Successful - ${this.companyName}`,
      userName: userName || 'User',
      userEmail,
      resetDate: new Date().toLocaleString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      })
    };

    const tags = [
      { Name: 'EmailType', Value: 'PasswordResetConfirmation' }
    ];

    return await this.sendTemplatedEmail('password-reset-confirmation', userEmail, variables, tags);
  }

  async sendAdminPasswordResetNotificationEmail(userEmail, userName, adminName) {
    const variables = {
      subject: `Password Reset Initiated - ${this.companyName}`,
      userName: userName || 'User',
      userEmail,
      adminName: adminName || 'Administrator',
      actionDate: new Date().toLocaleString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      })
    };

    const tags = [
      { Name: 'EmailType', Value: 'AdminPasswordResetNotification' }
    ];

    return await this.sendTemplatedEmail('admin-password-reset-notification', userEmail, variables, tags);
  }

  // ===== USER STATUS EMAILS =====

  async sendUserSuspendedEmail(userEmail, userName, adminName) {
    const variables = {
      subject: `Account Suspended - ${this.companyName}`,
      userName: userName || 'User',
      userEmail,
      adminName: adminName || 'Administrator',
      actionDate: new Date().toLocaleString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      })
    };

    const tags = [
      { Name: 'EmailType', Value: 'UserSuspended' }
    ];

    return await this.sendTemplatedEmail('user-suspended', userEmail, variables, tags);
  }

  async sendUserInactiveEmail(userEmail, userName, adminName) {
    const variables = {
      subject: `Account Status Update - ${this.companyName}`,
      userName: userName || 'User',
      userEmail,
      adminName: adminName || 'Administrator',
      actionDate: new Date().toLocaleString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      })
    };

    const tags = [
      { Name: 'EmailType', Value: 'UserInactive' }
    ];

    return await this.sendTemplatedEmail('user-inactive', userEmail, variables, tags);
  }

  // ===== PROFILE STATUS EMAILS =====

  async sendProfileSubmittedEmail(user) {
    const name = user.firstName ? `${user.firstName} ${user.lastName || ''}`.trim() : 'there';
    const userTypeDisplay = user.userType === 'Broker' ? 'Energy Broker' : 'Energy Supplier';
    
    const variables = {
      subject: `Profile Submitted Successfully - ${this.companyName}`,
      userName: name,
      userEmail: user.email,
      userTypeDisplay,
      submissionDate: new Date().toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    };

    const tags = [
      { Name: 'EmailType', Value: 'ProfileSubmitted' },
      { Name: 'UserType', Value: user.userType }
    ];

    return await this.sendTemplatedEmail('profile-submitted', user.email, variables, tags);
  }

  async sendProfileApprovedEmail(user) {
    const name = user.firstName ? `${user.firstName} ${user.lastName || ''}`.trim() : 'there';
    const userTypeDisplay = user.userType === 'Broker' ? 'Energy Broker' : 'Energy Supplier';
    const dashboardUrl = `${this.frontendUrl}/dashboard`;
    
    const variables = {
      subject: `Welcome to ${this.companyName} - Your ${userTypeDisplay} Account is Approved!`,
      userName: name,
      userEmail: user.email,
      userTypeDisplay,
      dashboardUrl,
      approvalDate: new Date().toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    };

    const tags = [
      { Name: 'EmailType', Value: 'ProfileApproved' },
      { Name: 'UserType', Value: user.userType }
    ];

    return await this.sendTemplatedEmail('profile-approved', user.email, variables, tags);
  }

  async sendChangesRequestedEmail(user, changesRequested, adminNotes) {
    const name = user.firstName ? `${user.firstName} ${user.lastName || ''}`.trim() : 'there';
    const userTypeDisplay = user.userType === 'Broker' ? 'Energy Broker' : 'Energy Supplier';
    
    const variables = {
      subject: `Profile Changes Requested - ${this.companyName}`,
      userName: name,
      userEmail: user.email,
      userTypeDisplay,
      changesRequested,
      adminNotes,
      reviewDate: new Date().toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    };

    const tags = [
      { Name: 'EmailType', Value: 'ChangesRequested' },
      { Name: 'UserType', Value: user.userType }
    ];

    return await this.sendTemplatedEmail('changes-requested', user.email, variables, tags);
  }

  // ===== BROKER SPECIFIC EMAILS =====

  async sendBrokerRegistrationAdminEmail(brokerName, brokerEmail, adminEmails) {
    const adminDashboardUrl = `${this.frontendUrl}/admin/dashboard`;
    
    const variables = {
      subject: `New Broker Registration - ${this.companyName}`,
      brokerName,
      brokerEmail,
      adminDashboardUrl,
      registrationDate: new Date().toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    };

    const tags = [
      { Name: 'EmailType', Value: 'BrokerRegistrationAdmin' }
    ];

    // Send to all admin emails
    const promises = adminEmails.map(adminEmail => 
      this.sendTemplatedEmail('broker-registration-admin', adminEmail, variables, tags)
    );

    return await Promise.all(promises);
  }

  async sendBrokerApprovalEmail(brokerName, brokerEmail, adminNotes) {
    const loginUrl = `${this.frontendUrl}/login`;
    
    const variables = {
      subject: `🎉 Your Broker Application Has Been Approved!`,
      brokerName,
      brokerEmail,
      loginUrl,
      adminNotes,
      approvalDate: new Date().toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    };

    const tags = [
      { Name: 'EmailType', Value: 'BrokerApproval' }
    ];

    return await this.sendTemplatedEmail('broker-approval', brokerEmail, variables, tags);
  }

  // ===== UTILITY METHODS =====

  async verifyEmailAddress(email) {
    try {
      const params = {
        EmailAddress: email
      };
      
      await ses.verifyEmailIdentity(params).promise();
      logger.info(`Email verification initiated for: ${email}`);
      return true;
    } catch (error) {
      logger.error(`Failed to initiate email verification for: ${email}`, error);
      return false;
    }
  }

  async getVerifiedEmails() {
    try {
      const result = await ses.listVerifiedEmailAddresses().promise();
      return result.VerifiedEmailAddresses;
    } catch (error) {
      logger.error('Failed to get verified emails', error);
      return [];
    }
  }

  async getSendingQuota() {
    try {
      const result = await ses.getSendQuota().promise();
      return result;
    } catch (error) {
      logger.error('Failed to get sending quota', error);
      return null;
    }
  }

  async getSendingStatistics() {
    try {
      const result = await ses.getSendStatistics().promise();
      return result.SendDataPoints;
    } catch (error) {
      logger.error('Failed to get sending statistics', error);
      return [];
    }
  }
}

module.exports = new EmailService();
