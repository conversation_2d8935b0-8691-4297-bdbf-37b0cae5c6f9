/**
 * Smart Navigation Hook
 * Provides navigation decisions based on synchronized data
 * Handles fallbacks and error states gracefully
 */

import { useState, useEffect, useCallback } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import dataSync from '../utils/dataSync';
import logger from '../utils/logger';

/**
 * Smart navigation hook that handles data sync and navigation decisions
 * @param {Object} options - Configuration options
 * @returns {Object} - Navigation state and functions
 */
export const useSmartNavigation = (options = {}) => {
  const {
    skipAuthCheck = false,
    skipProfileCheck = false,
    allowedUserTypes = null, // Array of allowed user types for this page
    requireProfileComplete = false
  } = options;

  const navigate = useNavigate();
  const location = useLocation();
  
  const [navigationState, setNavigationState] = useState({
    isLoading: true,
    isAuthenticated: false,
    userType: null,
    profileComplete: false,
    shouldRedirect: false,
    redirectPath: null,
    redirectReason: null,
    dataSource: null,
    error: null
  });

  /**
   * Check and update navigation state
   */
  const checkNavigationState = useCallback(async () => {
    try {
      setNavigationState(prev => ({ ...prev, isLoading: true, error: null }));
      
      logger.debug('SmartNavigation: Checking navigation state for:', location.pathname);
      
      // Step 1: Check authentication if required
      let authResult = { isAuthenticated: true, user: null, source: 'skipped' };
      if (!skipAuthCheck) {
        authResult = await dataSync.getAuthenticationStatus();
        
        if (!authResult.isAuthenticated) {
          setNavigationState({
            isLoading: false,
            isAuthenticated: false,
            userType: null,
            profileComplete: false,
            shouldRedirect: true,
            redirectPath: '/login',
            redirectReason: 'not-authenticated',
            dataSource: authResult.source,
            error: null
          });
          return;
        }
      }
      
      // Step 2: Get user type
      const userTypeResult = await dataSync.getUserType();
      const userType = userTypeResult.userType;
      
      // Step 3: Check if user type is allowed for this page
      if (allowedUserTypes && userType && !allowedUserTypes.includes(userType)) {
        setNavigationState({
          isLoading: false,
          isAuthenticated: authResult.isAuthenticated,
          userType: userType,
          profileComplete: false,
          shouldRedirect: true,
          redirectPath: '/dashboard',
          redirectReason: 'user-type-not-allowed',
          dataSource: userTypeResult.source,
          error: null
        });
        return;
      }
      
      // Step 4: Handle missing user type
      if (!userType && !location.pathname.includes('/user-type')) {
        setNavigationState({
          isLoading: false,
          isAuthenticated: authResult.isAuthenticated,
          userType: null,
          profileComplete: false,
          shouldRedirect: true,
          redirectPath: '/user-type',
          redirectReason: 'no-user-type',
          dataSource: userTypeResult.source,
          error: null
        });
        return;
      }
      
      // Step 5: Check profile completion if required (skip for admin users)
      let profileResult = { isComplete: true, source: 'skipped' };
      if (!skipProfileCheck && userType && userType !== 'admin') {
        profileResult = await dataSync.getProfileCompletionStatus();
        
        // Handle profile completion requirements
        if (requireProfileComplete && !profileResult.isComplete) {
          const profilePath = getProfilePageForUserType(userType);
          if (profilePath && !location.pathname.includes(profilePath)) {
            setNavigationState({
              isLoading: false,
              isAuthenticated: authResult.isAuthenticated,
              userType: userType,
              profileComplete: false,
              shouldRedirect: true,
              redirectPath: profilePath,
              redirectReason: 'profile-incomplete',
              dataSource: profileResult.source,
              error: null
            });
            return;
          }
        }
        
        // Special handling for admin users (bypass all checks and go to dashboard)
        if (userType === 'admin' && !location.pathname.includes('/dashboard')) {
          logger.info('SmartNavigation: Admin user detected, redirecting to dashboard');
          setNavigationState({
            isLoading: false,
            isAuthenticated: authResult.isAuthenticated,
            userType: userType,
            profileComplete: true, // Admin users always have complete profile
            shouldRedirect: true,
            redirectPath: '/dashboard',
            redirectReason: 'admin-to-dashboard',
            dataSource: 'logic',
            error: null
          });
          return;
        }

        // Special handling for suppliers (skip profile completion)
        if (userType === 'supplier' && !location.pathname.includes('/dashboard')) {
          setNavigationState({
            isLoading: false,
            isAuthenticated: authResult.isAuthenticated,
            userType: userType,
            profileComplete: true, // Suppliers don't need profile completion
            shouldRedirect: true,
            redirectPath: '/dashboard',
            redirectReason: 'supplier-to-dashboard',
            dataSource: 'logic',
            error: null
          });
          return;
        }
      }
      
      // Step 6: All checks passed, no redirect needed
      setNavigationState({
        isLoading: false,
        isAuthenticated: authResult.isAuthenticated,
        userType: userType,
        profileComplete: profileResult.isComplete,
        shouldRedirect: false,
        redirectPath: null,
        redirectReason: null,
        dataSource: `auth:${authResult.source}, userType:${userTypeResult.source}, profile:${profileResult.source}`,
        error: null
      });
      
    } catch (error) {
      logger.error('SmartNavigation: Error checking navigation state:', error);
      setNavigationState({
        isLoading: false,
        isAuthenticated: false,
        userType: null,
        profileComplete: false,
        shouldRedirect: true,
        redirectPath: '/login',
        redirectReason: 'error',
        dataSource: 'error',
        error: error.message
      });
    }
  }, [skipAuthCheck, skipProfileCheck, allowedUserTypes, requireProfileComplete, location.pathname]);

  /**
   * Get the appropriate profile page for a user type
   */
  const getProfilePageForUserType = (userType) => {
    switch (userType) {
      case 'admin':
        return null; // Admin users don't need profile pages
      case 'individual':
        return '/individual-info';
      case 'professional':
        return '/professional-info';
      case 'broker':
        return '/broker-info';
      case 'supplier':
        return null; // Suppliers don't have profile pages
      default:
        return null;
    }
  };

  /**
   * Force refresh navigation state
   */
  const refreshNavigationState = useCallback(() => {
    dataSync.forceRefreshCache();
    checkNavigationState();
  }, [checkNavigationState]);

  /**
   * Handle navigation after profile updates
   */
  const handleProfileUpdate = useCallback(async (userType, profileComplete) => {
    try {
      await dataSync.syncAfterProfileUpdate(userType, profileComplete);
      await checkNavigationState();
    } catch (error) {
      logger.error('SmartNavigation: Error handling profile update:', error);
    }
  }, [checkNavigationState]);

  /**
   * Perform navigation if redirect is needed
   */
  useEffect(() => {
    if (navigationState.shouldRedirect && navigationState.redirectPath) {
      logger.debug('SmartNavigation: Redirecting to:', navigationState.redirectPath, 'Reason:', navigationState.redirectReason);
      navigate(navigationState.redirectPath, { replace: true });
    }
  }, [navigationState.shouldRedirect, navigationState.redirectPath, navigate]);

  /**
   * Check navigation state on mount and location change
   */
  useEffect(() => {
    checkNavigationState();
  }, [checkNavigationState]);

  return {
    // State
    isLoading: navigationState.isLoading,
    isAuthenticated: navigationState.isAuthenticated,
    userType: navigationState.userType,
    profileComplete: navigationState.profileComplete,
    error: navigationState.error,
    dataSource: navigationState.dataSource,
    
    // Actions
    refreshNavigationState,
    handleProfileUpdate,
    checkNavigationState,
    
    // Debug info
    navigationState: navigationState
  };
};

/**
 * Hook for pages that require authentication
 */
export const useAuthenticatedNavigation = (options = {}) => {
  return useSmartNavigation({
    skipAuthCheck: false,
    ...options
  });
};

/**
 * Hook for pages that require specific user types
 */
export const useUserTypeNavigation = (allowedUserTypes, options = {}) => {
  return useSmartNavigation({
    allowedUserTypes,
    skipAuthCheck: false,
    ...options
  });
};

/**
 * Hook for pages that require profile completion
 */
export const useProfileCompleteNavigation = (options = {}) => {
  return useSmartNavigation({
    skipAuthCheck: false,
    requireProfileComplete: true,
    ...options
  });
};

export default useSmartNavigation;
