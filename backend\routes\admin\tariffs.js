const express = require('express');
const router = express.Router();
const TariffGrid = require('../../models/TariffGrid');
const User = require('../../models/User');
const UserActivity = require('../../models/UserActivity');
const logger = require('../../utils/logger');
const { authMiddleware } = require('../../middleware/auth');
const multer = require('multer');
const csv = require('csv-parser');
const fs = require('fs');

// Configure multer for file uploads
const upload = multer({ dest: 'uploads/' });

// Apply auth middleware to all admin routes
router.use(authMiddleware);

// Middleware to check if user is admin
const requireAdmin = async (req, res, next) => {
  try {
    const userEmail = req.user?.email;
    if (!userEmail) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    const user = await User.findOne({ email: userEmail });
    if (!user || user.userType !== 'Admin') {
      return res.status(403).json({
        success: false,
        message: 'Admin access required'
      });
    }

    req.adminUser = user;
    next();
  } catch (error) {
    logger.error('Admin middleware error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// Get all tariff grids with filtering
router.get('/', requireAdmin, async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      supplierId,
      energyType,
      status,
      month,
      year
    } = req.query;

    const filter = {};
    
    if (supplierId && supplierId !== 'all') {
      filter.supplierId = supplierId;
    }
    
    if (energyType && energyType !== 'all') {
      filter.energyType = energyType;
    }
    
    if (status && status !== 'all') {
      filter.status = status;
    }
    
    if (month && year) {
      const startDate = new Date(year, month - 1, 1);
      const endDate = new Date(year, month, 0);
      filter.validFrom = { $lte: endDate };
      filter.validUntil = { $gte: startDate };
    }

    const skip = (parseInt(page) - 1) * parseInt(limit);

    const tariffGrids = await TariffGrid.find(filter)
      .populate('supplierId', 'firstName lastName email')
      .populate('uploadedBy', 'firstName lastName email')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    const totalGrids = await TariffGrid.countDocuments(filter);
    const totalPages = Math.ceil(totalGrids / parseInt(limit));

    res.json({
      success: true,
      data: {
        tariffGrids,
        pagination: {
          currentPage: parseInt(page),
          totalPages,
          totalGrids,
          hasNextPage: parseInt(page) < totalPages,
          hasPrevPage: parseInt(page) > 1
        }
      }
    });

  } catch (error) {
    logger.error('Error fetching tariff grids:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch tariff grids'
    });
  }
});

// Upload CSV tariff grid
router.post('/upload-csv', requireAdmin, upload.single('tariffFile'), async (req, res) => {
  try {
    const { supplierId, energyType, validFrom, validUntil, description } = req.body;
    
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'No file uploaded'
      });
    }

    // Validate supplier
    const supplier = await User.findById(supplierId);
    if (!supplier || supplier.userType !== 'Supplier') {
      return res.status(400).json({
        success: false,
        message: 'Invalid supplier'
      });
    }

    // Parse CSV file
    const tariffData = [];
    const filePath = req.file.path;

    await new Promise((resolve, reject) => {
      fs.createReadStream(filePath)
        .pipe(csv())
        .on('data', (row) => {
          // Validate and transform row data
          const tariffRow = {
            region: row.region || row.Region,
            rateType: row.rateType || row['Rate Type'],
            baseRate: parseFloat(row.baseRate || row['Base Rate']),
            standingCharge: parseFloat(row.standingCharge || row['Standing Charge']),
            peakRate: parseFloat(row.peakRate || row['Peak Rate']),
            offPeakRate: parseFloat(row.offPeakRate || row['Off Peak Rate']),
            unitRate: parseFloat(row.unitRate || row['Unit Rate']),
            tier1Rate: parseFloat(row.tier1Rate || row['Tier 1 Rate']),
            tier2Rate: parseFloat(row.tier2Rate || row['Tier 2 Rate']),
            tier3Rate: parseFloat(row.tier3Rate || row['Tier 3 Rate']),
            tier1Threshold: parseFloat(row.tier1Threshold || row['Tier 1 Threshold']),
            tier2Threshold: parseFloat(row.tier2Threshold || row['Tier 2 Threshold']),
            greenEnergyPercentage: parseFloat(row.greenEnergyPercentage || row['Green Energy %']),
            contractDuration: parseInt(row.contractDuration || row['Contract Duration']),
            additionalFees: parseFloat(row.additionalFees || row['Additional Fees']),
            discounts: parseFloat(row.discounts || row.Discounts),
            conditions: row.conditions || row.Conditions || '',
            notes: row.notes || row.Notes || ''
          };

          // Remove NaN values
          Object.keys(tariffRow).forEach(key => {
            if (typeof tariffRow[key] === 'number' && isNaN(tariffRow[key])) {
              delete tariffRow[key];
            }
          });

          tariffData.push(tariffRow);
        })
        .on('end', resolve)
        .on('error', reject);
    });

    // Create tariff grid document
    const tariffGrid = new TariffGrid({
      supplierId,
      energyType,
      validFrom: new Date(validFrom),
      validUntil: new Date(validUntil),
      description,
      tariffData,
      uploadedBy: req.adminUser._id,
      fileName: req.file.originalname,
      fileSize: req.file.size,
      status: 'Active'
    });

    await tariffGrid.save();

    // Clean up uploaded file
    fs.unlinkSync(filePath);

    // Log activity
    await UserActivity.logActivity({
      userId: req.adminUser._id,
      activityType: 'TariffUpload',
      description: `Uploaded tariff grid for ${supplier.firstName} ${supplier.lastName}`,
      details: {
        supplierId,
        energyType,
        fileName: req.file.originalname,
        recordsCount: tariffData.length
      },
      severity: 'Normal',
      isSystemGenerated: true
    });

    logger.info(`Admin ${req.adminUser.email} uploaded tariff grid for supplier ${supplier.email}`);

    res.json({
      success: true,
      message: 'Tariff grid uploaded successfully',
      data: {
        tariffGridId: tariffGrid._id,
        recordsProcessed: tariffData.length
      }
    });

  } catch (error) {
    // Clean up file if it exists
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }
    
    logger.error('Error uploading tariff grid:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to upload tariff grid'
    });
  }
});

// Get specific tariff grid details
router.get('/:gridId', requireAdmin, async (req, res) => {
  try {
    const { gridId } = req.params;

    const tariffGrid = await TariffGrid.findById(gridId)
      .populate('supplierId', 'firstName lastName email')
      .populate('uploadedBy', 'firstName lastName email');

    if (!tariffGrid) {
      return res.status(404).json({
        success: false,
        message: 'Tariff grid not found'
      });
    }

    res.json({
      success: true,
      data: tariffGrid
    });

  } catch (error) {
    logger.error('Error fetching tariff grid details:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch tariff grid details'
    });
  }
});

// Update tariff grid status
router.patch('/:gridId/status', requireAdmin, async (req, res) => {
  try {
    const { gridId } = req.params;
    const { status, notes } = req.body;

    if (!['Active', 'Inactive', 'Expired'].includes(status)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid status value'
      });
    }

    const tariffGrid = await TariffGrid.findByIdAndUpdate(
      gridId,
      { 
        status,
        statusNotes: notes,
        statusUpdatedBy: req.adminUser._id,
        statusUpdatedAt: new Date()
      },
      { new: true }
    );

    if (!tariffGrid) {
      return res.status(404).json({
        success: false,
        message: 'Tariff grid not found'
      });
    }

    logger.info(`Admin ${req.adminUser.email} updated tariff grid ${gridId} status to ${status}`);

    res.json({
      success: true,
      message: 'Tariff grid status updated successfully',
      data: tariffGrid
    });

  } catch (error) {
    logger.error('Error updating tariff grid status:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update tariff grid status'
    });
  }
});

// Delete tariff grid
router.delete('/:gridId', requireAdmin, async (req, res) => {
  try {
    const { gridId } = req.params;

    const tariffGrid = await TariffGrid.findByIdAndDelete(gridId);

    if (!tariffGrid) {
      return res.status(404).json({
        success: false,
        message: 'Tariff grid not found'
      });
    }

    logger.info(`Admin ${req.adminUser.email} deleted tariff grid ${gridId}`);

    res.json({
      success: true,
      message: 'Tariff grid deleted successfully'
    });

  } catch (error) {
    logger.error('Error deleting tariff grid:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete tariff grid'
    });
  }
});

module.exports = router;
