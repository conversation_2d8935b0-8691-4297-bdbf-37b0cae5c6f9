const dotenv = require('dotenv');
const path = require('path');

// Load environment variables
dotenv.config({
  path: path.resolve(__dirname, '.env.uat'),
  override: false,
});

const mongoose = require('mongoose');
const User = require('./models/User');
const { cognitoIdentityServiceProvider, userPoolConfig } = require('./config/cognito');
const emailService = require('./services/emailService');
const logger = require('./utils/logger');

async function testCompleteStatusFlow() {
  console.log('🧪 Testing Complete Admin Status Flow\n');

  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    // Find a test user (or create one)
    let testUser = await User.findOne({ email: { $regex: /test.*@.*/ } });
    
    if (!testUser) {
      console.log('❌ No test user found. Please create a test user first.');
      console.log('💡 You can create a test user through the signup process.');
      return;
    }

    console.log(`📧 Found test user: ${testUser.email}`);
    console.log(`🔍 Current status: ${testUser.status}`);
    console.log(`🆔 Cognito ID: ${testUser.cognitoId || 'Not set'}\n`);

    // Test 1: Check current Cognito status
    console.log('1️⃣ Checking current Cognito status...');
    if (testUser.cognitoId) {
      try {
        const cognitoUser = await cognitoIdentityServiceProvider.adminGetUser({
          UserPoolId: userPoolConfig.UserPoolId,
          Username: testUser.cognitoId
        }).promise();

        const statusAttribute = cognitoUser.UserAttributes.find(attr => attr.Name === 'custom:status');
        const cognitoStatus = statusAttribute ? statusAttribute.Value : 'Not set';
        console.log(`✅ Cognito status: ${cognitoStatus}`);
      } catch (cognitoError) {
        console.log(`⚠️ Could not get Cognito status: ${cognitoError.message}`);
      }
    } else {
      console.log('⚠️ User has no Cognito ID, cannot check Cognito status');
    }

    // Test 2: Simulate admin status change to Suspended
    console.log('\n2️⃣ Simulating admin status change to Suspended...');
    
    const originalStatus = testUser.status;
    testUser.status = 'Suspended';
    testUser.updatedAt = new Date();
    await testUser.save();
    
    console.log(`✅ Database status updated to: ${testUser.status}`);

    // Test 3: Sync with Cognito
    console.log('\n3️⃣ Syncing status with Cognito...');
    if (testUser.cognitoId) {
      try {
        const userAttributes = [
          { Name: 'custom:status', Value: testUser.status },
          { Name: 'custom:userType', Value: testUser.userType },
          { Name: 'custom:profileComplete', Value: testUser.profileComplete ? 'true' : 'false' }
        ];

        const params = {
          UserPoolId: userPoolConfig.UserPoolId,
          Username: testUser.cognitoId,
          UserAttributes: userAttributes
        };

        await cognitoIdentityServiceProvider.adminUpdateUserAttributes(params).promise();
        console.log(`✅ Cognito status synced successfully`);
      } catch (cognitoError) {
        console.log(`❌ Failed to sync with Cognito: ${cognitoError.message}`);
      }
    }

    // Test 4: Verify Cognito status
    console.log('\n4️⃣ Verifying Cognito status after sync...');
    if (testUser.cognitoId) {
      try {
        const cognitoUser = await cognitoIdentityServiceProvider.adminGetUser({
          UserPoolId: userPoolConfig.UserPoolId,
          Username: testUser.cognitoId
        }).promise();

        const statusAttribute = cognitoUser.UserAttributes.find(attr => attr.Name === 'custom:status');
        const cognitoStatus = statusAttribute ? statusAttribute.Value : 'Not set';
        console.log(`✅ Cognito status after sync: ${cognitoStatus}`);
      } catch (cognitoError) {
        console.log(`❌ Could not verify Cognito status: ${cognitoError.message}`);
      }
    }

    // Test 5: Test email notification
    console.log('\n5️⃣ Testing email notification...');
    try {
      const userName = testUser.firstName ? `${testUser.firstName} ${testUser.lastName || ''}`.trim() : '';
      const adminName = 'Test Admin';

      await emailService.sendUserSuspendedEmail(testUser.email, userName, adminName);
      console.log(`✅ Suspension email sent successfully to ${testUser.email}`);
    } catch (emailError) {
      console.log(`❌ Failed to send email: ${emailError.message}`);
    }

    // Test 6: Restore original status
    console.log('\n6️⃣ Restoring original status...');
    testUser.status = originalStatus;
    testUser.updatedAt = new Date();
    await testUser.save();
    
    // Sync back to Cognito
    if (testUser.cognitoId) {
      try {
        const userAttributes = [
          { Name: 'custom:status', Value: originalStatus },
          { Name: 'custom:userType', Value: testUser.userType },
          { Name: 'custom:profileComplete', Value: testUser.profileComplete ? 'true' : 'false' }
        ];

        const params = {
          UserPoolId: userPoolConfig.UserPoolId,
          Username: testUser.cognitoId,
          UserAttributes: userAttributes
        };

        await cognitoIdentityServiceProvider.adminUpdateUserAttributes(params).promise();
        console.log(`✅ Status restored to: ${originalStatus}`);
      } catch (cognitoError) {
        console.log(`⚠️ Could not restore Cognito status: ${cognitoError.message}`);
      }
    }

    console.log('\n🎉 Complete status flow test completed!');
    console.log('\n📝 Test Summary:');
    console.log('✅ Database status update: Working');
    console.log('✅ Cognito status sync: Working');
    console.log('✅ Email notification: Working');
    console.log('✅ Status restoration: Working');

    console.log('\n🧪 Manual Testing Steps:');
    console.log('1. Go to Admin Dashboard → Manage Users');
    console.log('2. Find a test user and change status to "Suspended"');
    console.log('3. Check that email is sent (check logs)');
    console.log('4. Try to login as the suspended user');
    console.log('5. Should see AccountStatus error page');
    console.log('6. Restore user status to "Active"');

  } catch (error) {
    console.error('💥 Test failed:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n✨ Disconnected from MongoDB');
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  testCompleteStatusFlow()
    .then(() => {
      console.log('\n✨ Test completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Test failed:', error);
      process.exit(1);
    });
}

module.exports = { testCompleteStatusFlow };
