/* Data Recovery Prompt Styles */

.data-recovery-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10000;
  padding: 1rem;
}

.data-recovery-prompt {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  max-width: 500px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.recovery-header {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1.5rem 1.5rem 1rem 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.recovery-icon {
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  background: #f0f9ff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.recovery-title h3 {
  margin: 0 0 0.25rem 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
}

.recovery-title p {
  margin: 0;
  font-size: 0.875rem;
  color: #6b7280;
  line-height: 1.4;
}

.recovery-content {
  padding: 1rem 1.5rem;
}

.recovery-stats {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
  padding: 0.75rem;
  background: #f9fafb;
  border-radius: 8px;
  font-size: 0.875rem;
}

.field-count {
  color: #059669;
  font-weight: 500;
}

.save-time {
  color: #6b7280;
}

.recovery-preview {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
}

.preview-toggle {
  width: 100%;
  padding: 0.75rem 1rem;
  background: #f9fafb;
  border: none;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  cursor: pointer;
  transition: background-color 0.2s;
}

.preview-toggle:hover {
  background: #f3f4f6;
}

.preview-arrow {
  transition: transform 0.2s;
}

.preview-arrow.expanded {
  transform: rotate(180deg);
}

.preview-data {
  padding: 1rem;
  background: white;
  border-top: 1px solid #e5e7eb;
}

.preview-item {
  display: flex;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
}

.preview-item:last-child {
  margin-bottom: 0;
}

.preview-key {
  font-weight: 500;
  color: #374151;
  min-width: 120px;
  flex-shrink: 0;
}

.preview-value {
  color: #6b7280;
  word-break: break-word;
}

.preview-more {
  margin-top: 0.5rem;
  font-size: 0.8rem;
  color: #9ca3af;
  font-style: italic;
}

.recovery-actions {
  display: flex;
  gap: 0.75rem;
  padding: 1rem 1.5rem;
  border-top: 1px solid #e5e7eb;
}

.recovery-actions button {
  flex: 1;
  padding: 0.75rem 1rem;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  border: none;
}

.btn-recover {
  background: #059669;
  color: white;
}

.btn-recover:hover {
  background: #047857;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(5, 150, 105, 0.3);
}

.btn-discard {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.btn-discard:hover {
  background: #e5e7eb;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.recovery-note {
  padding: 0 1.5rem 1.5rem 1.5rem;
  text-align: center;
}

.recovery-note small {
  color: #6b7280;
  font-size: 0.8rem;
  line-height: 1.4;
}

/* Mobile responsiveness */
@media (max-width: 640px) {
  .data-recovery-overlay {
    padding: 0.5rem;
  }
  
  .data-recovery-prompt {
    max-height: 95vh;
  }
  
  .recovery-header {
    padding: 1rem 1rem 0.75rem 1rem;
  }
  
  .recovery-content {
    padding: 0.75rem 1rem;
  }
  
  .recovery-actions {
    padding: 0.75rem 1rem 1rem 1rem;
    flex-direction: column;
  }
  
  .recovery-actions button {
    width: 100%;
  }
  
  .recovery-stats {
    flex-direction: column;
    gap: 0.25rem;
  }
  
  .preview-item {
    flex-direction: column;
    gap: 0.25rem;
  }
  
  .preview-key {
    min-width: auto;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .data-recovery-prompt {
    background: #1f2937;
    color: #f9fafb;
  }
  
  .recovery-header {
    border-bottom-color: #374151;
  }
  
  .recovery-title h3 {
    color: #f9fafb;
  }
  
  .recovery-title p {
    color: #d1d5db;
  }
  
  .recovery-stats {
    background: #374151;
  }
  
  .field-count {
    color: #10b981;
  }
  
  .save-time {
    color: #d1d5db;
  }
  
  .recovery-preview {
    border-color: #374151;
  }
  
  .preview-toggle {
    background: #374151;
    color: #f9fafb;
  }
  
  .preview-toggle:hover {
    background: #4b5563;
  }
  
  .preview-data {
    background: #1f2937;
    border-top-color: #374151;
  }
  
  .preview-key {
    color: #f9fafb;
  }
  
  .preview-value {
    color: #d1d5db;
  }
  
  .preview-more {
    color: #9ca3af;
  }
  
  .recovery-actions {
    border-top-color: #374151;
  }
  
  .btn-discard {
    background: #374151;
    color: #f9fafb;
    border-color: #4b5563;
  }
  
  .btn-discard:hover {
    background: #4b5563;
  }
  
  .recovery-note small {
    color: #d1d5db;
  }
}
