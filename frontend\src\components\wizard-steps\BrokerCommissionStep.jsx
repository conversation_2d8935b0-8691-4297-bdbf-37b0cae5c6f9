import React from 'react';

const BrokerCommissionStep = ({ formData, onChange, onNext, onPrev }) => {
  const handleChange = (e) => {
    const { name, value } = e.target;
    onChange(name, value);
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    onNext();
  };

  return (
    <div>
      <div className="stepper-header">
        <h3 className="page-title">Commission Structure</h3>
        <p className="page-subtitle">
          Set up your commission structure to help clients understand your pricing model.
        </p>
      </div>
      <form onSubmit={handleSubmit}>
        <div className="form-row">
          <div className="form-group">
            <label htmlFor="commissionStructure.type">Commission Type</label>
            <div className="input-with-icon">
              <span className="input-icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                  <path d="M4 10.781c.148 1.667 1.513 2.85 3.591 3.003V15h1.043v-1.216c2.27-.179 3.678-1.438 3.678-3.3 0-1.59-.947-2.51-2.956-3.028l-.722-.187V3.467c1.122.11 1.879.714 2.07 1.616h1.47c-.166-1.6-1.54-2.748-3.54-2.875V1H7.591v1.233c-1.939.23-3.27 1.472-3.27 3.156 0 1.454.966 2.483 2.661 2.917l.61.162v4.031c-1.149-.17-1.94-.8-2.131-1.718H4zm3.391-3.836c-1.043-.263-1.6-.825-1.6-1.616 0-.944.704-1.641 1.8-1.828v3.495l-.2-.05zm1.591 1.872c1.287.323 1.852.859 1.852 1.769 0 1.097-.826 1.828-2.2 1.939V8.73l.348.086z"/>
                </svg>
              </span>
              <select
                id="commissionStructure.type"
                name="commissionStructure.type"
                className="form-input"
                value={formData.commissionStructure.type}
                onChange={handleChange}
              >
                <option value="percentage">Percentage</option>
                <option value="fixed">Fixed Amount</option>
                <option value="tiered">Tiered Structure</option>
              </select>
            </div>
            <div className="hint-text">
              {formData.commissionStructure.type === 'percentage' && 'Commission as a percentage of the contract value'}
              {formData.commissionStructure.type === 'fixed' && 'Fixed commission amount per contract'}
              {formData.commissionStructure.type === 'tiered' && 'Different rates based on contract size'}
            </div>
          </div>

          <div className="form-group">
            <label htmlFor="commissionStructure.rate">
              {formData.commissionStructure.type === 'percentage' ? 'Rate (%)' : 'Amount (€)'}
            </label>
            <div className="input-with-icon">
              <span className="input-icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                  <path d="M0 3a2 2 0 0 1 2-2h13.5a.5.5 0 0 1 0 1H15v2a1 1 0 0 1 1 1v8.5a1.5 1.5 0 0 1-1.5 1.5h-12A2.5 2.5 0 0 1 0 12.5V3zm1 1.732V12.5A1.5 1.5 0 0 0 2.5 14h12a.5.5 0 0 0 .5-.5V5H2a1 1 0 0 1-1-.268zM1 3a1 1 0 0 0 1 1h12V2H2a1 1 0 0 0-1 1z"/>
                </svg>
              </span>
              <input
                type="number"
                id="commissionStructure.rate"
                name="commissionStructure.rate"
                className="form-input"
                value={formData.commissionStructure.rate}
                onChange={handleChange}
                placeholder={formData.commissionStructure.type === 'percentage' ? '5' : '100'}
                step={formData.commissionStructure.type === 'percentage' ? '0.1' : '1'}
                min="0"
              />
            </div>
            <div className="hint-text">
              {formData.commissionStructure.type === 'percentage'
                ? 'Enter percentage (e.g., 5 for 5%)'
                : 'Enter amount in euros'
              }
            </div>
          </div>

          {formData.commissionStructure.type === 'tiered' && (
            <div className="form-group">
              <div style={{
                padding: '1rem',
                backgroundColor: '#f8f9fa',
                borderRadius: '8px',
                border: '1px solid #e9ecef',
                marginTop: '1rem'
              }}>
                <h5 style={{ margin: '0 0 0.5rem 0', color: '#495057' }}>Tiered Structure Example</h5>
                <ul style={{ margin: 0, paddingLeft: '1.5rem', color: '#6c757d', fontSize: '0.9rem' }}>
                  <li>0 - €10,000: 3%</li>
                  <li>€10,001 - €50,000: 4%</li>
                  <li>€50,001+: 5%</li>
                </ul>
                <p style={{ margin: '0.5rem 0 0 0', fontSize: '0.85rem', color: '#6c757d' }}>
                  You can configure detailed tiers in your dashboard after setup.
                </p>
              </div>
            </div>
          )}

          <div className="form-group" style={{ marginTop: '2rem' }}>
            <div style={{
              padding: '1rem',
              backgroundColor: '#e8f4fd',
              borderRadius: '8px',
              border: '1px solid #bee5eb'
            }}>
              <h5 style={{ margin: '0 0 0.5rem 0', color: '#0c5460', fontSize: '0.95rem' }}>
                💡 Commission Structure Tips
              </h5>
              <ul style={{ margin: 0, paddingLeft: '1.5rem', color: '#0c5460', fontSize: '0.85rem' }}>
                <li>Be transparent about your commission structure with clients</li>
                <li>Consider offering different rates for different contract types</li>
                <li>You can always adjust these settings later in your dashboard</li>
              </ul>
            </div>
          </div>
        </div>

        <div className="stepper-buttons">
          <button type="button" className="stepper-button stepper-button-prev" onClick={onPrev}>
            Back
          </button>
          <button type="submit" className="stepper-button stepper-button-next">
            Next
          </button>
        </div>
      </form>
    </div>
  );
};

export default BrokerCommissionStep;
