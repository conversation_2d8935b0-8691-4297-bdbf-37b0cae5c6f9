.offers-container {
  padding: 10px 20px 20px;
  width: 100%;
  margin: 0;
}

.offers-header {
  margin-bottom: 20px;
}

.offers-header h1 {
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 10px;
  color: #000;
}

.offers-header p {
  font-size: 16px;
  color: #666;
}

.offers-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

.offers-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
}

.offer-card {
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  transition: transform 0.2s, box-shadow 0.2s;
}

.offer-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
}

.offer-header {
  padding: 20px;
  border-bottom: 1px solid #eee;
}

.offer-provider {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.provider-name {
  font-weight: 600;
  color: #555;
}

.offer-type {
  padding: 4px 10px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.offer-type.electricity {
  background-color: #e6f7ff;
  color: #0070f3;
}

.offer-type.gas {
  background-color: #fff7e6;
  color: #fa8c16;
}

.offer-type.both {
  background-color: #f6ffed;
  color: #52c41a;
}

.offer-name {
  font-size: 20px;
  font-weight: 700;
  margin: 0;
  color: #000;
}

.offer-content {
  padding: 20px;
  flex: 1;
}

.offer-description {
  margin-top: 0;
  margin-bottom: 20px;
  color: #555;
  line-height: 1.5;
}

.offer-details {
  margin-bottom: 20px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
}

.detail-label {
  color: #777;
}

.detail-value {
  font-weight: 600;
  color: #333;
}

.offer-highlights {
  margin-bottom: 20px;
}

.offer-highlights h3 {
  font-size: 16px;
  margin-top: 0;
  margin-bottom: 10px;
  color: #333;
}

.offer-highlights ul {
  padding-left: 20px;
  margin: 0;
}

.offer-highlights li {
  margin-bottom: 5px;
  color: #555;
}

.offer-savings {
  background: linear-gradient(to right, #000, #333);
  color: #fff;
  padding: 15px;
  border-radius: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.savings-amount {
  display: flex;
  flex-direction: column;
}

.savings-label {
  font-size: 12px;
  opacity: 0.8;
}

.savings-value {
  font-size: 24px;
  font-weight: 700;
}

.savings-percentage {
  display: flex;
  flex-direction: column;
  text-align: right;
}

.percentage-value {
  font-size: 20px;
  font-weight: 700;
}

.percentage-label {
  font-size: 12px;
  opacity: 0.8;
}

.offer-footer {
  padding: 15px 20px;
  border-top: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.offer-validity {
  font-size: 13px;
  color: #777;
}

.offer-actions {
  display: flex;
  gap: 10px;
}

.btn-details,
.btn-accept {
  padding: 8px 15px;
  border-radius: 5px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-details {
  background-color: transparent;
  border: 1px solid #000;
  color: #000;
}

.btn-details:hover {
  background-color: #f5f5f5;
}

.btn-accept {
  background: linear-gradient(to right, #000, #333);
  border: none;
  color: #fff;
}

.btn-accept:hover {
  opacity: 0.9;
}

.no-offers {
  grid-column: 1 / -1;
  text-align: center;
  padding: 50px 20px;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.no-offers i {
  font-size: 48px;
  color: #ccc;
  margin-bottom: 20px;
}

.no-offers h3 {
  font-size: 20px;
  margin-bottom: 10px;
  color: #333;
}

.no-offers p {
  color: #666;
  max-width: 400px;
  margin: 0 auto;
}

/* Compact Card Styles */
.offer-card-compact {
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  transition: transform 0.2s, box-shadow 0.2s;
  cursor: pointer;
  height: auto;
  min-height: 420px;
}

.offer-card-compact:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
}

.offer-header-compact {
  padding: 18px 20px 12px;
  border-bottom: 1px solid #f0f0f0;
}

.offer-provider-compact {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.provider-name-compact {
  font-weight: 600;
  color: #555;
  font-size: 13px;
}

.offer-type-compact {
  padding: 3px 8px;
  border-radius: 15px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
}

.offer-type-compact.electricity {
  background-color: #e6f7ff;
  color: #0070f3;
}

.offer-type-compact.gas {
  background-color: #fff7e6;
  color: #fa8c16;
}

.offer-type-compact.both {
  background-color: #f6ffed;
  color: #52c41a;
}

.offer-name-compact {
  font-size: 16px;
  font-weight: 700;
  margin: 0;
  color: #000;
  line-height: 1.3;
}

.offer-content-compact {
  padding: 15px 20px;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.offer-pricing-compact {
  display: flex;
  justify-content: space-between;
  gap: 10px;
}

.price-item-compact {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  flex: 1;
}

.price-label-compact {
  font-size: 11px;
  color: #777;
  margin-bottom: 4px;
  text-transform: uppercase;
  font-weight: 500;
}

.price-value-compact {
  font-size: 14px;
  font-weight: 700;
  color: #333;
}

.offer-savings-compact {
  background: linear-gradient(to right, #000, #333);
  color: #fff;
  padding: 12px;
  border-radius: 6px;
}

.savings-compact {
  text-align: center;
}

.savings-label-compact {
  font-size: 11px;
  opacity: 0.8;
  display: block;
  margin-bottom: 6px;
  text-transform: uppercase;
}

.savings-values-compact {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
}

.savings-amount-compact {
  font-size: 18px;
  font-weight: 700;
}

.savings-percentage-compact {
  font-size: 14px;
  opacity: 0.9;
}

.offer-additional-details {
  background-color: #f8f9fa;
  padding: 10px;
  border-radius: 6px;
  border-left: 3px solid #000;
}

.detail-row-compact {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
  font-size: 13px;
}

.detail-row-compact:last-child {
  margin-bottom: 0;
}

.detail-label-compact {
  color: #666;
  font-weight: 500;
}

.detail-value-compact {
  font-weight: 600;
  color: #333;
}

.offer-highlights-compact {
  background-color: #f0f8ff;
  padding: 10px;
  border-radius: 6px;
}

.highlights-list-compact {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.highlight-item-compact {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #555;
}

.highlight-item-compact i {
  color: #52c41a;
  font-size: 14px;
  flex-shrink: 0;
}

.offer-validity-compact {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #0070f3;
  background-color: #e6f7ff;
  padding: 6px 10px;
  border-radius: 4px;
  justify-content: center;
}

.offer-validity-compact i {
  font-size: 14px;
}

.offer-footer-compact {
  padding: 12px 20px 18px;
  border-top: 1px solid #f0f0f0;
  display: flex;
  gap: 8px;
}

.btn-details-compact,
.btn-accept-compact {
  flex: 1;
  padding: 8px 12px;
  border-radius: 5px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  text-align: center;
}

.btn-details-compact {
  background-color: transparent;
  border: 1px solid #ddd;
  color: #666;
}

.btn-details-compact:hover {
  background-color: #f5f5f5;
  border-color: #ccc;
}

.btn-accept-compact {
  background: linear-gradient(to right, #000, #333);
  border: none;
  color: #fff;
}

.btn-accept-compact:hover {
  opacity: 0.9;
  transform: translateY(-1px);
}

/* Responsive styles */
@media (max-width: 1200px) {
  .offers-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .offers-container {
    padding: 5px 15px 15px;
  }

  .offers-grid {
    grid-template-columns: 1fr;
  }

  .offer-card-compact {
    height: auto;
    min-height: 380px;
  }

  .offer-savings {
    flex-direction: column;
    align-items: flex-start;
  }

  .savings-percentage {
    text-align: left;
    margin-top: 10px;
  }

  .offer-footer {
    flex-direction: column;
    gap: 15px;
  }

  .offer-actions {
    width: 100%;
  }

  .btn-details,
  .btn-accept {
    flex: 1;
    text-align: center;
  }
}
