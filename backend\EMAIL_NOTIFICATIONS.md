# Email Notifications for Admin Actions

This document describes the email notification system implemented for admin actions in the My Energy Bill application.

## Overview

The system automatically sends professional email notifications to users when administrators perform certain actions on their accounts. This ensures transparency and keeps users informed about changes to their account status.

## Supported Admin Actions

### 1. User Account Suspension
- **Trigger**: When an admin changes a user's status to "Suspended"
- **Email Template**: Professional notification explaining the suspension
- **Content**: Warning about account access, action details, and support contact information

### 2. User Account Deactivation
- **Trigger**: When an admin changes a user's status to "Inactive"
- **Email Template**: Informative notification about status change
- **Content**: Information about limited access, action details, and reactivation instructions

### 3. Admin-Initiated Password Reset
- **Trigger**: When an admin initiates a password reset for a user
- **Email Template**: Security notification about the password reset
- **Content**: Information about the reset process, security notices, and support contact

## Technical Implementation

### Email Service Configuration

The email service uses AWS SES (Simple Email Service) with the following configuration:

```javascript
// Environment Variables
FROM_EMAIL=<EMAIL>
COMPANY_NAME=My Energy Bill
AWS_REGION=eu-west-3
FRONTEND_URL=https://uat.mafactureenergie.fr
```

### Email Templates

All email templates include:
- Professional HTML formatting with responsive design
- Company branding and colors
- Clear action information (who, when, what)
- Security notices where applicable
- Contact information for support
- Plain text fallback for accessibility

### Integration Points

#### Admin Routes (`backend/routes/admin.js`)

1. **Status Update Route** (`PATCH /api/admin/users/:userId/status`)
   - Automatically sends notifications for "Suspended" and "Inactive" status changes
   - Includes admin and user information in the email

2. **Password Reset Route** (`POST /api/admin/users/:userId/reset-password`)
   - Sends notification about admin-initiated password reset
   - Includes security information and next steps

#### Email Service Methods (`backend/services/emailService.js`)

- `sendUserSuspendedEmail(userEmail, userName, adminName)`
- `sendUserInactiveEmail(userEmail, userName, adminName)`
- `sendAdminPasswordResetNotificationEmail(userEmail, userName, adminName)`

## Email Template Features

### Professional Design
- Gradient header with company branding
- Clean, readable typography
- Responsive design for mobile devices
- Consistent color scheme and spacing

### Security Elements
- Clear identification of admin actions
- Timestamp information
- Security notices and warnings
- Contact information for support

### User Experience
- Clear subject lines
- Friendly but professional tone
- Action-specific content
- Easy-to-understand explanations

## Testing

### Manual Testing
Use the provided test script to verify email functionality:

```bash
node test-email-notifications.js
```

### Test Checklist
- [ ] Email delivery to inbox (not spam)
- [ ] Professional appearance on desktop and mobile
- [ ] Correct user and admin information
- [ ] Working support contact links
- [ ] Plain text version readability

## Configuration Requirements

### AWS SES Setup
1. Verify the FROM_EMAIL address in AWS SES
2. Ensure proper IAM permissions for sending emails
3. Configure appropriate sending limits
4. Set up bounce and complaint handling

### Environment Variables
Ensure all required environment variables are set:
- `FROM_EMAIL`: Verified sender email address
- `COMPANY_NAME`: Company name for branding
- `AWS_REGION`: AWS region for SES
- `FRONTEND_URL`: Frontend URL for links
- `AWS_ACCESS_KEY_ID`: AWS credentials
- `AWS_SECRET_ACCESS_KEY`: AWS credentials

## Error Handling

The system includes robust error handling:
- Email failures don't prevent admin actions from completing
- Detailed logging for troubleshooting
- Graceful degradation when email service is unavailable

## Monitoring and Logging

All email activities are logged with:
- Recipient email address
- Email type and template used
- Success/failure status
- AWS SES message ID for tracking
- Admin user who triggered the action

## Future Enhancements

Potential improvements for the email notification system:
- Email templates for account reactivation
- Bulk action notifications
- Email preferences for users
- Email analytics and delivery tracking
- Multi-language support
- Custom email templates per user type

## Support and Troubleshooting

### Common Issues
1. **Emails not delivered**: Check AWS SES sending limits and verify sender email
2. **Emails in spam**: Ensure proper SPF/DKIM records are configured
3. **Template formatting issues**: Test on multiple email clients
4. **Missing environment variables**: Verify all required variables are set

### Logs Location
Email-related logs can be found in:
- Application logs: `backend/logs/application-*.log`
- Error logs: `backend/logs/error-*.log`

### Contact
For technical support or questions about the email notification system, contact the development team.
