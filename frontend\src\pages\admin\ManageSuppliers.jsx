import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import DashboardLayout from '../../components/DashboardLayout';
import Spinner from '../../components/Spinner';
import { showSuccessMessage, showErrorMessage } from '../../utils/toastNotifications';
import logger from '../../utils/logger';
import '../../styles/admin-management.css';
import api from '../../services/api';

const ManageSuppliers = () => {
  const [loading, setLoading] = useState(true);
  const [suppliers, setSuppliers] = useState([]);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalUsers: 0,
    hasNextPage: false,
    hasPrevPage: false
  });
  const [filters, setFilters] = useState({
    status: 'all',
    verificationStatus: 'all',
    search: ''
  });
  const [selectedSupplier, setSelectedSupplier] = useState(null);
  const [showSupplierDetails, setShowSupplierDetails] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    fetchSuppliers();
  }, [filters, pagination.currentPage]);

  const fetchSuppliers = async () => {
    try {
      setLoading(true);
      logger.info('Fetching suppliers with filters:', filters);

      const queryParams = new URLSearchParams({
        page: pagination.currentPage,
        limit: 10,
        userType: 'Supplier',
        ...filters
      });

      const response = await api.get(`/api/admin/suppliers?${queryParams}`);
      setSuppliers(response.data.data.suppliers);
      setPagination(response.data.data.pagination);

      logger.info('Suppliers fetched successfully');
    } catch (error) {
      logger.error('Error fetching suppliers:', error);
      showErrorMessage('SUPPLIERS_LOAD_FAILED', 'Failed to load suppliers');
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = (filterName, value) => {
    setFilters(prev => ({
      ...prev,
      [filterName]: value
    }));
    setPagination(prev => ({ ...prev, currentPage: 1 }));
  };

  const handleStatusUpdate = async (supplierId, newStatus) => {
    try {
      await api.patch(`/api/admin/users/${supplierId}/status`, { status: newStatus });
      showSuccessMessage('SUPPLIER_STATUS_UPDATED', 'Supplier status updated successfully');
      fetchSuppliers(); // Refresh the list
    } catch (error) {
      logger.error('Error updating supplier status:', error);
      showErrorMessage('STATUS_UPDATE_FAILED', 'Failed to update supplier status');
    }
  };

  const handleViewSupplierDetails = (supplier) => {
    try {
      setSelectedSupplier(supplier);
      setShowSupplierDetails(true);
    } catch (error) {
      logger.error('Error viewing supplier details:', error);
      showErrorMessage('SUPPLIER_DETAILS_FAILED', 'Failed to load supplier details');
    }
  };

  const handleEditSupplier = (supplier) => {
    try {
      // Navigate to edit supplier page or open edit modal
      navigate(`/admin/suppliers/${supplier._id}/edit`);
    } catch (error) {
      logger.error('Error navigating to edit supplier:', error);
      showErrorMessage('NAVIGATION_FAILED', 'Failed to open supplier edit page');
    }
  };

  const handleDeleteSupplier = (supplier) => {
    setSelectedSupplier(supplier);
    setShowDeleteConfirm(true);
  };

  const confirmDeleteSupplier = async () => {
    try {
      await api.delete(`/api/admin/users/${selectedSupplier._id}`);
      showSuccessMessage('SUPPLIER_DELETED', 'Supplier deleted successfully');
      setShowDeleteConfirm(false);
      setSelectedSupplier(null);
      fetchSuppliers(); // Refresh the list
    } catch (error) {
      logger.error('Error deleting supplier:', error);
      showErrorMessage('DELETE_FAILED', 'Failed to delete supplier');
    }
  };

  const handlePageChange = (newPage) => {
    setPagination(prev => ({ ...prev, currentPage: newPage }));
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getStatusBadgeClass = (status) => {
    switch (status?.toLowerCase()) {
      case 'active': return 'status-badge active';
      case 'inactive': return 'status-badge inactive';
      case 'pending': return 'status-badge pending';
      case 'suspended': return 'status-badge suspended';
      default: return 'status-badge';
    }
  };

  if (loading) {
    return <Spinner fullScreen={true} message="Loading suppliers..." size="large" />;
  }

  return (
    <DashboardLayout>
      <div className="admin-management-container">
        {/* Header */}
        <div className="management-header">
          <div className="header-content">
            <h1>Manage Suppliers</h1>
            <p>View and manage all energy suppliers in the system</p>
          </div>
          <div className="header-stats">
            <div className="stat-item">
              <span className="stat-value">{pagination.totalUsers}</span>
              <span className="stat-label">Total Suppliers</span>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="management-filters">
          <div className="filter-group">
            <label>Status:</label>
            <select 
              value={filters.status} 
              onChange={(e) => handleFilterChange('status', e.target.value)}
            >
              <option value="all">All Status</option>
              <option value="Active">Active</option>
              <option value="Inactive">Inactive</option>
              <option value="Pending">Pending</option>
              <option value="Suspended">Suspended</option>
            </select>
          </div>

          <div className="filter-group">
            <label>Verification:</label>
            <select 
              value={filters.verificationStatus} 
              onChange={(e) => handleFilterChange('verificationStatus', e.target.value)}
            >
              <option value="all">All Verification</option>
              <option value="Verified">Verified</option>
              <option value="Pending">Pending</option>
              <option value="Rejected">Rejected</option>
            </select>
          </div>

          <div className="filter-group search-group">
            <label>Search:</label>
            <input
              type="text"
              placeholder="Search by company name or email..."
              value={filters.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
            />
          </div>
        </div>

        {/* Suppliers Table */}
        <div className="management-table-container">
          <table className="management-table suppliers-table">
            <thead>
              <tr>
                <th>Supplier</th>
                <th>Status</th>
                <th>Verification</th>
                <th>Actions</th>
                <th>Manage</th>
              </tr>
            </thead>
            <tbody>
              {suppliers.map(supplier => (
                <tr key={supplier._id}>
                  <td>
                    <div className="user-info">
                      <div className="user-icon">
                        <i className="fas fa-industry"></i>
                      </div>
                      <div className="user-details">
                        <div className="user-name">
                          {supplier.firstName} {supplier.lastName}
                        </div>
                        <div className="user-email">{supplier.email}</div>
                        {supplier.profile?.companyName && (
                          <div className="user-company">{supplier.profile.companyName}</div>
                        )}
                      </div>
                    </div>
                  </td>
                  <td>
                    <span className={getStatusBadgeClass(supplier.status)}>
                      {supplier.status || 'Unknown'}
                    </span>
                  </td>
                  <td>
                    <span className={getStatusBadgeClass(supplier.verificationStatus)}>
                      {supplier.verificationStatus || 'Unknown'}
                    </span>
                  </td>
                  <td>
                    <div className="action-buttons">
                      <button
                        className="action-btn view"
                        onClick={() => handleViewSupplierDetails(supplier)}
                        title="View Details"
                      >
                        <i className="fas fa-eye"></i>
                      </button>
                      <button
                        className="action-btn edit"
                        onClick={() => handleEditSupplier(supplier)}
                        title="Edit Supplier"
                      >
                        <i className="fas fa-edit"></i>
                      </button>
                      <button
                        className="action-btn delete"
                        onClick={() => handleDeleteSupplier(supplier)}
                        title="Delete Supplier"
                      >
                        <i className="fas fa-trash-can"></i>
                      </button>
                    </div>
                  </td>
                  <td>
                    <select
                      value={supplier.status || 'Active'}
                      onChange={(e) => handleStatusUpdate(supplier._id, e.target.value)}
                      className="status-select"
                    >
                      <option value="Active">Active</option>
                      <option value="Inactive">Inactive</option>
                      <option value="Pending">Pending</option>
                      <option value="Suspended">Suspended</option>
                    </select>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>

          {suppliers.length === 0 && (
            <div className="empty-state">
              <i className="fas fa-industry"></i>
              <h3>No suppliers found</h3>
              <p>No suppliers match your current filters.</p>
            </div>
          )}
        </div>

        {/* Pagination */}
        <div className="management-pagination">
          <button 
            disabled={!pagination.hasPrevPage}
            onClick={() => handlePageChange(pagination.currentPage - 1)}
            className="pagination-btn"
          >
            <i className="fas fa-chevron-left"></i>
            Previous
          </button>
          
          <span className="pagination-info">
            Page {pagination.currentPage} of {pagination.totalPages}
          </span>
          
          <button 
            disabled={!pagination.hasNextPage}
            onClick={() => handlePageChange(pagination.currentPage + 1)}
            className="pagination-btn"
          >
            Next
            <i className="fas fa-chevron-right"></i>
          </button>
        </div>

        {/* Supplier Details Modal */}
        {showSupplierDetails && selectedSupplier && (
          <div className="modal-overlay" onClick={() => setShowSupplierDetails(false)}>
            <div className="modal-content" onClick={(e) => e.stopPropagation()}>
              <div className="modal-header">
                <h3>Supplier Details</h3>
                <button
                  className="modal-close"
                  onClick={() => setShowSupplierDetails(false)}
                >
                  <i className="fas fa-times"></i>
                </button>
              </div>
              <div className="modal-body">
                <div className="supplier-details">
                  <div className="detail-group">
                    <label>Name:</label>
                    <span>{selectedSupplier.firstName} {selectedSupplier.lastName}</span>
                  </div>
                  <div className="detail-group">
                    <label>Email:</label>
                    <span>{selectedSupplier.email}</span>
                  </div>
                  <div className="detail-group">
                    <label>Company:</label>
                    <span>{selectedSupplier.profile?.companyName || 'Not provided'}</span>
                  </div>
                  <div className="detail-group">
                    <label>License Number:</label>
                    <span>{selectedSupplier.profile?.licenseNumber || 'Not provided'}</span>
                  </div>
                  <div className="detail-group">
                    <label>Status:</label>
                    <span className={getStatusBadgeClass(selectedSupplier.status)}>
                      {selectedSupplier.status || 'Unknown'}
                    </span>
                  </div>
                  <div className="detail-group">
                    <label>Verification:</label>
                    <span className={getStatusBadgeClass(selectedSupplier.verificationStatus)}>
                      {selectedSupplier.verificationStatus || 'Unknown'}
                    </span>
                  </div>
                  <div className="detail-group">
                    <label>Joined:</label>
                    <span>{formatDate(selectedSupplier.createdAt)}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Delete Confirmation Modal */}
        {showDeleteConfirm && selectedSupplier && (
          <div className="modal-overlay" onClick={() => setShowDeleteConfirm(false)}>
            <div className="modal-content" onClick={(e) => e.stopPropagation()}>
              <div className="modal-header">
                <h3>Confirm Delete</h3>
                <button
                  className="modal-close"
                  onClick={() => setShowDeleteConfirm(false)}
                >
                  <i className="fas fa-times"></i>
                </button>
              </div>
              <div className="modal-body">
                <p>Are you sure you want to delete supplier <strong>{selectedSupplier.firstName} {selectedSupplier.lastName}</strong>?</p>
                <p>This action cannot be undone.</p>
                <div className="modal-actions">
                  <button
                    className="btn btn-secondary"
                    onClick={() => setShowDeleteConfirm(false)}
                  >
                    Cancel
                  </button>
                  <button
                    className="btn btn-danger"
                    onClick={confirmDeleteSupplier}
                  >
                    Delete Supplier
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </DashboardLayout>
  );
};

export default ManageSuppliers;
