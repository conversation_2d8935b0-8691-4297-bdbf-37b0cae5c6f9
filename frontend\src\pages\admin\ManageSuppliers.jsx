import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import DashboardLayout from '../../components/DashboardLayout';
import Spinner from '../../components/Spinner';
import { showSuccessMessage, showErrorMessage } from '../../utils/toastNotifications';
import logger from '../../utils/logger';
import '../../styles/admin-management.css';
import api from '../../services/api';

const ManageSuppliers = () => {
  const [loading, setLoading] = useState(true);
  const [suppliers, setSuppliers] = useState([]);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalUsers: 0,
    hasNextPage: false,
    hasPrevPage: false
  });
  const [filters, setFilters] = useState({
    status: 'all',
    verificationStatus: 'all',
    search: ''
  });
  const navigate = useNavigate();

  useEffect(() => {
    fetchSuppliers();
  }, [filters, pagination.currentPage]);

  const fetchSuppliers = async () => {
    try {
      setLoading(true);
      logger.info('Fetching suppliers with filters:', filters);

      const queryParams = new URLSearchParams({
        page: pagination.currentPage,
        limit: 10,
        userType: 'Supplier',
        ...filters
      });

      const response = await api.get(`/api/admin/suppliers?${queryParams}`);
      setSuppliers(response.data.data.suppliers);
      setPagination(response.data.data.pagination);

      logger.info('Suppliers fetched successfully');
    } catch (error) {
      logger.error('Error fetching suppliers:', error);
      showErrorMessage('SUPPLIERS_LOAD_FAILED', 'Failed to load suppliers');
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = (filterName, value) => {
    setFilters(prev => ({
      ...prev,
      [filterName]: value
    }));
    setPagination(prev => ({ ...prev, currentPage: 1 }));
  };

  const handleStatusUpdate = async (supplierId, newStatus) => {
    try {
      await api.patch(`/api/admin/users/${supplierId}/status`, { status: newStatus });
      showSuccessMessage('SUPPLIER_STATUS_UPDATED', 'Supplier status updated successfully');
      fetchSuppliers(); // Refresh the list
    } catch (error) {
      logger.error('Error updating supplier status:', error);
      showErrorMessage('STATUS_UPDATE_FAILED', 'Failed to update supplier status');
    }
  };

  const handlePageChange = (newPage) => {
    setPagination(prev => ({ ...prev, currentPage: newPage }));
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getStatusBadgeClass = (status) => {
    switch (status?.toLowerCase()) {
      case 'active': return 'status-badge active';
      case 'inactive': return 'status-badge inactive';
      case 'pending': return 'status-badge pending';
      case 'suspended': return 'status-badge suspended';
      default: return 'status-badge';
    }
  };

  if (loading) {
    return <Spinner fullScreen={true} message="Loading suppliers..." size="large" />;
  }

  return (
    <DashboardLayout>
      <div className="admin-management-container">
        {/* Header */}
        <div className="management-header">
          <div className="header-content">
            <h1>Manage Suppliers</h1>
            <p>View and manage all energy suppliers in the system</p>
          </div>
          <div className="header-stats">
            <div className="stat-item">
              <span className="stat-value">{pagination.totalUsers}</span>
              <span className="stat-label">Total Suppliers</span>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="management-filters">
          <div className="filter-group">
            <label>Status:</label>
            <select 
              value={filters.status} 
              onChange={(e) => handleFilterChange('status', e.target.value)}
            >
              <option value="all">All Status</option>
              <option value="Active">Active</option>
              <option value="Inactive">Inactive</option>
              <option value="Pending">Pending</option>
              <option value="Suspended">Suspended</option>
            </select>
          </div>

          <div className="filter-group">
            <label>Verification:</label>
            <select 
              value={filters.verificationStatus} 
              onChange={(e) => handleFilterChange('verificationStatus', e.target.value)}
            >
              <option value="all">All Verification</option>
              <option value="Verified">Verified</option>
              <option value="Pending">Pending</option>
              <option value="Rejected">Rejected</option>
            </select>
          </div>

          <div className="filter-group search-group">
            <label>Search:</label>
            <input
              type="text"
              placeholder="Search by company name or email..."
              value={filters.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
            />
          </div>
        </div>

        {/* Suppliers Table */}
        <div className="management-table-container">
          <table className="management-table">
            <thead>
              <tr>
                <th>Supplier</th>
                <th>Company</th>
                <th>License Number</th>
                <th>Status</th>
                <th>Verification</th>
                <th>Joined</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {suppliers.map(supplier => (
                <tr key={supplier._id}>
                  <td>
                    <div className="user-info">
                      <div className="user-icon">
                        <i className="fas fa-industry"></i>
                      </div>
                      <div className="user-details">
                        <div className="user-name">
                          {supplier.firstName} {supplier.lastName}
                        </div>
                        <div className="user-email">{supplier.email}</div>
                      </div>
                    </div>
                  </td>
                  <td>
                    <span className="company-name">
                      {supplier.profile?.companyName || 'Not provided'}
                    </span>
                  </td>
                  <td>
                    <span className="license-number">
                      {supplier.profile?.licenseNumber || 'Not provided'}
                    </span>
                  </td>
                  <td>
                    <span className={getStatusBadgeClass(supplier.status)}>
                      {supplier.status || 'Unknown'}
                    </span>
                  </td>
                  <td>
                    <span className={getStatusBadgeClass(supplier.verificationStatus)}>
                      {supplier.verificationStatus || 'Unknown'}
                    </span>
                  </td>
                  <td>{formatDate(supplier.createdAt)}</td>
                  <td>
                    <div className="action-buttons">
                      <select
                        value={supplier.status || 'Active'}
                        onChange={(e) => handleStatusUpdate(supplier._id, e.target.value)}
                        className="status-select"
                      >
                        <option value="Active">Active</option>
                        <option value="Inactive">Inactive</option>
                        <option value="Pending">Pending</option>
                        <option value="Suspended">Suspended</option>
                      </select>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>

          {suppliers.length === 0 && (
            <div className="empty-state">
              <i className="fas fa-industry"></i>
              <h3>No suppliers found</h3>
              <p>No suppliers match your current filters.</p>
            </div>
          )}
        </div>

        {/* Pagination */}
        <div className="management-pagination">
          <button 
            disabled={!pagination.hasPrevPage}
            onClick={() => handlePageChange(pagination.currentPage - 1)}
            className="pagination-btn"
          >
            <i className="fas fa-chevron-left"></i>
            Previous
          </button>
          
          <span className="pagination-info">
            Page {pagination.currentPage} of {pagination.totalPages}
          </span>
          
          <button 
            disabled={!pagination.hasNextPage}
            onClick={() => handlePageChange(pagination.currentPage + 1)}
            className="pagination-btn"
          >
            Next
            <i className="fas fa-chevron-right"></i>
          </button>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default ManageSuppliers;
