const express = require('express');
const router = express.Router();
const brokerController = require('../controllers/brokerController');
const { verifyToken } = require('../middleware/auth');

// Apply authentication middleware to all routes
router.use(verifyToken);

// Dashboard routes
router.get('/dashboard/stats/:cognitoId', brokerController.getDashboardStats);

// Clients routes
router.get('/clients/active', brokerController.getActiveClients);
router.get('/clients', brokerController.getAllClients);
router.post('/clients', brokerController.addClient);

// Deals routes
router.get('/deals/recent', brokerController.getRecentDeals);

// Profile routes
router.get('/profile', brokerController.getProfile);
router.put('/profile', brokerController.updateProfile);

// Analytics routes
router.get('/analytics', brokerController.getAnalytics);

module.exports = router;
