import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import DashboardLayout from '../../components/DashboardLayout';
import Spinner from '../../components/Spinner';
import { showSuccessMessage, showErrorMessage } from '../../utils/toastNotifications';
import logger from '../../utils/logger';
import '../../styles/admin-management.css';

const NotificationManagement = () => {
  const [loading, setLoading] = useState(true);
  const [notifications, setNotifications] = useState([]);
  const [notificationSettings, setNotificationSettings] = useState({
    contractExpiration: {
      enabled: true,
      daysBeforeExpiry: 30,
      frequency: 'weekly'
    },
    paymentReminders: {
      enabled: true,
      daysAfterDue: 7,
      frequency: 'daily'
    },
    systemAlerts: {
      enabled: true,
      severity: 'high'
    }
  });
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [selectedUsers, setSelectedUsers] = useState([]);
  const navigate = useNavigate();

  useEffect(() => {
    fetchNotifications();
    fetchNotificationSettings();
  }, []);

  const fetchNotifications = async () => {
    try {
      setLoading(true);
      logger.info('Fetching notifications');

      // Mock data for now - replace with actual API call
      const mockNotifications = [
        {
          _id: '1',
          title: 'Contract Expiration Reminder',
          message: 'Your energy contract will expire in 30 days',
          type: 'ContractExpiration',
          priority: 'High',
          recipients: 45,
          sentAt: new Date(),
          status: 'Sent',
          deliveryStats: {
            sent: 45,
            delivered: 42,
            failed: 3
          }
        },
        {
          _id: '2',
          title: 'New Offer Available',
          message: 'A new energy offer matching your preferences is available',
          type: 'OfferReceived',
          priority: 'Normal',
          recipients: 23,
          sentAt: new Date(Date.now() - 86400000),
          status: 'Sent',
          deliveryStats: {
            sent: 23,
            delivered: 23,
            failed: 0
          }
        },
        {
          _id: '3',
          title: 'System Maintenance Notice',
          message: 'Scheduled maintenance will occur this weekend',
          type: 'SystemAlert',
          priority: 'Normal',
          recipients: 156,
          sentAt: new Date(Date.now() - 172800000),
          status: 'Scheduled',
          deliveryStats: {
            sent: 0,
            delivered: 0,
            failed: 0
          }
        }
      ];

      setNotifications(mockNotifications);
      logger.info('Notifications fetched successfully');
    } catch (error) {
      logger.error('Error fetching notifications:', error);
      showErrorMessage('NOTIFICATIONS_LOAD_FAILED', 'Failed to load notifications');
    } finally {
      setLoading(false);
    }
  };

  const fetchNotificationSettings = async () => {
    try {
      // Mock API call - replace with actual implementation
      logger.info('Notification settings loaded');
    } catch (error) {
      logger.error('Error fetching notification settings:', error);
    }
  };

  const handleCreateNotification = () => {
    setShowCreateModal(true);
  };

  const handleSendNotification = async (notificationData) => {
    try {
      // Mock API call - replace with actual implementation
      showSuccessMessage('NOTIFICATION_SENT', 'Notification sent successfully');
      setShowCreateModal(false);
      fetchNotifications();
    } catch (error) {
      logger.error('Error sending notification:', error);
      showErrorMessage('NOTIFICATION_SEND_FAILED', 'Failed to send notification');
    }
  };

  const handleUpdateSettings = async () => {
    try {
      // Mock API call - replace with actual implementation
      showSuccessMessage('SETTINGS_UPDATED', 'Notification settings updated successfully');
    } catch (error) {
      logger.error('Error updating settings:', error);
      showErrorMessage('SETTINGS_UPDATE_FAILED', 'Failed to update settings');
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusBadgeClass = (status) => {
    switch (status?.toLowerCase()) {
      case 'sent': return 'status-badge active';
      case 'scheduled': return 'status-badge pending';
      case 'failed': return 'status-badge inactive';
      case 'draft': return 'status-badge suspended';
      default: return 'status-badge';
    }
  };

  const getPriorityBadgeClass = (priority) => {
    switch (priority?.toLowerCase()) {
      case 'low': return 'priority-badge low';
      case 'normal': return 'priority-badge normal';
      case 'high': return 'priority-badge high';
      case 'urgent': return 'priority-badge urgent';
      default: return 'priority-badge normal';
    }
  };

  const getNotificationTypeIcon = (type) => {
    switch (type) {
      case 'ContractExpiration': return 'fas fa-calendar-times';
      case 'OfferReceived': return 'fas fa-tags';
      case 'SystemAlert': return 'fas fa-exclamation-triangle';
      case 'PaymentReceived': return 'fas fa-credit-card';
      default: return 'fas fa-bell';
    }
  };

  if (loading) {
    return <Spinner fullScreen={true} message="Loading notifications..." size="large" />;
  }

  return (
    <DashboardLayout>
      <div className="admin-management-container">
        {/* Header */}
        <div className="management-header">
          <div className="header-content">
            <h1>Notification Management</h1>
            <p>Configure system alerts, manage notifications, and set up automated messaging</p>
          </div>
          <div className="header-actions">
            <button 
              className="btn-primary"
              onClick={handleCreateNotification}
            >
              <i className="fas fa-plus"></i>
              Create Notification
            </button>
          </div>
        </div>

        {/* Notification Settings */}
        <div className="settings-section">
          <h2>Notification Settings</h2>
          <div className="notification-settings-grid">
            <div className="setting-card">
              <div className="setting-header">
                <h3>Contract Expiration Reminders</h3>
                <label className="toggle-switch">
                  <input
                    type="checkbox"
                    checked={notificationSettings.contractExpiration.enabled}
                    onChange={(e) => setNotificationSettings(prev => ({
                      ...prev,
                      contractExpiration: { ...prev.contractExpiration, enabled: e.target.checked }
                    }))}
                  />
                  <span className="toggle-slider"></span>
                </label>
              </div>
              <div className="setting-content">
                <div className="setting-row">
                  <label>Days before expiry:</label>
                  <input
                    type="number"
                    value={notificationSettings.contractExpiration.daysBeforeExpiry}
                    onChange={(e) => setNotificationSettings(prev => ({
                      ...prev,
                      contractExpiration: { ...prev.contractExpiration, daysBeforeExpiry: e.target.value }
                    }))}
                    min="1"
                    max="90"
                  />
                </div>
                <div className="setting-row">
                  <label>Frequency:</label>
                  <select
                    value={notificationSettings.contractExpiration.frequency}
                    onChange={(e) => setNotificationSettings(prev => ({
                      ...prev,
                      contractExpiration: { ...prev.contractExpiration, frequency: e.target.value }
                    }))}
                  >
                    <option value="daily">Daily</option>
                    <option value="weekly">Weekly</option>
                    <option value="monthly">Monthly</option>
                  </select>
                </div>
              </div>
            </div>

            <div className="setting-card">
              <div className="setting-header">
                <h3>Payment Reminders</h3>
                <label className="toggle-switch">
                  <input
                    type="checkbox"
                    checked={notificationSettings.paymentReminders.enabled}
                    onChange={(e) => setNotificationSettings(prev => ({
                      ...prev,
                      paymentReminders: { ...prev.paymentReminders, enabled: e.target.checked }
                    }))}
                  />
                  <span className="toggle-slider"></span>
                </label>
              </div>
              <div className="setting-content">
                <div className="setting-row">
                  <label>Days after due:</label>
                  <input
                    type="number"
                    value={notificationSettings.paymentReminders.daysAfterDue}
                    onChange={(e) => setNotificationSettings(prev => ({
                      ...prev,
                      paymentReminders: { ...prev.paymentReminders, daysAfterDue: e.target.value }
                    }))}
                    min="1"
                    max="30"
                  />
                </div>
                <div className="setting-row">
                  <label>Frequency:</label>
                  <select
                    value={notificationSettings.paymentReminders.frequency}
                    onChange={(e) => setNotificationSettings(prev => ({
                      ...prev,
                      paymentReminders: { ...prev.paymentReminders, frequency: e.target.value }
                    }))}
                  >
                    <option value="daily">Daily</option>
                    <option value="weekly">Weekly</option>
                  </select>
                </div>
              </div>
            </div>

            <div className="setting-card">
              <div className="setting-header">
                <h3>System Alerts</h3>
                <label className="toggle-switch">
                  <input
                    type="checkbox"
                    checked={notificationSettings.systemAlerts.enabled}
                    onChange={(e) => setNotificationSettings(prev => ({
                      ...prev,
                      systemAlerts: { ...prev.systemAlerts, enabled: e.target.checked }
                    }))}
                  />
                  <span className="toggle-slider"></span>
                </label>
              </div>
              <div className="setting-content">
                <div className="setting-row">
                  <label>Minimum severity:</label>
                  <select
                    value={notificationSettings.systemAlerts.severity}
                    onChange={(e) => setNotificationSettings(prev => ({
                      ...prev,
                      systemAlerts: { ...prev.systemAlerts, severity: e.target.value }
                    }))}
                  >
                    <option value="low">Low</option>
                    <option value="normal">Normal</option>
                    <option value="high">High</option>
                    <option value="critical">Critical</option>
                  </select>
                </div>
              </div>
            </div>
          </div>
          
          <div className="settings-actions">
            <button className="btn-save" onClick={handleUpdateSettings}>
              <i className="fas fa-save"></i>
              Save Settings
            </button>
          </div>
        </div>

        {/* Recent Notifications */}
        <div className="management-table-container">
          <div className="table-header">
            <h2>Recent Notifications</h2>
          </div>
          <table className="management-table">
            <thead>
              <tr>
                <th>Notification</th>
                <th>Type</th>
                <th>Priority</th>
                <th>Recipients</th>
                <th>Delivery Stats</th>
                <th>Sent</th>
                <th>Status</th>
              </tr>
            </thead>
            <tbody>
              {notifications.map(notification => (
                <tr key={notification._id}>
                  <td>
                    <div className="notification-info">
                      <div className="notification-icon">
                        <i className={getNotificationTypeIcon(notification.type)}></i>
                      </div>
                      <div className="notification-details">
                        <div className="notification-title">{notification.title}</div>
                        <div className="notification-message">{notification.message}</div>
                      </div>
                    </div>
                  </td>
                  <td>
                    <span className="type-badge notification">
                      {notification.type.replace(/([A-Z])/g, ' $1').trim()}
                    </span>
                  </td>
                  <td>
                    <span className={getPriorityBadgeClass(notification.priority)}>
                      {notification.priority}
                    </span>
                  </td>
                  <td>
                    <span className="recipients-count">
                      {notification.recipients} users
                    </span>
                  </td>
                  <td>
                    <div className="delivery-stats">
                      <span className="stat-item success">
                        <i className="fas fa-check"></i>
                        {notification.deliveryStats.delivered}
                      </span>
                      {notification.deliveryStats.failed > 0 && (
                        <span className="stat-item failed">
                          <i className="fas fa-times"></i>
                          {notification.deliveryStats.failed}
                        </span>
                      )}
                    </div>
                  </td>
                  <td>{formatDate(notification.sentAt)}</td>
                  <td>
                    <span className={getStatusBadgeClass(notification.status)}>
                      {notification.status}
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default NotificationManagement;
