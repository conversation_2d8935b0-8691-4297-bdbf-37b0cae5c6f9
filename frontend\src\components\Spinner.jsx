import React from 'react';

/**
 * Reusable Spinner component for loading states
 * @param {Object} props - Component props
 * @param {string} props.size - Size of the spinner ('small', 'medium', 'large')
 * @param {string} props.color - Color of the spinner
 * @param {string} props.message - Optional message to display below the spinner
 * @param {boolean} props.fullScreen - Whether to display the spinner in fullscreen mode
 * @param {boolean} props.overlay - Whether to display the spinner as an overlay
 * @param {string} props.className - Additional CSS classes
 */
const Spinner = ({ 
  size = 'medium', 
  color = '#3498db', 
  message = 'Loading...', 
  fullScreen = false,
  overlay = false,
  className = ''
}) => {
  // Determine spinner size
  const sizeMap = {
    small: { width: '20px', height: '20px', border: '3px' },
    medium: { width: '40px', height: '40px', border: '4px' },
    large: { width: '60px', height: '60px', border: '5px' }
  };
  
  const { width, height, border } = sizeMap[size] || sizeMap.medium;
  
  // Styles for the spinner
  const spinnerStyle = {
    width,
    height,
    border: `${border} solid #f3f3f3`,
    borderTop: `${border} solid ${color}`,
    borderRadius: '50%',
    animation: 'spin 1s linear infinite',
    margin: '0 auto'
  };
  
  // Styles for the container
  const containerStyle = {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    padding: fullScreen ? '0' : '20px',
    textAlign: 'center',
    ...(fullScreen && {
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(255, 255, 255, 0.8)',
      zIndex: 9999
    }),
    ...(overlay && !fullScreen && {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(255, 255, 255, 0.8)',
      zIndex: 9999
    })
  };
  
  // Styles for the message
  const messageStyle = {
    marginTop: '10px',
    fontSize: size === 'small' ? '12px' : size === 'large' ? '18px' : '14px',
    fontWeight: 'bold',
    color: '#333'
  };
  
  return (
    <div style={containerStyle} className={className}>
      <div style={spinnerStyle}></div>
      {message && <p style={messageStyle}>{message}</p>}
      
      <style jsx>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
};

export default Spinner;
