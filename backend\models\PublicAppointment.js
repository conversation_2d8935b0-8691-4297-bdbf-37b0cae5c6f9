const mongoose = require('mongoose');

const publicAppointmentSchema = new mongoose.Schema({
  firstName: {
    type: String,
    required: true,
    trim: true
  },
  lastName: {
    type: String,
    required: true,
    trim: true
  },
  email: {
    type: String,
    required: true,
    trim: true,
    lowercase: true
  },
  phone: {
    type: String,
    trim: true
  },
  preferredDate: {
    type: Date,
    required: true
  },
  preferredTime: {
    type: String,
    required: true,
    trim: true
  },
  contactMethod: {
    type: String,
    enum: ['phone', 'email', 'video'],
    required: true,
    default: 'phone'
  },
  status: {
    type: String,
    enum: ['Pending', 'Confirmed', 'Completed', 'Cancelled', 'Rescheduled'],
    default: 'Pending'
  },
  assignedTo: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  notes: {
    type: String,
    trim: true
  },
  adminNotes: {
    type: String,
    trim: true
  },
  confirmedDateTime: {
    type: Date
  },
  meetingLink: {
    type: String,
    trim: true
  },
  duration: {
    type: Number,
    default: 30, // minutes
    min: 15,
    max: 120
  },
  reminderSent: {
    type: Boolean,
    default: false
  },
  reminderSentAt: {
    type: Date
  },
  source: {
    type: String,
    default: 'Website Contact Page'
  },
  ipAddress: {
    type: String
  },
  userAgent: {
    type: String
  },
  followUpRequired: {
    type: Boolean,
    default: true
  },
  followUpDate: {
    type: Date
  }
}, {
  timestamps: true
});

// Create indexes for frequently queried fields
publicAppointmentSchema.index({ email: 1 });
publicAppointmentSchema.index({ status: 1 });
publicAppointmentSchema.index({ preferredDate: 1 });
publicAppointmentSchema.index({ createdAt: -1 });
publicAppointmentSchema.index({ assignedTo: 1 });

const PublicAppointment = mongoose.model('PublicAppointment', publicAppointmentSchema);

module.exports = PublicAppointment;
