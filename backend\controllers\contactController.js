const { ContactForm, PublicAppointment } = require('../models');

// Submit contact form
const submitContactForm = async (req, res) => {
  try {
    const { firstName, lastName, email, phone, message } = req.body;

    // Validation
    if (!firstName || !lastName || !email || !message) {
      return res.status(400).json({
        success: false,
        message: 'First name, last name, email, and message are required'
      });
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return res.status(400).json({
        success: false,
        message: 'Please provide a valid email address'
      });
    }

    // Create contact form entry
    const contactForm = new ContactForm({
      firstName: firstName.trim(),
      lastName: lastName.trim(),
      email: email.trim().toLowerCase(),
      phone: phone ? phone.trim() : undefined,
      message: message.trim(),
      ipAddress: req.ip || req.connection.remoteAddress,
      userAgent: req.get('User-Agent')
    });

    await contactForm.save();

    // TODO: Send notification email to admin
    // TODO: Send confirmation email to user

    res.status(201).json({
      success: true,
      message: 'Contact form submitted successfully. We will get back to you soon!',
      data: {
        id: contactForm._id,
        submittedAt: contactForm.createdAt
      }
    });

  } catch (error) {
    console.error('Error submitting contact form:', error);
    res.status(500).json({
      success: false,
      message: 'An error occurred while submitting your message. Please try again.'
    });
  }
};

// Submit appointment booking
const submitAppointmentBooking = async (req, res) => {
  try {
    const { firstName, lastName, email, phone, date, time, contactMethod } = req.body;

    // Validation
    if (!firstName || !lastName || !email || !date || !time) {
      return res.status(400).json({
        success: false,
        message: 'First name, last name, email, date, and time are required'
      });
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return res.status(400).json({
        success: false,
        message: 'Please provide a valid email address'
      });
    }

    // Date validation
    const appointmentDate = new Date(date);
    const now = new Date();
    if (appointmentDate < now) {
      return res.status(400).json({
        success: false,
        message: 'Appointment date cannot be in the past'
      });
    }

    // Contact method validation
    const validContactMethods = ['phone', 'email', 'video'];
    if (contactMethod && !validContactMethods.includes(contactMethod)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid contact method'
      });
    }

    // Create appointment booking
    const appointment = new PublicAppointment({
      firstName: firstName.trim(),
      lastName: lastName.trim(),
      email: email.trim().toLowerCase(),
      phone: phone ? phone.trim() : undefined,
      preferredDate: appointmentDate,
      preferredTime: time.trim(),
      contactMethod: contactMethod || 'phone',
      ipAddress: req.ip || req.connection.remoteAddress,
      userAgent: req.get('User-Agent')
    });

    await appointment.save();

    // TODO: Send notification email to admin
    // TODO: Send confirmation email to user

    res.status(201).json({
      success: true,
      message: 'Appointment request submitted successfully. We will contact you to confirm the details.',
      data: {
        id: appointment._id,
        submittedAt: appointment.createdAt,
        preferredDate: appointment.preferredDate,
        preferredTime: appointment.preferredTime,
        contactMethod: appointment.contactMethod
      }
    });

  } catch (error) {
    console.error('Error submitting appointment booking:', error);
    res.status(500).json({
      success: false,
      message: 'An error occurred while booking your appointment. Please try again.'
    });
  }
};

// Get contact form submissions (admin only)
const getContactForms = async (req, res) => {
  try {
    const { page = 1, limit = 10, status, search } = req.query;
    const skip = (page - 1) * limit;

    // Build query
    let query = {};
    if (status) {
      query.status = status;
    }
    if (search) {
      query.$or = [
        { firstName: { $regex: search, $options: 'i' } },
        { lastName: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
        { message: { $regex: search, $options: 'i' } }
      ];
    }

    const [contactForms, total] = await Promise.all([
      ContactForm.find(query)
        .populate('assignedTo', 'firstName lastName email')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(parseInt(limit)),
      ContactForm.countDocuments(query)
    ]);

    res.json({
      success: true,
      data: {
        contactForms,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(total / limit),
          totalItems: total,
          itemsPerPage: parseInt(limit)
        }
      }
    });

  } catch (error) {
    console.error('Error fetching contact forms:', error);
    res.status(500).json({
      success: false,
      message: 'An error occurred while fetching contact forms'
    });
  }
};

// Get appointment bookings (admin only)
const getAppointmentBookings = async (req, res) => {
  try {
    const { page = 1, limit = 10, status, search } = req.query;
    const skip = (page - 1) * limit;

    // Build query
    let query = {};
    if (status) {
      query.status = status;
    }
    if (search) {
      query.$or = [
        { firstName: { $regex: search, $options: 'i' } },
        { lastName: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } }
      ];
    }

    const [appointments, total] = await Promise.all([
      PublicAppointment.find(query)
        .populate('assignedTo', 'firstName lastName email')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(parseInt(limit)),
      PublicAppointment.countDocuments(query)
    ]);

    res.json({
      success: true,
      data: {
        appointments,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(total / limit),
          totalItems: total,
          itemsPerPage: parseInt(limit)
        }
      }
    });

  } catch (error) {
    console.error('Error fetching appointment bookings:', error);
    res.status(500).json({
      success: false,
      message: 'An error occurred while fetching appointment bookings'
    });
  }
};

module.exports = {
  submitContactForm,
  submitAppointmentBooking,
  getContactForms,
  getAppointmentBookings
};
