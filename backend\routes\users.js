const express = require('express');
const router = express.Router();
// Import MongoDB models
const { User, IndividualProfile, ProfessionalProfile } = require('../models');
const { verifyToken } = require('../middleware/auth');
const mongoose = require('mongoose');
const logger = require('../utils/logger');

// Get user profile
router.get('/profile', verifyToken, async (req, res) => {
  try {
    logger.info('Received request to /users/profile');
    logger.debug('Request user:', {
      sub: req.user.sub,
      email: req.user.email,
      username: req.user.username
    });

    // Find user by Cognito ID or email
    let user;

    if (req.user.sub) {
      // First try to find by Cognito ID
      user = await User.findOne({ cognitoId: req.user.sub });
      logger.debug('Searched for user by cognitoId:', req.user.sub, 'Found:', !!user);
    }

    if (!user && req.user.email) {
      // If not found by Cognito ID, try by email
      user = await User.findOne({ email: req.user.email });
      logger.debug('Searched for user by email:', req.user.email, 'Found:', !!user);
    }

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Get additional profile data based on user type
    let profileData = null;
    if (user.userType === 'Individual') {
      profileData = await IndividualProfile.findOne({ userId: user._id });
    } else if (user.userType === 'Professional') {
      profileData = await ProfessionalProfile.findOne({ userId: user._id });
    }

    res.status(200).json({
      success: true,
      data: {
        _id: user._id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        phone: user.phone,
        userType: user.userType,
        status: user.status,
        profileComplete: user.profileComplete,
        profile: profileData
      }
    });
  } catch (error) {
    logger.error('Error fetching user profile:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching user profile',
      error: error.message
    });
  }
});

// Get user profile with verification details by Cognito ID
router.get('/profile/:cognitoId', async (req, res) => {
  try {
    const { cognitoId } = req.params;

    if (!cognitoId) {
      return res.status(400).json({
        success: false,
        message: 'Cognito ID is required'
      });
    }

    // Get user from database using the Cognito ID with populated verification details
    const user = await User.findOne({ cognitoId })
      .populate('verificationDetails.reviewedBy', 'firstName lastName email')
      .select('-passwordResetToken -passwordResetExpires');

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Get additional profile data based on user type
    let profileData = null;
    if (user.userType === 'Individual') {
      profileData = await IndividualProfile.findOne({ userId: user._id });
    } else if (['Professional', 'Broker', 'Supplier', 'Referrer'].includes(user.userType)) {
      profileData = await ProfessionalProfile.findOne({ userId: user._id });
    }

    res.status(200).json({
      success: true,
      data: {
        _id: user._id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        phone: user.phone,
        userType: user.userType,
        status: user.status,
        profileComplete: user.profileComplete,
        verificationStatus: user.verificationStatus,
        verificationDetails: user.verificationDetails,
        profile: profileData,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt
      }
    });

  } catch (error) {
    logger.error('Error fetching user profile:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Get user by Cognito ID (legacy endpoint)
router.get('/cognito/:cognitoId', async (req, res) => {
  try {
    const { cognitoId } = req.params;

    if (!cognitoId) {
      return res.status(400).json({
        success: false,
        message: 'Cognito ID is required'
      });
    }

    // Get user from database using the Cognito ID
    const user = await User.findOne({ cognitoId });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Get additional profile data based on user type
    let profileData = null;
    if (user.userType === 'Individual') {
      profileData = await IndividualProfile.findOne({ userId: user._id });
    } else if (['Professional', 'Broker', 'Supplier', 'Referrer'].includes(user.userType)) {
      profileData = await ProfessionalProfile.findOne({ userId: user._id });
    }

    res.status(200).json({
      success: true,
      data: {
        _id: user._id,
        cognitoId: user.cognitoId,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        phone: user.phone,
        userType: user.userType,
        status: user.status,
        profileComplete: user.profileComplete,
        profile: profileData
      }
    });
  } catch (error) {
    logger.error('Error fetching user by Cognito ID:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching user by Cognito ID',
      error: error.message
    });
  }
});

// Update user profile
router.put('/profile', verifyToken, async (req, res) => {
  try {
    const { firstName, lastName, phone, userType } = req.body;

    logger.info('Received request to /users/profile (PUT)');
    logger.debug('Request user:', {
      sub: req.user.sub,
      email: req.user.email,
      username: req.user.username
    });
    logger.debug('Request body:', { firstName, lastName, phone, userType });

    // Find user by Cognito ID or email
    let user;

    if (req.user.sub) {
      // First try to find by Cognito ID
      user = await User.findOne({ cognitoId: req.user.sub });
      logger.debug('Searched for user by cognitoId:', req.user.sub, 'Found:', !!user);
    }

    if (!user && req.user.email) {
      // If not found by Cognito ID, try by email
      user = await User.findOne({ email: req.user.email });
      logger.debug('Searched for user by email:', req.user.email, 'Found:', !!user);
    }

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Update user fields
    if (firstName) user.firstName = firstName;
    if (lastName) user.lastName = lastName;
    if (phone) user.phone = phone;
    if (userType && !user.profileComplete) user.userType = userType;

    // Save updated user
    await user.save();

    // Get updated profile data
    let profileData = null;
    if (user.userType === 'Individual') {
      profileData = await IndividualProfile.findOne({ userId: user._id });
    } else if (user.userType === 'Professional') {
      profileData = await ProfessionalProfile.findOne({ userId: user._id });
    }

    res.status(200).json({
      success: true,
      message: 'Profile updated successfully',
      data: {
        _id: user._id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        phone: user.phone,
        userType: user.userType,
        status: user.status,
        profileComplete: user.profileComplete,
        profile: profileData
      }
    });
  } catch (error) {
    logger.error('Error updating user profile:', error);
    res.status(500).json({
      success: false,
      message: 'Error updating user profile',
      error: error.message
    });
  }
});

// Create or update individual profile
router.post('/individual-profile', verifyToken, async (req, res) => {
  try {
    logger.info('Received request to /users/individual-profile');
    logger.debug('Request user:', {
      sub: req.user.sub,
      email: req.user.email,
      username: req.user.username
    });

    // Find user by Cognito ID or email
    let user;

    if (req.user.sub) {
      // First try to find by Cognito ID
      user = await User.findOne({ cognitoId: req.user.sub });
      logger.debug('Searched for user by cognitoId:', req.user.sub, 'Found:', !!user);
    }

    if (!user && req.user.email) {
      // If not found by Cognito ID, try by email
      user = await User.findOne({ email: req.user.email });
      logger.debug('Searched for user by email:', req.user.email, 'Found:', !!user);
    }

    if (!user) {
      logger.info('User not found, creating new user record');
      // If user still not found, create a new user record
      user = await User.create({
        cognitoId: req.user.sub,
        email: req.user.email,
        userType: 'Individual',
        status: 'Active',
        verificationStatus: 'Verified'
      });
      logger.info('Created new user in MongoDB with Cognito ID:', req.user.sub);
    }

    // Check if user type is Individual
    if (user.userType !== 'Individual') {
      logger.info('User type is not Individual:', user.userType);
      // Update user type to Individual
      user.userType = 'Individual';
      await user.save();
      logger.info('Updated user type to Individual');
    }

    // Check if profile already exists
    let profile = await IndividualProfile.findOne({ userId: user._id });

    const {
      address,
      energyIdentifiers,
      currentSupplier,
      consumptionDetails,
      preferredContactMethod
    } = req.body;

    if (profile) {
      // Update existing profile
      if (address) profile.address = address;
      if (energyIdentifiers) profile.energyIdentifiers = energyIdentifiers;
      if (currentSupplier) profile.currentSupplier = currentSupplier;
      if (consumptionDetails) profile.consumptionDetails = consumptionDetails;
      if (preferredContactMethod) profile.preferredContactMethod = preferredContactMethod;

      await profile.save();
    } else {
      // Create new profile
      profile = await IndividualProfile.create({
        userId: user._id,
        address,
        energyIdentifiers,
        currentSupplier,
        consumptionDetails,
        preferredContactMethod
      });
    }

    // Update user's profileComplete status
    user.profileComplete = true;
    await user.save();

    res.status(200).json({
      success: true,
      message: 'Individual profile updated successfully',
      data: profile
    });
  } catch (error) {
    logger.error('Error updating individual profile:', error);
    res.status(500).json({
      success: false,
      message: 'Error updating individual profile',
      error: error.message
    });
  }
});

// Update user type (PUT endpoint for frontend compatibility)
router.put('/update-type', verifyToken, async (req, res) => {
  try {
    const { userType } = req.body;

    if (!userType) {
      return res.status(400).json({
        success: false,
        message: 'User type is required'
      });
    }

    // Validate user type
    const validUserTypes = ['Individual', 'Professional', 'Broker', 'Supplier', 'Referrer', 'Admin'];
    if (!validUserTypes.includes(userType)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid user type'
      });
    }

    // MongoDB operations
    let user;
    if (req.user.sub) {
      // First try to find by Cognito ID
      user = await User.findOne({ cognitoId: req.user.sub });
    }

    if (!user && req.user.email) {
      // If not found by Cognito ID, try by email
      user = await User.findOne({ email: req.user.email });
    }

    if (!user) {
      // If user still not found, create a new user record
      user = await User.create({
        cognitoId: req.user.sub,
        email: req.user.email,
        userType: userType,
        status: 'Active',
        verificationStatus: 'Verified'
      });

      logger.info('Created new user in MongoDB with Cognito ID:', req.user.sub);
    } else {
      // Update user type
      user.userType = userType;
      await user.save();

      logger.info('Updated user type in MongoDB for user:', user._id);
    }

    res.status(200).json({
      success: true,
      message: 'User type updated successfully',
      data: {
        id: user._id,
        email: user.email,
        userType: user.userType
      }
    });
  } catch (error) {
    logger.error('Error updating user type:', error);
    res.status(500).json({
      success: false,
      message: 'Error updating user type',
      error: error.message
    });
  }
});

// Update user type (POST endpoint for backward compatibility)
router.post('/update-user-type', verifyToken, async (req, res) => {
  try {
    const { userType } = req.body;

    if (!userType) {
      return res.status(400).json({
        success: false,
        message: 'User type is required'
      });
    }

    // Validate user type
    const validUserTypes = ['Individual', 'Professional', 'Broker', 'Supplier', 'Referrer', 'Admin'];
    if (!validUserTypes.includes(userType)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid user type'
      });
    }

    // Find user by email or cognitoId
    let user;
    if (req.user.sub) {
      // First try to find by Cognito ID
      user = await User.findOne({ cognitoId: req.user.sub });
    }

    if (!user && req.user.email) {
      // If not found by Cognito ID, try by email
      user = await User.findOne({ email: req.user.email });
    }

    if (!user) {
      // If user still not found, create a new user record
      user = await User.create({
        cognitoId: req.user.sub,
        email: req.user.email,
        userType: userType,
        status: 'Active',
        verificationStatus: 'Verified'
      });

      logger.info('Created new user in MongoDB with Cognito ID:', req.user.sub);
    } else {
      // Update user type
      user.userType = userType;
      await user.save();

      logger.info('Updated user type in MongoDB for user:', user._id);
    }

    res.status(200).json({
      success: true,
      message: 'User type updated successfully',
      data: {
        id: user._id,
        email: user.email,
        userType: user.userType
      }
    });
  } catch (error) {
    logger.error('Error updating user type:', error);
    res.status(500).json({
      success: false,
      message: 'Error updating user type',
      error: error.message
    });
  }
});

// Update profile completion status
router.post('/update-profile-completion', async (req, res) => {
  try {
    logger.info('Received request to /users/update-profile-completion');
    logger.debug('Request body:', req.body);

    const { email, cognitoId, profileComplete, userType, status } = req.body;

    if (!email && !cognitoId) {
      logger.warn('Missing required fields: email or cognitoId');
      return res.status(400).json({
        success: false,
        message: 'Email or Cognito ID is required'
      });
    }

    if (profileComplete === undefined) {
      logger.warn('Missing required field: profileComplete');
      return res.status(400).json({
        success: false,
        message: 'Profile completion status is required'
      });
    }

    // Find user by Cognito ID or email
    let user;

    if (cognitoId) {
      user = await User.findOne({ cognitoId });
      logger.debug('Searched for user by cognitoId:', cognitoId, 'Found:', !!user);
    }

    if (!user && email) {
      user = await User.findOne({ email });
      logger.debug('Searched for user by email:', email, 'Found:', !!user);
    }

    if (!user) {
      logger.warn('User not found');
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    logger.info('User found, updating profile completion status');

    // Update profile completion status
    user.profileComplete = profileComplete;

    // Update user type if provided
    if (userType) {
      logger.info('Updating user type to:', userType);
      user.userType = userType.charAt(0).toUpperCase() + userType.slice(1).toLowerCase();
    }

    // Update status if provided (for broker/supplier profile submission)
    if (status) {
      logger.info('Updating user status to:', status);
      user.status = status;

      // If status is Profile_Submitted, update verification details
      if (status === 'Profile_Submitted') {
        if (!user.verificationDetails) {
          user.verificationDetails = {};
        }
        user.verificationDetails.profileSubmittedAt = new Date();
        user.verificationDetails.resubmissionCount = (user.verificationDetails.resubmissionCount || 0) + 1;
      }
    }

    await user.save();
    logger.info('User saved with profileComplete:', profileComplete, 'userType:', user.userType, 'status:', user.status);

    res.status(200).json({
      success: true,
      message: 'Profile completion status updated successfully',
      data: {
        id: user._id,
        email: user.email,
        userType: user.userType,
        profileComplete: user.profileComplete,
        status: user.status,
        verificationDetails: user.verificationDetails
      }
    });
  } catch (error) {
    logger.error('Error updating profile completion status:', error);
    res.status(500).json({
      success: false,
      message: 'Error updating profile completion status',
      error: error.message
    });
  }
});

// Create or update professional profile
router.post('/professional-profile', verifyToken, async (req, res) => {
  try {
    logger.info('Received request to /users/professional-profile');
    logger.debug('Request user:', {
      sub: req.user.sub,
      email: req.user.email,
      username: req.user.username
    });

    // Find user by Cognito ID or email
    let user;

    if (req.user.sub) {
      // First try to find by Cognito ID
      user = await User.findOne({ cognitoId: req.user.sub });
      logger.debug('Searched for user by cognitoId:', req.user.sub, 'Found:', !!user);
    }

    if (!user && req.user.email) {
      // If not found by Cognito ID, try by email
      user = await User.findOne({ email: req.user.email });
      logger.debug('Searched for user by email:', req.user.email, 'Found:', !!user);
    }

    if (!user) {
      logger.info('User not found, creating new user record');
      // If user still not found, create a new user record
      user = await User.create({
        cognitoId: req.user.sub,
        email: req.user.email,
        userType: 'Professional',
        status: 'Active',
        verificationStatus: 'Verified'
      });
      logger.info('Created new user in MongoDB with Cognito ID:', req.user.sub);
    }

    // Check if user type is Professional
    if (user.userType !== 'Professional') {
      logger.info('User type is not Professional:', user.userType);
      // Update user type to Professional
      user.userType = 'Professional';
      await user.save();
      logger.info('Updated user type to Professional');
    }

    // Check if profile already exists
    let profile = await ProfessionalProfile.findOne({ userId: user._id });

    const {
      companyName,
      companyAddress,
      siretNumber,
      vatNumber,
      companyRole,
      businessType,
      energyTypes,
      numberOfLocations,
      annualConsumption,
      currentSuppliers
    } = req.body;

    if (profile) {
      // Update existing profile
      if (companyName) profile.companyName = companyName;
      if (companyAddress) profile.companyAddress = companyAddress;
      if (siretNumber) profile.siretNumber = siretNumber;
      if (vatNumber) profile.vatNumber = vatNumber;
      if (companyRole) profile.companyRole = companyRole;
      if (businessType) profile.businessType = businessType;
      if (energyTypes) profile.energyTypes = energyTypes;
      if (numberOfLocations) profile.numberOfLocations = numberOfLocations;
      if (annualConsumption) profile.annualConsumption = annualConsumption;
      if (currentSuppliers) profile.currentSuppliers = currentSuppliers;

      await profile.save();
    } else {
      // Create new profile
      profile = await ProfessionalProfile.create({
        userId: user._id,
        companyName,
        companyAddress,
        siretNumber,
        vatNumber,
        companyRole,
        businessType,
        energyTypes,
        numberOfLocations,
        annualConsumption,
        currentSuppliers
      });
    }

    // Update user's profileComplete status
    user.profileComplete = true;
    await user.save();

    res.status(200).json({
      success: true,
      message: 'Professional profile updated successfully',
      data: profile
    });
  } catch (error) {
    logger.error('Error updating professional profile:', error);
    res.status(500).json({
      success: false,
      message: 'Error updating professional profile',
      error: error.message
    });
  }
});

module.exports = router;
