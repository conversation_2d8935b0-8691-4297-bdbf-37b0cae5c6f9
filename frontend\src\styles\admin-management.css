/* Admin Management Pages Styles - Black & White Theme */
.admin-management-container {
  padding: 32px;
  width: 100%;
  max-width: 100%;
  margin: 0;
  min-height: calc(100vh - 80px);
  background: #ffffff;
  position: relative;
  overflow-x: hidden;
  box-sizing: border-box;
}

/* Header Styles */
.management-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
  padding: 32px;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
  position: relative;
  z-index: 1;
}

.header-content h1 {
  margin: 0 0 12px 0;
  font-size: 32px;
  font-weight: 700;
  color: #000000;
  line-height: 1.2;
}

.header-content p {
  margin: 0;
  color: #333333;
  font-size: 16px;
  font-weight: 500;
}

.back-button {
  display: inline-flex;
  align-items: center;
  gap: 10px;
  padding: 12px 20px;
  background: #ffffff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  color: #333333;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.back-button:hover {
  background: #f5f5f5;
  color: #000000;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-color: #000000;
}

.header-stats {
  display: flex;
  gap: 24px;
}

.stat-item {
  text-align: center;
  padding: 20px 24px;
  background: #ffffff;
  border-radius: 8px;
  min-width: 120px;
  border: 1px solid #e0e0e0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-color: #000000;
}

.stat-value {
  display: block;
  font-size: 28px;
  font-weight: 700;
  color: #000000;
  margin-bottom: 4px;
}

.stat-label {
  display: block;
  font-size: 13px;
  color: #333333;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Filters Styles */
.management-filters {
  display: flex;
  gap: 24px;
  margin-bottom: 32px;
  padding: 28px;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
  flex-wrap: wrap;
  position: relative;
  z-index: 1;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 160px;
}

.filter-group.search-group {
  flex: 1;
  min-width: 280px;
}

.filter-group label {
  font-size: 14px;
  font-weight: 600;
  color: #000000;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.filter-group select,
.filter-group input {
  padding: 12px 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-size: 14px;
  background: #ffffff;
  color: #000000;
  font-weight: 500;
  transition: all 0.3s ease;
}

.filter-group select:focus,
.filter-group input:focus {
  outline: none;
  border-color: #000000;
  box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

/* Table Styles */
.management-table-container {
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
  overflow-x: auto;
  overflow-y: visible;
  margin-bottom: 32px;
  position: relative;
  z-index: 1;
  width: 100%;
  max-width: 100%;
  -webkit-overflow-scrolling: touch;
}

.management-table {
  width: 100%;
  min-width: 700px;
  border-collapse: collapse;
  table-layout: fixed;
}

.management-table th {
  background: #f5f5f5;
  padding: 14px 12px; /* Reduced padding for tighter layout */
  text-align: left;
  font-weight: 700;
  color: #000000;
  border-bottom: 2px solid #e0e0e0;
  font-size: 13px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  position: relative;
  white-space: nowrap;
}

/* Center align specific header columns */
.management-table th:nth-child(2), /* Type column */
.management-table th:nth-child(3), /* Status column */
.management-table th:nth-child(4) { /* Verification column */
  text-align: center;
  padding: 14px 8px; /* Reduced padding for badge columns */
}

/* Define column widths for better control - Users Table (5 columns) */
.management-table th:nth-child(1) { width: 35%; } /* User - Increased for name/email */
.management-table th:nth-child(2) { width: 12%; } /* Type - Reduced */
.management-table th:nth-child(3) { width: 12%; } /* Status - Reduced */
.management-table th:nth-child(4) { width: 14%; } /* Verification - Slightly larger */
.management-table th:nth-child(5) { width: 27%; } /* Actions - Optimized for buttons + dropdown */

/* Brokers Table - 7 columns (needs more space for 3 buttons + dropdown) */
.management-table th:nth-child(7) { width: 30%; min-width: 240px; } /* Actions for 7-column tables */

/* Contracts Table - 8 columns */
.management-table th:nth-child(8) { width: 20%; min-width: 160px; } /* Actions for 8-column tables */

/* Remove special broker handling - use standard styling */

.management-table td {
  padding: 12px 8px; /* Reduced padding for compact layout */
  border-bottom: 1px solid #f5f5f5;
  vertical-align: middle;
  transition: all 0.3s ease;
  overflow: visible; /* Changed from hidden to visible for proper badge display */
  text-overflow: ellipsis;
  text-align: left;
}

/* Add extra padding to the Actions column to prevent dropdown cutoff */
.management-table td:nth-child(5) {
  padding-right: 16px;
  padding-left: 12px;
}

/* Align Actions header with action buttons */
.management-table th:nth-child(5) {
  padding-left: 12px;
  text-align: left;
}

/* Ensure specific columns have proper alignment */
.management-table td:nth-child(2), /* Type column */
.management-table td:nth-child(3), /* Status column */
.management-table td:nth-child(4) { /* Verification column */
  text-align: center;
  padding: 12px 4px; /* Reduced padding for badge columns */
}

.management-table td:nth-child(6), /* Actions column */
.management-table td:nth-child(7), /* Actions column for 7-column tables */
.management-table td:nth-child(8) { /* Actions column for 8-column tables */
  text-align: left;
  padding: 20px 24px; /* Extra padding for actions column */
}

.management-table tr:hover {
  background: #f5f5f5;
}

.management-table tr:nth-child(even) {
  background: #fafafa;
}

.management-table tr:nth-child(even):hover {
  background: #f0f0f0;
}

/* User Info Styles */
.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
  min-width: 160px;
}

.user-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #000000;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  font-size: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  position: relative;
  overflow: hidden;
  margin-right: 8px; /* Reduced spacing from adjacent content */
  flex-shrink: 0;
}

.user-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.user-name {
  font-weight: 600;
  color: #000000;
  font-size: 15px;
  line-height: 1.2;
}

.user-email {
  font-size: 13px;
  color: #333333;
  font-weight: 500;
}

/* Badge Styles */
.status-badge,
.type-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 2px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
  white-space: nowrap;
  text-align: center;
  min-width: 60px;
  max-width: 100px;
  margin: 1px 2px; /* Reduced margin around badges */
}

.status-badge.active {
  background: #000000;
  color: #ffffff;
  border-color: #000000;
}

.status-badge.inactive {
  background: #ffffff;
  color: #000000;
  border-color: #000000;
}

.status-badge.pending {
  background: #666666;
  color: #ffffff;
  border-color: #666666;
}

.status-badge.suspended {
  background: #333333;
  color: #ffffff;
  border-color: #333333;
}

.type-badge.individual,
.type-badge.professional,
.type-badge.broker,
.type-badge.supplier,
.type-badge.admin {
  background: #f5f5f5;
  color: #000000;
  border-color: #e0e0e0;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 4px;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: nowrap;
  margin: 0;
  padding: 0;
}

.status-select {
  padding: 4px 8px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  font-size: 11px;
  font-weight: 500;
  background: #ffffff;
  color: #000000;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 70px;
  height: 32px;
  margin-left: 6px;
  box-sizing: border-box;
}

.status-select:focus {
  outline: none;
  border-color: #000000;
  box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.1);
}

.action-btn {
  padding: 0;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  cursor: pointer;
  font-size: 13px;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  background: #ffffff;
  color: #333333;
  flex-shrink: 0;
  box-sizing: border-box;
}

.action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  background: #f5f5f5;
  border-color: #000000;
  color: #000000;
}

.action-btn.view,
.action-btn.download,
.action-btn.edit,
.action-btn.assign {
  background: #000000;
  color: #ffffff;
  border-color: #000000;
}

.action-btn.view:hover,
.action-btn.download:hover,
.action-btn.edit:hover,
.action-btn.assign:hover {
  background: #333333;
}

/* Ensure icons are visible in action buttons - FontAwesome 6 */
.action-btn i {
  color: inherit !important;
  font-style: normal !important;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
  font-family: "Font Awesome 6 Free" !important;
  font-weight: 900 !important;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: auto;
  width: 32px;
  height: 32px;
  font-size: 13px;
}

.action-btn.view i,
.action-btn.download i,
.action-btn.edit i,
.action-btn.assign i {
  color: #ffffff !important;
}

.action-btn.delete i {
  color: #000000 !important;
}

.action-btn.delete:hover i {
  color: #ffffff !important;
}

/* FontAwesome 6 icon fixes with proper content codes */
.fa-eye::before { content: "\f06e"; }
.fa-key::before { content: "\f084"; }
.fa-edit::before { content: "\f044"; }
.fa-pen-to-square::before { content: "\f044"; }
.fa-trash::before { content: "\f2ed"; }
.fa-trash-can::before { content: "\f2ed"; }
.fa-download::before { content: "\f019"; }
.fa-user-plus::before { content: "\f234"; }

/* Additional icon visibility fixes */
.action-btn i.fas,
.action-btn i.fa-solid {
  font-family: "Font Awesome 6 Free" !important;
  font-weight: 900 !important;
}

/* Ensure proper spacing between table elements */
.management-table td > * {
  margin: 0 2px; /* Add small margin to all direct children of td */
}

/* Remove margin from action buttons container to align with header */
.management-table td:nth-child(5) > .action-buttons {
  margin: 0;
}

.management-table td .status-badge,
.management-table td .type-badge {
  margin: 2px 8px;
}

/* Specific spacing for badge combinations */
.management-table td .type-badge + .status-badge {
  margin-left: 12px; /* Extra space between type and status badges */
}

/* Fix any remaining alignment issues */
.management-table tbody tr {
  border-bottom: 1px solid #f5f5f5;
}

.management-table tbody tr:hover {
  background-color: #fafafa;
}

/* Ensure action buttons container has proper spacing */
.action-buttons > * {
  margin: 0 2px; /* Add margin between action elements */
}

/* Override margins for action buttons to align with header */
.action-buttons .action-btn:first-child {
  margin-left: 0;
}

.action-buttons .status-select {
  margin-left: 12px !important; /* Ensure dropdown has proper separation */
}

.action-btn.delete {
  background: #ffffff;
  color: #000000;
  border-color: #000000;
}

.action-btn.delete:hover {
  background: #000000;
  color: #ffffff;
}

/* Additional Styles */
.contract-number {
  font-weight: 500;
  color: #007bff;
}

.supplier-name {
  font-weight: 500;
  color: #495057;
}

.amount {
  font-weight: 600;
  color: #28a745;
}

.license-number {
  font-family: monospace;
  background: #f8f9fa;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
}

.company-name {
  font-weight: 500;
  color: #495057;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #6c757d;
}

.empty-state i {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-state h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  color: #495057;
}

.empty-state p {
  margin: 0;
  font-size: 14px;
}

/* Pagination Styles */
.management-pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 24px;
  padding: 28px;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
  position: relative;
  z-index: 1;
}

.pagination-btn {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px 20px;
  background: #000000;
  color: #ffffff;
  border: 1px solid #000000;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.pagination-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  background: #333333;
}

.pagination-btn:disabled {
  background: #f5f5f5;
  color: #666666;
  border-color: #e0e0e0;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.pagination-info {
  font-size: 14px;
  color: #000000;
  font-weight: 600;
  padding: 12px 20px;
  background: #f5f5f5;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
}

/* Enhanced Modal Styles */
.modal-overlay {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background: rgba(0, 0, 0, 0.95) !important;
  backdrop-filter: blur(20px) !important;
  -webkit-backdrop-filter: blur(20px) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  z-index: 9999999 !important;
  padding: 20px;
  animation: modalFadeIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  width: 100vw !important;
  height: 100vh !important;
  min-height: 100vh !important;
}

/* Specific Password Reset Modal Overlay */
.modal-overlay.password-reset-overlay {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background: rgba(0, 0, 0, 0.95) !important;
  backdrop-filter: blur(20px) !important;
  -webkit-backdrop-filter: blur(20px) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  z-index: 9999999 !important;
  padding: 0 !important;
  margin: 0 !important;
  transform: none !important;
  min-height: 100vh !important;
  height: 100vh !important;
  width: 100vw !important;
}

/* Override global app.css modal rules for password reset */
.modal-overlay.password-reset-overlay .password-reset-modal {
  margin: 0 !important;
  position: static !important;
  top: auto !important;
  left: auto !important;
  right: auto !important;
  bottom: auto !important;
  transform: none !important;
}

.modal-overlay.password-reset-overlay .password-reset-modal .modal-content {
  margin: 0 !important;
  padding: 24px !important;
  width: 100% !important;
  max-width: 100% !important;
}

/* Force true centering */
.modal-overlay.password-reset-overlay {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  flex-direction: row !important;
}

.modal-overlay.password-reset-overlay .password-reset-modal {
  position: static !important;
  top: auto !important;
  transform: none !important;
  margin: 0 !important;
}

/* Specific User Details Modal Overlay */
.modal-overlay.user-details-overlay {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background: rgba(0, 0, 0, 0.95) !important;
  backdrop-filter: blur(20px) !important;
  -webkit-backdrop-filter: blur(20px) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  z-index: 9999999 !important;
  padding: 0 !important;
  margin: 0 !important;
  transform: none !important;
  min-height: 100vh !important;
  height: 100vh !important;
  width: 100vw !important;
}

/* Force true centering for user details */
.modal-overlay.user-details-overlay {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  flex-direction: row !important;
}

.modal-overlay.user-details-overlay .modal-content {
  position: static !important;
  top: auto !important;
  transform: none !important;
  margin: 0 !important;
}

/* Override global app.css modal rules for user details */
.modal-overlay.user-details-overlay .modal-content.large {
  margin: 0 !important;
  padding: 0 !important;
  width: 90% !important;
  max-width: 1000px !important;
  max-height: 90vh !important;
  position: static !important;
  top: auto !important;
  left: auto !important;
  right: auto !important;
  bottom: auto !important;
  transform: none !important;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    backdrop-filter: blur(0px);
  }
  to {
    opacity: 1;
    backdrop-filter: blur(8px);
  }
}

.modal-content {
  background: #ffffff;
  border-radius: 16px;
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(0, 0, 0, 0.05);
  border: none;
  max-width: 500px;
  width: 100%;
  max-height: 90vh;
  overflow: hidden;
  animation: modalSlideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  flex-direction: column;
}

@keyframes modalSlideIn {
  from {
    transform: translateY(20px) scale(0.95);
    opacity: 0;
  }
  to {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
}

.modal-content.large {
  max-width: 1100px;
  min-height: 600px;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 32px;
  border-bottom: 1px solid #e5e7eb;
  background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
  border-radius: 16px 16px 0 0;
  position: relative;
  flex-shrink: 0;
}

.modal-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, #000000 50%, transparent 100%);
}

.modal-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 700;
  color: #111827;
  display: flex;
  align-items: center;
  gap: 12px;
}

.modal-header h3::before {
  content: '\f007';
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  color: #6b7280;
  font-size: 18px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #ffffff;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.modal-close {
  background: #ffffff;
  border: 1px solid #d1d5db;
  font-size: 16px;
  color: #6b7280;
  cursor: pointer;
  padding: 0;
  border-radius: 8px;
  transition: all 0.2s ease;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.modal-close:hover {
  background: #f3f4f6;
  color: #374151;
  border-color: #9ca3af;
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.modal-body {
  padding: 32px;
  background: #ffffff;
  min-height: 400px;
  overflow-y: auto;
  flex: 1;
}

/* Custom Scrollbar for Modal */
.modal-body::-webkit-scrollbar {
  width: 8px;
}

.modal-body::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

.modal-body::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
  transition: background 0.2s ease;
}

.modal-body::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Firefox scrollbar */
.modal-body {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
}

.modal-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  margin-top: 20px;
}

.btn-cancel,
.btn-confirm {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-cancel {
  background: #f8f9fa;
  color: #495057;
  border: 1px solid #dee2e6;
}

.btn-cancel:hover {
  background: #e9ecef;
}

.btn-confirm {
  background: #dc3545;
  color: white;
}

.btn-confirm:hover {
  background: #c82333;
}

/* Enhanced User Details Modal Styles */
.user-details-content {
  display: flex;
  flex-direction: column;
  gap: 32px;
  padding: 8px;
}

/* User Profile Header */
.user-profile-header {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 12px;
  padding: 28px;
  margin-bottom: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  position: relative;
}

.user-profile-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #111827 0%, #374151 50%, #111827 100%);
  border-radius: 12px 12px 0 0;
}

.profile-avatar {
  display: flex;
  align-items: center;
  gap: 24px;
}

.user-icon.large {
  width: 72px;
  height: 72px;
  font-size: 28px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 3px solid #ffffff;
  background: linear-gradient(135deg, #111827 0%, #374151 100%);
}

.profile-info h2 {
  margin: 0 0 6px 0;
  font-size: 24px;
  font-weight: 700;
  color: #111827;
  line-height: 1.2;
}

.profile-email {
  margin: 0 0 16px 0;
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  background: #f3f4f6;
  padding: 4px 8px;
  border-radius: 4px;
  display: inline-block;
}

.profile-badges {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.details-section {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  position: relative;
}

.details-section:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
  border-color: #d1d5db;
}

.details-section h4 {
  margin: 0 0 20px 0;
  font-size: 16px;
  font-weight: 700;
  color: #111827;
  display: flex;
  align-items: center;
  gap: 10px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e5e7eb;
  position: relative;
}

.details-section h4::before {
  content: '';
  width: 3px;
  height: 20px;
  background: linear-gradient(135deg, #111827 0%, #374151 100%);
  border-radius: 2px;
}

.details-section h4::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 40px;
  height: 2px;
  background: #111827;
  border-radius: 1px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.btn-view-all {
  background: #007bff;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-view-all:hover {
  background: #0056b3;
}

.details-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 6px;
  padding: 16px;
  background: #f9fafb;
  border-radius: 6px;
  border-left: 3px solid #e5e7eb;
  transition: all 0.2s ease;
  position: relative;
}

.detail-item:hover {
  background: #f3f4f6;
  border-left-color: #111827;
  transform: translateX(3px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.detail-item label {
  font-size: 11px;
  font-weight: 700;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.8px;
  margin-bottom: 2px;
}

.detail-item span {
  font-size: 14px;
  color: #111827;
  font-weight: 600;
  line-height: 1.4;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 15px;
}

.stat-card.mini {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  text-align: center;
  border: 1px solid #dee2e6;
}

.stat-card.mini .stat-value {
  display: block;
  font-size: 20px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 4px;
}

.stat-card.mini .stat-label {
  font-size: 12px;
  color: #6c757d;
}

.activities-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-height: 350px;
  overflow-y: auto;
  padding: 4px;
  padding-right: 8px;
}

/* Custom Scrollbar for Activities */
.activities-list::-webkit-scrollbar {
  width: 6px;
}

.activities-list::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.activities-list::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
  transition: background 0.2s ease;
}

.activities-list::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Firefox scrollbar */
.activities-list {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
}

/* Password Reset Modal Styles */
.password-reset-modal {
  background: white !important;
  border-radius: 12px !important;
  width: 90% !important;
  max-width: 600px !important;
  max-height: 80vh !important;
  overflow: hidden !important;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2) !important;
  animation: modalSlideIn 0.3s ease-out !important;
  position: static !important;
  margin: 0 !important;
  display: flex !important;
  flex-direction: column !important;
  transform: none !important;
  top: auto !important;
  left: auto !important;
  right: auto !important;
  bottom: auto !important;
  flex-shrink: 0 !important;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.password-reset-modal .modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #dee2e6;
  background: #f8f9fa;
}

.password-reset-modal .modal-header h2 {
  margin: 0;
  color: #212529;
  font-size: 20px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 10px;
}

.password-reset-modal .modal-header h2 i {
  color: #6c757d;
  font-size: 18px;
}

.password-reset-modal .close-btn {
  background: none;
  border: none;
  font-size: 18px;
  color: #6c757d;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: color 0.2s ease;
}

.password-reset-modal .close-btn:hover {
  color: #495057;
}

.password-reset-modal .modal-content {
  padding: 24px;
  overflow-y: auto;
  max-height: calc(90vh - 80px);
}

.password-reset-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
  width: 100%;
  max-width: 100%;
}

.reset-header {
  text-align: center;
  padding-bottom: 20px;
  border-bottom: 1px solid #e9ecef;
}

.reset-header .reset-icon {
  font-size: 48px;
  color: #28a745;
  margin-bottom: 16px;
}

.reset-header h3 {
  color: #212529;
  font-size: 22px;
  margin-bottom: 12px;
  font-weight: 600;
}

.reset-header .reset-description {
  color: #6c757d;
  font-size: 15px;
  line-height: 1.5;
  margin: 0;
}

.reset-features {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin: 20px 0;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  transition: all 0.2s ease;
}

.feature-item:hover {
  background: #e9ecef;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.feature-icon {
  background: #fff;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
}

.feature-icon i {
  color: #28a745;
  font-size: 16px;
}

.feature-content h4 {
  margin: 0 0 4px 0;
  color: #212529;
  font-size: 14px;
  font-weight: 600;
}

.feature-content p {
  margin: 0;
  color: #6c757d;
  font-size: 13px;
  line-height: 1.4;
}

.warning-notice {
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
  border: 1px solid #ffc107;
  border-radius: 8px;
  padding: 16px;
  display: flex;
  align-items: flex-start;
  gap: 12px;
  box-shadow: 0 2px 8px rgba(255, 193, 7, 0.2);
}

.warning-icon {
  background: #fff;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.warning-icon i {
  color: #dc3545;
  font-size: 16px;
}

.warning-content h4 {
  margin: 0 0 6px 0;
  color: #856404;
  font-size: 14px;
  font-weight: 600;
}

.warning-content p {
  margin: 0;
  color: #856404;
  font-size: 13px;
  line-height: 1.4;
}

.modal-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  margin-top: 24px;
}

.btn-secondary {
  padding: 12px 24px;
  background: #f8f9fa;
  color: #6c757d;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.btn-secondary:hover {
  background: #e9ecef;
  color: #495057;
  border-color: #adb5bd;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.btn-primary {
  padding: 12px 24px;
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: #fff;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
}

.btn-primary:hover {
  background: linear-gradient(135deg, #218838 0%, #1e7e34 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.4);
}

.btn-primary:disabled {
  background: #6c757d;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.btn-primary:disabled:hover {
  background: #6c757d;
  transform: none;
  box-shadow: none;
}

.btn-cancel,
.btn-confirm {
  padding: 10px 20px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  border: none;
  min-width: 120px;
  justify-content: center;
}

.btn-cancel {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.btn-cancel:hover {
  background: #e5e7eb;
  border-color: #9ca3af;
}

.btn-confirm {
  background: #3b82f6;
  color: #ffffff;
  border: 1px solid #3b82f6;
}

.btn-confirm:hover:not(:disabled) {
  background: #2563eb;
  border-color: #2563eb;
}

.btn-confirm:disabled {
  background: #9ca3af;
  border-color: #9ca3af;
  cursor: not-allowed;
  opacity: 0.6;
}

.activity-item {
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  background: #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  position: relative;
}

.activity-item:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.activity-item.high {
  border-left: 4px solid #ffc107;
  background: linear-gradient(135deg, #ffffff 0%, #fffbf0 100%);
}

.activity-item.critical {
  border-left: 4px solid #dc3545;
  background: linear-gradient(135deg, #ffffff 0%, #fff5f5 100%);
}

.activity-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.activity-type {
  font-size: 11px;
  font-weight: 700;
  color: #666666;
  text-transform: uppercase;
  letter-spacing: 1px;
  background: #f0f0f0;
  padding: 4px 8px;
  border-radius: 4px;
  display: inline-block;
  width: fit-content;
}

.activity-content p {
  margin: 0;
  font-size: 15px;
  color: #000000;
  font-weight: 500;
  line-height: 1.5;
}

.activity-time {
  font-size: 12px;
  color: #666666;
  font-weight: 600;
  margin-top: 8px;
}

.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px;
}

/* Quote Management Specific Styles */
.energy-type {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #495057;
}

.energy-type i {
  color: #007bff;
}

.priority-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-transform: capitalize;
}

.priority-badge.low {
  background: #d1ecf1;
  color: #0c5460;
}

.priority-badge.normal {
  background: #d4edda;
  color: #155724;
}

.priority-badge.high {
  background: #fff3cd;
  color: #856404;
}

.priority-badge.urgent {
  background: #f8d7da;
  color: #721c24;
}

.quotes-count {
  font-weight: 500;
  color: #007bff;
}

.status-badge.processing {
  background: #cce5ff;
  color: #004085;
}

.status-badge.sent {
  background: #d1ecf1;
  color: #0c5460;
}

/* Document Templates Styles */
.header-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

.btn-primary {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-primary:hover {
  background: #0056b3;
}

.templates-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.template-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
  transition: all 0.2s ease;
  border: 2px solid transparent;
}

.template-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.template-card.inactive {
  opacity: 0.6;
  border-color: #dee2e6;
}

.template-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.template-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #007bff;
  font-size: 20px;
}

.toggle-switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.4s;
  border-radius: 24px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.4s;
  border-radius: 50%;
}

input:checked + .toggle-slider {
  background-color: #007bff;
}

input:checked + .toggle-slider:before {
  transform: translateX(26px);
}

.template-content h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.template-content p {
  margin: 0 0 15px 0;
  color: #6c757d;
  font-size: 14px;
  line-height: 1.4;
}

.template-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.type-badge.comparison {
  background: #cce5ff;
  color: #004085;
}

.type-badge.mandate {
  background: #d4edda;
  color: #155724;
}

.type-badge.authorization {
  background: #fff3cd;
  color: #856404;
}

.type-badge.contract {
  background: #f8d7da;
  color: #721c24;
}

.usage-count {
  font-size: 12px;
  color: #6c757d;
  font-weight: 500;
}

.template-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 15px;
  border-top: 1px solid #dee2e6;
}

.last-modified {
  font-size: 12px;
  color: #6c757d;
}

.template-actions {
  display: flex;
  gap: 8px;
}

/* Settings Section */
.settings-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 25px;
  margin-bottom: 20px;
}

.settings-section h2 {
  margin: 0 0 20px 0;
  font-size: 20px;
  font-weight: 600;
  color: #2c3e50;
}

.settings-grid {
  display: grid;
  gap: 25px;
  margin-bottom: 25px;
}

.setting-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.setting-item label {
  font-size: 14px;
  font-weight: 500;
  color: #495057;
}

.file-upload-area {
  border: 2px dashed #dee2e6;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  background: #f8f9fa;
  transition: all 0.2s ease;
  cursor: pointer;
}

.file-upload-area:hover {
  border-color: #007bff;
  background: #f0f8ff;
}

.file-upload-area i {
  font-size: 24px;
  color: #6c757d;
  margin-bottom: 8px;
  display: block;
}

.file-upload-area span {
  display: block;
  color: #6c757d;
  margin-bottom: 10px;
}

.btn-upload {
  background: #007bff;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
}

.setting-item textarea {
  padding: 12px;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  font-size: 14px;
  resize: vertical;
  font-family: inherit;
}

.formula-settings {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 15px;
}

.formula-item {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.formula-item label {
  font-size: 12px;
  font-weight: 500;
  color: #6c757d;
}

.formula-item input,
.formula-item select {
  padding: 8px 12px;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  font-size: 14px;
}

.settings-actions {
  display: flex;
  justify-content: flex-end;
}

.btn-save {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  background: #28a745;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-save:hover {
  background: #1e7e34;
}

/* Upload Form */
.upload-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.form-group label {
  font-size: 14px;
  font-weight: 500;
  color: #495057;
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 10px 12px;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  font-size: 14px;
  font-family: inherit;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

/* Notification Management Styles */
.notification-settings-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 25px;
}

.setting-card {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 20px;
}

.setting-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.setting-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.setting-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.setting-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.setting-row label {
  font-size: 14px;
  color: #495057;
  font-weight: 500;
}

.setting-row input,
.setting-row select {
  padding: 6px 10px;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  font-size: 14px;
  width: 120px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: white;
  border-bottom: 1px solid #dee2e6;
}

.table-header h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.notification-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.notification-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #e9ecef;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6c757d;
  font-size: 16px;
}

.notification-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.notification-title {
  font-weight: 500;
  color: #2c3e50;
  font-size: 14px;
}

.notification-message {
  font-size: 12px;
  color: #7f8c8d;
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.type-badge.notification {
  background: #d1ecf1;
  color: #0c5460;
}

.recipients-count {
  font-weight: 500;
  color: #007bff;
}

.delivery-stats {
  display: flex;
  gap: 10px;
  align-items: center;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 500;
}

.stat-item.success {
  color: #28a745;
}

.stat-item.failed {
  color: #dc3545;
}

/* Responsive Design */

/* Desktop with sidebar considerations */
@media (min-width: 1200px) {
  .management-table {
    min-width: 800px;
  }

  /* For tables with more columns (contracts = 8 columns) */
  .management-table th:nth-child(8) ~ th,
  .management-table td:nth-child(8) ~ td {
    min-width: 100px; /* Ensure 8+ column tables have adequate space */
  }
}

@media (min-width: 992px) and (max-width: 1199px) {
  .management-table {
    min-width: 750px;
  }

  .management-table th,
  .management-table td {
    padding: 16px 12px; /* Reduce padding for medium screens */
  }

  /* For tables with 8+ columns, increase minimum width */
  .management-table th:nth-child(8),
  .management-table td:nth-child(8) {
    min-width: 90px;
  }

  /* Adjust action buttons for medium screens */
  .action-buttons {
    min-width: 180px;
  }

  .action-btn {
    min-width: 32px;
    min-height: 32px;
    padding: 6px 8px;
  }

  .status-select {
    min-width: 70px;
    max-width: 90px;
  }

  /* Special handling for broker tables on medium screens */
  .management-table tbody tr td:nth-child(7) .action-buttons {
    min-width: 200px;
    gap: 3px;
  }

  .management-table tbody tr td:nth-child(7) .action-btn {
    min-width: 28px;
    min-height: 28px;
    padding: 4px 6px;
    font-size: 11px;
  }
}

/* Tablet and small desktop */
@media (min-width: 769px) and (max-width: 991px) {
  .admin-management-container {
    padding: 20px;
  }

  .management-table {
    min-width: 900px; /* Increased for better action column spacing */
  }

  .management-table th,
  .management-table td {
    padding: 16px 10px;
  }

  .action-buttons {
    gap: 4px;
    min-width: 160px;
  }

  .action-btn {
    min-width: 30px;
    min-height: 30px;
    padding: 6px 8px;
    font-size: 12px;
  }

  .status-select {
    min-width: 65px;
    max-width: 80px;
    font-size: 10px;
    padding: 4px 6px;
  }

  /* Special handling for broker tables on tablet */
  .management-table tbody tr td:nth-child(7) .action-buttons {
    min-width: 180px;
    gap: 2px;
    flex-wrap: wrap;
  }

  .management-table tbody tr td:nth-child(7) .action-btn {
    min-width: 26px;
    min-height: 26px;
    padding: 3px 5px;
    font-size: 10px;
  }

  .management-table tbody tr td:nth-child(7) .status-select {
    min-width: 55px;
    max-width: 70px;
    font-size: 9px;
  }
}

/* Mobile */
@media (max-width: 768px) {
  .admin-management-container {
    padding: 15px;
  }

  .management-header {
    flex-direction: column;
    gap: 15px;
  }

  .management-filters {
    flex-direction: column;
    gap: 15px;
  }

  .filter-group {
    min-width: auto;
  }

  .management-table-container {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    border-radius: 8px;
  }

  .management-table {
    min-width: 850px; /* Increased minimum width for mobile to accommodate actions */
  }

  .management-table th,
  .management-table td {
    padding: 12px 6px;
    font-size: 13px;
  }

  .action-buttons {
    gap: 3px;
    min-width: 140px;
    flex-wrap: wrap;
  }

  .action-btn {
    min-width: 28px;
    min-height: 28px;
    padding: 4px 6px;
    font-size: 11px;
  }

  .status-select {
    min-width: 60px;
    max-width: 75px;
    font-size: 10px;
    padding: 3px 5px;
  }

  /* Special handling for broker tables on mobile */
  .management-table tbody tr td:nth-child(7) .action-buttons {
    min-width: 160px;
    gap: 2px;
    flex-wrap: wrap;
  }

  .management-table tbody tr td:nth-child(7) .action-btn {
    min-width: 24px;
    min-height: 24px;
    padding: 2px 4px;
    font-size: 9px;
  }

  .management-table tbody tr td:nth-child(7) .status-select {
    min-width: 50px;
    max-width: 65px;
    font-size: 8px;
    padding: 2px 4px;
  }

  .pagination-btn {
    padding: 8px 12px;
    font-size: 12px;
  }

  .modal-content {
    margin: 0 !important;
    max-width: none;
    width: calc(100% - 20px);
    position: static !important;
    top: auto !important;
    left: auto !important;
    right: auto !important;
    bottom: auto !important;
  }

  .modal-content.large {
    margin: 0 !important;
    max-width: none;
    width: calc(100% - 20px);
    position: static !important;
    top: auto !important;
    left: auto !important;
    right: auto !important;
    bottom: auto !important;
  }

  .password-reset-modal {
    margin: 0 !important;
    max-width: none;
    width: calc(100% - 20px);
    position: static !important;
    top: auto !important;
    left: auto !important;
    right: auto !important;
    bottom: auto !important;
  }

  .password-reset-modal .modal-header {
    padding: 16px 20px;
  }

  .password-reset-modal .modal-header h2 {
    font-size: 18px;
  }

  .password-reset-modal .modal-content {
    padding: 20px;
  }

  .reset-features {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .feature-item {
    padding: 12px;
  }

  .modal-actions {
    flex-direction: column;
    gap: 8px;
  }

  .btn-secondary,
  .btn-primary {
    width: 100%;
    justify-content: center;
  }

  .details-grid {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  }
}

/* Small mobile */
@media (max-width: 480px) {
  .admin-management-container {
    padding: 10px;
  }

  .management-table {
    min-width: 600px;
  }

  .management-table th,
  .management-table td {
    padding: 10px 6px;
    font-size: 12px;
  }

  .user-info {
    gap: 8px;
  }

  .user-icon {
    width: 36px;
    height: 36px;
    font-size: 14px;
  }

  .action-buttons {
    gap: 2px;
    min-width: 100px;
  }

  .action-btn {
    min-width: 28px;
    min-height: 28px;
    padding: 4px 6px;
    font-size: 11px;
  }
}
