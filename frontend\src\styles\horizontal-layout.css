/* Horizontal Layout Styles */

/* CSS Variables */
:root {
  --primary-black: #000000;
  --primary-white: #ffffff;
  --border-color: #e0e0e0;
  --text-color: #333333;
  --input-height: 48px;
  --button-height: 48px;
  --error-color: #ff3b30;
}

/* Full-screen container */
.fullscreen-container {
  width: 100%;
  min-height: calc(100vh - 140px); /* Account for header and footer */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0; /* Remove vertical padding */
  max-width: 100%;
  margin: 0 auto;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  background-image:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.02) 0%, transparent 50%);
}

/* Content wrapper */
.content-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center; /* Center content vertically */
  width: 95%; /* Wider container */
  max-width: 1200px;
  padding: 3rem 1.5rem 2rem 1.5rem; /* Reduced padding at the bottom */
  margin: 0; /* Remove margin */
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 0; /* Remove border radius */
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  min-height: calc(100vh - 140px); /* Make it fill the available height */
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Page title styles */
.page-title {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.75rem;
  color: #000;
  text-align: center;
  position: relative;
  padding-bottom: 0.75rem;
}

.page-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, #000, #333);
  border-radius: 3px;
}

.page-subtitle {
  font-size: 1.1rem;
  color: #555;
  margin-bottom: 3rem;
  max-width: 700px;
  text-align: center;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
}

/* Horizontal card layout */
.horizontal-cards {
  display: flex;
  flex-wrap: nowrap;
  justify-content: center;
  gap: 2rem;
  width: 100%;
  max-width: 800px; /* Limit maximum width for better proportions */
  margin: 0 auto;
  padding: 0 1rem;
}

/* Card styles */
.card {
  background-color: white;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.06);
  padding: 2rem 1.5rem;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  border: 2px solid #f0f0f0;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  flex: 0 0 auto;
  width: 280px; /* Fixed width for better proportions */
  max-width: 280px;
  margin: 0;
  min-height: 280px;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.card:hover {
  border-color: #000;
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.12);
  transform: translateY(-8px) scale(1.02);
}

.card.selected {
  border-color: #000;
  border-width: 2px;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.02) 0%, rgba(0, 0, 0, 0.05) 100%);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  transform: translateY(-4px);
}

.card:hover::after, .card.selected::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, #000, #333, #000);
  border-radius: 0 0 16px 16px;
}

.card-icon {
  margin-bottom: 1.5rem;
  width: 80px;
  height: 80px;
  border-radius: 20px;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.06) 0%, rgba(0, 0, 0, 0.12) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
  margin-top: 0.5rem;
  position: relative;
  overflow: hidden;
}

.card-icon::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 50%);
  border-radius: 20px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.card-icon svg {
  width: 36px;
  height: 36px;
  color: #000;
  z-index: 1;
  position: relative;
}

.card:hover .card-icon {
  transform: scale(1.1) rotate(5deg);
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.12) 0%, rgba(0, 0, 0, 0.18) 100%);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
}

.card:hover .card-icon::before {
  opacity: 1;
}

.card.selected .card-icon {
  background: linear-gradient(135deg, #000 0%, #333 100%);
  color: white;
  transform: scale(1.15);
  box-shadow: 0 16px 40px rgba(0, 0, 0, 0.25);
}

.card.selected .card-icon svg {
  color: white;
}

.card-title {
  font-size: 1.4rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: #1a1a1a;
  transition: all 0.3s ease;
  letter-spacing: -0.02em;
}

.card:hover .card-title {
  color: #000;
  transform: translateY(-2px);
}

.card.selected .card-title {
  color: #000;
  transform: translateY(-2px);
}

.card-description {
  font-size: 0.95rem;
  color: #666;
  margin-bottom: 0;
  flex-grow: 1;
  line-height: 1.6;
  max-width: 100%;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  transition: all 0.3s ease;
  font-weight: 400;
}

.card:hover .card-description {
  color: #444;
}

.card.selected .card-description {
  color: #444;
}

/* Action button container */
.action-container {
  display: flex;
  justify-content: center;
  padding: 0.5rem 0;
  max-width: 1000px;
  margin: 3.5rem auto 0;
}

.action-button {
  background-color: #000;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0.8rem 2rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 180px;
  letter-spacing: 0.5px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  position: relative;
  overflow: hidden;
}

.action-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: all 0.6s ease;
}

.action-button:hover {
  background-color: #333;
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.3);
  transform: translateY(-2px);
}

.action-button:hover::before {
  left: 100%;
}

.action-button:disabled {
  background-color: #a0a0a0;
  cursor: not-allowed;
  box-shadow: none;
  transform: none;
}

/* Error message */
.error-container {
  margin-bottom: 1rem;
  color: var(--error-color);
  font-size: 0.95rem;
}

/* Loading overlay */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.loading-content {
  background-color: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.loading-spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-left-color: #000;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Wizard fullscreen styles */
.wizard-fullscreen {
  width: 100%;
  max-width: 100%;
  margin: 0 auto 2rem auto; /* Added bottom margin for better spacing */
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  padding: 2rem;
  border: 1px solid #e0e0e0;
}

.wizard-fullscreen .wizard-container {
  box-shadow: none;
  max-width: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
}

.wizard-fullscreen .wizard-content {
  padding: 1rem 0;
  display: flex;
  width: 100%;
}

.wizard-fullscreen .form-group {
  margin-bottom: 1.25rem;
  width: 100%;
}

.wizard-fullscreen .form-row {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  justify-content: space-between;
}

.wizard-fullscreen .form-group-half {
  flex: 0 0 48%;
  width: 48%;
  margin-bottom: 1.25rem;
  display: inline-block;
}

.wizard-fullscreen .form-group {
  flex: 0 0 100%;
  width: 100%;
}

.wizard-fullscreen .form-input {
  width: 100%;
  height: var(--input-height);
  padding: 0 20px 0 40px;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  font-size: 0.95rem;
  background-color: var(--primary-white);
  color: var(--text-color);
  transition: all 0.3s ease;
  margin-bottom: 0.5rem;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
}

.wizard-fullscreen .input-with-icon {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
}

.wizard-fullscreen .input-icon {
  position: absolute !important;
  left: 16px !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  color: #666 !important;
  z-index: 10 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 18px !important;
  height: 18px !important;
  pointer-events: none !important;
  transition: all 0.3s ease !important;
}

.wizard-fullscreen label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  font-size: 0.95rem;
}

.wizard-fullscreen .wizard-title {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: #000;
  width: 100%;
  flex: 0 0 100%;
}

.wizard-fullscreen .required {
  color: #ff3b30;
}

.wizard-fullscreen .btn {
  height: var(--button-height);
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 0 1.5rem;
}

.wizard-fullscreen .btn-primary {
  background-color: var(--primary-black) !important;
  color: var(--primary-white) !important;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15) !important;
  background-image: none !important;
  border: 1px solid var(--primary-black) !important;
}

.wizard-fullscreen .btn-primary:hover {
  background-color: #333333 !important;
  color: var(--primary-white) !important;
  border-color: #333333 !important;
}

.wizard-fullscreen .btn-primary:focus,
.wizard-fullscreen .btn-primary:active {
  background-color: var(--primary-black) !important;
  color: var(--primary-white) !important;
  border-color: var(--primary-black) !important;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15) !important;
}

.wizard-fullscreen .btn-secondary {
  background-color: #f5f5f5;
  color: #333;
  border: 1px solid #ddd;
}

.wizard-fullscreen .wizard-progress-container {
  background-color: transparent;
  border-bottom: 1px solid #f0f0f0;
  padding: 0 0 1rem 0;
  margin-bottom: 1rem;
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

.wizard-fullscreen .wizard-progress {
  display: flex;
  justify-content: space-between;
  position: relative;
  z-index: 1;
  min-width: 500px;
  margin: 0 auto;
}

.wizard-fullscreen .progress-bar-container {
  position: absolute;
  top: 50%;
  left: 15%;
  right: 15%;
  height: 3px;
  background-color: #e0e0e0;
  z-index: 1;
  transform: translateY(-50%);
}

.wizard-fullscreen .wizard-progress-bar {
  height: 4px;
  background-color: #f0f0f0;
}

.wizard-fullscreen .wizard-progress-bar-inner {
  background-color: #000;
}

.wizard-fullscreen .wizard-step {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  width: 100%;
  margin-bottom: 1rem;
}

.wizard-fullscreen .wizard-step form {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  width: 100%;
}

.wizard-fullscreen .wizard-step-content {
  padding: 0.75rem 0 1rem;
}

.wizard-fullscreen .wizard-buttons {
  padding: 0.75rem 0 0;
  display: flex;
  justify-content: space-between;
  gap: 1rem;
  border-top: 1px solid #f0f0f0;
  padding-top: 1rem;
  width: 100%;
  margin-top: 1rem;
  order: 999;
}

.wizard-fullscreen .wizard-buttons button {
  min-width: 100px;
  padding: 0.6rem 1.2rem;
  font-size: 0.9rem;
}

/* Responsive adjustments */
@media (max-width: 992px) {
  .horizontal-cards {
    flex-wrap: wrap;
    gap: 1.5rem;
    justify-content: center;
    padding: 0 1rem;
    width: 100%;
    max-width: 600px;
  }

  .card {
    width: 260px;
    max-width: 260px;
    margin: 0;
    min-height: 260px;
  }
}

@media (max-width: 768px) {
  .fullscreen-container {
    padding: 0;
    justify-content: center;
  }

  .content-wrapper {
    padding: 2.5rem 1.5rem 1.5rem 1.5rem; /* Reduced bottom padding */
    width: 95%;
    margin: 0;
    min-height: calc(100vh - 140px);
  }

  .page-title {
    font-size: 1.6rem;
    padding-bottom: 0.6rem;
  }

  .page-title::after {
    width: 50px;
    height: 3px;
  }

  .page-subtitle {
    font-size: 1rem;
    margin-bottom: 2rem;
    padding: 0 1rem;
  }

  .horizontal-cards {
    flex-direction: column;
    align-items: center;
    gap: 1.5rem;
    padding: 0 1rem;
    width: 100%;
    max-width: 400px;
  }

  .card {
    width: 100%;
    max-width: 320px;
    margin: 0;
    min-height: 240px;
    padding: 1.5rem 1.25rem;
  }

  .card-icon {
    width: 70px;
    height: 70px;
    margin-bottom: 1.25rem;
    border-radius: 18px;
  }

  .card-icon svg {
    width: 32px;
    height: 32px;
  }

  .card-title {
    font-size: 1.2rem;
    margin-bottom: 0.75rem;
  }

  .card-description {
    font-size: 0.9rem;
    -webkit-line-clamp: 3;
    line-height: 1.5;
  }

  .action-container {
    justify-content: center;
    margin-top: 1rem;
  }

  .action-button {
    width: 100%;
  }

  .wizard-fullscreen {
    padding: 1rem;
    max-width: 100%;
  }

  .wizard-fullscreen .wizard-progress-container {
    padding: 0 0 0.75rem 0;
    margin-bottom: 0.75rem;
  }

  .wizard-fullscreen .wizard-progress {
    min-width: 450px;
  }

  .wizard-fullscreen .progress-bar-container {
    left: 15%;
    right: 15%;
    top: 50%;
  }

  .wizard-fullscreen .wizard-step {
    width: 100%;
    margin-bottom: 0.75rem;
  }

  .wizard-fullscreen .wizard-step form {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
  }

  .wizard-fullscreen .form-input {
    height: 44px;
    font-size: 0.9rem;
  }

  .wizard-fullscreen .form-group-half {
    flex: 0 0 48%;
    width: 48%;
  }

  .wizard-fullscreen .wizard-step-content {
    padding: 0.5rem 0 0.75rem;
  }

  .wizard-fullscreen .wizard-buttons {
    flex-direction: column;
    padding-top: 0.75rem;
    gap: 0.5rem;
  }

  .wizard-fullscreen .wizard-buttons button {
    width: 100%;
    margin-bottom: 0;
    padding: 0.5rem 1rem;
  }
}

@media (max-width: 576px) {
  .fullscreen-container {
    padding: 0;
  }

  .content-wrapper {
    padding: 2rem 1.5rem 1rem 1.5rem; /* Reduced bottom padding */
    width: 90%;
    min-height: calc(100vh - 140px);
  }

  .horizontal-cards {
    flex-direction: column;
    align-items: center;
    padding: 0 0.5rem;
    width: 100%;
    max-width: 350px;
    gap: 1.25rem;
  }

  .card {
    width: 100%;
    max-width: 300px;
    margin: 0;
    min-height: 220px;
    padding: 1.25rem 1rem;
  }

  .card-icon {
    width: 60px;
    height: 60px;
    margin-bottom: 1rem;
    border-radius: 16px;
  }

  .card-icon svg {
    width: 28px;
    height: 28px;
  }

  .card-title {
    font-size: 1.1rem;
    margin-bottom: 0.6rem;
  }

  .card-description {
    font-size: 0.85rem;
    -webkit-line-clamp: 3;
    line-height: 1.4;
  }

  .wizard-fullscreen {
    padding: 0.75rem;
  }

  .wizard-fullscreen .wizard-progress-container {
    padding: 0 0 0.5rem 0;
    margin-bottom: 0.5rem;
  }

  .wizard-fullscreen .wizard-progress {
    min-width: 400px;
  }

  .wizard-fullscreen .progress-bar-container {
    left: 15%;
    right: 15%;
    top: 50%;
  }

  .wizard-fullscreen .wizard-step {
    width: 100%;
    margin-bottom: 0.5rem;
  }

  .wizard-fullscreen .wizard-step form {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
  }

  .wizard-fullscreen .form-input {
    height: 42px;
    font-size: 0.85rem;
  }

  .wizard-fullscreen label {
    font-size: 0.9rem;
  }

  .wizard-fullscreen .form-group-half {
    flex: 0 0 100%;
    width: 100%;
  }
}

@media (max-width: 320px) {
  .fullscreen-container {
    padding: 0;
  }

  .content-wrapper {
    padding: 1.5rem 1rem 0.75rem 1rem; /* Reduced bottom padding */
    width: 100%;
  }

  .horizontal-cards {
    width: 100%;
    padding: 0 0.5rem;
  }

  .page-title {
    font-size: 1.2rem;
  }

  .page-subtitle {
    font-size: 0.85rem;
    margin-bottom: 1rem;
  }

  .card {
    padding: 1rem 0.75rem;
  }

  .card-icon {
    width: 40px;
    height: 40px;
  }

  .card-icon svg {
    width: 20px;
    height: 20px;
  }

  .wizard-fullscreen {
    padding: 0.5rem;
  }

  .wizard-fullscreen .wizard-progress-container {
    padding: 0 0 0.4rem 0;
    margin-bottom: 0.4rem;
  }

  .wizard-fullscreen .wizard-progress {
    min-width: 350px;
  }

  .wizard-fullscreen .progress-bar-container {
    left: 15%;
    right: 15%;
    top: 50%;
  }

  .wizard-fullscreen .wizard-step {
    width: 100%;
    margin-bottom: 0.4rem;
  }

  .wizard-fullscreen .wizard-step form {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
  }

  .wizard-fullscreen .form-input {
    height: 40px;
    font-size: 0.8rem;
    padding: 0 15px 0 35px;
  }

  .wizard-fullscreen label {
    font-size: 0.85rem;
    margin-bottom: 0.3rem;
  }

  .wizard-fullscreen .input-icon {
    left: 10px;
  }

  .wizard-fullscreen .wizard-step-content {
    padding: 0.4rem 0 0.5rem;
  }

  .wizard-fullscreen .wizard-buttons {
    padding-top: 0.5rem;
  }

  .wizard-fullscreen .wizard-buttons button {
    padding: 0.4rem 0.75rem;
    font-size: 0.8rem;
  }
}
