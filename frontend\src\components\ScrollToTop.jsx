import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';

function ScrollToTop() {
  const { pathname } = useLocation();

  useEffect(() => {
    console.log('🔄 ScrollToTop: Route changed to:', pathname);

    // SUPER AGGRESSIVE scroll to top - try EVERY possible method
    const scrollToTopNow = () => {
      try {
        console.log('🚀 ScrollToTop: Executing all scroll methods...');

        // Method 1: Standard window.scrollTo
        window.scrollTo(0, 0);

        // Method 2: Direct DOM manipulation
        document.documentElement.scrollTop = 0;
        document.body.scrollTop = 0;

        // Method 3: Modern API with instant behavior
        try {
          window.scrollTo({ top: 0, left: 0, behavior: 'instant' });
        } catch (e) {
          window.scrollTo({ top: 0, left: 0, behavior: 'auto' });
        }

        // Method 4: Force scroll on html element
        const htmlElement = document.querySelector('html');
        if (htmlElement) {
          htmlElement.scrollTop = 0;
          htmlElement.style.scrollBehavior = 'auto';
        }

        // Method 5: Scroll ALL possible containers (SUPER comprehensive list)
        const containers = document.querySelectorAll(
          'main, .main-content, .content, .page-content, [role="main"], ' +
          '.home-container, .app-container, .page-container, .container, ' +
          '.wrapper, .fullscreen-container, .stepper-container, ' +
          '.auth-container, .dashboard-content, .authenticated-content, ' +
          '.authenticated-layout, .wizard-fullscreen, .content-wrapper, ' +
          '.upload-container, .invoice-upload-container, .form-container, ' +
          '.step-container, .wizard-container, .info-page-container, ' +
          'body, html, #root, .App, .router-outlet, .page-wrapper, ' +
          '.layout-container, .main-wrapper, .upload-first-invoice-page'
        );

        let scrolledCount = 0;
        containers.forEach((container, index) => {
          try {
            if (container.scrollTo) {
              container.scrollTo(0, 0);
              container.scrollTo({ top: 0, left: 0, behavior: 'instant' });
            } else {
              container.scrollTop = 0;
            }
            scrolledCount++;
          } catch (e) {
            console.warn('ScrollToTop: Error scrolling container:', e);
          }
        });

        console.log(`✅ ScrollToTop: Scrolled ${scrolledCount} containers`);

        // Method 6: Force reset with requestAnimationFrame
        requestAnimationFrame(() => {
          window.scrollTo(0, 0);
          document.documentElement.scrollTop = 0;
          document.body.scrollTop = 0;
        });

        // Method 7: Override any CSS scroll behavior
        document.documentElement.style.scrollBehavior = 'auto';
        document.body.style.scrollBehavior = 'auto';

        console.log('✅ ScrollToTop: All methods executed successfully');
      } catch (error) {
        console.error('❌ ScrollToTop: Error during scroll:', error);
      }
    };

    // Execute immediately
    scrollToTopNow();

    // Only one quick follow-up to handle async content, then stop
    setTimeout(scrollToTopNow, 50);     // After quick renders

    console.log('⏰ ScrollToTop: Executed immediately with one 50ms follow-up');
  }, [pathname]);

  return null;
}

export default ScrollToTop;
