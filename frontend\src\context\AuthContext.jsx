import { createContext, useContext, useState, useEffect } from 'react';
import { Auth } from 'aws-amplify';
import { STORAGE_KEYS, setItem, getItem, removeItem, clearUserData, clearAuthData, clearAll } from '../utils/localStorage';
import { showSuccessMessage, showErrorMessage } from '../utils/toastNotifications';
import logger from '../utils/logger';

// Create the context
const AuthContext = createContext(null);

// Custom hook to use the auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    logger.error('useAuth must be used within an AuthProvider');
    // Return a default object to prevent crashes
    return {
      currentUser: null,
      isAuthenticated: false,
      loading: false,
      signUp: async () => {},
      confirmSignUp: async () => {},
      signIn: async () => {},
      signOut: async () => {},
      forgotPassword: async () => {},
      resetPassword: async () => {},
      updateUserAttributes: async () => {}
    };
  }
  return context;
};

// Provider component
export const AuthProvider = ({ children }) => {
  const [currentUser, setCurrentUser] = useState(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [loading, setLoading] = useState(true);

  // Check authentication status on mount
  useEffect(() => {
    // Check auth state without clearing user data
    checkAuthState();
  }, []);

  // Check if user is authenticated
  const checkAuthState = async () => {
    try {
      // First check localStorage for tokens
      const idToken = getItem(STORAGE_KEYS.ID_TOKEN);
      const accessToken = getItem(STORAGE_KEYS.ACCESS_TOKEN);

      if (idToken && accessToken) {
        try {
          // Verify the token is still valid with Cognito
          const user = await Auth.currentAuthenticatedUser();
          setCurrentUser(user);
          setIsAuthenticated(true);
          setItem(STORAGE_KEYS.IS_AUTHENTICATED, 'true');
          logger.info('User is authenticated from tokens');

          // Store essential user data in localStorage
          try {
            // Get user attributes from Cognito
            const attributes = user.attributes || {};
            logger.debug('User attributes from Cognito:', attributes);

            // Store the Cognito ID
            if (attributes.sub) {
              setItem(STORAGE_KEYS.COGNITO_ID, attributes.sub);
              logger.debug('Cognito ID stored in localStorage:', attributes.sub);
            }

            // Store user type
            if (attributes['custom:userType']) {
              setItem(STORAGE_KEYS.USER_TYPE, attributes['custom:userType']);
              logger.debug('User type stored in localStorage:', attributes['custom:userType']);
            }

            // Store profile completion status
            const profileCompletion = attributes['custom:profileCompletion'];
            const profileComplete = attributes['custom:profileComplete'];
            const isProfileComplete = profileCompletion === 'true' || profileComplete === 'true';

            setItem(STORAGE_KEYS.PROFILE_COMPLETION, isProfileComplete.toString());
            logger.debug('Profile completion status stored in localStorage:', isProfileComplete);

            // Check if the user has completed the first-time flow
            // If they have a user type and profile is complete, they've gone through the initial flow
            const hasUserType = !!attributes['custom:userType'];
            const firstTimeFlowCompleted = hasUserType && isProfileComplete;

            // Store the first-time flow completion status
            if (firstTimeFlowCompleted) {
              localStorage.setItem('firstTimeFlowCompleted', 'true');
              logger.debug('First-time flow completion status stored in localStorage: true');
            }

            logger.debug('Profile completion attributes from Cognito:', {
              profileCompletion,
              profileComplete,
              isComplete: isProfileComplete,
              firstTimeFlowCompleted
            });

            // Store user data for form prepopulation
            const userData = {
              email: attributes.email,
              firstName: attributes.given_name || '',
              lastName: attributes.family_name || '',
              phoneNumber: attributes.phone_number || '',
              cognitoId: attributes.sub || ''
            };
            setItem(STORAGE_KEYS.USER_DATA, userData);
            logger.debug('User data stored in localStorage for form prepopulation');
          } catch (attributeError) {
            logger.error('Error processing user attributes:', attributeError);
          }
        } catch (tokenError) {
          // Token is invalid or expired
          logger.error('Token validation failed:', tokenError);

          // Only clear auth tokens, not user data
          clearAuthData();
          setCurrentUser(null);
          setIsAuthenticated(false);
        }
      } else {
        // No tokens found, but don't clear user data
        setCurrentUser(null);
        setIsAuthenticated(false);
        removeItem(STORAGE_KEYS.IS_AUTHENTICATED);
        logger.debug('No auth tokens found, but preserving user data');
      }
    } catch (error) {
      logger.error('Error checking auth state:', error);

      // Only clear auth tokens, not user data
      clearAuthData();
      setCurrentUser(null);
      setIsAuthenticated(false);
    } finally {
      setLoading(false);
    }
  };

  // Clear authentication state
  const clearAuthState = () => {
    setCurrentUser(null);
    setIsAuthenticated(false);

    // Only clear auth tokens, not user data
    clearAuthData();

    logger.debug('Auth state cleared, but user data preserved');
  };

  // Sign in function
  const signIn = async (username, password) => {
    try {
      // Only clear auth tokens, not user data
      // We'll skip clearAuthData() here to avoid errors when tokens don't exist
      // This is safe because we'll set new tokens below if sign-in is successful

      setLoading(true);
      const user = await Auth.signIn(username, password);

      // Store user in state
      setCurrentUser(user);
      setIsAuthenticated(true);

      // Store authentication state in localStorage
      if (user && user.signInUserSession) {
        const { idToken, accessToken, refreshToken } = user.signInUserSession;

        if (idToken) {
          setItem(STORAGE_KEYS.ID_TOKEN, idToken.jwtToken);
        }

        if (accessToken) {
          setItem(STORAGE_KEYS.ACCESS_TOKEN, accessToken.jwtToken);
        }

        if (refreshToken) {
          setItem(STORAGE_KEYS.REFRESH_TOKEN, refreshToken.token);
        }

        setItem(STORAGE_KEYS.IS_AUTHENTICATED, 'true');
        setItem(STORAGE_KEYS.VERIFIED, 'true'); // User is verified if they can sign in
        logger.debug('Authentication tokens stored in localStorage');

        // Store essential user data in localStorage
        try {
          // Get user attributes from Cognito
          const attributes = user.attributes || {};
          logger.debug('User attributes from Cognito:', attributes);

          // Store the Cognito ID
          if (attributes.sub) {
            setItem(STORAGE_KEYS.COGNITO_ID, attributes.sub);
            logger.debug('Cognito ID stored in localStorage:', attributes.sub);
          }

          // Store user type
          if (attributes['custom:userType']) {
            setItem(STORAGE_KEYS.USER_TYPE, attributes['custom:userType']);
            logger.debug('User type stored in localStorage:', attributes['custom:userType']);
          }

          // Store profile completion status
          const profileCompletion = attributes['custom:profileCompletion'];
          const profileComplete = attributes['custom:profileComplete'];
          const isProfileComplete = profileCompletion === 'true' || profileComplete === 'true';

          setItem(STORAGE_KEYS.PROFILE_COMPLETION, isProfileComplete.toString());
          logger.debug('Profile completion status stored in localStorage:', isProfileComplete);

          // Check if the user has completed the first-time flow
          // If they have a user type and profile is complete, they've gone through the initial flow
          const hasUserType = !!attributes['custom:userType'];
          const firstTimeFlowCompleted = hasUserType && isProfileComplete;

          // Store the first-time flow completion status
          if (firstTimeFlowCompleted) {
            localStorage.setItem('firstTimeFlowCompleted', 'true');
            logger.debug('First-time flow completion status stored in localStorage: true');
          }

          logger.debug('Profile completion attributes from Cognito:', {
            profileCompletion,
            profileComplete,
            isComplete: isProfileComplete,
            firstTimeFlowCompleted
          });

          // Store user data for form prepopulation
          // Get existing user data from localStorage to preserve signup-specific data
          const existingUserData = getItem(STORAGE_KEYS.USER_DATA, true) || {};

          const cognitoUserData = {
            email: attributes.email,
            firstName: attributes.given_name || '',
            lastName: attributes.family_name || '',
            phoneNumber: attributes.phone_number || '',
            cognitoId: attributes.sub || ''
          };

          // Merge existing data with Cognito data, preserving signup-specific fields
          const mergedUserData = {
            ...existingUserData,
            ...cognitoUserData,
            // Ensure we preserve important signup flags
            verified: existingUserData.verified !== undefined ? existingUserData.verified : true, // Set to true since user is now authenticated
            authenticated: true, // User is now authenticated
            signupCompleted: existingUserData.signupCompleted || false
          };

          setItem(STORAGE_KEYS.USER_DATA, mergedUserData);
          logger.debug('User data merged and stored in localStorage:', mergedUserData);
        } catch (attributeError) {
          logger.error('Error processing user attributes:', attributeError);
        }
      }

      return user;
    } catch (error) {
      logger.error('Error signing in:', error);
      clearAuthState();
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Sign out function
  const signOut = async () => {
    try {
      setLoading(true);
      logger.info('Signing out user and clearing all authentication state');

      // Import the logout handler dynamically to avoid circular dependencies
      const { performLogout } = await import('../utils/logoutHandler');

      // Use the comprehensive logout handler
      await performLogout();

      // Update component state
      setCurrentUser(null);
      setIsAuthenticated(false);

      logger.info('User signed out successfully and all authentication state cleared');
    } catch (error) {
      logger.error('Error signing out:', error);

      // Even if there's an error, try to clear everything
      try {
        clearAll();
        localStorage.clear();
        sessionStorage.clear();
      } catch (e) {
        // Silently fail
      }

      // Update component state
      setCurrentUser(null);
      setIsAuthenticated(false);

      logger.warn('Error during sign out, but authentication state has been cleared');
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Sign up function
  const signUp = async (username, password, attributes) => {
    logger.debug('AuthContext.signUp called with:', { username, attributes });
    try {
      setLoading(true);
      logger.info('Calling Auth.signUp with:', { username, attributes });

      // Log the Auth object to verify it's properly initialized
      logger.debug('Auth object initialized');

      const result = await Auth.signUp({
        username,
        password,
        attributes
      });

      logger.debug('Auth.signUp result:', result);
      return result;
    } catch (error) {
      logger.error('Error signing up:', error);
      logger.error('Error details:', { code: error.code, message: error.message, stack: error.stack });
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Confirm sign up function
  const confirmSignUp = async (username, code) => {
    try {
      setLoading(true);
      return await Auth.confirmSignUp(username, code);
    } catch (error) {
      logger.error('Error confirming sign up:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Forgot password function
  const forgotPassword = async (username) => {
    try {
      setLoading(true);
      return await Auth.forgotPassword(username);
    } catch (error) {
      logger.error('Error initiating password reset:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Reset password function
  const resetPassword = async (username, code, newPassword) => {
    try {
      setLoading(true);
      return await Auth.forgotPasswordSubmit(username, code, newPassword);
    } catch (error) {
      logger.error('Error resetting password:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Update user attributes
  const updateUserAttributes = async (attributes) => {
    try {
      setLoading(true);
      const user = await Auth.currentAuthenticatedUser();

      // If updating profile completion, try both attribute names
      if (attributes['custom:profileComplete'] !== undefined) {
        try {
          // Try with profileCompletion first (the likely correct name)
          await Auth.updateUserAttributes(user, {
            'custom:profileCompletion': attributes['custom:profileComplete']
          });
          logger.debug('Successfully updated profileCompletion');
        } catch (error) {
          logger.error('Error updating profileCompletion:', error);
          // Still try the original attribute name
        }
      }

      return await Auth.updateUserAttributes(user, attributes);
    } catch (error) {
      logger.error('Error updating user attributes:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // The context value that will be provided
  const value = {
    currentUser,
    isAuthenticated,
    loading,
    signUp,
    confirmSignUp,
    signIn,
    signOut,
    forgotPassword,
    resetPassword,
    updateUserAttributes
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export default AuthContext;
