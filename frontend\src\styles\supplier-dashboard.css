/* Supplier Dashboard Styles */
.supplier-dashboard-container {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
  background: transparent;
  min-height: 100vh;
  color: #000;
}

/* Header */
.supplier-dashboard-container .dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  padding: 1.5rem 2rem;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid #000;
  min-height: auto;
  flex-wrap: wrap;
  gap: 1rem;
}

.supplier-dashboard-container .header-content {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.supplier-dashboard-container .header-content h1 {
  font-size: 1.8rem;
  font-weight: 600;
  margin: 0 0 0.25rem 0;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: #000;
  line-height: 1.2;
}

.supplier-dashboard-container .header-content h1 i {
  color: #000;
  font-size: 1.6rem;
  flex-shrink: 0;
}

.supplier-dashboard-container .header-content p {
  color: #666;
  margin: 0;
  font-size: 0.95rem;
  line-height: 1.4;
}

.supplier-dashboard-container .header-actions {
  flex-shrink: 0;
  display: flex;
  align-items: center;
}

.supplier-dashboard-container .header-actions .btn-primary {
  background: #000;
  color: #fff;
  border: 1px solid #000;
  padding: 0.75rem 1.25rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  white-space: nowrap;
  height: fit-content;
}

.supplier-dashboard-container .header-actions .btn-primary:hover {
  background: #fff;
  color: #000;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Supplier Stats Grid - Identical to dashboard-stats.css but with supplier- prefix */
.supplier-stats-container {
  width: 100%;
  margin-bottom: 25px;
  margin-top: 0;
  padding: 0;
  max-width: 100%;
  background-color: transparent;
  overflow: hidden;
  box-sizing: border-box;
}

.supplier-stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  padding: 0;
}

/* New elegant card design */
.supplier-stat-card {
  background: #fff;
  border-radius: 10px;
  padding: 20px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-start;
  transition: all 0.25s ease;
  cursor: pointer;
  position: relative;
  border: 1px solid #000;
  height: 140px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  color: #000;
  overflow: hidden;
  width: 100%;
}

.supplier-stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
  background: #f9f9f9;
}

/* Refined icon style */
.supplier-stat-card-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  color: #000;
  background: transparent;
}

.supplier-stat-card-icon svg {
  width: 30px;
  height: 30px;
}

.supplier-stat-card-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
}

.supplier-stat-card-title {
  font-size: 14px;
  font-weight: 600;
  color: #000;
  margin: 0 0 5px 0;
  letter-spacing: 0.3px;
}

.supplier-stat-card-value {
  font-size: 42px;
  font-weight: 700;
  color: #000;
  margin: 0;
  line-height: 1;
  position: absolute;
  bottom: 20px;
  right: 20px;
}

.supplier-stat-card-description {
  font-size: 13px;
  color: #666;
  margin: 5px 0 0 0;
  line-height: 1.4;
  max-width: 90%;
}

/* Dashboard Content Grid */
.dashboard-content-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
  gap: 1.5rem;
}

.dashboard-card {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.dashboard-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.card-header {
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f8f9fa;
}

.card-header h3 {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #2c3e50;
}

.card-header h3 i {
  color: #000;
}

.btn-link {
  background: none;
  border: none;
  color: #000;
  cursor: pointer;
  font-size: 0.9rem;
  text-decoration: none;
  transition: color 0.3s ease;
  font-weight: 500;
}

.btn-link:hover {
  color: #333;
}

.card-content {
  padding: 2rem;
}

/* Offers List */
.offers-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.offer-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
}

.offer-item:hover {
  background: #e9ecef;
  transform: translateY(-1px);
}

.offer-info h4 {
  margin: 0 0 0.25rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #2c3e50;
}

.offer-info p {
  margin: 0 0 0.25rem 0;
  color: #6c757d;
  font-size: 0.9rem;
}

.offer-rate {
  color: #000;
  font-weight: 700;
  font-size: 0.95rem;
}

.offer-stats {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.25rem;
}

.offer-stats span {
  font-size: 0.85rem;
  color: #6c757d;
  font-weight: 500;
}

/* Contracts List */
.contracts-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.contract-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
}

.contract-item:hover {
  background: #e9ecef;
  transform: translateY(-1px);
}

.contract-info h4 {
  margin: 0 0 0.25rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #2c3e50;
}

.contract-info p {
  margin: 0 0 0.25rem 0;
  color: #6c757d;
  font-size: 0.9rem;
}

.contract-date {
  color: #000;
  font-size: 0.85rem;
  font-weight: 500;
}

.status-badge {
  padding: 0.4rem 0.8rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.status-badge.active {
  background: #d4edda;
  color: #155724;
}

.status-badge.pending {
  background: #fff3cd;
  color: #856404;
}

/* Quick Actions */
.quick-actions {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
  padding: 1.5rem;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  color: #2c3e50;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}

.action-btn:hover {
  background: #e9ecef;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.action-btn i {
  font-size: 1.5rem;
  color: #000;
}

.action-btn span {
  font-size: 0.9rem;
  font-weight: 600;
  color: #2c3e50;
}

/* Performance Metrics */
.performance-metrics {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.metric {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 0;
  border-bottom: 1px solid #e9ecef;
}

.metric:last-child {
  border-bottom: none;
}

.metric-label {
  color: #6c757d;
  font-size: 0.95rem;
  font-weight: 500;
}

.metric-value {
  color: #000;
  font-weight: 700;
  font-size: 1.1rem;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 3rem 2rem;
  color: #6c757d;
}

.empty-state i {
  font-size: 3rem;
  color: #dee2e6;
  margin-bottom: 1rem;
}

.empty-state p {
  margin: 0 0 1.5rem 0;
  font-size: 1rem;
}

/* Loading Container */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .supplier-dashboard-container {
    padding: 1rem;
    background: transparent;
    min-height: calc(100vh - 60px);
  }

  .supplier-dashboard-container .dashboard-header {
    flex-direction: column;
    align-items: center;
    gap: 1.5rem;
    text-align: center;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
  }

  .supplier-dashboard-container .header-content {
    text-align: center;
    width: 100%;
  }

  .supplier-dashboard-container .header-content h1 {
    font-size: 1.6rem;
    justify-content: center;
  }

  .supplier-dashboard-container .header-content h1 i {
    font-size: 1.4rem;
  }

  .supplier-dashboard-container .header-actions {
    width: 100%;
    justify-content: center;
  }

  .supplier-dashboard-container .header-actions .btn-primary {
    padding: 0.7rem 1.3rem;
    font-size: 0.9rem;
  }

  /* Supplier Stats Mobile Styles */
  .supplier-stats-container {
    padding: 0 10px !important;
    margin-bottom: 20px !important;
    width: calc(100% - 20px) !important;
    box-sizing: border-box !important;
  }

  .supplier-stats-container .supplier-stats-grid {
    grid-template-columns: 1fr !important;
    gap: 15px !important;
    width: 100% !important;
    box-sizing: border-box !important;
    padding: 0 !important;
    margin: 0 !important;
  }

  .supplier-stats-container .supplier-stat-card {
    padding: 15px !important;
    height: auto !important;
    min-height: 80px !important;
    width: 100% !important;
    box-sizing: border-box !important;
    overflow: hidden !important;
    display: grid !important;
    grid-template-columns: 1fr auto !important;
    gap: 15px !important;
    align-items: center !important;
    position: relative !important;
  }

  .supplier-stats-container .supplier-stat-card-content {
    grid-column: 1 !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: flex-start !important;
    justify-content: center !important;
    min-width: 0 !important;
  }

  .supplier-stats-container .supplier-stat-card-icon {
    width: 32px !important;
    height: 32px !important;
    margin-bottom: 8px !important;
  }

  .supplier-stats-container .supplier-stat-card-icon svg {
    width: 28px !important;
    height: 28px !important;
  }

  .supplier-stats-container .supplier-stat-card-title {
    font-size: 14px !important;
    line-height: 1.2 !important;
    margin-bottom: 4px !important;
    word-wrap: break-word !important;
  }

  .supplier-stats-container .supplier-stat-card-description {
    font-size: 12px !important;
    line-height: 1.3 !important;
    word-wrap: break-word !important;
  }

  .supplier-stats-container .supplier-stat-card-value {
    grid-column: 2 !important;
    font-size: 36px !important;
    font-weight: 700 !important;
    color: #000 !important;
    margin: 0 !important;
    line-height: 1 !important;
    position: static !important;
    text-align: center !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    min-width: 60px !important;
    height: auto !important;
    bottom: auto !important;
    right: auto !important;
  }

  .header-content h1 {
    font-size: 1.6rem;
  }

  .header-content h1 i {
    font-size: 1.4rem;
  }

  .header-actions .btn-primary {
    padding: 0.7rem 1.3rem;
    font-size: 0.9rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
    margin-bottom: 1.5rem;
  }

  .stat-card {
    padding: 1.5rem;
    flex-direction: row;
    text-align: left;
    gap: 1rem;
  }

  .stat-icon {
    width: 56px;
    height: 56px;
    font-size: 1.3rem;
  }

  .stat-content h3 {
    font-size: 1.8rem;
  }

  .dashboard-content-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .card-header {
    padding: 1.5rem;
  }

  .card-header h3 {
    font-size: 1.1rem;
  }

  .card-content {
    padding: 1.5rem;
  }

  .offer-item,
  .contract-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
    padding: 1.5rem;
  }

  .offer-stats,
  .contract-status {
    width: 100%;
    align-items: flex-start;
  }

  .quick-actions {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .action-btn {
    padding: 1.2rem;
  }

  .action-btn i {
    font-size: 1.3rem;
  }

  .action-btn span {
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .supplier-dashboard-container {
    padding: 0.5rem;
  }

  .dashboard-header {
    padding: 0.75rem;
    margin-bottom: 1rem;
  }

  .header-content h1 {
    font-size: 1.3rem;
  }

  .stats-grid {
    gap: 0.75rem;
    margin-bottom: 1rem;
  }

  .stat-card {
    padding: 0.75rem;
  }

  .stat-icon {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }

  .stat-content h3 {
    font-size: 1.3rem;
  }

  .dashboard-content-grid {
    gap: 0.75rem;
  }

  .card-header {
    padding: 0.75rem;
  }

  .card-content {
    padding: 0.75rem;
  }
}
