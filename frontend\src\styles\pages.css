/* ========== SHARED PAGE STYLES ========== */

/* Page Hero Section */
.page-hero {
  padding: 5rem 5%;
  background: linear-gradient(135deg, #f5f7fa 0%, #e4e8f0 100%);
  text-align: center;
}

.page-hero-content {
  max-width: 800px;
  margin: 0 auto;
}

.page-hero-title {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  color: #000;
}

.page-hero-subtitle {
  font-size: 1.25rem;
  line-height: 1.6;
  color: #555;
}

/* Utility Classes */
.bg-light {
  background-color: #f5f7fa;
}

.text-center {
  text-align: center;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  text-align: center !important;
  margin: 0 0 2rem 0;
  color: #000;
}

.section-subtitle {
  font-size: 1.2rem;
  line-height: 1.6;
  color: #64748b;
  margin: 0 auto 2rem;
  max-width: 600px;
  text-align: center;
}

/* When section has subtitle, adjust spacing */
.section-title:has(+ .section-subtitle) {
  margin-bottom: 1.5rem;
}

.section-title + .section-subtitle {
  margin-top: 0;
}

/* Image Placeholder */
.image-placeholder {
  width: 300px;
  height: 300px;
  background-color: rgba(0, 0, 0, 0.03);
  border-radius: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: rgba(0, 0, 0, 0.3);
  margin: 0 auto;
}

.image-placeholder svg {
  margin-bottom: 1rem;
}

.image-placeholder p {
  font-size: 0.9rem;
}

/* ========== FEATURES PAGE STYLES ========== */

/* Features Container */
.features-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Features Main Section */
.features-main-section {
  padding: 5rem 5%;
  background-color: #fff;
}

/* Feature Highlight */
.feature-highlight {
  display: flex;
  align-items: center;
  gap: 4rem;
  margin-bottom: 4rem;
}

.feature-highlight.reverse {
  flex-direction: row-reverse;
}

.feature-content {
  flex: 1;
}

.feature-content h2 {
  text-align: left;
  margin-bottom: 1.5rem;
}

.feature-content .feature-description {
  margin-bottom: 1.5rem;
  font-size: 1.1rem;
  line-height: 1.6;
  color: #555;
}

.feature-list {
  list-style-type: none;
  padding: 0;
}

.feature-list li {
  padding: 0.5rem 0;
  position: relative;
  padding-left: 1.5rem;
}

.feature-list li::before {
  content: "✓";
  position: absolute;
  left: 0;
  color: #000;
  font-weight: bold;
}

.feature-image {
  flex: 1;
  display: flex;
  justify-content: center;
}

/* Additional Features Section */
.additional-features-section {
  padding: 3rem 5% 5rem;
}

/* Business Features Section */
.business-features-section {
  padding: 3rem 5% 5rem;
  background-color: #fff;
}

.business-features-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1.5rem;
  margin: 2rem auto 0;
  max-width: 1200px;
  align-items: stretch;
}

@media (max-width: 1024px) {
  .business-features-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
  }
}

@media (max-width: 768px) {
  .business-features-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}

.business-feature {
  background-color: #f9f9f9;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 180px;
}

.business-feature h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #000;
}

.business-feature p {
  font-size: 1rem;
  line-height: 1.6;
  color: #555;
  flex-grow: 1;
  display: flex;
  align-items: center;
}

/* ========== HOW IT WORKS PAGE STYLES ========== */

/* Process Overview Section */
.process-overview-section {
  padding: 3rem 5% 5rem;
  background-color: #fff;
}

.process-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.process-steps {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  max-width: 800px;
  margin: 0 auto;
}

.process-step {
  display: flex;
  align-items: flex-start;
  gap: 1.5rem;
  background-color: #f9f9f9;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.process-step .step-number {
  width: 40px;
  height: 40px;
  background-color: #000;
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  font-weight: 600;
  flex-shrink: 0;
}

.step-content {
  flex: 1;
}

.step-content h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #000;
}

.step-content p {
  font-size: 1rem;
  line-height: 1.6;
  color: #555;
  margin: 0;
}

/* Detailed Steps Section */
.detailed-steps-section {
  padding: 3rem 5% 5rem;
}

.detailed-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.detailed-step {
  display: flex;
  align-items: center;
  gap: 4rem;
  margin: 3rem 0 4rem 0;
  padding: 2rem;
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.detailed-step:first-of-type {
  margin-top: 2rem;
}

.detailed-step.reverse {
  flex-direction: row-reverse;
}

.detailed-step-content {
  flex: 1.5;
}

.detailed-step-content h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #000;
}

.detailed-step-content p {
  font-size: 1rem;
  line-height: 1.6;
  color: #555;
  margin-bottom: 1.5rem;
}

.detailed-step-content ul {
  margin-bottom: 1.5rem;
  padding-left: 1.5rem;
}

.detailed-step-content li {
  margin-bottom: 0.5rem;
  color: #555;
}

.detailed-step-image {
  flex: 1;
  display: flex;
  justify-content: center;
}

/* Business Process Section */
.business-process-section {
  padding: 3rem 5% 5rem;
  background-color: #fff;
}

.business-process-content {
  display: flex;
  align-items: center;
  gap: 4rem;
  margin-top: 3rem;
  padding: 2rem;
  background-color: #f9f9f9;
  border-radius: 12px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.business-process-text {
  flex: 1.5;
}

.business-process-text p {
  font-size: 1rem;
  line-height: 1.6;
  color: #555;
  margin-bottom: 1.5rem;
}

.business-process-text ul {
  margin-bottom: 1.5rem;
  padding-left: 1.5rem;
}

.business-process-text li {
  margin-bottom: 0.5rem;
  color: #555;
}

.business-process-image {
  flex: 1;
  display: flex;
  justify-content: center;
}

/* FAQ Section */
.faq-section {
  padding: 3rem 5% 5rem;
}

.faq-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.faq-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
  margin: 2rem auto 0;
  max-width: 1200px;
  align-items: stretch;
}

@media (max-width: 1024px) {
  .faq-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
  }
}

@media (max-width: 768px) {
  .faq-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}

.faq-item {
  background-color: #f9f9f9;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 180px;
}

.faq-item h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #000;
}

.faq-item p {
  font-size: 1rem;
  line-height: 1.6;
  color: #555;
  margin: 0;
  flex-grow: 1;
  display: flex;
  align-items: center;
}

/* ========== ABOUT PAGE STYLES ========== */

/* About Section */
.about-section {
  padding: 3rem 5% 5rem;
  background-color: #fff;
}

.about-container {
  display: flex;
  align-items: center;
  gap: 4rem;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.about-content {
  flex: 1.5;
}

.about-text {
  font-size: 1.1rem;
  line-height: 1.6;
  color: #555;
  margin-bottom: 1.5rem;
}

.about-image {
  flex: 1;
  display: flex;
  justify-content: center;
}

/* Mission Section */
.mission-section {
  padding: 3rem 5% 5rem;
  background-color: #f5f7fa;
}

.mission-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.mission-content {
  text-align: center;
}

.mission-cards {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
  margin: 2rem auto 0;
  max-width: 1000px;
  align-items: stretch;
}

@media (max-width: 1024px) {
  .mission-cards {
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
  }
}

@media (max-width: 768px) {
  .mission-cards {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}

.mission-card {
  background-color: #fff;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  text-align: center;
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 280px;
}

.mission-icon {
  width: 80px;
  height: 80px;
  background-color: #000;
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
}

.mission-card h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #000;
}

.mission-card p {
  font-size: 1rem;
  line-height: 1.6;
  color: #555;
  flex-grow: 1;
  display: flex;
  align-items: center;
}

/* Team Section */
.team-section {
  padding: 3rem 5% 5rem;
  background-color: #fff;
}

.team-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  text-align: center;
}

.team-intro {
  font-size: 1.1rem;
  line-height: 1.6;
  color: #555;
  margin-bottom: 0;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.team-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1.5rem;
  margin: 2rem auto 0;
  max-width: 1200px;
  align-items: stretch;
}

@media (max-width: 1024px) {
  .team-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
  }
}

@media (max-width: 768px) {
  .team-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}

.team-member {
  background-color: #f9f9f9;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 320px;
  text-align: center;
}

.team-member-photo {
  margin-bottom: 1.5rem;
}

.photo-placeholder {
  width: 120px;
  height: 120px;
  background-color: rgba(0, 0, 0, 0.03);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  color: rgba(0, 0, 0, 0.3);
}

.team-member h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #000;
}

.team-member-role {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 1rem;
}

.team-member-bio {
  font-size: 0.95rem;
  line-height: 1.6;
  color: #555;
  flex-grow: 1;
  display: flex;
  align-items: center;
}

/* Commitment Section */
.commitment-section {
  padding: 4rem 0;
  background-color: #f8f9fa;
}

.commitment-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 2rem;
}

.commitment-content {
  display: flex;
  align-items: center;
  gap: 3rem;
  margin-top: 2rem;
}

.commitment-text {
  flex: 1;
}

.commitment-description {
  font-size: 1.2rem;
  line-height: 1.6;
  color: #555;
  margin-bottom: 1.5rem;
}

.commitment-description:last-child {
  margin-bottom: 0;
}

.commitment-icon {
  flex-shrink: 0;
  color: #000;
}

@media (max-width: 768px) {
  .commitment-content {
    flex-direction: column;
    text-align: center;
    gap: 2rem;
  }

  .commitment-description {
    font-size: 1.1rem;
  }

  .commitment-icon svg {
    width: 80px;
    height: 80px;
  }
}

.partner-logo {
  background-color: #fff;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo-placeholder {
  width: 100%;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(0, 0, 0, 0.3);
  border: 1px dashed rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

/* Responsive Styles */
@media (max-width: 1024px) {
  .page-hero-title {
    font-size: 2.5rem;
  }

  .feature-highlight,
  .detailed-step,
  .business-process-content,
  .about-container {
    gap: 2rem;
  }

  .image-placeholder {
    width: 250px;
    height: 250px;
  }
}

@media (max-width: 768px) {
  .feature-highlight,
  .feature-highlight.reverse,
  .detailed-step,
  .detailed-step.reverse,
  .business-process-content,
  .about-container {
    flex-direction: column;
    text-align: center;
  }

  .feature-content h2,
  .detailed-step-content h3 {
    text-align: center;
  }

  .feature-list li {
    text-align: left;
  }

  .detailed-step-content ul,
  .business-process-text ul {
    text-align: left;
  }

  /* Grid responsive rules are now handled in individual grid sections */
}

@media (max-width: 480px) {
  .page-hero-title {
    font-size: 2rem;
  }

  .page-hero-subtitle {
    font-size: 1rem;
  }

  .image-placeholder {
    width: 200px;
    height: 200px;
  }

  .process-step {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  /* Partners grid responsive rule handled in partners-grid section */
}

/* Contact Page Styles */

/* Contact Methods Section */
.contact-methods-section {
  padding: 4rem 0;
  background-color: #fff;
}

.contact-methods-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 2rem;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
}

.contact-method-block {
  text-align: center;
  padding: 2rem;
}

.method-header {
  margin-bottom: 1rem;
}

.method-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.method-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #000;
  margin-bottom: 1rem;
}

.method-description {
  font-size: 1.1rem;
  line-height: 1.6;
  color: #555;
  margin: 0;
}

/* Contact Form Section */
.contact-form-section {
  padding: 4rem 0;
  background-color: #f8f9fa;
}

.contact-form-container-new {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 2rem;
}

.contact-form-wrapper {
  background: white;
  padding: 3rem;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.contact-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #333;
}

.required {
  color: #e74c3c;
}

.form-input,
.form-textarea,
.form-select {
  padding: 0.75rem;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
  outline: none;
  border-color: #007bff;
}

.form-textarea {
  resize: vertical;
  min-height: 120px;
}

.form-success {
  text-align: center;
  padding: 2rem;
}

.success-icon {
  color: #28a745;
  margin-bottom: 1rem;
}

.form-success h2 {
  color: #28a745;
  margin-bottom: 1rem;
}

.form-success p {
  color: #666;
  margin-bottom: 2rem;
  line-height: 1.6;
}

.form-error-message {
  background-color: #f8d7da;
  color: #721c24;
  padding: 0.75rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  border: 1px solid #f5c6cb;
}

/* Appointment Section */
.appointment-section {
  padding: 4rem 0;
  background-color: #fff;
}

.appointment-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 2rem;
}

.appointment-header {
  text-align: center;
  margin-bottom: 3rem;
}

.appointment-description {
  font-size: 1.1rem;
  line-height: 1.6;
  color: #555;
  margin-top: 1rem;
}

.appointment-form {
  background: white;
  padding: 3rem;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.form-section {
  border-bottom: 1px solid #eee;
  padding-bottom: 2rem;
}

.form-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.form-section-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: #000;
  margin-bottom: 1.5rem;
}

.appointment-note {
  margin-top: 2rem;
  padding: 1.5rem;
  background-color: #f8f9fa;
  border-radius: 8px;
  text-align: center;
}

.appointment-note p {
  margin: 0;
  color: #555;
  font-style: italic;
}

/* Responsive Styles for Contact Page */
@media (max-width: 768px) {
  .contact-methods-container {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .contact-form-wrapper,
  .appointment-form {
    padding: 2rem;
  }

  .method-icon {
    font-size: 2.5rem;
  }

  .method-title {
    font-size: 1.3rem;
  }
}

@media (max-width: 480px) {
  .contact-form-wrapper,
  .appointment-form {
    padding: 1.5rem;
  }

  .method-icon {
    font-size: 2rem;
  }
}

/* Commitment Charter Page Styles */
.charter-section {
  padding: 4rem 0;
  background-color: #fff;
}

.charter-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 2rem;
}

.charter-content {
  display: flex;
  flex-direction: column;
  gap: 4rem;
}

.charter-intro {
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
}

.charter-text {
  font-size: 1.2rem;
  line-height: 1.6;
  color: #555;
  margin-bottom: 1.5rem;
}

.charter-text:last-child {
  margin-bottom: 0;
}

.charter-section-title {
  font-size: 2rem;
  font-weight: 700;
  color: #000;
  text-align: center;
  margin-bottom: 3rem;
}

.charter-principles {
  display: flex;
  flex-direction: column;
  gap: 3rem;
}

.principle-item {
  display: flex;
  align-items: flex-start;
  gap: 2rem;
  padding: 2rem 0;
  border-bottom: 1px solid #eee;
}

.principle-item:last-child {
  border-bottom: none;
}

.principle-icon {
  width: 80px;
  height: 80px;
  background-color: #000;
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.principle-content {
  flex: 1;
  min-width: 0;
}

.principle-title {
  font-size: 1.4rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: #000;
  line-height: 1.3;
}

.principle-description {
  font-size: 1.1rem;
  line-height: 1.6;
  color: #555;
  margin: 0;
}

.charter-mission {
  background-color: #f8f9fa;
  padding: 3rem;
  border-radius: 12px;
  text-align: center;
}

.mission-statement {
  max-width: 700px;
  margin: 0 auto;
}

.mission-text {
  font-size: 1.3rem;
  line-height: 1.6;
  color: #333;
  margin-bottom: 1.5rem;
  font-weight: 500;
}

.mission-text:last-child {
  margin-bottom: 0;
}

/* Responsive Styles for Charter Page */
@media (max-width: 768px) {
  .charter-content {
    gap: 3rem;
  }

  .principle-item {
    flex-direction: column;
    text-align: center;
    gap: 1.5rem;
    padding: 1.5rem 0;
  }

  .principle-icon {
    width: 70px;
    height: 70px;
    margin: 0 auto;
  }

  .principle-title {
    font-size: 1.3rem;
  }

  .principle-description {
    font-size: 1rem;
  }

  .charter-mission {
    padding: 2rem;
  }

  .mission-text {
    font-size: 1.2rem;
  }
}

@media (max-width: 480px) {
  .charter-section-title {
    font-size: 1.7rem;
  }

  .charter-text {
    font-size: 1.1rem;
  }

  .principle-icon {
    width: 60px;
    height: 60px;
  }

  .principle-title {
    font-size: 1.2rem;
  }

  .principle-description {
    font-size: 0.95rem;
  }

  .charter-mission {
    padding: 1.5rem;
  }

  .mission-text {
    font-size: 1.1rem;
  }
}
