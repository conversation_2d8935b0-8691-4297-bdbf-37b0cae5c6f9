const mongoose = require('mongoose');

const variableSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  type: {
    type: String,
    enum: ['text', 'number', 'date', 'boolean', 'email', 'phone', 'currency', 'percentage'],
    required: true
  },
  label: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    trim: true
  },
  required: {
    type: Boolean,
    default: false
  },
  defaultValue: {
    type: mongoose.Schema.Types.Mixed
  },
  validation: {
    min: Number,
    max: Number,
    pattern: String,
    options: [String]
  }
}, { _id: false });

const documentTemplateSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true,
    unique: true
  },
  description: {
    type: String,
    trim: true
  },
  templateType: {
    type: String,
    enum: [
      'PDF_Comparison',
      'Contract_Template',
      'Mandate_Form',
      'Data_Authorization',
      'Quote_Document',
      'Invoice_Template',
      'Email_Template',
      'SMS_Template',
      'Legal_Document',
      'Terms_Conditions',
      'Privacy_Policy',
      'Other'
    ],
    required: true
  },
  content: {
    type: String,
    required: true
  },
  variables: [variableSchema],
  settings: {
    pageSize: {
      type: String,
      enum: ['A4', 'A3', 'Letter', 'Legal'],
      default: 'A4'
    },
    orientation: {
      type: String,
      enum: ['portrait', 'landscape'],
      default: 'portrait'
    },
    margins: {
      top: { type: Number, default: 20 },
      bottom: { type: Number, default: 20 },
      left: { type: Number, default: 20 },
      right: { type: Number, default: 20 }
    },
    header: {
      enabled: { type: Boolean, default: true },
      content: String,
      height: { type: Number, default: 50 }
    },
    footer: {
      enabled: { type: Boolean, default: true },
      content: String,
      height: { type: Number, default: 30 }
    },
    watermark: {
      enabled: { type: Boolean, default: false },
      text: String,
      opacity: { type: Number, default: 0.1 }
    },
    styling: {
      fontFamily: { type: String, default: 'Arial' },
      fontSize: { type: Number, default: 12 },
      lineHeight: { type: Number, default: 1.5 },
      primaryColor: { type: String, default: '#000000' },
      secondaryColor: { type: String, default: '#666666' },
      accentColor: { type: String, default: '#007bff' }
    },
    branding: {
      logoEnabled: { type: Boolean, default: true },
      logoPosition: { type: String, enum: ['top-left', 'top-center', 'top-right'], default: 'top-left' },
      logoMaxWidth: { type: Number, default: 200 },
      logoMaxHeight: { type: Number, default: 100 },
      companyName: String,
      companyAddress: String,
      companyPhone: String,
      companyEmail: String,
      companyWebsite: String
    }
  },
  fileInfo: {
    originalName: String,
    fileName: String,
    filePath: String,
    fileSize: Number,
    mimeType: String
  },
  status: {
    type: String,
    enum: ['Active', 'Inactive', 'Draft'],
    default: 'Draft'
  },
  version: {
    type: Number,
    default: 1
  },
  isDefault: {
    type: Boolean,
    default: false
  },
  usage: {
    timesUsed: {
      type: Number,
      default: 0
    },
    lastUsed: Date,
    documentsGenerated: {
      type: Number,
      default: 0
    }
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  lastModifiedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  lastModifiedAt: {
    type: Date
  },
  approvedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  approvedAt: {
    type: Date
  },
  tags: [{
    type: String,
    trim: true
  }],
  metadata: {
    language: {
      type: String,
      default: 'en'
    },
    region: {
      type: String,
      default: 'FR'
    },
    legalCompliance: [{
      regulation: String,
      compliant: Boolean,
      notes: String
    }],
    accessLevel: {
      type: String,
      enum: ['Public', 'Internal', 'Restricted', 'Confidential'],
      default: 'Internal'
    }
  }
}, {
  timestamps: true
});

// Pre-save middleware
documentTemplateSchema.pre('save', function(next) {
  if (this.isModified('content') || this.isModified('variables') || this.isModified('settings')) {
    this.lastModifiedAt = new Date();
    if (!this.isNew) {
      this.version += 1;
    }
  }
  next();
});

// Instance methods
documentTemplateSchema.methods.incrementUsage = function() {
  this.usage.timesUsed += 1;
  this.usage.lastUsed = new Date();
  return this.save();
};

documentTemplateSchema.methods.generateDocument = function(data) {
  let content = this.content;
  
  // Replace variables in content
  this.variables.forEach(variable => {
    const value = data[variable.name] || variable.defaultValue || '';
    const placeholder = new RegExp(`{{${variable.name}}}`, 'g');
    content = content.replace(placeholder, value);
  });
  
  return content;
};

documentTemplateSchema.methods.validateData = function(data) {
  const errors = [];
  
  this.variables.forEach(variable => {
    const value = data[variable.name];
    
    // Check required fields
    if (variable.required && (!value || value === '')) {
      errors.push(`${variable.label} is required`);
      return;
    }
    
    if (value !== undefined && value !== null && value !== '') {
      // Type validation
      switch (variable.type) {
        case 'number':
          if (isNaN(value)) {
            errors.push(`${variable.label} must be a number`);
          }
          break;
        case 'email':
          const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
          if (!emailRegex.test(value)) {
            errors.push(`${variable.label} must be a valid email`);
          }
          break;
        case 'date':
          if (isNaN(Date.parse(value))) {
            errors.push(`${variable.label} must be a valid date`);
          }
          break;
      }
      
      // Range validation
      if (variable.validation) {
        if (variable.validation.min !== undefined && value < variable.validation.min) {
          errors.push(`${variable.label} must be at least ${variable.validation.min}`);
        }
        if (variable.validation.max !== undefined && value > variable.validation.max) {
          errors.push(`${variable.label} must be at most ${variable.validation.max}`);
        }
        if (variable.validation.pattern) {
          const pattern = new RegExp(variable.validation.pattern);
          if (!pattern.test(value)) {
            errors.push(`${variable.label} format is invalid`);
          }
        }
        if (variable.validation.options && !variable.validation.options.includes(value)) {
          errors.push(`${variable.label} must be one of: ${variable.validation.options.join(', ')}`);
        }
      }
    }
  });
  
  return errors;
};

// Static methods
documentTemplateSchema.statics.getActiveTemplates = function(templateType) {
  const filter = { status: 'Active' };
  if (templateType) {
    filter.templateType = templateType;
  }
  return this.find(filter).sort({ isDefault: -1, name: 1 });
};

documentTemplateSchema.statics.getDefaultTemplate = function(templateType) {
  return this.findOne({
    templateType,
    status: 'Active',
    isDefault: true
  });
};

// Create indexes
documentTemplateSchema.index({ templateType: 1, status: 1 });
documentTemplateSchema.index({ name: 1 });
documentTemplateSchema.index({ isDefault: 1 });
documentTemplateSchema.index({ createdBy: 1 });
documentTemplateSchema.index({ tags: 1 });

const DocumentTemplate = mongoose.model('DocumentTemplate', documentTemplateSchema);

module.exports = DocumentTemplate;
