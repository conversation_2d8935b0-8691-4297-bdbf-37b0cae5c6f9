const express = require('express');
const router = express.Router();
const contractController = require('../controllers/contractController');
// const { verifyToken } = require('../middleware/auth');
// const logger = require('../utils/logger');

// Temporarily disable authentication for demo purposes
// router.use(verifyToken);

// Get all contracts for a user
router.get('/user', contractController.getUserContracts);

// Get a specific contract by ID
router.get('/:id', contractController.getContractById);

// Update contract signature status (DocuSign webhook)
router.post('/:id/signature-status', contractController.updateSignatureStatus);

// Get contract signing status
router.get('/:id/signing-status', contractController.getSigningStatus);

// Download signed contract
router.get('/:id/download', contractController.downloadContract);

// Resend signing invitation
router.post('/:id/resend-signing', contractController.resendSigning);

module.exports = router;
