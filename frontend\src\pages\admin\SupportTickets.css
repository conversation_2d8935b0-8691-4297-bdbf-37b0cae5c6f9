.support-tickets-container {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.support-tickets-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

/* Header */
.page-header {
  margin-bottom: 30px;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 10px;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  color: #495057;
  text-decoration: none;
  font-size: 14px;
  transition: all 0.2s ease;
}

.back-button:hover {
  background: #e9ecef;
  color: #212529;
}

.page-header h1 {
  margin: 0;
  color: #212529;
  font-size: 28px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #6c757d;
  font-size: 16px;
}

/* Statistics Cards */
.tickets-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
}

.stat-card.total .stat-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-card.open .stat-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-card.in-progress .stat-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-card.critical .stat-icon {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.stat-content h3 {
  margin: 0 0 4px 0;
  font-size: 24px;
  font-weight: 700;
  color: #212529;
}

.stat-content p {
  margin: 0;
  font-size: 14px;
  color: #6c757d;
  font-weight: 500;
}

/* Filters */
.tickets-filters {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
  align-items: end;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
  min-width: 150px;
}

.filter-group.search-group {
  flex: 1;
  min-width: 250px;
}

.filter-group label {
  font-size: 14px;
  font-weight: 500;
  color: #495057;
}

.filter-group select,
.filter-group input {
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 6px;
  font-size: 14px;
  background: white;
  transition: border-color 0.2s ease;
}

.filter-group select:focus,
.filter-group input:focus {
  outline: none;
  border-color: #80bdff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Error Message */
.error-message {
  background: #f8d7da;
  color: #721c24;
  padding: 12px 16px;
  border-radius: 6px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Tickets Table */
.tickets-table-container {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.tickets-table {
  width: 100%;
  border-collapse: collapse;
}

.tickets-table th {
  background: #f8f9fa;
  padding: 16px 12px;
  text-align: left;
  font-weight: 600;
  color: #495057;
  font-size: 14px;
  border-bottom: 1px solid #dee2e6;
}

.tickets-table td {
  padding: 16px 12px;
  border-bottom: 1px solid #f1f3f4;
  vertical-align: top;
}

.tickets-table tbody tr {
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.tickets-table tbody tr:hover {
  background: #f8f9fa;
}

.ticket-id {
  font-family: 'Courier New', monospace;
  font-weight: 600;
  color: #495057;
  font-size: 13px;
}

.ticket-subject {
  max-width: 300px;
}

.subject-text {
  font-weight: 500;
  color: #212529;
  margin-bottom: 4px;
}

.subject-preview {
  font-size: 13px;
  color: #6c757d;
  line-height: 1.4;
}

.ticket-user {
  min-width: 180px;
}

.user-email {
  font-weight: 500;
  color: #212529;
  font-size: 14px;
}

.user-name {
  font-size: 13px;
  color: #6c757d;
  margin-top: 2px;
}

/* Badges */
.type-badge,
.priority-badge,
.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Type badges */
.type-technical { background: #e3f2fd; color: #1565c0; }
.type-billing { background: #f3e5f5; color: #7b1fa2; }
.type-account { background: #e8f5e8; color: #2e7d32; }
.type-offer { background: #fff3e0; color: #ef6c00; }
.type-contract { background: #fce4ec; color: #c2185b; }
.type-other { background: #f5f5f5; color: #616161; }

/* Priority badges */
.priority-low { background: #e8f5e8; color: #2e7d32; }
.priority-medium { background: #fff3e0; color: #ef6c00; }
.priority-high { background: #ffebee; color: #d32f2f; }
.priority-critical { background: #ffcdd2; color: #b71c1c; }

/* Status badges */
.status-open { background: #e3f2fd; color: #1565c0; }
.status-in-progress { background: #fff3e0; color: #ef6c00; }
.status-resolved { background: #e8f5e8; color: #2e7d32; }
.status-closed { background: #f5f5f5; color: #616161; }
.status-reopened { background: #ffebee; color: #d32f2f; }

.ticket-created {
  font-size: 13px;
  color: #6c757d;
  white-space: nowrap;
}

/* Action buttons */
.ticket-actions {
  text-align: center;
}

.action-btn {
  padding: 6px 10px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.view-btn {
  background: #e3f2fd;
  color: #1565c0;
}

.view-btn:hover {
  background: #bbdefb;
  color: #0d47a1;
}

/* No tickets message */
.no-tickets {
  text-align: center;
  padding: 60px 20px;
  color: #6c757d;
}

.no-tickets i {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.no-tickets p {
  font-size: 16px;
  margin: 0;
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  padding: 20px;
}

.pagination-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  color: #495057;
  cursor: pointer;
  transition: all 0.2s ease;
}

.pagination-btn:hover:not(:disabled) {
  background: #f8f9fa;
  border-color: #adb5bd;
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-info {
  font-size: 14px;
  color: #6c757d;
  font-weight: 500;
}

/* Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.ticket-modal {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #dee2e6;
  background: #f8f9fa;
}

.modal-header h2 {
  margin: 0;
  color: #212529;
  font-size: 20px;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  font-size: 18px;
  color: #6c757d;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: color 0.2s ease;
}

.close-btn:hover {
  color: #495057;
}

.modal-content {
  padding: 24px;
  overflow-y: auto;
  max-height: calc(90vh - 80px);
}
