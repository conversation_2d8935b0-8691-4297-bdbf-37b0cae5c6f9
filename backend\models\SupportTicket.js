const mongoose = require('mongoose');

const supportTicketSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  subject: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    required: true,
    trim: true
  },
  type: {
    type: String,
    enum: ['Technical', 'Billing', 'Account', 'Offer', 'Contract', 'Other'],
    default: 'Technical'
  },
  priority: {
    type: String,
    enum: ['Low', 'Medium', 'High', 'Critical'],
    default: 'Medium'
  },
  status: {
    type: String,
    enum: ['Open', 'InProgress', 'Resolved', 'Closed', 'Reopened'],
    default: 'Open'
  },
  assignedTo: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  resolution: {
    type: String,
    trim: true
  },
  resolutionDate: {
    type: Date
  },
  attachments: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Document'
  }],
  relatedEntities: [{
    entityType: {
      type: String,
      enum: ['EnergyRequest', 'Offer', 'Contract', 'Appointment', 'AccountStatus', 'User']
    },
    entityId: {
      type: mongoose.Schema.Types.ObjectId
    }
  }],
  comments: [{
    text: {
      type: String,
      required: true
    },
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    isInternal: {
      type: Boolean,
      default: false
    },
    createdAt: {
      type: Date,
      default: Date.now
    }
  }],
  timeToResolution: {
    type: Number
  }
}, {
  timestamps: true
});

// Create indexes
supportTicketSchema.index({ userId: 1 });
supportTicketSchema.index({ assignedTo: 1 });
supportTicketSchema.index({ status: 1 });
supportTicketSchema.index({ priority: 1 });
supportTicketSchema.index({ type: 1 });

const SupportTicket = mongoose.model('SupportTicket', supportTicketSchema);

module.exports = SupportTicket;
