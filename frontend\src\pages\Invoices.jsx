import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import DashboardLayout from '../components/DashboardLayout';
import Spinner from '../components/Spinner';
import invoiceService from '../services/invoice.service';
import { getItem, STORAGE_KEYS } from '../utils/localStorage';
import { showSuccessMessage, showErrorMessage } from '../utils/toastNotifications';
import '../styles/invoices.css';

const Invoices = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [invoices, setInvoices] = useState([]);
  const [filter, setFilter] = useState('all');
  const [error, setError] = useState(null);

  useEffect(() => {
    // Fetch real invoices from the database
    const fetchInvoices = async () => {
      try {
        setLoading(true);
        setError(null);

        // Get user info from localStorage
        const cognitoId = getItem(STORAGE_KEYS.COGNITO_ID);



        if (!cognitoId) {
          console.error('No cognitoId found in localStorage');
          console.error('Available localStorage keys:', Object.keys(localStorage));

          // For testing purposes, show empty state instead of error
          console.log('User not authenticated - showing empty state');
          setInvoices([]);
          setLoading(false);
          return;
        }

        console.log('Fetching invoices for user:', cognitoId);

        // Fetch invoices from the backend
        console.log('Fetching invoices for user:', cognitoId);
        const response = await invoiceService.getUserInvoices();

        console.log('Invoices API response:', response);

        // Handle the response structure
        let invoiceList = [];
        if (response && response.invoices && Array.isArray(response.invoices)) {
          invoiceList = response.invoices;
        } else if (response && Array.isArray(response)) {
          invoiceList = response;
        } else {
          console.warn('Unexpected response structure:', response);
          invoiceList = [];
        }

        // Transform the data to match the UI expectations
        const transformedInvoices = invoiceList.map(invoice => ({
          id: invoice._id || invoice.id,
          originalFilename: invoice.originalFilename || 'Unknown File',
          provider: invoice.metadata?.provider || 'Unknown Provider',
          invoiceDate: invoice.metadata?.invoiceDate || invoice.createdAt,
          invoiceNumber: invoice.metadata?.invoiceNumber || 'N/A',
          amount: invoice.metadata?.amount || 0,
          currency: invoice.metadata?.currency || 'EUR',
          consumption: invoice.metadata?.consumption || 0,
          energyType: invoice.metadata?.energyType || 'Electricity',
          pointOfDelivery: invoice.metadata?.pointOfDelivery || 'N/A',
          status: invoice.status || 'pending',
          uploadedAt: invoice.createdAt || new Date().toISOString(),
          publicUrl: invoice.publicUrl || '#',
          s3Key: invoice.s3Key,
          s3Bucket: invoice.s3Bucket
        }));

        console.log('Transformed invoices:', transformedInvoices);

        setInvoices(transformedInvoices);
        setLoading(false);

        if (transformedInvoices.length === 0) {
          console.log('No invoices found for user');
        } else {
          console.log(`Found ${transformedInvoices.length} invoices for user`);
        }

      } catch (error) {
        console.error('Error fetching invoices:', error);
        setError(`Failed to load invoices: ${error.message}`);
        setLoading(false);

        // Show error message to user
        showErrorMessage('FETCH_INVOICES_FAILED', error.message);
      }
    };

    fetchInvoices();
  }, []);

  // Filter invoices based on energy type
  const filteredInvoices = invoices.filter(invoice => {
    if (filter === 'electricity') return invoice.energyType === 'Electricity';
    if (filter === 'gas') return invoice.energyType === 'Gas';
    return true; // 'all' filter
  });

  // Handle upload new invoice
  const handleUploadNewInvoice = () => {
    navigate('/upload-first-invoice');
  };

  // Handle view invoice
  const handleViewInvoice = (invoice) => {
    console.log('Viewing invoice:', invoice);
    // TODO: Implement invoice view modal or page
    showSuccessMessage('FEATURE_COMING_SOON', 'Invoice view feature coming soon!');
  };

  // Handle download invoice
  const handleDownloadInvoice = async (invoice) => {
    try {
      console.log('Downloading invoice:', invoice);

      if (invoice.publicUrl && invoice.publicUrl !== '#') {
        // If we have a public URL, open it in a new tab
        window.open(invoice.publicUrl, '_blank');
      } else {
        // TODO: Implement download via backend API
        showSuccessMessage('FEATURE_COMING_SOON', 'Invoice download feature coming soon!');
      }
    } catch (error) {
      console.error('Error downloading invoice:', error);
      showErrorMessage('DOWNLOAD_FAILED', 'Failed to download invoice');
    }
  };

  // Handle delete invoice
  const handleDeleteInvoice = async (invoice) => {
    if (!window.confirm(`Are you sure you want to delete "${invoice.originalFilename}"?`)) {
      return;
    }

    try {
      console.log('Deleting invoice:', invoice);
      // TODO: Implement delete via backend API
      showSuccessMessage('FEATURE_COMING_SOON', 'Invoice delete feature coming soon!');
    } catch (error) {
      console.error('Error deleting invoice:', error);
      showErrorMessage('DELETE_FAILED', 'Failed to delete invoice');
    }
  };

  // Format date to readable format
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-GB', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    });
  };

  // Format timestamp to readable format
  const formatTimestamp = (timestamp) => {
    const date = new Date(timestamp);
    return date.toLocaleDateString('en-GB', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <DashboardLayout>
      <div className="invoices-container">
        <div className="invoices-header">
          <div className="invoices-header-content">
            <h1>My Energy Invoices</h1>
            <p>View and manage your uploaded energy invoices.</p>
          </div>
          <button className="upload-invoice-btn" onClick={handleUploadNewInvoice}>
            <i className="fas fa-plus"></i> Upload New Invoice
          </button>
        </div>



        {loading ? (
          <div className="invoices-loading">
            <Spinner size="medium" message="Loading your invoices..." />
          </div>
        ) : error ? (
          <div className="invoices-error">
            <i className="fas fa-exclamation-triangle"></i>
            <h3>Error Loading Invoices</h3>
            <p>{error}</p>
            <button
              className="retry-btn"
              onClick={() => window.location.reload()}
            >
              <i className="fas fa-redo"></i> Retry
            </button>
          </div>
        ) : (
          <>
            <div className="invoices-actions">
              <div className="filter-buttons">
                <button
                  className={`filter-btn ${filter === 'all' ? 'active' : ''}`}
                  onClick={() => setFilter('all')}
                >
                  All Invoices
                </button>
                <button
                  className={`filter-btn ${filter === 'electricity' ? 'active' : ''}`}
                  onClick={() => setFilter('electricity')}
                >
                  Electricity
                </button>
                <button
                  className={`filter-btn ${filter === 'gas' ? 'active' : ''}`}
                  onClick={() => setFilter('gas')}
                >
                  Gas
                </button>
              </div>
            </div>

            {filteredInvoices.length > 0 ? (
              <div className="invoices-table-container">
                <table className="invoices-table">
                  <thead>
                    <tr>
                      <th>Invoice</th>
                      <th>Provider</th>
                      <th>Date</th>
                      <th>Amount</th>
                      <th>Consumption</th>
                      <th>Uploaded</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredInvoices.map(invoice => (
                      <tr key={invoice.id}>
                        <td className="invoice-name-cell">
                          <div className="invoice-name">
                            <i className={`fas fa-file-pdf invoice-icon ${invoice.energyType.toLowerCase()}`}></i>
                            <div className="invoice-details">
                              <div className="filename">{invoice.originalFilename}</div>
                              <div className="invoice-number">#{invoice.invoiceNumber}</div>
                            </div>
                          </div>
                        </td>
                        <td>{invoice.provider}</td>
                        <td>{formatDate(invoice.invoiceDate)}</td>
                        <td className="amount-cell">
                          {invoice.amount} {invoice.currency}
                        </td>
                        <td>
                          {invoice.consumption} {invoice.energyType === 'Gas' ? 'm³' : 'kWh'}
                        </td>
                        <td>{formatTimestamp(invoice.uploadedAt)}</td>
                        <td className="actions-cell">
                          <div className="actions-container">
                            <button
                              className="action-btn view-btn"
                              title="View Invoice"
                              onClick={() => handleViewInvoice(invoice)}
                            >
                              <i className="fas fa-folder-open"></i>
                            </button>
                            <button
                              className="action-btn download-btn"
                              title="Download Invoice"
                              onClick={() => handleDownloadInvoice(invoice)}
                            >
                              <i className="fas fa-download"></i>
                            </button>
                            <button
                              className="action-btn delete-btn"
                              title="Delete Invoice"
                              onClick={() => handleDeleteInvoice(invoice)}
                            >
                              <i className="fas fa-trash-alt"></i>
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="no-invoices">
                <i className="fas fa-file-invoice"></i>
                <h3>No invoices found</h3>
                <p>You haven't uploaded any energy invoices yet.</p>
                <button className="upload-invoice-btn" onClick={handleUploadNewInvoice}>
                  <i className="fas fa-plus"></i> Upload Your First Invoice
                </button>
              </div>
            )}
          </>
        )}
      </div>
    </DashboardLayout>
  );
};

export default Invoices;
