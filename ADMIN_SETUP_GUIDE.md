# Admin User Setup Guide

This guide will help you set up the admin user for the Energy Bill Management System.

## 🎯 Overview

We need to create an admin user with:
- **Username**: `admin`
- **Password**: `Admin@123`
- **Email**: `<EMAIL>`
- **User Type**: `Admin`

## 📋 Step 1: Create Admin User in MongoDB

### Option A: Using MongoDB Compass
1. Open MongoDB Compass
2. Connect to your database
3. Navigate to the `users` collection
4. Click "Insert Document"
5. Paste this JSON:

```json
{
  "cognitoId": "admin-temp-id",
  "email": "<EMAIL>",
  "firstName": "System",
  "lastName": "Administrator",
  "phone": "+33123456789",
  "userType": "Admin",
  "status": "Active",
  "verificationStatus": "Verified",
  "profileComplete": true,
  "lastLogin": new Date(),
  "createdAt": new Date(),
  "updatedAt": new Date()
}
```

### Option B: Using MongoDB Shell
```javascript
db.users.insertOne({
  cognitoId: "admin-temp-id",
  email: "<EMAIL>",
  firstName: "System",
  lastName: "Administrator",
  phone: "+33123456789",
  userType: "Admin",
  status: "Active",
  verificationStatus: "Verified",
  profileComplete: true,
  lastLogin: new Date(),
  createdAt: new Date(),
  updatedAt: new Date()
})
```

### Option C: Using the Script
```bash
cd backend
node scripts/create-admin-mongodb.js
```

## 🔐 Step 2: Create Admin User in AWS Cognito

### Using AWS Console:
1. Go to AWS Cognito Console
2. Select your User Pool
3. Go to "Users" tab
4. Click "Create user"
5. Fill in:
   - **Username**: `admin`
   - **Email**: `<EMAIL>`
   - **Temporary password**: `Admin@123`
   - **Given name**: `System`
   - **Family name**: `Administrator`
   - **Phone number**: `+33123456789`

6. In "Custom attributes" section:
   - **custom:userType**: `Admin`
   - **custom:profileComplete**: `true`

7. Uncheck "Send an invitation to this new user?"
8. Click "Create user"

### Set Permanent Password:
1. After creating the user, select the user
2. Go to "Actions" → "Set password"
3. Set password to: `Admin@123`
4. Select "Permanent" password type
5. Click "Set password"

### Confirm User:
1. In the user details, go to "Actions" → "Confirm user"
2. This marks the user as verified

## 🔄 Step 3: Update MongoDB with Cognito ID

After creating the Cognito user:
1. Copy the Cognito User ID (sub attribute)
2. Update the MongoDB document:

```javascript
db.users.updateOne(
  { email: "<EMAIL>" },
  { $set: { cognitoId: "PASTE_ACTUAL_COGNITO_ID_HERE" } }
)
```

## ✅ Step 4: Test Admin Login

1. Go to your application login page
2. Enter:
   - **Username**: `admin`
   - **Password**: `Admin@123`
3. You should be redirected to the Admin Dashboard

## 🎨 Admin Dashboard Features

The admin dashboard includes:

### 📊 **Statistics Overview**
- Total Users, Brokers, Suppliers, Clients
- Active Contracts, Invoices, Offers
- System health metrics

### 🔧 **Management Tools**
- **User Management**: View, edit, activate/deactivate users
- **Broker Management**: Approve/reject broker applications
- **Supplier Management**: Review and approve suppliers
- **Contract Oversight**: Monitor all contracts in the system

### 📈 **Analytics & Reports**
- System performance metrics
- User activity reports
- Financial summaries
- Export capabilities

### ⚙️ **System Administration**
- System settings and configuration
- User role management
- Security settings
- Backup and maintenance tools

## 🚀 Quick Actions Available

- Add new users
- Approve pending brokers
- Review supplier applications
- Export system data
- View system logs
- Manage user permissions

## 🔒 Security Features

- Admin-only access to sensitive data
- Audit logs for all admin actions
- Role-based permissions
- Secure authentication flow

## 📱 Navigation

Admin users get a specialized sidebar with:
- Dashboard (overview)
- Manage Users
- Manage Brokers  
- Manage Suppliers
- All Contracts
- Reports & Analytics
- System Settings
- Profile & Settings

## 🆘 Troubleshooting

### If login fails:
1. Check Cognito user is confirmed
2. Verify custom attributes are set correctly
3. Ensure MongoDB document exists
4. Check browser console for errors

### If dashboard doesn't load:
1. Verify userType is exactly "Admin" (case-sensitive)
2. Check localStorage for user data
3. Ensure profileComplete is true

### If navigation is wrong:
1. Clear browser cache and localStorage
2. Re-login to refresh user data
3. Check userType in localStorage

## 🎉 Success!

Once set up correctly, you'll have a fully functional admin dashboard with comprehensive system management capabilities!
