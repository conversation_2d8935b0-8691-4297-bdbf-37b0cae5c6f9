const mongoose = require('mongoose');

const energyRequestSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  userType: {
    type: String,
    enum: ['Individual', 'Professional'],
    required: true
  },
  requestType: {
    type: String,
    enum: ['Electricity', 'Gas', 'Both'],
    required: true
  },
  status: {
    type: String,
    enum: ['Draft', 'Submitted', 'UnderReview', 'OffersAvailable', 'Accepted', 'Completed', 'Cancelled'],
    default: 'Draft'
  },
  consumptionDetails: {
    annualConsumption: Number,
    averageMonthlyBill: Number,
    peakHoursUsage: Number,
    offPeakHoursUsage: Number
  },
  currentSupplier: {
    type: String,
    trim: true
  },
  currentContractEndDate: {
    type: Date
  },
  preferredDuration: {
    type: String,
    enum: ['6 months', '12 months', '24 months', '36 months', 'Other'],
  },
  additionalRequirements: {
    type: String,
    trim: true
  },
  documents: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Document'
  }],
  assignedBroker: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  notes: [{
    text: String,
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    createdAt: {
      type: Date,
      default: Date.now
    },
    isInternal: {
      type: Boolean,
      default: false
    }
  }]
}, {
  timestamps: true
});

// Create indexes for frequently queried fields
energyRequestSchema.index({ userId: 1 });
energyRequestSchema.index({ status: 1 });
energyRequestSchema.index({ assignedBroker: 1 });
energyRequestSchema.index({ requestType: 1 });

const EnergyRequest = mongoose.model('EnergyRequest', energyRequestSchema);

module.exports = EnergyRequest;
