/**
 * Utility function to force scroll to top
 * Call this manually if the automatic scroll-to-top isn't working
 */
export const forceScrollToTop = () => {
  // Try every possible method to scroll to top
  try {
    window.scrollTo(0, 0);
  } catch (e) {
    // Ignore errors
  }
  
  try {
    document.documentElement.scrollTop = 0;
  } catch (e) {
    // Ignore errors
  }
  
  try {
    document.body.scrollTop = 0;
  } catch (e) {
    // Ignore errors
  }
  
  try {
    window.scrollTo({ top: 0, left: 0, behavior: 'auto' });
  } catch (e) {
    // Ignore errors
  }
  
  // Scroll any containers that might be scrollable
  try {
    const containers = document.querySelectorAll(
      'main, .main-content, .content, .page-content, [role="main"], ' +
      '.dashboard-content, .authenticated-content, .home-container, ' +
      '.app-container, .page-container, .container, .wrapper, ' +
      '.fullscreen-container, .stepper-container'
    );
    containers.forEach(container => {
      if (container.scrollTo) {
        container.scrollTo(0, 0);
      } else {
        container.scrollTop = 0;
      }
    });
  } catch (e) {
    // Ignore errors
  }

  // Force scroll on html element as well
  try {
    const htmlElement = document.querySelector('html');
    if (htmlElement) {
      htmlElement.scrollTop = 0;
    }
  } catch (e) {
    // Ignore errors
  }

  console.log('forceScrollToTop: Executed all scroll methods');
};

/**
 * SMART scroll to top for authenticated pages and steppers
 * This version executes immediately and only has one quick follow-up to prevent glitches
 */
export const useForceScrollToTopAuthenticated = () => {
  console.log('🔥 useForceScrollToTopAuthenticated: Starting smart scroll to top');

  const smartScroll = () => {
    try {
      // Method 1: Standard methods
      window.scrollTo(0, 0);
      document.documentElement.scrollTop = 0;
      document.body.scrollTop = 0;
      window.scrollTo({ top: 0, left: 0, behavior: 'instant' });

      // Method 2: Target ALL possible authenticated containers
      const authContainers = document.querySelectorAll(
        '.authenticated-layout, .authenticated-content, .fullscreen-container, ' +
        '.stepper-container, .wizard-fullscreen, .content-wrapper, ' +
        '.main, main, .page-container, .app-container, .layout-container, ' +
        '.upload-container, .invoice-upload-container, .form-container, ' +
        '.step-container, .wizard-container, .info-page-container, ' +
        'body, html, #root, .App, [role="main"]'
      );

      let scrolledCount = 0;
      authContainers.forEach((container) => {
        try {
          if (container.scrollTo) {
            container.scrollTo(0, 0);
            container.scrollTo({ top: 0, left: 0, behavior: 'instant' });
          } else {
            container.scrollTop = 0;
          }
          scrolledCount++;
        } catch (e) {
          // Ignore individual container errors
        }
      });

      // Method 3: Force scroll on specific authenticated elements
      const specificElements = ['html', 'body', '#root', '.App'];
      specificElements.forEach(selector => {
        const element = document.querySelector(selector);
        if (element) {
          element.scrollTop = 0;
          if (element.scrollTo) {
            element.scrollTo(0, 0);
          }
        }
      });

      // Method 4: Override any CSS that might prevent scrolling
      document.documentElement.style.scrollBehavior = 'auto';
      document.body.style.scrollBehavior = 'auto';

      // Method 5: Use requestAnimationFrame for smooth execution
      requestAnimationFrame(() => {
        window.scrollTo(0, 0);
        document.documentElement.scrollTop = 0;
        document.body.scrollTop = 0;
      });

      console.log(`✅ useForceScrollToTopAuthenticated: Scrolled ${scrolledCount} containers`);
    } catch (error) {
      console.error('❌ useForceScrollToTopAuthenticated: Error:', error);
    }
  };

  // Execute immediately
  smartScroll();

  // Only one quick follow-up to handle any async content, then stop
  setTimeout(smartScroll, 50);

  console.log('⏰ useForceScrollToTopAuthenticated: Executed immediately with one 50ms follow-up');
};

/**
 * Call this in useEffect of any component that needs guaranteed scroll to top
 * Smart version with minimal delayed attempts to prevent glitches
 */
export const useForceScrollToTop = () => {
  console.log('useForceScrollToTop: Starting smart scroll to top');

  // Immediate execution
  forceScrollToTop();

  // Only one quick follow-up to handle async content
  setTimeout(forceScrollToTop, 50);   // After quick renders
};

export default forceScrollToTop;
