const express = require('express');
const router = express.Router();
const contactController = require('../controllers/contactController');
const { verifyToken } = require('../middleware/auth');

// Public routes (no authentication required)
router.post('/contact-form', contactController.submitContactForm);
router.post('/appointment', contactController.submitAppointmentBooking);

// Admin routes (authentication required)
router.get('/contact-forms', verifyToken, contactController.getContactForms);
router.get('/appointments', verifyToken, contactController.getAppointmentBookings);

module.exports = router;
