const dotenv = require('dotenv');
const path = require('path');

// Load environment variables
dotenv.config({
  path: path.resolve(__dirname, '.env.uat'),
  override: false,
});

const mongoose = require('mongoose');
const User = require('./models/User');

async function suspendTestUser() {
  try {
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    const user = await User.findOne({email: '<EMAIL>'});
    if (!user) {
      console.log('❌ User not found');
      return;
    }

    console.log('📧 User found:', user.email);
    console.log('🔍 Current status:', user.status);

    user.status = 'Suspended';
    user.updatedAt = new Date();
    await user.save();

    console.log('✅ User suspended successfully');
    console.log('🔍 New status:', user.status);

    await mongoose.disconnect();
    console.log('✨ Disconnected from MongoDB');
  } catch (error) {
    console.error('💥 Error:', error);
  }
}

suspendTestUser();
