const mongoose = require('mongoose');

const quoteRequestSchema = new mongoose.Schema({
  requestId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'EnergyRequest',
    required: true
  },
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  brokerId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  requestType: {
    type: String,
    enum: ['Automatic', 'Manual', 'Broker_Initiated'],
    default: 'Automatic'
  },
  status: {
    type: String,
    enum: ['Pending', 'Processing', 'Quotes_Available', 'Sent_to_Client', 'Approved', 'Rejected', 'Expired'],
    default: 'Pending'
  },
  priority: {
    type: String,
    enum: ['Low', 'Normal', 'High', 'Urgent'],
    default: 'Normal'
  },
  energyType: {
    type: String,
    enum: ['Electricity', 'Gas', 'Both'],
    required: true
  },
  consumptionDetails: {
    annualConsumption: Number,
    averageMonthlyBill: Number,
    peakHoursUsage: Number,
    offPeakHoursUsage: Number
  },
  currentSupplier: {
    type: String,
    trim: true
  },
  preferredDuration: {
    type: String,
    enum: ['6 months', '12 months', '24 months', '36 months', 'Other']
  },
  specialRequirements: {
    greenEnergy: Boolean,
    fixedRate: Boolean,
    variableRate: Boolean,
    dualFuel: Boolean,
    businessHours: Boolean
  },
  quotes: [{
    supplierId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    offerDetails: {
      name: String,
      description: String,
      highlights: [String]
    },
    pricing: {
      baseRate: Number,
      standingCharge: Number,
      totalEstimatedAnnual: Number,
      currency: {
        type: String,
        default: 'EUR'
      }
    },
    estimatedSavings: {
      amount: Number,
      percentage: Number
    },
    contractDuration: Number,
    rateType: {
      type: String,
      enum: ['Fixed', 'Variable', 'Indexed', 'Green']
    },
    additionalBenefits: [String],
    termsUrl: String,
    validUntil: Date,
    isRecommended: {
      type: Boolean,
      default: false
    },
    adminNotes: String,
    status: {
      type: String,
      enum: ['Active', 'Modified', 'Removed', 'Expired'],
      default: 'Active'
    },
    createdAt: {
      type: Date,
      default: Date.now
    }
  }],
  comparisonData: {
    totalQuotes: Number,
    bestSavings: {
      amount: Number,
      percentage: Number,
      supplierId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      }
    },
    averageSavings: {
      amount: Number,
      percentage: Number
    },
    recommendedQuote: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    }
  },
  adminActions: [{
    adminId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    action: {
      type: String,
      enum: ['Quote_Added', 'Quote_Modified', 'Quote_Removed', 'Status_Changed', 'Approved', 'Rejected', 'Notes_Added'],
      required: true
    },
    description: String,
    details: mongoose.Schema.Types.Mixed,
    timestamp: {
      type: Date,
      default: Date.now
    }
  }],
  clientNotifications: [{
    type: {
      type: String,
      enum: ['Email', 'SMS', 'In_App'],
      required: true
    },
    sentAt: {
      type: Date,
      default: Date.now
    },
    status: {
      type: String,
      enum: ['Sent', 'Delivered', 'Failed'],
      default: 'Sent'
    },
    content: String
  }],
  expiresAt: {
    type: Date,
    default: function() {
      return new Date(Date.now() + 30 * 24 * 60 * 60 * 1000); // 30 days from now
    }
  },
  processedAt: Date,
  sentToClientAt: Date,
  clientViewedAt: Date,
  adminNotes: String,
  internalNotes: String
}, {
  timestamps: true
});

// Create indexes for efficient querying
quoteRequestSchema.index({ userId: 1, createdAt: -1 });
quoteRequestSchema.index({ brokerId: 1, createdAt: -1 });
quoteRequestSchema.index({ status: 1 });
quoteRequestSchema.index({ priority: 1 });
quoteRequestSchema.index({ expiresAt: 1 });
quoteRequestSchema.index({ 'quotes.supplierId': 1 });

// Virtual for checking if request is expired
quoteRequestSchema.virtual('isExpired').get(function() {
  return this.expiresAt < new Date();
});

// Method to add admin action
quoteRequestSchema.methods.addAdminAction = function(adminId, action, description, details = {}) {
  this.adminActions.push({
    adminId,
    action,
    description,
    details
  });
  return this.save();
};

// Method to update quote status
quoteRequestSchema.methods.updateQuoteStatus = function(quoteIndex, newStatus, adminNotes = '') {
  if (this.quotes[quoteIndex]) {
    this.quotes[quoteIndex].status = newStatus;
    if (adminNotes) {
      this.quotes[quoteIndex].adminNotes = adminNotes;
    }
  }
  return this.save();
};

// Static method to get requests with filters
quoteRequestSchema.statics.getFilteredRequests = async function(filters = {}, options = {}) {
  const {
    page = 1,
    limit = 20,
    status,
    priority,
    energyType,
    brokerId,
    startDate,
    endDate
  } = options;

  const filter = { ...filters };
  
  if (status) filter.status = status;
  if (priority) filter.priority = priority;
  if (energyType) filter.energyType = energyType;
  if (brokerId) filter.brokerId = brokerId;
  
  if (startDate || endDate) {
    filter.createdAt = {};
    if (startDate) filter.createdAt.$gte = new Date(startDate);
    if (endDate) filter.createdAt.$lte = new Date(endDate);
  }

  const skip = (parseInt(page) - 1) * parseInt(limit);

  const [requests, totalCount] = await Promise.all([
    this.find(filter)
      .populate('userId', 'firstName lastName email userType')
      .populate('brokerId', 'firstName lastName email')
      .populate('quotes.supplierId', 'firstName lastName email')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit)),
    this.countDocuments(filter)
  ]);

  return {
    requests,
    pagination: {
      currentPage: parseInt(page),
      totalPages: Math.ceil(totalCount / parseInt(limit)),
      totalCount,
      hasNextPage: parseInt(page) < Math.ceil(totalCount / parseInt(limit)),
      hasPrevPage: parseInt(page) > 1
    }
  };
};

const QuoteRequest = mongoose.model('QuoteRequest', quoteRequestSchema);

module.exports = QuoteRequest;
