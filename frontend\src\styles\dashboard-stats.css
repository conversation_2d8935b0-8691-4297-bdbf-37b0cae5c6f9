.dashboard-stats-container {
  width: 100%;
  margin-bottom: 25px;
  padding: 0;
  max-width: 100%;
  background-color: transparent;
  overflow: hidden;
  box-sizing: border-box;
}

.dashboard-stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  padding: 0;
}

@media (max-width: 1200px) {
  .dashboard-stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }
}







/* New elegant card design */
.dashboard-stat-card {
  background: #fff;
  border-radius: 10px;
  padding: 20px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-start;
  transition: all 0.25s ease;
  cursor: pointer;
  position: relative;
  border: 1px solid #000;
  height: 140px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  color: #000;
  overflow: hidden; /* Prevent content overflow */
  width: 100%; /* Ensure full width */
}

.dashboard-stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
  background: #f9f9f9;
}

/* Refined icon style */
.stat-card-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  color: #000;
  background: transparent;
}

.stat-card-icon svg {
  width: 30px;
  height: 30px;
}

.stat-card-icon.invoices,
.stat-card-icon.offers,
.stat-card-icon.contracts,
.stat-card-icon.appointments {
  background: transparent;
}

.stat-card-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
}

.stat-card-title {
  font-size: 14px;
  font-weight: 600;
  color: #000;
  margin: 0 0 5px 0;
  letter-spacing: 0.3px;
}

.stat-card-value {
  font-size: 42px;
  font-weight: 700;
  color: #000;
  margin: 0;
  line-height: 1;
  position: absolute;
  bottom: 20px;
  right: 20px;
}

/* Mobile Override - Single Column Layout with Grid */
@media (max-width: 768px) {
  .dashboard-stats-container {
    padding: 0 10px !important;
    margin-bottom: 20px !important;
    width: calc(100% - 20px) !important;
    box-sizing: border-box !important;
  }

  .dashboard-stats-container .dashboard-stats-grid {
    grid-template-columns: 1fr !important;
    gap: 15px !important;
    width: 100% !important;
    box-sizing: border-box !important;
    padding: 0 !important;
    margin: 0 !important;
  }

  .dashboard-stats-container .dashboard-stat-card {
    padding: 15px !important;
    height: auto !important;
    min-height: 80px !important;
    width: 100% !important;
    box-sizing: border-box !important;
    overflow: hidden !important;
    display: grid !important;
    grid-template-columns: 1fr auto !important;
    gap: 15px !important;
    align-items: center !important;
    position: relative !important;
  }

  .dashboard-stats-container .stat-card-content {
    grid-column: 1 !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: flex-start !important;
    justify-content: center !important;
    min-width: 0 !important;
  }

  .dashboard-stats-container .stat-card-icon {
    width: 32px !important;
    height: 32px !important;
    margin-bottom: 8px !important;
  }

  .dashboard-stats-container .stat-card-icon svg {
    width: 28px !important;
    height: 28px !important;
  }

  .dashboard-stats-container .stat-card-title {
    font-size: 14px !important;
    line-height: 1.2 !important;
    margin-bottom: 4px !important;
    word-wrap: break-word !important;
  }

  .dashboard-stats-container .stat-card-description {
    font-size: 12px !important;
    line-height: 1.3 !important;
    word-wrap: break-word !important;
  }

  .dashboard-stats-container .stat-card-value {
    grid-column: 2 !important;
    font-size: 36px !important;
    font-weight: 700 !important;
    color: #000 !important;
    margin: 0 !important;
    line-height: 1 !important;
    position: static !important;
    text-align: center !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    min-width: 60px !important;
    height: auto !important;
    bottom: auto !important;
    right: auto !important;
  }
}

/* Small Mobile Screens */
@media (max-width: 480px) {
  .dashboard-stats-container {
    padding: 0 5px !important;
    width: calc(100% - 10px) !important;
  }

  .dashboard-stats-container .dashboard-stat-card {
    padding: 12px !important;
    min-height: 70px !important;
    gap: 10px !important;
  }

  .dashboard-stats-container .stat-card-icon {
    width: 28px !important;
    height: 28px !important;
  }

  .dashboard-stats-container .stat-card-icon svg {
    width: 24px !important;
    height: 24px !important;
  }

  .dashboard-stats-container .stat-card-title {
    font-size: 13px !important;
  }

  .dashboard-stats-container .stat-card-description {
    font-size: 11px !important;
  }

  .dashboard-stats-container .stat-card-value {
    font-size: 32px !important;
    min-width: 50px !important;
  }
}

.stat-card-description {
  font-size: 13px;
  color: #666;
  margin: 5px 0 0 0;
  line-height: 1.4;
  max-width: 90%;
}

/* Loading state */
.dashboard-stats-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  background: #fff;
  border-radius: 10px;
  border: 1px solid #000;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: #000;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Error state */
.dashboard-stats-error {
  padding: 20px;
  background: #fff;
  border-radius: 10px;
  border: 1px solid #000;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  text-align: center;
  color: #ff3b30;
}
