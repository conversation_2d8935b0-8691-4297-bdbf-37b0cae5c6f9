import React from 'react';
import { showErrorMessage } from '../../utils/toastNotifications';

const ProfessionalConfirmationStep = ({ formData, onChange, onSubmit, onPrev, onCancel }) => {
  const handleChange = (e) => {
    const { name, checked } = e.target;
    onChange(name, checked);
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (formData.authorizeDataAccess) {
      console.log('Authorization checkbox checked, submitting professional form');

      // Call the onSubmit function
      onSubmit();

      // No fallback redirect needed - the parent component handles this
    } else {
      console.warn('Authorization checkbox not checked, cannot submit form');
      showErrorMessage('VALIDATION_FAILED', 'You must authorize data access to proceed');
    }
  };

  return (
    <div>
      <div className="stepper-header">
        <h3 className="page-title">Confirmation</h3>
        <p className="page-subtitle">
          Please review your company information below and confirm that all details are correct
          before submitting your professional application.
        </p>
      </div>
      <form onSubmit={handleSubmit}>
        <div className="confirmation-summary">
          <h4>Review Your Information</h4>

          <div className="summary-section">
            <h5>Company Information</h5>
            <div className="summary-row">
              <span className="summary-label">Company Name:</span>
              <span className="summary-value">{formData.companyName}</span>
            </div>
            <div className="summary-row">
              <span className="summary-label">Your Role:</span>
              <span className="summary-value">{formData.role}</span>
            </div>
            <div className="summary-row">
              <span className="summary-label">SIRET Number:</span>
              <span className="summary-value">{formData.siretNumber}</span>
            </div>
            <div className="summary-row">
              <span className="summary-label">Company Address:</span>
              <span className="summary-value">
                {formData.streetAddress ? (
                  <>
                    {formData.streetAddress}<br />
                    {formData.city && formData.postalCode ? `${formData.city}, ${formData.postalCode}` : ''}<br />
                    {formData.country}
                  </>
                ) : (
                  'Not provided'
                )}
              </span>
            </div>
          </div>

          <div className="summary-section">
            <h5>Energy Details</h5>
            <div className="summary-row">
              <span className="summary-label">Energy Types:</span>
              <span className="summary-value">
                {[
                  formData.energyTypes.electricity ? 'Electricity' : null,
                  formData.energyTypes.gas ? 'Gas' : null
                ].filter(Boolean).join(', ')}
              </span>
            </div>
            <div className="summary-row">
              <span className="summary-label">Contract End Date:</span>
              <span className="summary-value">{formData.contractEndDate || 'Not provided'}</span>
            </div>
            <div className="summary-row">
              <span className="summary-label">Current Supplier:</span>
              <span className="summary-value">
                {formData.currentSupplier === 'Other' ? formData.otherSupplier : formData.currentSupplier}
              </span>
            </div>
            <div className="summary-row">
              <span className="summary-label">Preferred Contract Length:</span>
              <span className="summary-value">
                {formData.preferredContractLength ? `${formData.preferredContractLength} months` : 'Not provided'}
              </span>
            </div>
            <div className="summary-row">
              <span className="summary-label">PDL/PRM/RAE Number:</span>
              <span className="summary-value">{formData.meterNumber}</span>
            </div>
          </div>
        </div>

        <div className="form-group">
          <div className="checkbox-item authorization-checkbox">
            <input
              type="checkbox"
              id="authorizeDataAccess"
              name="authorizeDataAccess"
              checked={formData.authorizeDataAccess}
              onChange={handleChange}
            />
            <label htmlFor="authorizeDataAccess">
              I authorize My Energy Bill and its partners to access my consumption data for the past 12 months in order to provide offers <span className="required">*</span>
            </label>
          </div>
          {!formData.authorizeDataAccess && (
            <div className="error-message">Please authorize data access to continue</div>
          )}
        </div>

        <div className="stepper-buttons">
          <button type="button" className="stepper-button stepper-button-prev" onClick={onPrev}>
            Back
          </button>
          <button
            type="submit"
            className="stepper-button stepper-button-next"
            disabled={!formData.authorizeDataAccess}
            style={{
              backgroundColor: '#000000',
              borderColor: '#000000',
              color: '#ffffff',
              border: '1px solid #000000'
            }}
          >
            Submit
          </button>
        </div>
      </form>
    </div>
  );
};

export default ProfessionalConfirmationStep;
