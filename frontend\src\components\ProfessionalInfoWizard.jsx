import React, { useState, useEffect } from 'react';
import CompanyInfoStep from './wizard-steps/CompanyInfoStep';
import EnergyDetailsStep from './wizard-steps/EnergyDetailsStep';
import ProfessionalConfirmationStep from './wizard-steps/ProfessionalConfirmationStep';
import StepperProgress from './StepperProgress';
import { useForceScrollToTopAuthenticated } from '../utils/scrollToTop';
import '../styles/stepper.css';

const ProfessionalInfoWizard = ({ onSubmit, onCancel, userData }) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState({
    companyName: '',
    role: '',
    siretNumber: '',
    streetAddress: '',
    city: '',
    postalCode: '',
    country: 'France',
    energyTypes: {
      electricity: false,
      gas: false
    },
    contractEndDate: '',
    currentSupplier: '',
    otherSupplier: '',
    preferredContractLength: '',
    meterNumber: '',
    authorizeDataAccess: false
  });

  // Force scroll to top whenever step changes
  useEffect(() => {
    console.log('🔄 ProfessionalInfoWizard: Step changed to:', currentStep);
    useForceScrollToTopAuthenticated();
  }, [currentStep]);

  const totalSteps = 3;
  const stepLabels = ['Company Info', 'Energy & Meter', 'Confirmation'];

  const handleNextStep = () => {
    console.log('🔄 ProfessionalInfoWizard: Moving to next step');
    setCurrentStep(prev => Math.min(prev + 1, totalSteps));

    // Force scroll to top when moving to next step
    setTimeout(() => {
      useForceScrollToTopAuthenticated();
    }, 50); // Small delay to ensure state update completes
  };

  const handlePrevStep = () => {
    console.log('🔄 ProfessionalInfoWizard: Moving to previous step');
    setCurrentStep(prev => Math.max(prev - 1, 1));

    // Force scroll to top when moving to previous step
    setTimeout(() => {
      useForceScrollToTopAuthenticated();
    }, 50); // Small delay to ensure state update completes
  };

  const handleChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = () => {
    onSubmit(formData);
  };

  return (
    <div className="stepper-container">
      <div className="stepper-content">
        <StepperProgress
          currentStep={currentStep}
          steps={stepLabels}
        />

        <div className="stepper-form">
        {currentStep === 1 && (
          <CompanyInfoStep
            formData={formData}
            onChange={handleChange}
            onNext={handleNextStep}
            onCancel={onCancel}
          />
        )}

        {currentStep === 2 && (
          <EnergyDetailsStep
            formData={formData}
            onChange={handleChange}
            onNext={handleNextStep}
            onPrev={handlePrevStep}
          />
        )}

        {currentStep === 3 && (
          <ProfessionalConfirmationStep
            formData={formData}
            onChange={handleChange}
            onSubmit={handleSubmit}
            onPrev={handlePrevStep}
            onCancel={onCancel}
          />
        )}
        </div>
      </div>
    </div>
  );
};

export default ProfessionalInfoWizard;
