/**
 * <PERSON><PERSON><PERSON> to set up S3 bucket policy for AWS Textract access
 * Run this script to configure proper permissions for Textract to access S3 objects
 */

const AWS = require('aws-sdk');
require('dotenv').config();

// Configure AWS SDK
const awsConfig = {
  region: process.env.AWS_REGION || 'eu-west-3'
};

if (process.env.AWS_ACCESS_KEY_ID && process.env.AWS_SECRET_ACCESS_KEY) {
  awsConfig.accessKeyId = process.env.AWS_ACCESS_KEY_ID;
  awsConfig.secretAccessKey = process.env.AWS_SECRET_ACCESS_KEY;
}

AWS.config.update(awsConfig);

const s3 = new AWS.S3();
const bucketName = process.env.AWS_S3_BUCKET_NAME || 'energy-app-uat-backend-files';

/**
 * S3 Bucket Policy to allow Textract access
 */
const textractBucketPolicy = {
  Version: "2012-10-17",
  Statement: [
    {
      Sid: "AllowTextractAccess",
      Effect: "Allow",
      Principal: {
        Service: "textract.amazonaws.com"
      },
      Action: [
        "s3:GetObject",
        "s3:GetObjectVersion"
      ],
      Resource: `arn:aws:s3:::${bucketName}/*`
    },
    {
      Sid: "AllowTextractListBucket",
      Effect: "Allow", 
      Principal: {
        Service: "textract.amazonaws.com"
      },
      Action: "s3:ListBucket",
      Resource: `arn:aws:s3:::${bucketName}`
    }
  ]
};

/**
 * Set up S3 bucket policy for Textract access
 */
async function setupTextractPermissions() {
  try {
    console.log('Setting up S3 bucket policy for Textract access...');
    console.log('Bucket:', bucketName);
    
    // Get current bucket policy
    let currentPolicy = null;
    try {
      const policyResult = await s3.getBucketPolicy({ Bucket: bucketName }).promise();
      currentPolicy = JSON.parse(policyResult.Policy);
      console.log('Current bucket policy found');
    } catch (error) {
      if (error.code === 'NoSuchBucketPolicy') {
        console.log('No existing bucket policy found');
      } else {
        throw error;
      }
    }
    
    // Merge with existing policy if present
    let finalPolicy = textractBucketPolicy;
    if (currentPolicy) {
      finalPolicy = {
        Version: "2012-10-17",
        Statement: [
          ...currentPolicy.Statement,
          ...textractBucketPolicy.Statement
        ]
      };
      console.log('Merged with existing bucket policy');
    }
    
    // Apply the bucket policy
    await s3.putBucketPolicy({
      Bucket: bucketName,
      Policy: JSON.stringify(finalPolicy, null, 2)
    }).promise();
    
    console.log('✅ S3 bucket policy updated successfully!');
    console.log('Textract should now be able to access objects in the bucket');
    
    // Test bucket access
    console.log('\nTesting bucket access...');
    const objects = await s3.listObjectsV2({ 
      Bucket: bucketName, 
      MaxKeys: 5 
    }).promise();
    
    console.log(`Found ${objects.Contents.length} objects in bucket`);
    if (objects.Contents.length > 0) {
      console.log('Sample objects:', objects.Contents.map(obj => obj.Key).slice(0, 3));
    }
    
  } catch (error) {
    console.error('❌ Error setting up Textract permissions:', error.message);
    console.error('Error details:', {
      code: error.code,
      statusCode: error.statusCode,
      message: error.message
    });
    
    if (error.code === 'AccessDenied') {
      console.log('\n💡 Suggestions:');
      console.log('1. Ensure your AWS credentials have s3:PutBucketPolicy permission');
      console.log('2. Check if bucket policy restrictions prevent modification');
      console.log('3. Verify the bucket name is correct');
    }
  }
}

/**
 * Remove Textract permissions (cleanup)
 */
async function removeTextractPermissions() {
  try {
    console.log('Removing Textract permissions from bucket policy...');
    
    const policyResult = await s3.getBucketPolicy({ Bucket: bucketName }).promise();
    const currentPolicy = JSON.parse(policyResult.Policy);
    
    // Filter out Textract-related statements
    const filteredStatements = currentPolicy.Statement.filter(statement => 
      !(statement.Principal && statement.Principal.Service === 'textract.amazonaws.com')
    );
    
    if (filteredStatements.length === 0) {
      // Delete the entire policy if no statements remain
      await s3.deleteBucketPolicy({ Bucket: bucketName }).promise();
      console.log('✅ Bucket policy deleted (no remaining statements)');
    } else {
      // Update with filtered statements
      const updatedPolicy = {
        Version: "2012-10-17",
        Statement: filteredStatements
      };
      
      await s3.putBucketPolicy({
        Bucket: bucketName,
        Policy: JSON.stringify(updatedPolicy, null, 2)
      }).promise();
      
      console.log('✅ Textract permissions removed from bucket policy');
    }
    
  } catch (error) {
    console.error('❌ Error removing Textract permissions:', error.message);
  }
}

// Main execution
if (require.main === module) {
  const action = process.argv[2];
  
  if (action === 'remove') {
    removeTextractPermissions();
  } else {
    setupTextractPermissions();
  }
}

module.exports = {
  setupTextractPermissions,
  removeTextractPermissions
};
