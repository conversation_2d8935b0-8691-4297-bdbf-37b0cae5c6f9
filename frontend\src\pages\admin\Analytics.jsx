import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import DashboardLayout from '../../components/DashboardLayout';
import Spinner from '../../components/Spinner';
import { showSuccessMessage, showErrorMessage } from '../../utils/toastNotifications';
import logger from '../../utils/logger';
import { loadCSS, unloadCSS, ADMIN_CSS, CSS_IDS } from '../../utils/cssLoader';
import { API_BASE_URL } from '../../config/api-config';

const Analytics = () => {
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');
  const [period, setPeriod] = useState('30days');
  const [analyticsData, setAnalyticsData] = useState({
    overview: null,
    users: null,
    contracts: null,
    brokers: null,
    suppliers: null
  });
  const navigate = useNavigate();

  // Dynamically load CSS only for this component
  useEffect(() => {
    loadCSS(ADMIN_CSS.ANALYTICS, CSS_IDS.ADMIN_ANALYTICS);

    // Cleanup function to remove CSS when component unmounts
    return () => {
      unloadCSS(CSS_IDS.ADMIN_ANALYTICS);
    };
  }, []);

  useEffect(() => {
    fetchAnalyticsData();
  }, [period, activeTab]);

  const fetchAnalyticsData = async () => {
    try {
      setLoading(true);
      logger.info(`Fetching ${activeTab} analytics for period: ${period}`);

      const response = await fetch(`${API_BASE_URL}/api/admin/analytics/${activeTab}?period=${period}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch analytics data');
      }

      const data = await response.json();
      setAnalyticsData(prev => ({
        ...prev,
        [activeTab]: data.data
      }));
      
      logger.info('Analytics data fetched successfully');
    } catch (error) {
      logger.error('Error fetching analytics data:', error);
      showErrorMessage('ANALYTICS_LOAD_FAILED', 'Failed to load analytics data');
    } finally {
      setLoading(false);
    }
  };

  const formatNumber = (num) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num?.toLocaleString() || '0';
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR'
    }).format(amount || 0);
  };

  const renderOverviewTab = () => {
    const data = analyticsData.overview;
    if (!data) return null;

    return (
      <div className="analytics-overview">
        {/* Key Metrics Cards */}
        <div className="metrics-grid">
          <div className="metric-card users">
            <div className="metric-icon">
              <i className="fas fa-users"></i>
            </div>
            <div className="metric-content">
              <h3>{formatNumber(data.userStats?.totalUsers)}</h3>
              <p>Total Users</p>
              <span className="metric-change positive">
                +{data.userStats?.newUsers} new
              </span>
            </div>
          </div>

          <div className="metric-card contracts">
            <div className="metric-icon">
              <i className="fas fa-file-contract"></i>
            </div>
            <div className="metric-content">
              <h3>{formatNumber(data.contractStats?.totalContracts)}</h3>
              <p>Total Contracts</p>
              <span className="metric-change positive">
                +{data.contractStats?.newContracts} new
              </span>
            </div>
          </div>

          <div className="metric-card revenue">
            <div className="metric-icon">
              <i className="fas fa-euro-sign"></i>
            </div>
            <div className="metric-content">
              <h3>{formatCurrency(data.revenueStats?.totalRevenue)}</h3>
              <p>Total Revenue</p>
              <span className="metric-change positive">
                {formatCurrency(data.revenueStats?.averageRevenue)} avg
              </span>
            </div>
          </div>

          <div className="metric-card savings">
            <div className="metric-icon">
              <i className="fas fa-piggy-bank"></i>
            </div>
            <div className="metric-content">
              <h3>{formatCurrency(data.savingsStats?.totalSavings)}</h3>
              <p>Total Savings</p>
              <span className="metric-change positive">
                {formatCurrency(data.savingsStats?.averageSavings)} avg
              </span>
            </div>
          </div>

          <div className="metric-card conversion">
            <div className="metric-icon">
              <i className="fas fa-chart-line"></i>
            </div>
            <div className="metric-content">
              <h3>{data.conversionStats?.conversionRate}%</h3>
              <p>Conversion Rate</p>
              <span className="metric-change">
                {data.conversionStats?.quotes} quotes → {data.conversionStats?.contracts} contracts
              </span>
            </div>
          </div>

          <div className="metric-card active-users">
            <div className="metric-icon">
              <i className="fas fa-user-check"></i>
            </div>
            <div className="metric-content">
              <h3>{formatNumber(data.userStats?.activeUsers)}</h3>
              <p>Active Users</p>
              <span className="metric-change">
                Last 30 days
              </span>
            </div>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="quick-stats">
          <div className="stat-section">
            <h4>Platform Performance</h4>
            <div className="stat-list">
              <div className="stat-item">
                <span className="stat-label">Lead Conversion Rate:</span>
                <span className="stat-value">{data.conversionStats?.conversionRate}%</span>
              </div>
              <div className="stat-item">
                <span className="stat-label">Average Contract Value:</span>
                <span className="stat-value">{formatCurrency(data.revenueStats?.averageRevenue)}</span>
              </div>
              <div className="stat-item">
                <span className="stat-label">Total Savings Generated:</span>
                <span className="stat-value">{formatCurrency(data.savingsStats?.totalSavings)}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const renderUsersTab = () => {
    const data = analyticsData.users;
    if (!data) return null;

    return (
      <div className="analytics-users">
        <div className="analytics-section">
          <h4>User Type Breakdown</h4>
          <div className="user-type-grid">
            {data.userTypeBreakdown?.map(type => (
              <div key={type._id} className="user-type-card">
                <div className="type-icon">
                  <i className={`fas fa-${type._id === 'Individual' ? 'user' : 
                    type._id === 'Professional' ? 'user-tie' :
                    type._id === 'Broker' ? 'handshake' :
                    type._id === 'Supplier' ? 'industry' : 'user-shield'}`}></i>
                </div>
                <div className="type-content">
                  <h5>{type.count}</h5>
                  <p>{type._id}</p>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="analytics-section">
          <h4>User Activity</h4>
          <div className="activity-stats">
            {data.activityStats?.map(activity => (
              <div key={activity._id} className="activity-item">
                <span className="activity-type">{activity._id}</span>
                <span className="activity-count">{activity.count}</span>
              </div>
            ))}
          </div>
        </div>

        <div className="analytics-section">
          <h4>User Retention</h4>
          <div className="retention-stats">
            <div className="retention-metric">
              <span className="retention-label">Active Users (7 days):</span>
              <span className="retention-value">{data.retentionStats?.activeUsers}</span>
            </div>
            <div className="retention-metric">
              <span className="retention-label">Retention Rate:</span>
              <span className="retention-value">{data.retentionStats?.retentionRate}%</span>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const renderContractsTab = () => {
    const data = analyticsData.contracts;
    if (!data) return null;

    return (
      <div className="analytics-contracts">
        <div className="analytics-section">
          <h4>Contract Statistics</h4>
          <div className="contract-stats-grid">
            <div className="stat-card">
              <h5>Average Contract Value</h5>
              <p>{formatCurrency(data.averageContractValue?.average)}</p>
            </div>
            <div className="stat-card">
              <h5>Contract Duration</h5>
              <p>{data.contractDuration?.[0]?.averageDuration?.toFixed(1)} months</p>
            </div>
          </div>
        </div>

        <div className="analytics-section">
          <h4>Contracts by Energy Type</h4>
          <div className="energy-type-breakdown">
            {data.contractsByType?.map(type => (
              <div key={type._id} className="energy-type-item">
                <span className="energy-type-label">{type._id}</span>
                <span className="energy-type-count">{type.count}</span>
              </div>
            ))}
          </div>
        </div>

        <div className="analytics-section">
          <h4>Top Suppliers</h4>
          <div className="supplier-ranking">
            {data.contractsBySupplier?.map((supplier, index) => (
              <div key={supplier._id} className="supplier-item">
                <span className="supplier-rank">#{index + 1}</span>
                <span className="supplier-name">
                  {supplier.supplier?.firstName} {supplier.supplier?.lastName}
                </span>
                <span className="supplier-contracts">{supplier.count} contracts</span>
                <span className="supplier-value">{formatCurrency(supplier.totalValue)}</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  };

  const renderBrokersTab = () => {
    const data = analyticsData.brokers;
    if (!data) return null;

    return (
      <div className="analytics-brokers">
        <div className="analytics-section">
          <h4>Top Performing Brokers</h4>
          <div className="broker-ranking">
            {data.topBrokers?.map((broker, index) => (
              <div key={broker._id} className="broker-item">
                <span className="broker-rank">#{index + 1}</span>
                <span className="broker-name">
                  {broker.broker?.firstName} {broker.broker?.lastName}
                </span>
                <span className="broker-contracts">{broker.contractsCount} contracts</span>
                <span className="broker-value">{formatCurrency(broker.totalValue)}</span>
                <span className="broker-commission">{formatCurrency(broker.totalCommissions)}</span>
              </div>
            ))}
          </div>
        </div>

        <div className="analytics-section">
          <h4>Commission Statistics</h4>
          <div className="commission-stats">
            <div className="commission-metric">
              <span className="commission-label">Total Commissions:</span>
              <span className="commission-value">{formatCurrency(data.commissionStats?.totalCommissions)}</span>
            </div>
            <div className="commission-metric">
              <span className="commission-label">Paid Commissions:</span>
              <span className="commission-value">{formatCurrency(data.commissionStats?.paidCommissions)}</span>
            </div>
            <div className="commission-metric">
              <span className="commission-label">Average Commission:</span>
              <span className="commission-value">{formatCurrency(data.commissionStats?.averageCommission)}</span>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const renderSuppliersTab = () => {
    const data = analyticsData.suppliers;
    if (!data) return null;

    return (
      <div className="analytics-suppliers">
        <div className="analytics-section">
          <h4>Top Suppliers</h4>
          <div className="supplier-ranking">
            {data.topSuppliers?.map((supplier, index) => (
              <div key={supplier._id} className="supplier-item">
                <span className="supplier-rank">#{index + 1}</span>
                <span className="supplier-name">
                  {supplier.supplier?.firstName} {supplier.supplier?.lastName}
                </span>
                <span className="supplier-contracts">{supplier.contractsCount} contracts</span>
                <span className="supplier-value">{formatCurrency(supplier.totalValue)}</span>
              </div>
            ))}
          </div>
        </div>

        <div className="analytics-section">
          <h4>Offer Statistics</h4>
          <div className="offer-stats">
            <div className="offer-metric">
              <span className="offer-label">Total Offers:</span>
              <span className="offer-value">{data.offerStats?.totalOffers}</span>
            </div>
            <div className="offer-metric">
              <span className="offer-label">New Offers:</span>
              <span className="offer-value">{data.offerStats?.newOffers}</span>
            </div>
            <div className="offer-metric">
              <span className="offer-label">Acceptance Rate:</span>
              <span className="offer-value">{data.offerStats?.acceptanceRate}%</span>
            </div>
          </div>
        </div>
      </div>
    );
  };

  if (loading) {
    return <Spinner fullScreen={true} message="Loading analytics..." size="large" />;
  }

  return (
    <DashboardLayout>
      <div className="analytics-container">
        {/* Header */}
        <div className="analytics-header">
          <div className="header-content">
            <button 
              className="back-button"
              onClick={() => navigate('/dashboard')}
            >
              <i className="fas fa-arrow-left"></i>
              Back to Dashboard
            </button>
            <h1>Statistics & KPIs</h1>
            <p>Comprehensive platform analytics and performance metrics</p>
          </div>
          
          {/* Period Selector */}
          <div className="period-selector">
            <label>Period:</label>
            <select 
              value={period} 
              onChange={(e) => setPeriod(e.target.value)}
            >
              <option value="7days">Last 7 Days</option>
              <option value="30days">Last 30 Days</option>
              <option value="90days">Last 90 Days</option>
              <option value="1year">Last Year</option>
            </select>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="analytics-tabs">
          <button 
            className={`tab-button ${activeTab === 'overview' ? 'active' : ''}`}
            onClick={() => setActiveTab('overview')}
          >
            <i className="fas fa-chart-pie"></i>
            Overview
          </button>
          <button 
            className={`tab-button ${activeTab === 'users' ? 'active' : ''}`}
            onClick={() => setActiveTab('users')}
          >
            <i className="fas fa-users"></i>
            Users
          </button>
          <button 
            className={`tab-button ${activeTab === 'contracts' ? 'active' : ''}`}
            onClick={() => setActiveTab('contracts')}
          >
            <i className="fas fa-file-contract"></i>
            Contracts
          </button>
          <button 
            className={`tab-button ${activeTab === 'brokers' ? 'active' : ''}`}
            onClick={() => setActiveTab('brokers')}
          >
            <i className="fas fa-handshake"></i>
            Brokers
          </button>
          <button 
            className={`tab-button ${activeTab === 'suppliers' ? 'active' : ''}`}
            onClick={() => setActiveTab('suppliers')}
          >
            <i className="fas fa-industry"></i>
            Suppliers
          </button>
        </div>

        {/* Tab Content */}
        <div className="analytics-content">
          {activeTab === 'overview' && renderOverviewTab()}
          {activeTab === 'users' && renderUsersTab()}
          {activeTab === 'contracts' && renderContractsTab()}
          {activeTab === 'brokers' && renderBrokersTab()}
          {activeTab === 'suppliers' && renderSuppliersTab()}
        </div>
      </div>
    </DashboardLayout>
  );
};

export default Analytics;
