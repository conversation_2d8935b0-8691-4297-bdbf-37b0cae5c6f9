service: energy-app-backend

frameworkVersion: '3'
provider:
  name: aws
  region: ${opt:stage, 'eu-west-3'}
  stage: ${opt:stage, 'uat'}
  deploymentBucket:
    name: ${self:custom.deploymentBucketName}
  
custom:
  prefix: ${param:PREFIX}
  emailId: ${param:EMAIL_ID}
  deploymentBucketName: ${param:PIPELINE_ARTIFACT_BUCKET_NAME}
  backendEcrRepositoryUri: ${param:BACKEND_ECR_REPOSITORY_URI}
  imageTag: ${param:IMAGE_TAG}
  vpcId: ${param:VPC_ID}
  publicsubnet1: ${param:PUBLIC_SUBNET_1}
  publicsubnet2: ${param:PUBLIC_SUBNET_2}
  BackendDomainCertificateArn: ${param:BACKEND_DOMAIN_CERTIFICATE_ARN}

resources:
  Resources:
    EcsClusterTaskRole:
      Type: AWS::IAM::Role
      Properties:
        RoleName: !Sub ${self:custom.prefix}-${self:provider.stage}-ecs-task-role
        AssumeRolePolicyDocument:
          Version: '2012-10-17'
          Statement:
            - Effect: Allow
              Principal:
                Service: ecs-tasks.amazonaws.com
              Action: sts:AssumeRole
        Policies:
          - PolicyName: ECS-Task-Execution-Policy
            PolicyDocument:
              Version: '2012-10-17'
              Statement:
                - Effect: Allow
                  Action:
                    - ecr:GetAuthorizationToken
                    - ecr:BatchCheckLayerAvailability
                    - ecr:GetDownloadUrlForLayer
                    - ecr:BatchGetImage
                    - logs:CreateLogGroup
                    - logs:CreateLogStream
                    - logs:PutLogEvents
                    - logs:DescribeLogStreams
                    - logs:DescribeLogGroups
                  Resource: '*'
    #Security Group for ALB
    ALBSecurityGroup:
      Type: AWS::EC2::SecurityGroup
      Properties:
        GroupDescription: Allow traffic to ALB
        VpcId: ${self:custom.vpcId}
        SecurityGroupIngress:
          - IpProtocol: tcp
            FromPort: 443
            ToPort: 443
            CidrIp: 0.0.0.0/0

    LoadBalancer:
      Type: AWS::ElasticLoadBalancingV2::LoadBalancer
      Properties:
        Name: !Sub ${self:custom.prefix}-${self:provider.stage}-alb
        Subnets:
          - ${self:custom.publicsubnet1}
          - ${self:custom.publicsubnet2}
        SecurityGroups:
          - !Ref ALBSecurityGroup
        Scheme: internet-facing
        LoadBalancerAttributes:
          - Key: idle_timeout.timeout_seconds
            Value: '60'
    
    TargetGroup:
      Type: AWS::ElasticLoadBalancingV2::TargetGroup
      Properties:
        Name: !Sub ${self:custom.prefix}-${self:provider.stage}-tg
        VpcId: ${self:custom.vpcId}
        Protocol: HTTP
        Port: 80
        HealthCheckProtocol: HTTP
        HealthCheckPath: /api/health/
        Matcher:
          HttpCode: 200
        TargetType: ip
  
    ALBListenerHTTPS:
      Type: AWS::ElasticLoadBalancingV2::Listener
      Properties:
        DefaultActions:
          - Type: forward
            TargetGroupArn: !Ref TargetGroup
        LoadBalancerArn: !Ref LoadBalancer
        Port: 443
        Protocol: HTTPS
        Certificates:
          - CertificateArn: !Sub ${self:custom.BackendDomainCertificateArn}

    ECSSecurityGroup:
      Type: AWS::EC2::SecurityGroup
      Properties:
        GroupDescription: Allow traffic to ECS
        VpcId: ${self:custom.vpcId}
        SecurityGroupIngress:
          - IpProtocol: tcp
            FromPort: 3000
            ToPort: 3000
            SourceSecurityGroupId: !Ref ALBSecurityGroup

    #Resource for ECS
    ECSExecutionRole:
      Type: AWS::IAM::Role
      Properties:
        RoleName: !Sub ${self:custom.prefix}-${self:provider.stage}-ecs-execution-role
        AssumeRolePolicyDocument:
          Version: '2012-10-17'
          Statement:
            - Effect: Allow
              Principal:
                Service:
                  - ecs-tasks.amazonaws.com
              Action:
                - sts:AssumeRole
        Policies:
          - PolicyName: ECRAccess
            PolicyDocument:
              Version: '2012-10-17'
              Statement:
                - Effect: Allow
                  Action:
                    - ecr:GetAuthorizationToken
                    - ecr:BatchCheckLayerAvailability
                    - ecr:GetDownloadUrlForLayer
                    - ecr:BatchGetImage
                    - logs:CreateLogGroup
                    - logs:CreateLogStream
                    - logs:PutLogEvents
                    - logs:DescribeLogStreams
                    - logs:DescribeLogGroups
                  Resource: '*'

    TaskExecutionRole:
      Type: AWS::IAM::Role
      Properties:
        RoleName: !Sub ${self:custom.prefix}-${self:provider.stage}-ecs-task-execution-role
        AssumeRolePolicyDocument:
          Version: '2012-10-17'
          Statement:
            - Effect: Allow
              Principal:
                Service:
                  - ecs-tasks.amazonaws.com
              Action:
                - sts:AssumeRole
        Policies:
            - PolicyName: CloudWatchLogsPolicy
              PolicyDocument:
                Version: '2012-10-17'
                Statement:
                  - Effect: Allow
                    Action:
                      - logs:*
                    Resource: "*"
            - PolicyName: S3FullAccessPolicy
              PolicyDocument:
                Version: '2012-10-17'
                Statement:
                  - Effect: Allow
                    Action:
                      - s3:*
                    Resource:
                      - !Sub "arn:aws:s3:::${FileUploadBucket}"
                      - !Sub "arn:aws:s3:::${FileUploadBucket}/*"
            - PolicyName: CognitoAccessPolicy
              PolicyDocument:
                Version: '2012-10-17'
                Statement:
                  - Effect: Allow
                    Action:
                      - cognito-idp:*
                    Resource: "*"
            - PolicyName: SESAccessPolicy
              PolicyDocument:
                Version: '2012-10-17'
                Statement:
                  - Effect: Allow
                    Action: 
                      - ses:*
                    Resource: "*"
            - PolicyName: TextractAccessPolicy
              PolicyDocument:
                Version: '2012-10-17'
                Statement:
                  - Effect: Allow
                    Action:
                      - textract:*
                    Resource: "*"
     
    BackendTaskDefinition:
      Type: AWS::ECS::TaskDefinition
      Properties:
        Family: !Sub ${self:custom.prefix}-${self:provider.stage}-task-definition
        RequiresCompatibilities:
          - FARGATE
        NetworkMode: awsvpc
        Cpu: 1024
        Memory: 2048 
        EphemeralStorage:
          SizeInGiB: 21
        RuntimePlatform:
          CpuArchitecture: X86_64
          OperatingSystemFamily: LINUX
        ExecutionRoleArn: !GetAtt ECSExecutionRole.Arn
        TaskRoleArn: !GetAtt TaskExecutionRole.Arn
        ContainerDefinitions:
          - Name: !Sub ${self:custom.prefix}-${self:provider.stage}-container
            Image: !Sub ${self:custom.backendEcrRepositoryUri}:${self:custom.imageTag}
            PortMappings:
              - Name: "express_server_port"
                ContainerPort: 3000
                HostPort: 3000
                Protocol: tcp
                AppProtocol: http
            Essential: true
            Environment:
              - Name: NODE_ENV
                Value: !Sub ${self:provider.stage}
              - Name: AWS_REGION
                Value: !Ref AWS::Region
              - Name: AWS_S3_BUCKET_NAME
                Value: !Ref FileUploadBucket
              - Name: AWS_S3_BUCKET
                Value: !Ref FileUploadBucket
              - Name: COGNITO_USER_POOL_ID
                Value: !Ref CognitoUserPool
              - Name: COGNITO_CLIENT_ID
                Value: !Ref CognitoUserPoolClient
            LogConfiguration:
              LogDriver: awslogs
              Options:
                awslogs-group: !Sub /ecs/${self:custom.prefix}-${self:provider.stage}-logs
                awslogs-create-group: "true"
                awslogs-region: !Ref AWS::Region
                awslogs-stream-prefix: ecs
    
    BackendECSCluster:
      Type: AWS::ECS::Cluster
      Properties:
        ClusterName: !Sub ${self:custom.prefix}-${self:provider.stage}-ecs-cluster

    BackendECSService:
      Type: AWS::ECS::Service
      DependsOn: ALBListenerHTTPS
      Properties:
        Cluster: !Ref BackendECSCluster
        ServiceName: !Sub ${self:custom.prefix}-${self:provider.stage}-service
        TaskDefinition: !Ref BackendTaskDefinition
        LaunchType: FARGATE
        DesiredCount: 1
        NetworkConfiguration:
          AwsvpcConfiguration:
            Subnets:
              - ${self:custom.publicsubnet1}
              - ${self:custom.publicsubnet2}
            SecurityGroups:
              - !Ref ECSSecurityGroup
            AssignPublicIp: ENABLED
        LoadBalancers:
          - ContainerName: !Sub ${self:custom.prefix}-${self:provider.stage}-container
            ContainerPort: 3000
            TargetGroupArn: !Ref TargetGroup
    
    FileUploadBucket:
      Type: AWS::S3::Bucket
      Properties:
        BucketName: !Sub ${self:custom.prefix}-${self:provider.stage}-backend-file-upload-bucket
        BucketEncryption:
          ServerSideEncryptionConfiguration:
            - ServerSideEncryptionByDefault:
                SSEAlgorithm: AES256
        PublicAccessBlockConfiguration:
          BlockPublicAcls: true
          BlockPublicPolicy: true
          IgnorePublicAcls: true
          RestrictPublicBuckets: true

    CognitoEmailConfigSet:
      Type: AWS::SES::ConfigurationSet
      Properties:
        Name: !Sub ${self:custom.prefix}-${self:provider.stage}-cognito-email

    CognitoUserPool:
      Type: AWS::Cognito::UserPool
      Properties:
        UserPoolName: !Sub ${self:custom.prefix}-${self:provider.stage}-userpool
        AccountRecoverySetting:
          RecoveryMechanisms:
            - Name: verified_email
              Priority: 1
        AdminCreateUserConfig:
          AllowAdminCreateUserOnly: false
        MfaConfiguration: "OFF"
        AutoVerifiedAttributes:
          - email
        UsernameAttributes:
          - email
        Policies:
          PasswordPolicy:
            MinimumLength: 8
            RequireLowercase: True
            RequireNumbers: True
            RequireSymbols: True
            RequireUppercase: True
        Schema:
          - Name: email
            AttributeDataType: String
            Mutable: true
            Required: true
          - Name: phone_number
            AttributeDataType: String
            Mutable: true
            Required: true
          - Name: given_name
            AttributeDataType: String
            Mutable: true
            Required: true
          - Name: family_name 
            AttributeDataType: String
            Mutable: true
            Required: true
          - Name: profileCompletion
            AttributeDataType: String
            DeveloperOnlyAttribute: false
            Mutable: true
            Required: false
            StringAttributeConstraints:
              MinLength: "0"
              MaxLength: "2048"
          - Name: userType
            AttributeDataType: String
            DeveloperOnlyAttribute: false
            Mutable: true
            Required: false
            StringAttributeConstraints:
              MinLength: "0"
              MaxLength: "2048"
        VerificationMessageTemplate:
          DefaultEmailOption: CONFIRM_WITH_CODE
          EmailMessage: !Sub ${file('./email-template/verificationEmail.html')}
          EmailSubject: "My Energy Bill Verification Code"
        EmailConfiguration:
          ConfigurationSet: !Ref CognitoEmailConfigSet
          EmailSendingAccount: DEVELOPER
          From: !Sub My Energy Bill App <${self:custom.emailId}>
          SourceArn: !Sub arn:aws:ses:${AWS::Region}:${AWS::AccountId}:identity/${self:custom.emailId}

    CognitoUserPoolClient:
      Type: AWS::Cognito::UserPoolClient
      Properties:
        ClientName: !Sub ${self:custom.prefix}-${self:provider.stage}-UserPoolClient
        UserPoolId: !Ref CognitoUserPool
        SupportedIdentityProviders:
          - COGNITO
        ExplicitAuthFlows:
          - ALLOW_ADMIN_USER_PASSWORD_AUTH
          - ALLOW_USER_PASSWORD_AUTH
          - ALLOW_REFRESH_TOKEN_AUTH
          - ALLOW_USER_SRP_AUTH
          - ALLOW_CUSTOM_AUTH
          - ALLOW_USER_AUTH
        GenerateSecret: false
        IdTokenValidity: 60
        AccessTokenValidity: 60
        RefreshTokenValidity: 1
        TokenValidityUnits:
          AccessToken: "minutes"
          IdToken: "minutes"
          RefreshToken: "days"
