import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { getItem, STORAGE_KEYS } from '../utils/localStorage';
import { showSuccessMessage, showErrorMessage } from '../utils/toastNotifications';
import Spinner from '../components/Spinner';
import { RequestResetForm, ResetPasswordForm, SuccessForm } from '../components/ForgotPasswordForm';
import api from '../services/api';
import logger from '../utils/logger';

const ForgotPassword = () => {
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  const [resetCode, setResetCode] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [passwordStrength, setPasswordStrength] = useState({ score: 0, isValid: false, text: 'Very Weak', color: '#ff4d4d' });
  const [step, setStep] = useState(1); // 1: Request reset, 2: Enter code and new password, 3: Success
  const [useEmailReset, setUseEmailReset] = useState(true); // Toggle between email and Cognito reset
  const navigate = useNavigate();
  const { forgotPassword, resetPassword, isAuthenticated, loading: authLoading } = useAuth();

  // Check if user is already authenticated and redirect to dashboard
  useEffect(() => {
    // First, ensure we're not in a state where there are residual tokens but the user isn't actually authenticated
    const checkAndCleanupAuth = async () => {
      try {
        // Try to get the current authenticated user from Cognito
        await Auth.currentAuthenticatedUser();
        // If we get here, the user is authenticated
      } catch (error) {
        // If we get here, the user is not authenticated, so clear any residual state
        logger.info('User not authenticated but may have residual tokens, clearing state');
        localStorage.clear();
        sessionStorage.clear();
        return; // Exit early
      }

      // Only proceed with redirection if the user is actually authenticated
      if (!authLoading && isAuthenticated) {
        logger.info('User is already authenticated, redirecting to dashboard');

        // Check if user has a user type and profile completion status
        const userType = getItem(STORAGE_KEYS.USER_TYPE);
        const profileComplete = getItem(STORAGE_KEYS.PROFILE_COMPLETION) === 'true';

        // Only redirect if we have valid user data
        if (userType) {
          if (!profileComplete) {
            // If profile not complete, redirect to appropriate info page
            if (userType.toLowerCase() === 'individual') {
              navigate('/individual-info');
            } else {
              navigate('/professional-info');
            }
          } else {
            // If profile is complete, redirect to dashboard
            navigate('/dashboard');
          }
        } else {
          // If authenticated but no user type, redirect to dashboard
          // The ProtectedRoute component will handle the redirection to user type selection
          navigate('/dashboard');
        }
      }
    };

    checkAndCleanupAuth();
  }, [isAuthenticated, authLoading, navigate]);

  // Validate email format
  const validateEmail = (email) => {
    const emailRegex = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    return emailRegex.test(email.toLowerCase());
  };

  // Password strength checker
  const checkPasswordStrength = (password) => {
    let strength = 0;

    // Length check
    if (password.length >= 8) strength += 1;

    // Contains lowercase
    if (/[a-z]/.test(password)) strength += 1;

    // Contains uppercase
    if (/[A-Z]/.test(password)) strength += 1;

    // Contains number
    if (/[0-9]/.test(password)) strength += 1;

    // Contains special character
    if (/[^A-Za-z0-9]/.test(password)) strength += 1;

    return {
      score: strength,
      isValid: strength >= 4,
      text: strength === 0 ? 'Very Weak' :
            strength === 1 ? 'Weak' :
            strength === 2 ? 'Fair' :
            strength === 3 ? 'Good' :
            strength === 4 ? 'Strong' : 'Very Strong',
      color: strength === 0 ? '#ff4d4d' :
             strength === 1 ? '#ffaa00' :
             strength === 2 ? '#ffcc00' :
             strength === 3 ? '#cccc00' :
             strength === 4 ? '#73cc00' : '#00cc00'
    };
  };

  // Handle request password reset with email
  const handleEmailReset = async (e) => {
    e.preventDefault();

    // Validate email
    if (!email) {
      setError('Please enter your email address');
      return;
    }

    if (!validateEmail(email)) {
      setError('Please enter a valid email address');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      logger.info('Email-based password reset requested for:', email);

      const response = await api.post('/api/auth/request-password-reset', {
        email: email.toLowerCase().trim()
      });

      if (response.data.success) {
        showSuccessMessage('PASSWORD_RESET_SENT', 'Password reset instructions have been sent to your email.');
        setStep(3); // Go directly to success step for email reset
        setSuccess(true);
        logger.info('Password reset email sent successfully');
      } else {
        setError(response.data.message || 'Failed to send password reset email.');
      }
    } catch (err) {
      logger.error('Email password reset request error:', err);
      setError('Failed to send password reset email. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Handle request password reset (Cognito method)
  const handleRequestReset = async (e) => {
    e.preventDefault();

    // Use email reset if enabled
    if (useEmailReset) {
      return handleEmailReset(e);
    }

    // Validate email
    if (!email) {
      setError('Please enter your email address');
      return;
    }

    if (!validateEmail(email)) {
      setError('Please enter a valid email address');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      logger.info('Cognito password reset requested for:', email);

      // Call Cognito to initiate forgot password flow
      await forgotPassword(email);

      logger.info('Password reset code sent');

      // Move to next step
      setStep(2);
      setSuccess(true);
    } catch (err) {
      logger.error('Password reset request error:', err);

      if (err.code === 'UserNotFoundException') {
        // For security reasons, we don't want to reveal if a user exists or not
        // So we'll show a generic success message even if the user doesn't exist
        setStep(2);
        setSuccess(true);
      } else if (err.code === 'LimitExceededException') {
        setError('Too many attempts. Please try again later.');
      } else {
        setError(err.message || 'Failed to request password reset. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  // Handle password change
  const handlePasswordChange = (e) => {
    const password = e.target.value;
    setNewPassword(password);

    // Check password strength
    const strength = checkPasswordStrength(password);
    setPasswordStrength(strength);
  };

  // Handle password reset confirmation
  const handleResetPassword = async (e) => {
    e.preventDefault();

    // Validate inputs
    if (!resetCode) {
      setError('Please enter the verification code');
      return;
    }

    if (!newPassword) {
      setError('Please enter a new password');
      return;
    }

    // Check password strength
    const strength = checkPasswordStrength(newPassword);
    if (!strength.isValid) {
      setError('Password does not meet security requirements. Please include at least 8 characters with uppercase, lowercase, numbers, and special characters.');
      return;
    }

    if (newPassword !== confirmPassword) {
      setError('Passwords do not match');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      logger.info('Confirming password reset for:', email);

      // Call Cognito to reset password
      await resetPassword(email, resetCode, newPassword);

      logger.info('Password reset successful');

      // Show success message
      setSuccess(true);
      setStep(3); // Success step
    } catch (err) {
      logger.error('Password reset error:', err);

      if (err.code === 'CodeMismatchException') {
        setError('Invalid verification code. Please try again.');
      } else if (err.code === 'ExpiredCodeException') {
        setError('Verification code has expired. Please request a new code.');
      } else if (err.code === 'LimitExceededException') {
        setError('Too many attempts. Please try again later.');
      } else if (err.code === 'InvalidPasswordException') {
        setError('Password does not meet AWS Cognito requirements. Please use at least 8 characters, including uppercase, lowercase, numbers, and special characters.');
      } else {
        setError(err.message || 'Failed to reset password. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      {step === 1 && (
        <RequestResetForm
          email={email}
          setEmail={setEmail}
          handleRequestReset={handleRequestReset}
          error={error}
          useEmailReset={useEmailReset}
          setUseEmailReset={setUseEmailReset}
        />
      )}

      {step === 2 && !useEmailReset && (
        <ResetPasswordForm
          email={email}
          resetCode={resetCode}
          setResetCode={setResetCode}
          newPassword={newPassword}
          setNewPassword={handlePasswordChange}
          confirmPassword={confirmPassword}
          setConfirmPassword={setConfirmPassword}
          passwordStrength={passwordStrength}
          handleResetPassword={handleResetPassword}
          error={error}
        />
      )}

      {step === 3 && <SuccessForm useEmailReset={useEmailReset} />}

      {loading && (
        <Spinner
          fullScreen={true}
          message={useEmailReset ? 'Sending reset email...' : (step === 1 ? 'Sending reset code...' : 'Resetting password...')}
          size="large"
          color="#000000"
        />
      )}
    </>
  );
};

export default ForgotPassword;
