import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import logger from '../utils/logger';
import './AccountStatus.css';

const AccountStatus = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const [statusInfo, setStatusInfo] = useState(null);

  useEffect(() => {
    // Get status information from location state or URL params
    const state = location.state;
    const urlParams = new URLSearchParams(location.search);
    
    const status = state?.status || urlParams.get('status') || 'UNKNOWN';
    const message = state?.message || urlParams.get('message') || 'Account access restricted';
    const supportEmail = state?.supportEmail || '<EMAIL>';

    setStatusInfo({
      status,
      message,
      supportEmail
    });

    logger.info('AccountStatus page loaded with status:', status);
  }, [location]);

  const getStatusConfig = (status) => {
    switch (status) {
      case 'ACCOUNT_SUSPENDED':
        return {
          title: 'Account Suspended',
          icon: '🚫',
          color: '#ff3b30',
          backgroundColor: '#fff5f5',
          borderColor: '#fed7d7',
          description: 'Your account has been suspended by our administrative team.',
          reasons: [
            'Violation of terms of service',
            'Suspicious account activity',
            'Administrative review required',
            'Security concerns'
          ],
          actions: [
            'Contact our support team for assistance',
            'Provide any requested documentation',
            'Wait for administrative review'
          ]
        };
      
      case 'ACCOUNT_INACTIVE':
        return {
          title: 'Account Inactive',
          icon: '⏸️',
          color: '#ff9500',
          backgroundColor: '#fffbf0',
          borderColor: '#fed7aa',
          description: 'Your account is currently inactive and requires reactivation.',
          reasons: [
            'Account was deactivated by admin',
            'Extended period of inactivity',
            'Account maintenance required',
            'Profile updates needed'
          ],
          actions: [
            'Contact support to reactivate your account',
            'Update your profile information if requested',
            'Verify your contact details'
          ]
        };
      
      case 'ACCOUNT_PENDING':
        return {
          title: 'Account Pending Approval',
          icon: '⏳',
          color: '#007aff',
          backgroundColor: '#f0f9ff',
          borderColor: '#bfdbfe',
          description: 'Your account is pending administrative approval.',
          reasons: [
            'New account awaiting verification',
            'Profile information under review',
            'Documentation verification in progress',
            'Administrative approval required'
          ],
          actions: [
            'Wait for admin approval (usually 1-2 business days)',
            'Check your email for any requests for additional information',
            'Contact support if approval is taking longer than expected'
          ]
        };
      
      default:
        return {
          title: 'Account Access Restricted',
          icon: '⚠️',
          color: '#8e8e93',
          backgroundColor: '#f2f2f7',
          borderColor: '#d1d1d6',
          description: 'Your account access is currently restricted.',
          reasons: [
            'Account status needs verification',
            'Administrative review required'
          ],
          actions: [
            'Contact our support team for assistance',
            'Provide account verification if requested'
          ]
        };
    }
  };

  const handleContactSupport = () => {
    const subject = encodeURIComponent(`Account Status Issue - ${statusInfo?.status || 'Unknown'}`);
    const body = encodeURIComponent(`Hello,

I am experiencing an account status issue and need assistance.

Account Status: ${statusInfo?.status || 'Unknown'}
Message: ${statusInfo?.message || 'No message provided'}

Please help me resolve this issue.

Thank you.`);
    
    window.location.href = `mailto:${statusInfo?.supportEmail}?subject=${subject}&body=${body}`;
  };

  const handleBackToLogin = () => {
    navigate('/login');
  };

  if (!statusInfo) {
    return (
      <div className="account-status-container">
        <div className="loading">Loading...</div>
      </div>
    );
  }

  const config = getStatusConfig(statusInfo.status);

  return (
    <div className="account-status-container">
      <div 
        className="account-status-card"
        style={{
          backgroundColor: config.backgroundColor,
          borderColor: config.borderColor
        }}
      >
        <div className="status-header">
          <div className="status-icon" style={{ fontSize: '48px' }}>
            {config.icon}
          </div>
          <h1 className="status-title" style={{ color: config.color }}>
            {config.title}
          </h1>
        </div>

        <div className="status-content">
          <p className="status-description">
            {config.description}
          </p>

          <div className="status-message">
            <strong>Message:</strong> {statusInfo.message}
          </div>

          <div className="status-section">
            <h3>Possible Reasons:</h3>
            <ul>
              {config.reasons.map((reason, index) => (
                <li key={index}>{reason}</li>
              ))}
            </ul>
          </div>

          <div className="status-section">
            <h3>What You Can Do:</h3>
            <ul>
              {config.actions.map((action, index) => (
                <li key={index}>{action}</li>
              ))}
            </ul>
          </div>

          <div className="support-info">
            <h3>Need Help?</h3>
            <p>
              If you believe this is an error or need immediate assistance, 
              please contact our support team at{' '}
              <strong>{statusInfo.supportEmail}</strong>
            </p>
          </div>
        </div>

        <div className="status-actions">
          <button 
            className="btn-primary"
            onClick={handleContactSupport}
            style={{ backgroundColor: config.color }}
          >
            📧 Contact Support
          </button>
          <button 
            className="btn-secondary"
            onClick={handleBackToLogin}
          >
            ← Back to Login
          </button>
        </div>
      </div>
    </div>
  );
};

export default AccountStatus;
