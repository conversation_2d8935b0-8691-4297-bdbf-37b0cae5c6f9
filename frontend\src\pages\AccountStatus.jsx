import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { showSuccessMessage, showErrorMessage } from '../utils/toastNotifications';
import logger from '../utils/logger';
import './AccountStatus.css';

const AccountStatus = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const [statusInfo, setStatusInfo] = useState(null);
  const [showContactOptions, setShowContactOptions] = useState(false);
  const [supportTicketForm, setSupportTicketForm] = useState({
    subject: '',
    message: '',
    priority: 'high'
  });
  const [isSubmittingTicket, setIsSubmittingTicket] = useState(false);
  const [ticketSubmissionStatus, setTicketSubmissionStatus] = useState(null);

  useEffect(() => {
    // Get status information from location state or URL params
    const state = location.state;
    const urlParams = new URLSearchParams(location.search);
    
    const status = state?.status || urlParams.get('status') || 'UNKNOWN';
    const message = state?.message || urlParams.get('message') || 'Account access restricted';
    const supportEmail = state?.supportEmail || '<EMAIL>';

    setStatusInfo({
      status,
      message,
      supportEmail
    });

    logger.info('AccountStatus page loaded with status:', status);
  }, [location]);

  const getStatusConfig = (status) => {
    switch (status) {
      case 'ACCOUNT_SUSPENDED':
        return {
          title: 'Account Suspended',
          icon: '🚫',
          color: '#ff3b30',
          backgroundColor: '#fff5f5',
          borderColor: '#fed7d7',
          description: 'Your account has been suspended by our administrative team.',
          reasons: [
            'Violation of terms of service',
            'Suspicious account activity',
            'Administrative review required',
            'Security concerns'
          ],
          actions: [
            'Contact our support team for assistance',
            'Provide any requested documentation',
            'Wait for administrative review'
          ]
        };
      
      case 'ACCOUNT_INACTIVE':
        return {
          title: 'Account Inactive',
          icon: '⏸️',
          color: '#ff9500',
          backgroundColor: '#fffbf0',
          borderColor: '#fed7aa',
          description: 'Your account is currently inactive and requires reactivation.',
          reasons: [
            'Account was deactivated by admin',
            'Extended period of inactivity',
            'Account maintenance required',
            'Profile updates needed'
          ],
          actions: [
            'Contact support to reactivate your account',
            'Update your profile information if requested',
            'Verify your contact details'
          ]
        };
      
      case 'ACCOUNT_PENDING':
        return {
          title: 'Account Pending Approval',
          icon: '⏳',
          color: '#007aff',
          backgroundColor: '#f0f9ff',
          borderColor: '#bfdbfe',
          description: 'Your account is pending administrative approval.',
          reasons: [
            'New account awaiting verification',
            'Profile information under review',
            'Documentation verification in progress',
            'Administrative approval required'
          ],
          actions: [
            'Wait for admin approval (usually 1-2 business days)',
            'Check your email for any requests for additional information',
            'Contact support if approval is taking longer than expected'
          ]
        };
      
      default:
        return {
          title: 'Account Access Restricted',
          icon: '⚠️',
          color: '#8e8e93',
          backgroundColor: '#f2f2f7',
          borderColor: '#d1d1d6',
          description: 'Your account access is currently restricted.',
          reasons: [
            'Account status needs verification',
            'Administrative review required'
          ],
          actions: [
            'Contact our support team for assistance',
            'Provide account verification if requested'
          ]
        };
    }
  };

  const handleContactSupport = () => {
    setShowContactOptions(true);
  };

  const handleSupportTicketSubmit = async (e) => {
    e.preventDefault();

    // Validation
    if (!supportTicketForm.message.trim()) {
      showErrorMessage('VALIDATION_ERROR', 'Please provide a detailed description of your issue.');
      return;
    }

    if (supportTicketForm.message.trim().length < 10) {
      showErrorMessage('VALIDATION_ERROR', 'Please provide a more detailed description (at least 10 characters).');
      return;
    }

    setIsSubmittingTicket(true);
    setTicketSubmissionStatus(null);

    try {
      // Get user email from URL params or status info
      const urlParams = new URLSearchParams(location.search);
      const userEmail = urlParams.get('email') || statusInfo?.userEmail || '<EMAIL>';

      const ticketData = {
        email: userEmail,
        subject: supportTicketForm.subject.trim() || `Account Status Issue - ${statusInfo?.status || 'Unknown'}`,
        message: supportTicketForm.message.trim(),
        priority: supportTicketForm.priority,
        status: statusInfo?.status,
        errorCode: statusInfo?.error || statusInfo?.errorCode
      };

      logger.info('Creating support ticket:', {
        email: ticketData.email,
        subject: ticketData.subject,
        priority: ticketData.priority,
        status: ticketData.status
      });

      const response = await fetch(`${import.meta.env.VITE_API_URL || 'http://localhost:3000'}/auth/create-account-status-ticket`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(ticketData)
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to create support ticket');
      }

      logger.info('Support ticket created successfully:', data);

      // Show success message with ticket details
      setTicketSubmissionStatus({
        success: true,
        ticketNumber: data.ticketNumber,
        ticketId: data.ticketId,
        message: data.message
      });

      showSuccessMessage('TICKET_CREATED',
        `🎫 Support ticket ${data.ticketNumber} created successfully! We'll respond within 24 hours.`
      );

      // Reset form after a short delay
      setTimeout(() => {
        setSupportTicketForm({
          subject: '',
          message: '',
          priority: 'high'
        });
        setShowContactOptions(false);
        setTicketSubmissionStatus(null);
      }, 3000);

    } catch (error) {
      console.error('Error creating support ticket:', error);

      setTicketSubmissionStatus({
        success: false,
        error: error.message
      });

      showErrorMessage('TICKET_CREATION_FAILED',
        `Failed to create support ticket: ${error.message}. Please try again or contact us directly.`
      );
    } finally {
      setIsSubmittingTicket(false);
    }
  };

  const handleFormChange = (e) => {
    const { name, value } = e.target;
    setSupportTicketForm(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleBackToLogin = () => {
    navigate('/login');
  };

  if (!statusInfo) {
    return (
      <div className="account-status-container">
        <div className="loading">Loading...</div>
      </div>
    );
  }

  const config = getStatusConfig(statusInfo.status);

  return (
    <div className="account-status-container">
      <div 
        className="account-status-card"
        style={{
          backgroundColor: config.backgroundColor,
          borderColor: config.borderColor
        }}
      >
        <div className="status-header">
          <div className="status-icon" style={{ fontSize: '48px' }}>
            {config.icon}
          </div>
          <h1 className="status-title" style={{ color: config.color }}>
            {config.title}
          </h1>
        </div>

        <div className="status-content">
          <p className="status-description">
            {config.description}
          </p>

          <div className="status-message">
            <strong>Message:</strong> {statusInfo.message}
          </div>

          <div className="status-section">
            <h3>Possible Reasons:</h3>
            <ul>
              {config.reasons.map((reason, index) => (
                <li key={index}>{reason}</li>
              ))}
            </ul>
          </div>

          <div className="status-section">
            <h3>What You Can Do:</h3>
            <ul>
              {config.actions.map((action, index) => (
                <li key={index}>{action}</li>
              ))}
            </ul>
          </div>

          <div className="support-info">
            <h3>Need Help?</h3>
            <p>
              If you believe this is an error or need immediate assistance, 
              please contact our support team at{' '}
              <strong>{statusInfo.supportEmail}</strong>
            </p>
          </div>
        </div>

        <div className="status-actions">
          <button
            className="btn-primary"
            onClick={handleContactSupport}
            style={{ backgroundColor: config.color }}
          >
            📧 Contact Support
          </button>
          <button
            className="btn-secondary"
            onClick={handleBackToLogin}
          >
            ← Back to Login
          </button>
        </div>

        {/* Enhanced Contact Support Modal */}
        {showContactOptions && (
          <div className="contact-modal-overlay" onClick={() => setShowContactOptions(false)}>
            <div className="contact-modal" onClick={(e) => e.stopPropagation()}>
              <div className="contact-modal-header">
                <h2>🎫 Contact Support</h2>
                <button
                  className="close-modal"
                  onClick={() => setShowContactOptions(false)}
                  title="Close"
                >
                  ✕
                </button>
              </div>

              <div className="modal-content-wrapper">
                <div className="support-ticket-form">
                  <div className="form-intro">
                    <div className="form-intro-icon">🎫</div>
                    <h3>Create Support Ticket</h3>
                    <p>Get professional help with your account status issue. We'll respond within 24 hours.</p>
                  </div>

                  <form onSubmit={handleSupportTicketSubmit}>
                    <div className="form-group">
                      <label htmlFor="ticket-subject">Subject *</label>
                      <input
                        type="text"
                        id="ticket-subject"
                        name="subject"
                        value={supportTicketForm.subject}
                        onChange={handleFormChange}
                        placeholder={`Account Status Issue - ${statusInfo?.status || 'Unknown'}`}
                        required
                      />
                    </div>

                    <div className="form-group">
                      <label htmlFor="ticket-message">Detailed Description *</label>
                      <textarea
                        id="ticket-message"
                        name="message"
                        value={supportTicketForm.message}
                        onChange={handleFormChange}
                        placeholder="Please describe your account status issue in detail. Include any relevant information that might help us resolve your case quickly..."
                        rows="5"
                        required
                      ></textarea>
                    </div>

                    <div className="form-group">
                      <label htmlFor="ticket-priority">Priority Level</label>
                      <select
                        id="ticket-priority"
                        name="priority"
                        value={supportTicketForm.priority}
                        onChange={handleFormChange}
                      >
                        <option value="high">🔴 High - Account Access Issue</option>
                        <option value="medium">🟡 Medium - General Inquiry</option>
                        <option value="low">🟢 Low - Information Request</option>
                      </select>
                    </div>

                    {/* Submission Status */}
                    {ticketSubmissionStatus && (
                      <div className={`submission-status ${ticketSubmissionStatus.success ? 'success' : 'error'}`}>
                        {ticketSubmissionStatus.success ? (
                          <div className="success-message">
                            <div className="success-icon">✅</div>
                            <div className="success-text">
                              <strong>Ticket Created Successfully!</strong>
                              <p>Ticket Number: <strong>{ticketSubmissionStatus.ticketNumber}</strong></p>
                              <p>We'll respond within 24 hours to your email.</p>
                            </div>
                          </div>
                        ) : (
                          <div className="error-message">
                            <div className="error-icon">❌</div>
                            <div className="error-text">
                              <strong>Failed to Create Ticket</strong>
                              <p>{ticketSubmissionStatus.error}</p>
                            </div>
                          </div>
                        )}
                      </div>
                    )}

                    <div className="ticket-form-actions">
                      <button
                        type="submit"
                        className="btn-primary"
                        disabled={isSubmittingTicket || !supportTicketForm.message.trim()}
                      >
                        {isSubmittingTicket ? (
                          <>
                            <span className="loading-spinner"></span>
                            Creating Ticket...
                          </>
                        ) : (
                          <>
                            🎫 Create Support Ticket
                          </>
                        )}
                      </button>
                      <button
                        type="button"
                        className="btn-secondary"
                        onClick={() => setShowContactOptions(false)}
                        disabled={isSubmittingTicket}
                      >
                        Cancel
                      </button>
                    </div>
                  </form>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AccountStatus;
