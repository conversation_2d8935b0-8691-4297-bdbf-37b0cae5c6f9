import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { showSuccessMessage, showErrorMessage } from '../utils/toastNotifications';
import logger from '../utils/logger';
import './AccountStatus.css';

const AccountStatus = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const [statusInfo, setStatusInfo] = useState(null);
  const [showContactOptions, setShowContactOptions] = useState(false);
  const [supportTicketForm, setSupportTicketForm] = useState({
    subject: '',
    message: '',
    priority: 'high'
  });

  useEffect(() => {
    // Get status information from location state or URL params
    const state = location.state;
    const urlParams = new URLSearchParams(location.search);
    
    const status = state?.status || urlParams.get('status') || 'UNKNOWN';
    const message = state?.message || urlParams.get('message') || 'Account access restricted';
    const supportEmail = state?.supportEmail || '<EMAIL>';

    setStatusInfo({
      status,
      message,
      supportEmail
    });

    logger.info('AccountStatus page loaded with status:', status);
  }, [location]);

  const getStatusConfig = (status) => {
    switch (status) {
      case 'ACCOUNT_SUSPENDED':
        return {
          title: 'Account Suspended',
          icon: '🚫',
          color: '#ff3b30',
          backgroundColor: '#fff5f5',
          borderColor: '#fed7d7',
          description: 'Your account has been suspended by our administrative team.',
          reasons: [
            'Violation of terms of service',
            'Suspicious account activity',
            'Administrative review required',
            'Security concerns'
          ],
          actions: [
            'Contact our support team for assistance',
            'Provide any requested documentation',
            'Wait for administrative review'
          ]
        };
      
      case 'ACCOUNT_INACTIVE':
        return {
          title: 'Account Inactive',
          icon: '⏸️',
          color: '#ff9500',
          backgroundColor: '#fffbf0',
          borderColor: '#fed7aa',
          description: 'Your account is currently inactive and requires reactivation.',
          reasons: [
            'Account was deactivated by admin',
            'Extended period of inactivity',
            'Account maintenance required',
            'Profile updates needed'
          ],
          actions: [
            'Contact support to reactivate your account',
            'Update your profile information if requested',
            'Verify your contact details'
          ]
        };
      
      case 'ACCOUNT_PENDING':
        return {
          title: 'Account Pending Approval',
          icon: '⏳',
          color: '#007aff',
          backgroundColor: '#f0f9ff',
          borderColor: '#bfdbfe',
          description: 'Your account is pending administrative approval.',
          reasons: [
            'New account awaiting verification',
            'Profile information under review',
            'Documentation verification in progress',
            'Administrative approval required'
          ],
          actions: [
            'Wait for admin approval (usually 1-2 business days)',
            'Check your email for any requests for additional information',
            'Contact support if approval is taking longer than expected'
          ]
        };
      
      default:
        return {
          title: 'Account Access Restricted',
          icon: '⚠️',
          color: '#8e8e93',
          backgroundColor: '#f2f2f7',
          borderColor: '#d1d1d6',
          description: 'Your account access is currently restricted.',
          reasons: [
            'Account status needs verification',
            'Administrative review required'
          ],
          actions: [
            'Contact our support team for assistance',
            'Provide account verification if requested'
          ]
        };
    }
  };

  const handleContactSupport = () => {
    setShowContactOptions(true);
  };

  const handleEmailSupport = () => {
    const subject = encodeURIComponent(`Account Status Issue - ${statusInfo?.status || 'Unknown'}`);
    const body = encodeURIComponent(`Hello,

I am experiencing an account status issue and need assistance.

Account Status: ${statusInfo?.status || 'Unknown'}
Message: ${statusInfo?.message || 'No message provided'}

Please help me resolve this issue.

Thank you.`);

    window.location.href = `mailto:${statusInfo?.supportEmail}?subject=${subject}&body=${body}`;
  };

  const handlePhoneSupport = () => {
    // Copy phone number to clipboard and show message
    navigator.clipboard.writeText('+33 1 23 45 67 89').then(() => {
      showSuccessMessage('OPERATION_COMPLETED', 'Phone number copied to clipboard: +33 1 23 45 67 89');
    }).catch(() => {
      showErrorMessage('OPERATION_FAILED', 'Could not copy phone number. Please call: +33 1 23 45 67 89');
    });
  };

  const handleSupportTicketSubmit = async (e) => {
    e.preventDefault();

    try {
      // Get user email from URL params or status info
      const urlParams = new URLSearchParams(location.search);
      const userEmail = urlParams.get('email') || '<EMAIL>';

      const ticketData = {
        email: userEmail,
        subject: supportTicketForm.subject || `Account Status Issue - ${statusInfo?.status || 'Unknown'}`,
        message: supportTicketForm.message,
        priority: supportTicketForm.priority,
        status: statusInfo?.status,
        errorCode: statusInfo?.error
      };

      logger.info('Creating support ticket:', ticketData);

      const response = await fetch(`${import.meta.env.VITE_API_URL || 'http://localhost:3000'}/auth/create-account-status-ticket`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(ticketData)
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to create support ticket');
      }

      showSuccessMessage('OPERATION_COMPLETED',
        `Support ticket ${data.ticketNumber} created successfully! We will contact you soon.`
      );

      // Reset form
      setSupportTicketForm({
        subject: '',
        message: '',
        priority: 'high'
      });
      setShowContactOptions(false);

    } catch (error) {
      console.error('Error creating support ticket:', error);
      showErrorMessage('OPERATION_FAILED',
        'Failed to create support ticket. Please try email or phone support.'
      );
    }
  };

  const handleFormChange = (e) => {
    const { name, value } = e.target;
    setSupportTicketForm(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleBackToLogin = () => {
    navigate('/login');
  };

  if (!statusInfo) {
    return (
      <div className="account-status-container">
        <div className="loading">Loading...</div>
      </div>
    );
  }

  const config = getStatusConfig(statusInfo.status);

  return (
    <div className="account-status-container">
      <div 
        className="account-status-card"
        style={{
          backgroundColor: config.backgroundColor,
          borderColor: config.borderColor
        }}
      >
        <div className="status-header">
          <div className="status-icon" style={{ fontSize: '48px' }}>
            {config.icon}
          </div>
          <h1 className="status-title" style={{ color: config.color }}>
            {config.title}
          </h1>
        </div>

        <div className="status-content">
          <p className="status-description">
            {config.description}
          </p>

          <div className="status-message">
            <strong>Message:</strong> {statusInfo.message}
          </div>

          <div className="status-section">
            <h3>Possible Reasons:</h3>
            <ul>
              {config.reasons.map((reason, index) => (
                <li key={index}>{reason}</li>
              ))}
            </ul>
          </div>

          <div className="status-section">
            <h3>What You Can Do:</h3>
            <ul>
              {config.actions.map((action, index) => (
                <li key={index}>{action}</li>
              ))}
            </ul>
          </div>

          <div className="support-info">
            <h3>Need Help?</h3>
            <p>
              If you believe this is an error or need immediate assistance, 
              please contact our support team at{' '}
              <strong>{statusInfo.supportEmail}</strong>
            </p>
          </div>
        </div>

        <div className="status-actions">
          <button
            className="btn-primary"
            onClick={handleContactSupport}
            style={{ backgroundColor: config.color }}
          >
            📧 Contact Support
          </button>
          <button
            className="btn-secondary"
            onClick={handleBackToLogin}
          >
            ← Back to Login
          </button>
        </div>

        {/* Enhanced Contact Support Modal */}
        {showContactOptions && (
          <div className="contact-modal-overlay" onClick={() => setShowContactOptions(false)}>
            <div className="contact-modal" onClick={(e) => e.stopPropagation()}>
              <div className="contact-modal-header">
                <h2>Contact Support</h2>
                <button
                  className="close-modal"
                  onClick={() => setShowContactOptions(false)}
                >
                  ✕
                </button>
              </div>

              <div className="contact-options">
                <div className="contact-option-card">
                  <div className="contact-option-icon">📞</div>
                  <h3>Phone Support</h3>
                  <p>Immediate assistance for urgent issues</p>
                  <p className="contact-detail">+33 1 23 45 67 89</p>
                  <p className="contact-hours">Mon-Fri, 9:00 AM - 6:00 PM</p>
                  <button
                    className="contact-option-btn"
                    onClick={handlePhoneSupport}
                  >
                    Copy Phone Number
                  </button>
                </div>

                <div className="contact-option-card">
                  <div className="contact-option-icon">📧</div>
                  <h3>Email Support</h3>
                  <p>Detailed assistance via email</p>
                  <p className="contact-detail">{statusInfo?.supportEmail || '<EMAIL>'}</p>
                  <p className="contact-hours">Response within 24 hours</p>
                  <button
                    className="contact-option-btn"
                    onClick={handleEmailSupport}
                  >
                    Send Email
                  </button>
                </div>

                <div className="contact-option-card">
                  <div className="contact-option-icon">🎫</div>
                  <h3>Support Ticket</h3>
                  <p>Track your issue with a ticket</p>
                  <p className="contact-detail">Organized support process</p>
                  <p className="contact-hours">Full issue tracking</p>
                  <button
                    className="contact-option-btn"
                    onClick={() => document.getElementById('ticket-form').scrollIntoView()}
                  >
                    Create Ticket
                  </button>
                </div>
              </div>

              <div id="ticket-form" className="support-ticket-form">
                <h3>Create Support Ticket</h3>
                <form onSubmit={handleSupportTicketSubmit}>
                  <div className="form-group">
                    <label htmlFor="ticket-subject">Subject</label>
                    <input
                      type="text"
                      id="ticket-subject"
                      name="subject"
                      value={supportTicketForm.subject}
                      onChange={handleFormChange}
                      placeholder={`Account Status Issue - ${statusInfo?.status || 'Unknown'}`}
                    />
                  </div>

                  <div className="form-group">
                    <label htmlFor="ticket-message">Message</label>
                    <textarea
                      id="ticket-message"
                      name="message"
                      value={supportTicketForm.message}
                      onChange={handleFormChange}
                      placeholder="Please describe your issue in detail..."
                      rows="4"
                      required
                    ></textarea>
                  </div>

                  <div className="form-group">
                    <label htmlFor="ticket-priority">Priority</label>
                    <select
                      id="ticket-priority"
                      name="priority"
                      value={supportTicketForm.priority}
                      onChange={handleFormChange}
                    >
                      <option value="high">High - Account Access Issue</option>
                      <option value="medium">Medium</option>
                      <option value="low">Low</option>
                    </select>
                  </div>

                  <div className="ticket-form-actions">
                    <button type="submit" className="btn-primary">
                      Create Ticket
                    </button>
                    <button
                      type="button"
                      className="btn-secondary"
                      onClick={() => setShowContactOptions(false)}
                    >
                      Cancel
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AccountStatus;
