/**
 * Navigation Tracker Utility
 *
 * This utility helps track navigation events and diagnose routing issues.
 */

// Store navigation history
const navigationHistory = [];

/**
 * Track a navigation event
 * @param {string} from - The source route
 * @param {string} to - The destination route
 * @param {string} method - The navigation method used
 * @param {Object} data - Additional data about the navigation
 */
const trackNavigation = (from, to, method, data = {}) => {
  const timestamp = new Date().toISOString();
  const event = {
    timestamp,
    from,
    to,
    method,
    data
  };

  navigationHistory.push(event);

  // Store in sessionStorage for persistence across page loads
  const storedHistory = JSON.parse(sessionStorage.getItem('navigationHistory') || '[]');
  storedHistory.push(event);
  sessionStorage.setItem('navigationHistory', JSON.stringify(storedHistory));

  return event;
};

/**
 * Get the navigation history
 * @returns {Array} The navigation history
 */
const getNavigationHistory = () => {
  return navigationHistory;
};

/**
 * Clear the navigation history
 */
const clearNavigationHistory = () => {
  navigationHistory.length = 0;
  sessionStorage.removeItem('navigationHistory');
};

/**
 * Get the navigation history from sessionStorage
 * @returns {Array} The navigation history from sessionStorage
 */
const getStoredNavigationHistory = () => {
  return JSON.parse(sessionStorage.getItem('navigationHistory') || '[]');
};

export {
  trackNavigation,
  getNavigationHistory,
  clearNavigationHistory,
  getStoredNavigationHistory
};
