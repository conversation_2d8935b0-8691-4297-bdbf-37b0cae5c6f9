const express = require('express');
const router = express.Router();
const logger = require('../utils/logger');

/**
 * POST /api/logs
 * Endpoint for receiving logs from the frontend
 */
router.post('/', (req, res) => {
  try {
    const { level, message, timestamp, data, source, userAgent, url } = req.body;
    
    // Validate required fields
    if (!level || !message) {
      return res.status(400).json({ message: 'Missing required fields: level and message' });
    }
    
    // Log with appropriate level
    switch (level.toUpperCase()) {
      case 'ERROR':
        logger.error(`[FRONTEND] ${message}`, { 
          timestamp, 
          data, 
          source, 
          userAgent, 
          url 
        });
        break;
      case 'WARN':
        logger.warn(`[FRONTEND] ${message}`, { 
          timestamp, 
          data, 
          source, 
          userAgent, 
          url 
        });
        break;
      case 'INFO':
        logger.info(`[FRONTEND] ${message}`, { 
          timestamp, 
          data, 
          source, 
          userAgent, 
          url 
        });
        break;
      case 'DEBUG':
        logger.debug(`[FRONTEND] ${message}`, { 
          timestamp, 
          data, 
          source, 
          userAgent, 
          url 
        });
        break;
      default:
        logger.info(`[FRONTEND] ${message}`, { 
          timestamp, 
          data, 
          source, 
          userAgent, 
          url 
        });
    }
    
    res.status(200).json({ message: 'Log received' });
  } catch (error) {
    logger.error('Error processing frontend log:', error);
    res.status(500).json({ message: 'Error processing log' });
  }
});

module.exports = router;
