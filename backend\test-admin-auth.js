const axios = require('axios');

const testAdminAuth = async () => {
  try {
    console.log('Testing admin authentication...');
    
    // First, let's try to login with admin credentials
    const loginData = {
      username: '<EMAIL>',
      password: 'Admin@123'
    };
    
    console.log('Attempting login with:', loginData.username);
    
    const loginResponse = await axios.post('http://localhost:3000/auth/login', loginData);
    
    console.log('Login successful!');
    console.log('Response status:', loginResponse.status);
    console.log('Response data:', loginResponse.data);
    
    // Extract the ID token instead of access token (ID token contains email)
    const token = loginResponse.data.tokens?.idToken || loginResponse.data.data?.tokens?.idToken || loginResponse.data.idToken;

    if (!token) {
      console.error('No ID token found in login response');
      console.error('Available keys:', Object.keys(loginResponse.data));
      if (loginResponse.data.tokens) {
        console.error('Token keys:', Object.keys(loginResponse.data.tokens));
      }
      return;
    }

    console.log('ID token obtained, length:', token.length);
    
    // Now test the admin dashboard endpoint
    console.log('\nTesting admin dashboard endpoint...');
    
    const dashboardResponse = await axios.get('http://localhost:3000/api/admin/dashboard-stats', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('Dashboard request successful!');
    console.log('Dashboard response:', dashboardResponse.data);
    
  } catch (error) {
    console.error('Error during test:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
};

testAdminAuth();
