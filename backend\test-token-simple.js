const jwt = require('jsonwebtoken');
const dotenv = require('dotenv');
const path = require('path');

// Load environment variables
dotenv.config({
  path: path.resolve(__dirname, '.env.local'),
  override: false,
});

const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************.vT6v8flftwP8xU83gGf8I_GFsEoxp1zRRzeZ_Tqr9JI';

const jwtSecret = process.env.JWT_SECRET || 'temporary_jwt_secret_for_development';

console.log('JWT_SECRET:', jwtSecret);
console.log('Token:', token.substring(0, 50) + '...');

// Test 1: Decode without verification
console.log('\n=== Test 1: Decode without verification ===');
try {
  const decoded = jwt.decode(token);
  console.log('✅ Decoded successfully:', {
    email: decoded.email,
    userId: decoded.userId,
    exp: new Date(decoded.exp * 1000),
    iss: decoded.iss,
    aud: decoded.aud
  });
} catch (error) {
  console.log('❌ Decode failed:', error.message);
}

// Test 2: Verify with correct secret
console.log('\n=== Test 2: Verify with JWT secret ===');
try {
  const verified = jwt.verify(token, jwtSecret, {
    issuer: 'energy-platform',
    audience: 'password-reset'
  });
  console.log('✅ Verification successful:', {
    email: verified.email,
    userId: verified.userId
  });
} catch (error) {
  console.log('❌ Verification failed:', error.message);
}

// Test 3: Try with different secrets to see which one works
console.log('\n=== Test 3: Try different secrets ===');
const possibleSecrets = [
  'temporary_jwt_secret_for_development',
  'your-secret-key',
  process.env.JWT_SECRET
];

for (const secret of possibleSecrets) {
  if (!secret) continue;
  try {
    const verified = jwt.verify(token, secret, {
      issuer: 'energy-platform',
      audience: 'password-reset'
    });
    console.log(`✅ SUCCESS with secret: "${secret}"`);
    console.log('   Email:', verified.email);
    break;
  } catch (error) {
    console.log(`❌ Failed with secret: "${secret}" - ${error.message}`);
  }
}
