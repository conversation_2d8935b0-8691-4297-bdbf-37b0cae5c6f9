const express = require('express');
const router = express.Router();
const { ContactForm, PublicAppointment } = require('../../models');

// Get all contact form submissions
router.get('/contact-forms', async (req, res) => {
  try {
    const { page = 1, limit = 10, status, search } = req.query;
    const skip = (page - 1) * limit;

    // Build query
    let query = {};
    if (status) {
      query.status = status;
    }
    if (search) {
      query.$or = [
        { firstName: { $regex: search, $options: 'i' } },
        { lastName: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
        { message: { $regex: search, $options: 'i' } }
      ];
    }

    const [contactForms, total] = await Promise.all([
      ContactForm.find(query)
        .populate('assignedTo', 'firstName lastName email')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(parseInt(limit)),
      ContactForm.countDocuments(query)
    ]);

    res.json({
      success: true,
      data: {
        contactForms,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(total / limit),
          totalItems: total,
          itemsPerPage: parseInt(limit)
        }
      }
    });

  } catch (error) {
    console.error('Error fetching contact forms:', error);
    res.status(500).json({
      success: false,
      message: 'An error occurred while fetching contact forms'
    });
  }
});

// Get all appointment bookings
router.get('/appointments', async (req, res) => {
  try {
    const { page = 1, limit = 10, status, search } = req.query;
    const skip = (page - 1) * limit;

    // Build query
    let query = {};
    if (status) {
      query.status = status;
    }
    if (search) {
      query.$or = [
        { firstName: { $regex: search, $options: 'i' } },
        { lastName: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } }
      ];
    }

    const [appointments, total] = await Promise.all([
      PublicAppointment.find(query)
        .populate('assignedTo', 'firstName lastName email')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(parseInt(limit)),
      PublicAppointment.countDocuments(query)
    ]);

    res.json({
      success: true,
      data: {
        appointments,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(total / limit),
          totalItems: total,
          itemsPerPage: parseInt(limit)
        }
      }
    });

  } catch (error) {
    console.error('Error fetching appointment bookings:', error);
    res.status(500).json({
      success: false,
      message: 'An error occurred while fetching appointment bookings'
    });
  }
});

// Update contact form status
router.put('/contact-forms/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { status, assignedTo, response, priority } = req.body;

    const updateData = {};
    if (status) updateData.status = status;
    if (assignedTo) updateData.assignedTo = assignedTo;
    if (response) {
      updateData.response = response;
      updateData.responseDate = new Date();
    }
    if (priority) updateData.priority = priority;

    const contactForm = await ContactForm.findByIdAndUpdate(
      id,
      updateData,
      { new: true }
    ).populate('assignedTo', 'firstName lastName email');

    if (!contactForm) {
      return res.status(404).json({
        success: false,
        message: 'Contact form not found'
      });
    }

    res.json({
      success: true,
      data: contactForm
    });

  } catch (error) {
    console.error('Error updating contact form:', error);
    res.status(500).json({
      success: false,
      message: 'An error occurred while updating the contact form'
    });
  }
});

// Update appointment status
router.put('/appointments/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { status, assignedTo, confirmedDateTime, notes, adminNotes, meetingLink } = req.body;

    const updateData = {};
    if (status) updateData.status = status;
    if (assignedTo) updateData.assignedTo = assignedTo;
    if (confirmedDateTime) updateData.confirmedDateTime = new Date(confirmedDateTime);
    if (notes) updateData.notes = notes;
    if (adminNotes) updateData.adminNotes = adminNotes;
    if (meetingLink) updateData.meetingLink = meetingLink;

    const appointment = await PublicAppointment.findByIdAndUpdate(
      id,
      updateData,
      { new: true }
    ).populate('assignedTo', 'firstName lastName email');

    if (!appointment) {
      return res.status(404).json({
        success: false,
        message: 'Appointment not found'
      });
    }

    res.json({
      success: true,
      data: appointment
    });

  } catch (error) {
    console.error('Error updating appointment:', error);
    res.status(500).json({
      success: false,
      message: 'An error occurred while updating the appointment'
    });
  }
});

// Get contact form statistics
router.get('/stats', async (req, res) => {
  try {
    const [contactFormStats, appointmentStats] = await Promise.all([
      ContactForm.aggregate([
        {
          $group: {
            _id: '$status',
            count: { $sum: 1 }
          }
        }
      ]),
      PublicAppointment.aggregate([
        {
          $group: {
            _id: '$status',
            count: { $sum: 1 }
          }
        }
      ])
    ]);

    const totalContactForms = await ContactForm.countDocuments();
    const totalAppointments = await PublicAppointment.countDocuments();

    res.json({
      success: true,
      data: {
        contactForms: {
          total: totalContactForms,
          byStatus: contactFormStats
        },
        appointments: {
          total: totalAppointments,
          byStatus: appointmentStats
        }
      }
    });

  } catch (error) {
    console.error('Error fetching contact stats:', error);
    res.status(500).json({
      success: false,
      message: 'An error occurred while fetching statistics'
    });
  }
});

module.exports = router;
