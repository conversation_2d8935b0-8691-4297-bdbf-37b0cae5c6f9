const SupplierProfile = require('../models/SupplierProfile');
const User = require('../models/User');
const Offer = require('../models/Offer');
const Contract = require('../models/Contract');
const Appointment = require('../models/Appointment');

/**
 * Get supplier dashboard statistics
 */
const getDashboardStats = async (req, res) => {
  try {
    const { cognitoId } = req.params;
    console.log('Fetching supplier dashboard stats for user:', cognitoId);

    // Find the user first
    const user = await User.findOne({ cognitoId });
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Get real data from database
    const [
      totalOffers,
      activeContracts,
      totalAppointments,
      recentOffers,
      recentContracts,
      recentAppointments
    ] = await Promise.all([
      Offer.countDocuments({ supplierId: user._id }),
      Contract.countDocuments({ supplierId: user._id, status: 'Active' }),
      Appointment.countDocuments({ agentId: user._id, agentType: 'Supplier' }),
      Offer.find({ supplierId: user._id })
        .sort({ createdAt: -1 })
        .limit(5)
        .populate('requestId', 'energyType location')
        .select('offerDetails energyType rateType price status validUntil createdAt'),
      Contract.find({ supplierId: user._id })
        .sort({ createdAt: -1 })
        .limit(5)
        .populate('userId', 'firstName lastName')
        .select('contractDetails startDate endDate status monthlyValue'),
      Appointment.find({ agentId: user._id, agentType: 'Supplier' })
        .sort({ scheduledTime: 1 })
        .limit(5)
        .populate('userId', 'firstName lastName')
        .select('type scheduledTime status location notes')
    ]);

    // Calculate revenue from active contracts
    const activeContractsList = await Contract.find({
      supplierId: user._id,
      status: 'Active'
    }).select('monthlyValue');

    const totalRevenue = activeContractsList.reduce((sum, contract) => {
      return sum + (contract.monthlyValue || 0);
    }, 0);

    // Calculate new customers this month
    const startOfMonth = new Date();
    startOfMonth.setDate(1);
    startOfMonth.setHours(0, 0, 0, 0);

    const newCustomers = await Contract.countDocuments({
      supplierId: user._id,
      status: 'Active',
      createdAt: { $gte: startOfMonth }
    });

    // Calculate performance metrics
    const totalViews = recentOffers.reduce((sum, offer) => sum + (offer.views || 0), 0);
    const totalApplications = recentOffers.reduce((sum, offer) => sum + (offer.applications || 0), 0);
    const conversionRate = totalApplications > 0 ? (activeContracts / totalApplications * 100) : 0;

    // Calculate average contract value
    const avgContractValue = activeContracts > 0 ? Math.round(totalRevenue / activeContracts) : 0;

    // Calculate customer satisfaction (mock for now - could be from feedback/ratings)
    const customerSatisfaction = 4.7;

    const stats = {
      totalOffers,
      activeContracts,
      totalRevenue,
      newCustomers,
      totalAppointments,
      recentOffers,
      recentContracts,
      recentAppointments,
      performanceMetrics: {
        conversionRate: Math.round(conversionRate * 100) / 100,
        avgContractValue,
        customerSatisfaction,
        totalViews,
        totalApplications
      }
    };

    console.log('Supplier dashboard stats retrieved successfully:', {
      totalOffers,
      activeContracts,
      totalRevenue,
      newCustomers
    });

    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('Error fetching supplier dashboard stats:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch dashboard statistics',
      error: error.message
    });
  }
};

/**
 * Get active offers for supplier
 */
const getActiveOffers = async (req, res) => {
  try {
    console.log('Fetching active offers for supplier:', req.user.sub);

    // Mock data for development
    const offers = [
      {
        id: '1',
        name: 'Green Energy Fixed Rate',
        energyType: 'Electricity',
        rateType: 'Fixed',
        baseRate: 0.145,
        views: 234,
        applications: 12,
        status: 'Active'
      },
      {
        id: '2',
        name: 'Business Gas Plan',
        energyType: 'Gas',
        rateType: 'Variable',
        baseRate: 0.065,
        views: 156,
        applications: 8,
        status: 'Active'
      },
      {
        id: '3',
        name: 'Dual Fuel Package',
        energyType: 'Both',
        rateType: 'Fixed',
        baseRate: 0.135,
        views: 189,
        applications: 15,
        status: 'Active'
      }
    ];

    console.log('Active offers retrieved successfully');

    res.json({
      success: true,
      data: offers
    });
  } catch (error) {
    console.error('Error fetching active offers:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch active offers'
    });
  }
};

/**
 * Get recent contracts for supplier
 */
const getRecentContracts = async (req, res) => {
  try {
    console.log('Fetching recent contracts for supplier:', req.user.sub);

    // Mock data for development
    const contracts = [
      {
        id: '1',
        customerName: 'Acme Corporation',
        contractNumber: 'CNT-2024-001',
        startDate: '2024-01-15',
        status: 'Active',
        monthlyValue: 850
      },
      {
        id: '2',
        customerName: 'Tech Solutions Ltd',
        contractNumber: 'CNT-2024-002',
        startDate: '2024-01-20',
        status: 'Pending',
        monthlyValue: 1200
      },
      {
        id: '3',
        customerName: 'Green Manufacturing',
        contractNumber: 'CNT-2024-003',
        startDate: '2024-01-25',
        status: 'Active',
        monthlyValue: 2100
      }
    ];

    console.log('Recent contracts retrieved successfully');

    res.json({
      success: true,
      data: contracts
    });
  } catch (error) {
    console.error('Error fetching recent contracts:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch recent contracts'
    });
  }
};

/**
 * Create a new energy offer
 */
const createOffer = async (req, res) => {
  try {
    console.log('Creating new offer for supplier:', req.user.sub);
    console.log('Offer data:', req.body);

    const {
      name,
      description,
      energyType,
      rateType,
      baseRate,
      standingCharge,
      duration,
      serviceAreas,
      contractTypes,
      additionalBenefits,
      validUntil,
      isActive
    } = req.body;

    // Validate required fields
    if (!name || !baseRate || !standingCharge) {
      return res.status(400).json({
        success: false,
        message: 'Name, base rate, and standing charge are required'
      });
    }

    // In a real implementation, you would save this to the database
    // For now, return a mock response
    const newOffer = {
      id: Date.now().toString(),
      name,
      description,
      energyType,
      rateType,
      baseRate: parseFloat(baseRate),
      standingCharge: parseFloat(standingCharge),
      duration: parseInt(duration),
      serviceAreas: serviceAreas || [],
      contractTypes: contractTypes || [],
      additionalBenefits: additionalBenefits || [],
      validUntil,
      isActive: isActive !== false,
      createdAt: new Date(),
      supplierId: req.user.sub
    };

    console.log('Offer created successfully:', newOffer.id);

    res.status(201).json({
      success: true,
      message: 'Offer created successfully',
      data: newOffer
    });
  } catch (error) {
    console.error('Error creating offer:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create offer'
    });
  }
};

/**
 * Get supplier profile
 */
const getProfile = async (req, res) => {
  try {
    console.log('Fetching supplier profile for user:', req.user.sub);

    // Find user by Cognito ID
    const user = await User.findOne({ cognitoId: req.user.sub });
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Find supplier profile
    const supplierProfile = await SupplierProfile.findOne({ userId: user._id });
    if (!supplierProfile) {
      return res.status(404).json({
        success: false,
        message: 'Supplier profile not found'
      });
    }

    console.log('Supplier profile retrieved successfully');

    res.json({
      success: true,
      data: {
        user: {
          firstName: user.firstName,
          lastName: user.lastName,
          email: user.email,
          userType: user.userType
        },
        profile: supplierProfile
      }
    });
  } catch (error) {
    console.error('Error fetching supplier profile:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch supplier profile'
    });
  }
};

/**
 * Update supplier profile
 */
const updateProfile = async (req, res) => {
  try {
    console.log('Updating supplier profile for user:', req.user.sub);
    console.log('Profile data:', req.body);

    // Find user by Cognito ID
    const user = await User.findOne({ cognitoId: req.user.sub });
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Update or create supplier profile
    const supplierProfile = await SupplierProfile.findOneAndUpdate(
      { userId: user._id },
      { ...req.body, userId: user._id },
      { new: true, upsert: true }
    );

    console.log('Supplier profile updated successfully');

    res.json({
      success: true,
      message: 'Supplier profile updated successfully',
      data: supplierProfile
    });
  } catch (error) {
    console.error('Error updating supplier profile:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update supplier profile'
    });
  }
};

/**
 * Get supplier analytics
 */
const getAnalytics = async (req, res) => {
  try {
    console.log('Fetching supplier analytics for user:', req.user.sub);
    const { period = '30d' } = req.query;

    // Mock analytics data
    const analytics = {
      revenue: {
        current: 25750,
        previous: 23200,
        growth: 11.0
      },
      customers: {
        total: 45,
        new: 8,
        retention: 94.2
      },
      offers: {
        views: 1250,
        applications: 89,
        conversions: 23
      }
    };

    console.log('Supplier analytics retrieved successfully');

    res.json({
      success: true,
      data: analytics
    });
  } catch (error) {
    console.error('Error fetching supplier analytics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch analytics'
    });
  }
};

module.exports = {
  getDashboardStats,
  getActiveOffers,
  getRecentContracts,
  createOffer,
  getProfile,
  updateProfile,
  getAnalytics
};
