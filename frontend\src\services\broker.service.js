import api from './api';
import logger from '../utils/logger';

class BrokerService {
  /**
   * Get broker dashboard statistics
   */
  async getDashboardStats() {
    try {
      logger.info('Fetching broker dashboard stats');

      // Get cognitoId from localStorage
      const cognitoId = localStorage.getItem('cognitoId');
      if (!cognitoId) {
        throw new Error('No cognito ID found');
      }

      const response = await api.get(`/api/broker/dashboard/stats/${cognitoId}`);
      logger.info('Broker dashboard stats fetched successfully');
      return response.data;
    } catch (error) {
      logger.error('Error fetching broker dashboard stats:', error);

      // Return mock data for development
      return {
        success: true,
        data: {
          totalClients: 0,
          activeDeals: 0,
          monthlyCommission: 0,
          conversionRate: 0,
          totalAppointments: 0,
          recentClients: [],
          recentDeals: [],
          recentAppointments: [],
          pipelineData: {
            prospects: 0,
            qualified: 0,
            proposal: 0,
            closing: 0
          }
        }
      };
    }
  }

  /**
   * Get active clients for broker
   */
  async getActiveClients() {
    try {
      logger.info('Fetching broker active clients');
      const response = await api.get('/api/broker/clients/active');
      logger.info('Broker active clients fetched successfully');
      return response.data;
    } catch (error) {
      logger.error('Error fetching broker active clients:', error);

      // Return mock data for development
      return {
        success: true,
        data: [
          {
            id: '1',
            name: 'Acme Corporation',
            type: 'Business',
            location: 'Paris',
            activeContracts: 3,
            monthlyValue: 1250,
            status: 'Active'
          },
          {
            id: '2',
            name: 'Green Manufacturing',
            type: 'Industrial',
            location: 'Lyon',
            activeContracts: 2,
            monthlyValue: 2100,
            status: 'Active'
          },
          {
            id: '3',
            name: 'Tech Solutions Ltd',
            type: 'Business',
            location: 'Marseille',
            activeContracts: 1,
            monthlyValue: 850,
            status: 'Active'
          }
        ]
      };
    }
  }

  /**
   * Get recent deals for broker
   */
  async getRecentDeals() {
    try {
      logger.info('Fetching broker recent deals');
      const response = await api.get('/api/broker/deals/recent');
      logger.info('Broker recent deals fetched successfully');
      return response.data;
    } catch (error) {
      logger.error('Error fetching broker recent deals:', error);

      // Return mock data for development
      return {
        success: true,
        data: [
          {
            id: '1',
            clientName: 'Acme Corporation',
            supplier: 'EDF Energy',
            energyType: 'Electricity',
            commission: 450,
            closedDate: '2024-01-15',
            status: 'Completed'
          },
          {
            id: '2',
            clientName: 'Tech Solutions Ltd',
            supplier: 'Total Energies',
            energyType: 'Gas',
            commission: 320,
            closedDate: '2024-01-20',
            status: 'Pending'
          },
          {
            id: '3',
            clientName: 'Green Manufacturing',
            supplier: 'Engie',
            energyType: 'Both',
            commission: 680,
            closedDate: '2024-01-25',
            status: 'Completed'
          }
        ]
      };
    }
  }

  /**
   * Add a new client
   */
  async addClient(clientData) {
    try {
      logger.info('Adding new broker client:', clientData);
      const response = await api.post('/api/broker/clients', clientData);
      logger.info('Broker client added successfully');
      return response.data;
    } catch (error) {
      logger.error('Error adding broker client:', error);
      throw error;
    }
  }

  /**
   * Update client information
   */
  async updateClient(clientId, clientData) {
    try {
      logger.info('Updating broker client:', clientId, clientData);
      const response = await api.put(`/api/broker/clients/${clientId}`, clientData);
      logger.info('Broker client updated successfully');
      return response.data;
    } catch (error) {
      logger.error('Error updating broker client:', error);
      throw error;
    }
  }

  /**
   * Get all clients for broker
   */
  async getAllClients() {
    try {
      logger.info('Fetching all broker clients');
      const response = await api.get('/api/broker/clients');
      logger.info('All broker clients fetched successfully');
      return response.data;
    } catch (error) {
      logger.error('Error fetching all broker clients:', error);
      throw error;
    }
  }

  /**
   * Get client details
   */
  async getClientDetails(clientId) {
    try {
      logger.info('Fetching broker client details:', clientId);
      const response = await api.get(`/api/broker/clients/${clientId}`);
      logger.info('Broker client details fetched successfully');
      return response.data;
    } catch (error) {
      logger.error('Error fetching broker client details:', error);
      throw error;
    }
  }

  /**
   * Compare offers for a client
   */
  async compareOffers(clientId, criteria = {}) {
    try {
      logger.info('Comparing offers for client:', clientId, criteria);
      const response = await api.post(`/api/broker/clients/${clientId}/compare-offers`, criteria);
      logger.info('Offers comparison completed successfully');
      return response.data;
    } catch (error) {
      logger.error('Error comparing offers:', error);
      throw error;
    }
  }

  /**
   * Create a deal for a client
   */
  async createDeal(dealData) {
    try {
      logger.info('Creating new broker deal:', dealData);
      const response = await api.post('/api/broker/deals', dealData);
      logger.info('Broker deal created successfully');
      return response.data;
    } catch (error) {
      logger.error('Error creating broker deal:', error);
      throw error;
    }
  }

  /**
   * Update deal status
   */
  async updateDealStatus(dealId, status, notes = '') {
    try {
      logger.info('Updating broker deal status:', dealId, status);
      const response = await api.put(`/api/broker/deals/${dealId}/status`, {
        status,
        notes
      });
      logger.info('Broker deal status updated successfully');
      return response.data;
    } catch (error) {
      logger.error('Error updating broker deal status:', error);
      throw error;
    }
  }

  /**
   * Get all deals for broker
   */
  async getAllDeals() {
    try {
      logger.info('Fetching all broker deals');
      const response = await api.get('/api/broker/deals');
      logger.info('All broker deals fetched successfully');
      return response.data;
    } catch (error) {
      logger.error('Error fetching all broker deals:', error);
      throw error;
    }
  }

  /**
   * Get broker analytics data
   */
  async getAnalytics(period = '30d') {
    try {
      logger.info('Fetching broker analytics for period:', period);
      const response = await api.get(`/api/broker/analytics?period=${period}`);
      logger.info('Broker analytics fetched successfully');
      return response.data;
    } catch (error) {
      logger.error('Error fetching broker analytics:', error);

      // Return mock data for development
      return {
        success: true,
        data: {
          commission: {
            current: 3250,
            previous: 2890,
            growth: 12.5
          },
          clients: {
            total: 28,
            new: 5,
            retention: 92.8
          },
          deals: {
            total: 12,
            completed: 8,
            pending: 4,
            conversionRate: 15.5
          }
        }
      };
    }
  }

  /**
   * Get broker profile
   */
  async getProfile() {
    try {
      logger.info('Fetching broker profile');
      const response = await api.get('/api/broker/profile');
      logger.info('Broker profile fetched successfully');
      return response.data;
    } catch (error) {
      logger.error('Error fetching broker profile:', error);
      throw error;
    }
  }

  /**
   * Update broker profile
   */
  async updateProfile(profileData) {
    try {
      logger.info('Updating broker profile:', profileData);
      const response = await api.put('/api/broker/profile', profileData);
      logger.info('Broker profile updated successfully');
      return response.data;
    } catch (error) {
      logger.error('Error updating broker profile:', error);
      throw error;
    }
  }

  /**
   * Get commission structure
   */
  async getCommissionStructure() {
    try {
      logger.info('Fetching broker commission structure');
      const response = await api.get('/api/broker/commission-structure');
      logger.info('Broker commission structure fetched successfully');
      return response.data;
    } catch (error) {
      logger.error('Error fetching broker commission structure:', error);
      throw error;
    }
  }

  /**
   * Update commission structure
   */
  async updateCommissionStructure(commissionData) {
    try {
      logger.info('Updating broker commission structure:', commissionData);
      const response = await api.put('/api/broker/commission-structure', commissionData);
      logger.info('Broker commission structure updated successfully');
      return response.data;
    } catch (error) {
      logger.error('Error updating broker commission structure:', error);
      throw error;
    }
  }
}

export default new BrokerService();
