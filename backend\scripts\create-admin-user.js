const AWS = require('aws-sdk');
const mongoose = require('mongoose');
const User = require('../models/User');
const logger = require('../utils/logger');

// Configure AWS
AWS.config.update({
  region: process.env.AWS_REGION || 'eu-west-1',
  accessKeyId: process.env.AWS_ACCESS_KEY_ID,
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
});

const cognitoIdentityServiceProvider = new AWS.CognitoIdentityServiceProvider();

// Cognito User Pool configuration
const userPoolConfig = {
  UserPoolId: process.env.COGNITO_USER_POOL_ID,
  ClientId: process.env.COGNITO_CLIENT_ID
};

// MongoDB connection
const connectDB = async () => {
  try {
    const mongoURI = process.env.MONGODB_URI || 'mongodb://localhost:27017/energy-app';
    await mongoose.connect(mongoURI);
    logger.info('Connected to MongoDB for admin user creation');
  } catch (error) {
    logger.error('MongoDB connection error:', error);
    process.exit(1);
  }
};

// Admin user details
const adminUserData = {
  username: 'admin',
  email: '<EMAIL>',
  password: 'Admin@123',
  firstName: 'System',
  lastName: 'Administrator',
  phone: '+33123456789',
  userType: 'Admin'
};

// Create admin user in Cognito
const createAdminInCognito = async () => {
  try {
    logger.info('Creating admin user in Cognito...');

    // Check if user already exists
    try {
      const existingUser = await cognitoIdentityServiceProvider.adminGetUser({
        UserPoolId: userPoolConfig.UserPoolId,
        Username: adminUserData.username
      }).promise();
      
      if (existingUser) {
        logger.info('Admin user already exists in Cognito');
        return existingUser.UserAttributes.find(attr => attr.Name === 'sub').Value;
      }
    } catch (error) {
      if (error.code !== 'UserNotFoundException') {
        throw error;
      }
      // User doesn't exist, continue with creation
    }

    // Create user in Cognito
    const createUserParams = {
      UserPoolId: userPoolConfig.UserPoolId,
      Username: adminUserData.username,
      UserAttributes: [
        { Name: 'email', Value: adminUserData.email },
        { Name: 'email_verified', Value: 'true' },
        { Name: 'given_name', Value: adminUserData.firstName },
        { Name: 'family_name', Value: adminUserData.lastName },
        { Name: 'phone_number', Value: adminUserData.phone },
        { Name: 'custom:userType', Value: adminUserData.userType },
        { Name: 'custom:profileComplete', Value: 'true' }
      ],
      TemporaryPassword: adminUserData.password,
      MessageAction: 'SUPPRESS' // Don't send welcome email
    };

    const createResult = await cognitoIdentityServiceProvider.adminCreateUser(createUserParams).promise();
    const cognitoUserId = createResult.User.Attributes.find(attr => attr.Name === 'sub').Value;
    
    logger.info('Admin user created in Cognito with ID:', cognitoUserId);

    // Set permanent password
    const setPasswordParams = {
      UserPoolId: userPoolConfig.UserPoolId,
      Username: adminUserData.username,
      Password: adminUserData.password,
      Permanent: true
    };

    await cognitoIdentityServiceProvider.adminSetUserPassword(setPasswordParams).promise();
    logger.info('Admin user password set as permanent');

    // Confirm the user (mark as verified)
    const confirmParams = {
      UserPoolId: userPoolConfig.UserPoolId,
      Username: adminUserData.username
    };

    await cognitoIdentityServiceProvider.adminConfirmSignUp(confirmParams).promise();
    logger.info('Admin user confirmed in Cognito');

    return cognitoUserId;
  } catch (error) {
    logger.error('Error creating admin user in Cognito:', error);
    throw error;
  }
};

// Create admin user in MongoDB
const createAdminInMongoDB = async (cognitoUserId) => {
  try {
    logger.info('Creating admin user in MongoDB...');

    // Check if user already exists
    const existingUser = await User.findOne({ 
      $or: [
        { email: adminUserData.email },
        { cognitoId: cognitoUserId }
      ]
    });

    if (existingUser) {
      logger.info('Admin user already exists in MongoDB, updating...');
      
      // Update existing user
      existingUser.cognitoId = cognitoUserId;
      existingUser.email = adminUserData.email;
      existingUser.firstName = adminUserData.firstName;
      existingUser.lastName = adminUserData.lastName;
      existingUser.phone = adminUserData.phone;
      existingUser.userType = adminUserData.userType;
      existingUser.status = 'Active';
      existingUser.verificationStatus = 'Verified';
      existingUser.profileComplete = true;
      existingUser.lastLogin = new Date();

      await existingUser.save();
      logger.info('Admin user updated in MongoDB');
      return existingUser;
    }

    // Create new admin user
    const adminUser = await User.create({
      cognitoId: cognitoUserId,
      email: adminUserData.email,
      firstName: adminUserData.firstName,
      lastName: adminUserData.lastName,
      phone: adminUserData.phone,
      userType: adminUserData.userType,
      status: 'Active',
      verificationStatus: 'Verified',
      profileComplete: true,
      lastLogin: new Date()
    });

    logger.info('Admin user created in MongoDB with ID:', adminUser._id);
    return adminUser;
  } catch (error) {
    logger.error('Error creating admin user in MongoDB:', error);
    throw error;
  }
};

// Main execution
const main = async () => {
  try {
    await connectDB();
    
    logger.info('🚀 Starting admin user creation process...');
    logger.info('Admin credentials:');
    logger.info(`Username: ${adminUserData.username}`);
    logger.info(`Email: ${adminUserData.email}`);
    logger.info(`Password: ${adminUserData.password}`);
    
    // Create admin user in Cognito
    const cognitoUserId = await createAdminInCognito();
    
    // Create admin user in MongoDB
    const mongoUser = await createAdminInMongoDB(cognitoUserId);
    
    logger.info('✅ Admin user creation completed successfully!');
    logger.info('Admin user details:');
    logger.info(`- Cognito ID: ${cognitoUserId}`);
    logger.info(`- MongoDB ID: ${mongoUser._id}`);
    logger.info(`- Email: ${mongoUser.email}`);
    logger.info(`- User Type: ${mongoUser.userType}`);
    logger.info(`- Status: ${mongoUser.status}`);
    logger.info(`- Profile Complete: ${mongoUser.profileComplete}`);
    
    logger.info('🎉 You can now login with:');
    logger.info(`Username: ${adminUserData.username}`);
    logger.info(`Password: ${adminUserData.password}`);
    
  } catch (error) {
    logger.error('❌ Admin user creation failed:', error);
  } finally {
    await mongoose.connection.close();
    logger.info('Connection closed');
    process.exit(0);
  }
};

// Run the script
if (require.main === module) {
  main();
}

module.exports = { createAdminInCognito, createAdminInMongoDB };
