const express = require('express');
const router = express.Router();
const User = require('../../models/User');
const SupplierProfile = require('../../models/SupplierProfile');
const Contract = require('../../models/Contract');
const Offer = require('../../models/Offer');
const UserActivity = require('../../models/UserActivity');
const logger = require('../../utils/logger');
const { authMiddleware } = require('../../middleware/auth');

// Apply auth middleware to all admin routes
router.use(authMiddleware);

// Middleware to check if user is admin
const requireAdmin = async (req, res, next) => {
  try {
    const userEmail = req.user?.email;
    if (!userEmail) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    const user = await User.findOne({ email: userEmail });
    if (!user || user.userType !== 'Admin') {
      return res.status(403).json({
        success: false,
        message: 'Admin access required'
      });
    }

    req.adminUser = user;
    next();
  } catch (error) {
    logger.error('Admin middleware error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// Get all suppliers with performance metrics
router.get('/', requireAdmin, async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      status,
      verificationStatus,
      energyType,
      search,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    // Build filter for suppliers
    const filter = { userType: 'Supplier' };
    
    if (status && status !== 'all') {
      filter.status = status;
    }
    
    if (verificationStatus && verificationStatus !== 'all') {
      filter.verificationStatus = verificationStatus;
    }
    
    if (search) {
      filter.$or = [
        { email: { $regex: search, $options: 'i' } },
        { firstName: { $regex: search, $options: 'i' } },
        { lastName: { $regex: search, $options: 'i' } }
      ];
    }

    const skip = (parseInt(page) - 1) * parseInt(limit);
    const sortOptions = {};
    sortOptions[sortBy] = sortOrder === 'desc' ? -1 : 1;

    // Get suppliers with their profiles
    const suppliers = await User.find(filter)
      .select('-__v')
      .sort(sortOptions)
      .skip(skip)
      .limit(parseInt(limit))
      .lean();

    // Get supplier profiles and performance data
    const suppliersWithMetrics = await Promise.all(suppliers.map(async (supplier) => {
      const profile = await SupplierProfile.findOne({ userId: supplier._id }).lean();
      
      // Calculate performance metrics
      const [
        totalOffers,
        totalContracts,
        activeContracts,
        monthlyOffers,
        monthlyContracts,
        averageContractValue
      ] = await Promise.all([
        Offer.countDocuments({ supplierId: supplier._id }),
        Contract.countDocuments({ supplierId: supplier._id }),
        Contract.countDocuments({ supplierId: supplier._id, status: 'Active' }),
        Offer.countDocuments({ 
          supplierId: supplier._id,
          createdAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }
        }),
        Contract.countDocuments({ 
          supplierId: supplier._id,
          createdAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }
        }),
        Contract.aggregate([
          { $match: { supplierId: supplier._id } },
          { $group: { _id: null, avg: { $avg: '$contractDetails.estimatedAnnualCost' } } }
        ])
      ]);

      const conversionRate = totalOffers > 0 ? ((totalContracts / totalOffers) * 100).toFixed(1) : 0;
      const avgValue = averageContractValue[0]?.avg || 0;

      return {
        ...supplier,
        profile,
        metrics: {
          totalOffers,
          totalContracts,
          activeContracts,
          monthlyOffers,
          monthlyContracts,
          conversionRate: parseFloat(conversionRate),
          averageContractValue: Math.round(avgValue)
        }
      };
    }));

    // Filter by energy type if specified
    let filteredSuppliers = suppliersWithMetrics;
    if (energyType && energyType !== 'all') {
      filteredSuppliers = suppliersWithMetrics.filter(supplier => 
        supplier.profile?.energyTypesProvided?.includes(energyType)
      );
    }

    // Get total count for pagination
    const totalSuppliers = await User.countDocuments(filter);
    const totalPages = Math.ceil(totalSuppliers / parseInt(limit));

    res.json({
      success: true,
      data: {
        suppliers: filteredSuppliers,
        pagination: {
          currentPage: parseInt(page),
          totalPages,
          totalSuppliers,
          hasNextPage: parseInt(page) < totalPages,
          hasPrevPage: parseInt(page) > 1
        }
      }
    });

  } catch (error) {
    logger.error('Error fetching suppliers:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch suppliers'
    });
  }
});

// Get supplier details with full information
router.get('/:supplierId/details', requireAdmin, async (req, res) => {
  try {
    const { supplierId } = req.params;

    const supplier = await User.findById(supplierId).select('-__v');
    if (!supplier || supplier.userType !== 'Supplier') {
      return res.status(404).json({
        success: false,
        message: 'Supplier not found'
      });
    }

    const profile = await SupplierProfile.findOne({ userId: supplierId })
      .populate('verificationDocuments');

    // Get detailed performance data
    const [
      recentOffers,
      recentContracts,
      monthlyStats,
      offerTemplates
    ] = await Promise.all([
      Offer.find({ supplierId })
        .populate('requestId')
        .sort({ createdAt: -1 })
        .limit(10),
      Contract.find({ supplierId })
        .populate('userId', 'firstName lastName email')
        .populate('brokerId', 'firstName lastName email')
        .sort({ createdAt: -1 })
        .limit(10),
      getSupplierMonthlyStats(supplierId),
      profile?.offerTemplates || []
    ]);

    res.json({
      success: true,
      data: {
        supplier,
        profile,
        recentOffers,
        recentContracts,
        monthlyStats,
        offerTemplates
      }
    });

  } catch (error) {
    logger.error('Error fetching supplier details:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch supplier details'
    });
  }
});

// Approve or reject supplier application
router.patch('/:supplierId/verification', requireAdmin, async (req, res) => {
  try {
    const { supplierId } = req.params;
    const { verificationStatus, notes } = req.body;

    if (!['Verified', 'Rejected'].includes(verificationStatus)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid verification status'
      });
    }

    const supplier = await User.findById(supplierId);
    if (!supplier || supplier.userType !== 'Supplier') {
      return res.status(404).json({
        success: false,
        message: 'Supplier not found'
      });
    }

    // Update supplier verification status
    supplier.verificationStatus = verificationStatus;
    if (verificationStatus === 'Verified') {
      supplier.status = 'Active';
    }
    await supplier.save();

    // Update supplier profile with notes
    await SupplierProfile.findOneAndUpdate(
      { userId: supplierId },
      { 
        verificationStatus,
        verificationNotes: notes,
        verifiedAt: verificationStatus === 'Verified' ? new Date() : undefined,
        verifiedBy: req.adminUser._id
      }
    );

    // Log activity
    await UserActivity.logActivity({
      userId: supplierId,
      activityType: 'VerificationUpdate',
      description: `Supplier application ${verificationStatus.toLowerCase()} by admin`,
      details: {
        verificationStatus,
        notes,
        adminId: req.adminUser._id
      },
      severity: 'High',
      isSystemGenerated: true
    });

    logger.info(`Admin ${req.adminUser.email} ${verificationStatus.toLowerCase()} supplier ${supplier.email}`);

    res.json({
      success: true,
      message: `Supplier application ${verificationStatus.toLowerCase()} successfully`,
      data: supplier
    });

  } catch (error) {
    logger.error('Error updating supplier verification:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update supplier verification'
    });
  }
});

// Helper function to get monthly statistics
async function getSupplierMonthlyStats(supplierId) {
  const sixMonthsAgo = new Date();
  sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

  const monthlyData = await Contract.aggregate([
    {
      $match: {
        supplierId: supplierId,
        createdAt: { $gte: sixMonthsAgo }
      }
    },
    {
      $group: {
        _id: {
          year: { $year: '$createdAt' },
          month: { $month: '$createdAt' }
        },
        contracts: { $sum: 1 },
        totalValue: { $sum: '$contractDetails.estimatedAnnualCost' }
      }
    },
    {
      $sort: { '_id.year': 1, '_id.month': 1 }
    }
  ]);

  return monthlyData;
}

module.exports = router;
