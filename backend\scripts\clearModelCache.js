const mongoose = require('mongoose');

// Clear the model cache to force reload of updated schemas
console.log('Clearing Mongoose model cache...');

// Delete the UserActivity model from cache if it exists
if (mongoose.models.UserActivity) {
  delete mongoose.models.UserActivity;
  console.log('UserActivity model removed from cache');
}

// Delete the schema from cache if it exists
if (mongoose.modelSchemas && mongoose.modelSchemas.UserActivity) {
  delete mongoose.modelSchemas.UserActivity;
  console.log('UserActivity schema removed from cache');
}

console.log('Model cache cleared successfully');
console.log('Please restart your server to reload the updated models');
