import React, { useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import Navbar from '../Navbar';
import Footer from '../Footer';
import { useForceScrollToTopAuthenticated } from '../../utils/scrollToTop';

/**
 * AuthenticatedLayout component that provides the layout for authenticated pages
 * This component does NOT check authentication - that should be done by PrivateRoute
 * This is just a layout wrapper that adds the Navbar and Footer
 */
const AuthenticatedLayout = ({ children }) => {
  const location = useLocation();

  // Force scroll to top whenever the authenticated layout renders
  useEffect(() => {
    console.log('🔄 AuthenticatedLayout: Route changed to:', location.pathname);
    useForceScrollToTopAuthenticated();
  }, [location.pathname]);

  // Check if we're on the upload-first-invoice page
  const isUploadInvoicePage = location.pathname === '/upload-first-invoice';

  return (
    <div className="authenticated-layout">
      <Navbar />
      <main className="authenticated-content" style={isUploadInvoicePage ? { paddingBottom: '0' } : {}}>
        {children}
      </main>
      <Footer />
    </div>
  );
};

export default AuthenticatedLayout;
