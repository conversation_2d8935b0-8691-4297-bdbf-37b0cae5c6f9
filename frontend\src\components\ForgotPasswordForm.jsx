import React from 'react';
import { Link } from 'react-router-dom';
import styled from 'styled-components';
import AuthPageContainer from './AuthPageContainer';
import ForgotPasswordWrapper from './ForgotPasswordWrapper';

const Title = styled.h1`
  font-size: 28px;
  font-weight: 900;
  margin-bottom: 3px;
  color: #000000;
  text-shadow: 1px 1px 3px rgba(255, 255, 255, 0.8);
  text-align: center;

  @media (max-width: 768px) {
    font-size: 28px;
  }
`;

const SubTitle = styled.p`
  font-size: 15px;
  font-weight: 600;
  color: #000000;
  text-shadow: 0.5px 0.5px 2px rgba(255, 255, 255, 0.8);
  margin: 0 0 15px 0;
  text-align: center;

  @media (max-width: 768px) {
    font-size: 14px;
    margin: 0 0 15px 0;
  }
`;

const FormElementsContainer = styled.div`
  padding: 0;
  margin-bottom: 5px;
  background-color: transparent;
  width: 100%;
`;

const InputWrapper = styled.div`
  margin-bottom: 15px;
  display: flex;
  flex-direction: column;
  width: 100%;
`;

const InputContainer = styled.div`
  background-color: white;
  border-radius: 10px;
  display: flex;
  align-items: center;
  padding: 18px 20px;
  border: none;
  margin-bottom: 10px;
  width: 100%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
`;

const IconSpan = styled.span`
  margin-right: 12px;
  color: #000;

  svg {
    width: 20px !important;
    height: 20px !important;
  }
`;

const StyledInput = styled.input`
  border: none;
  outline: none;
  width: 100%;
  background-color: transparent;
  font-size: 16px;
  color: #000;
  padding: 0;
  margin: 0;

  &::placeholder {
    color: rgba(0, 0, 0, 0.6);
  }
`;

const ErrorMessage = styled.div`
  color: #ff6b6b;
  font-size: 12px;
  margin-top: 5px;
  font-weight: bold;
`;

const SuccessMessage = styled.div`
  color: #28a745;
  font-size: 14px;
  margin: 10px 0;
  text-align: center;
  padding: 10px;
  background-color: rgba(40, 167, 69, 0.1);
  border-radius: 8px;
`;

const ActionButton = styled.button`
  background-color: #1E3D5C;
  color: white;
  border: none;
  border-radius: 10px;
  padding: 18px;
  width: 100%;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  margin-bottom: 15px;
  margin-top: 10px;
  text-transform: uppercase;
  transition: all 0.3s;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);

  &:hover {
    background-color: #15304a;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.25);
  }
`;

const LinksContainer = styled.div`
  text-align: center;
  background-color: transparent;
  padding: 0;
  margin-top: 10px;
  margin-bottom: 5px;
`;

const StyledLink = styled(Link)`
  color: #000000;
  text-decoration: none;
  font-size: 14px;
  display: inline-block;
  margin: 2px 0;
  transition: color 0.3s;
  font-weight: 600;

  &:hover {
    color: #333333;
    text-decoration: underline;
  }
`;

const Separator = styled.span`
  margin: 0 10px;
  color: #666;
`;

const PasswordStrengthContainer = styled.div`
  margin-bottom: 15px;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
  padding: 10px;

  .strength-meter {
    height: 5px;
    background-color: #e0e0e0;
    border-radius: 3px;
    margin-bottom: 8px;
    overflow: hidden;
  }

  .strength-meter-fill {
    height: 100%;
    border-radius: 3px;
    transition: width 0.3s, background-color 0.3s;
  }

  .strength-text {
    font-size: 12px;
    text-align: right;
  }
`;

// Step 1: Request Password Reset Form
const RequestResetForm = ({ email, setEmail, handleRequestReset, error }) => (
  <AuthPageContainer style={{ display: 'block', overflow: 'hidden' }}>
    <ForgotPasswordWrapper>
      <form onSubmit={handleRequestReset} style={{ width: '100%' }}>
      <Title>Forgot Password</Title>
      <SubTitle>Enter your email to receive a reset code</SubTitle>

      <FormElementsContainer>
        <InputWrapper>
          <InputContainer>
            <IconSpan>
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" viewBox="0 0 16 16">
                <path d="M0 4a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V4zm2-1a1 1 0 0 0-1 1v.217l7 4.2 7-4.2V4a1 1 0 0 0-1-1H2zm13 2.383-4.758 2.855L15 11.114v-5.73zm-.034 6.878L9.271 8.82 8 9.583 6.728 8.82l-5.694 3.44A1 1 0 0 0 2 13h12a1 1 0 0 0 .966-.739zM1 11.114l4.758-2.876L1 5.383v5.73z"/>
              </svg>
            </IconSpan>
            <StyledInput
              type="email"
              id="email"
              placeholder="Email Address"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
            />
          </InputContainer>
          {error && <ErrorMessage>{error}</ErrorMessage>}
        </InputWrapper>

        <ActionButton type="submit">
          Send Reset Code
        </ActionButton>
      </FormElementsContainer>

      <LinksContainer>
        <StyledLink to="/login">Back to Login</StyledLink>
        <Separator>|</Separator>
        <StyledLink to="/signup">Create an account</StyledLink>
      </LinksContainer>
    </form>
    </ForgotPasswordWrapper>
  </AuthPageContainer>
);

// Step 2: Reset Password Form
const ResetPasswordForm = ({
  email,
  resetCode,
  setResetCode,
  newPassword,
  setNewPassword,
  confirmPassword,
  setConfirmPassword,
  passwordStrength,
  handleResetPassword,
  error
}) => (
  <AuthPageContainer style={{ display: 'block', overflow: 'hidden' }}>
    <ForgotPasswordWrapper>
      <form onSubmit={handleResetPassword} style={{ width: '100%' }}>
      <Title>Reset Password</Title>
      <SubTitle>Enter the code sent to {email} and your new password</SubTitle>

      <FormElementsContainer>
        <InputWrapper>
          <InputContainer>
            <IconSpan>
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" viewBox="0 0 16 16">
                <path d="M8 1a2 2 0 0 1 2 2v4H6V3a2 2 0 0 1 2-2zm3 6V3a3 3 0 0 0-6 0v4a2 2 0 0 0-2 2v5a2 2 0 0 0 2 2h6a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2z"/>
              </svg>
            </IconSpan>
            <StyledInput
              type="text"
              id="resetCode"
              placeholder="Verification Code"
              value={resetCode}
              onChange={(e) => setResetCode(e.target.value)}
              required
            />
          </InputContainer>
        </InputWrapper>

        <InputWrapper>
          <InputContainer>
            <IconSpan>
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" viewBox="0 0 16 16">
                <path d="M8 1a2 2 0 0 1 2 2v4H6V3a2 2 0 0 1 2-2zm3 6V3a3 3 0 0 0-6 0v4a2 2 0 0 0-2 2v5a2 2 0 0 0 2 2h6a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2z"/>
              </svg>
            </IconSpan>
            <StyledInput
              type="password"
              id="newPassword"
              placeholder="New Password"
              value={newPassword}
              onChange={(e) => setNewPassword(e.target.value)}
              required
            />
          </InputContainer>
        </InputWrapper>

        {newPassword && (
          <PasswordStrengthContainer>
            <div className="strength-meter">
              <div
                className="strength-meter-fill"
                style={{
                  width: `${(passwordStrength.score / 5) * 100}%`,
                  backgroundColor: passwordStrength.color
                }}
              ></div>
            </div>
            <div className="strength-text" style={{ color: passwordStrength.color }}>
              {passwordStrength.text}
            </div>
          </PasswordStrengthContainer>
        )}

        <InputWrapper>
          <InputContainer>
            <IconSpan>
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" viewBox="0 0 16 16">
                <path d="M8 1a2 2 0 0 1 2 2v4H6V3a2 2 0 0 1 2-2zm3 6V3a3 3 0 0 0-6 0v4a2 2 0 0 0-2 2v5a2 2 0 0 0 2 2h6a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2z"/>
              </svg>
            </IconSpan>
            <StyledInput
              type="password"
              id="confirmPassword"
              placeholder="Confirm Password"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              required
            />
          </InputContainer>
          {error && <ErrorMessage>{error}</ErrorMessage>}
        </InputWrapper>

        <ActionButton type="submit">
          Reset Password
        </ActionButton>
      </FormElementsContainer>

      <LinksContainer>
        <StyledLink to="/login">Back to Login</StyledLink>
      </LinksContainer>
    </form>
    </ForgotPasswordWrapper>
  </AuthPageContainer>
);

// Step 3: Success Message
const SuccessForm = () => (
  <AuthPageContainer style={{ display: 'block', overflow: 'hidden' }}>
    <ForgotPasswordWrapper>
      <div style={{ width: '100%' }}>
      <div style={{ fontSize: '2.5rem', color: '#28a745', marginBottom: '0.5rem', textAlign: 'center' }}>✓</div>
      <Title>Password Reset Successful</Title>
      <SubTitle>Your password has been reset successfully</SubTitle>

      <FormElementsContainer>
        <ActionButton as={Link} to="/login">
          Back to Login
        </ActionButton>
      </FormElementsContainer>
    </div>
    </ForgotPasswordWrapper>
  </AuthPageContainer>
);

// Export all forms
export { RequestResetForm, ResetPasswordForm, SuccessForm };
