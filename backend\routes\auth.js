const express = require('express');
const router = express.Router();
const { cognitoIdentityServiceProvider, userPoolConfig } = require('../config/cognito');
const { User } = require('../models');
const logger = require('../utils/logger');

// Register a new user
router.post('/signup', async (req, res) => {
  try {
    const { username, password, email, userType = 'Individual' } = req.body;
    const phone = req.body.phoneNumber || '';
    const firstName = req.body.firstName || '';
    const lastName = req.body.lastName || '';

    // Check if user already exists in our database
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return res.status(400).json({
        message: 'User with this email already exists',
        success: false
      });
    }

    // Prepare user attributes for Cognito
    const userAttributes = [
      { Name: 'email', Value: email },
      { Name: 'email_verified', Value: 'false' }
    ];

    if (phone) {
      userAttributes.push({ Name: 'phone_number', Value: phone });
    }

    if (firstName) {
      userAttributes.push({ Name: 'given_name', Value: firstName });
    }

    if (lastName) {
      userAttributes.push({ Name: 'family_name', Value: lastName });
    }

    // Add custom attribute for user type
    userAttributes.push({ Name: 'custom:userType', Value: userType });

    // Sign up user in Cognito
    const signUpParams = {
      ClientId: userPoolConfig.ClientId,
      Username: email,
      Password: password,
      UserAttributes: userAttributes
    };

    const cognitoResult = await cognitoIdentityServiceProvider.signUp(signUpParams).promise();
    const cognitoUserId = cognitoResult.UserSub;

    // Create user in MongoDB
    const newUser = await User.create({
      cognitoId: cognitoUserId,
      email,
      phone,
      firstName,
      lastName,
      userType,
      status: 'Pending',
      profileComplete: false
    });

    logger.info('User created in MongoDB:', newUser._id);

    res.status(201).json({
      message: 'User registration successful. Please check your email for verification code.',
      success: true,
      userSub: cognitoUserId,
      user: {
        id: newUser._id,
        email: newUser.email,
        firstName: newUser.firstName,
        lastName: newUser.lastName,
        userType: newUser.userType
      }
    });
  } catch (error) {
    logger.error('Error signing up user:', error);

    // Handle specific Cognito errors
    if (error.code === 'UsernameExistsException') {
      return res.status(400).json({
        message: 'User with this email already exists in Cognito',
        success: false,
        error: error.message
      });
    }

    if (error.code === 'InvalidPasswordException') {
      return res.status(400).json({
        message: 'Password does not meet requirements',
        success: false,
        error: error.message
      });
    }

    res.status(500).json({
      message: 'Error signing up user',
      success: false,
      error: error.message
    });
  }
});

// Confirm user registration
router.post('/confirm', async (req, res) => {
  try {
    const { username, code } = req.body;

    // Confirm signup with Cognito
    const params = {
      ClientId: userPoolConfig.ClientId,
      Username: username,
      ConfirmationCode: code
    };

    await cognitoIdentityServiceProvider.confirmSignUp(params).promise();

    // Update user status in our database
    const updatedUser = await User.findOneAndUpdate(
      { email: username },
      {
        status: 'Active',
        verificationStatus: 'Verified'
      },
      { new: true } // Return the updated document
    );

    if (updatedUser) {
      logger.info(`User ${username} confirmed and status updated to Active/Verified`);
    } else {
      logger.warn(`User ${username} confirmed in Cognito but not found in MongoDB`);

      // Try to find user by email in Cognito to get the Cognito ID
      try {
        const userParams = {
          UserPoolId: userPoolConfig.UserPoolId,
          Username: username
        };

        const cognitoUser = await cognitoIdentityServiceProvider.adminGetUser(userParams).promise();
        const cognitoId = cognitoUser.Username;

        // Extract user attributes
        const attributes = {};
        cognitoUser.UserAttributes.forEach(attr => {
          attributes[attr.Name] = attr.Value;
        });

        // Create user in MongoDB if not found
        const newUser = await User.create({
          cognitoId,
          email: username,
          firstName: attributes.given_name || '',
          lastName: attributes.family_name || '',
          phone: attributes.phone_number || '',
          userType: attributes['custom:userType'] || 'Individual',
          status: 'Active',
          verificationStatus: 'Verified',
          profileComplete: false
        });

        logger.info(`Created new user in MongoDB after confirmation: ${newUser._id}`);
      } catch (cognitoError) {
        logger.error('Error getting user from Cognito:', cognitoError);
      }
    }

    res.status(200).json({
      message: 'User confirmed successfully',
      success: true
    });
  } catch (error) {
    logger.error('Error confirming user:', error);

    // Handle specific Cognito errors
    if (error.code === 'CodeMismatchException') {
      return res.status(400).json({
        message: 'Invalid verification code',
        success: false,
        error: error.message
      });
    }

    if (error.code === 'ExpiredCodeException') {
      return res.status(400).json({
        message: 'Verification code has expired',
        success: false,
        error: error.message
      });
    }

    res.status(500).json({
      message: 'Error confirming user',
      success: false,
      error: error.message
    });
  }
});

// User login
router.post('/login', async (req, res) => {
  try {
    const { username, password } = req.body;

    // Authenticate with Cognito
    const params = {
      AuthFlow: 'USER_PASSWORD_AUTH',
      ClientId: userPoolConfig.ClientId,
      AuthParameters: {
        USERNAME: username,
        PASSWORD: password
      }
    };

    const authResult = await cognitoIdentityServiceProvider.initiateAuth(params).promise();

    // Get user from our database
    let user = await User.findOne({ email: username });

    // Check user status from database (primary and only source)
    const userStatus = user ? user.status : null;
    logger.debug(`Database status for ${username}: ${userStatus}`);

    // Check if user is suspended, inactive, or pending
    if (user && ['Suspended', 'Inactive', 'Pending'].includes(user.status)) {
      logger.warn(`User with status '${user.status}' attempted login: ${username}`);

      // Log the login attempt
      const UserActivity = require('../models/UserActivity');
      await UserActivity.logActivity({
        userId: user._id,
        activityType: `${user.status}LoginAttempt`,
        description: `User with status '${user.status}' attempted to log in`,
        details: {
          email: username,
          status: user.status,
          attemptedAt: new Date(),
          userAgent: req.headers['user-agent'],
          ipAddress: req.ip || req.connection.remoteAddress
        },
        severity: 'High',
        isSystemGenerated: true
      });

      // Return appropriate error based on status
      let message, errorCode;
      switch (user.status) {
        case 'Suspended':
          message = 'Account is suspended. Please contact support for assistance.';
          errorCode = 'ACCOUNT_SUSPENDED';
          break;
        case 'Inactive':
          message = 'Account is inactive. Please contact support to reactivate your account.';
          errorCode = 'ACCOUNT_INACTIVE';
          break;
        case 'Pending':
          message = 'Account is pending approval. Please wait for admin approval or contact support.';
          errorCode = 'ACCOUNT_PENDING';
          break;
      }

      return res.status(403).json({
        message: message,
        success: false,
        error: errorCode,
        status: user.status,
        supportEmail: '<EMAIL>'
      });
    }

    // Get user attributes from Cognito
    const userParams = {
      UserPoolId: userPoolConfig.UserPoolId,
      Username: username
    };

    const cognitoUser = await cognitoIdentityServiceProvider.adminGetUser(userParams).promise();

    // Extract user attributes
    const attributes = {};
    cognitoUser.UserAttributes.forEach(attr => {
      attributes[attr.Name] = attr.Value;
    });

    logger.debug('Cognito user attributes:', attributes);

    if (!user) {
      // If user exists in Cognito but not in our database, create a record
      // This can happen if Cognito was set up separately or if our database was reset

      // Create user in our database
      user = await User.create({
        cognitoId: cognitoUser.Username,
        email: attributes.email || username,
        phone: attributes.phone_number || '',
        firstName: attributes.given_name || '',
        lastName: attributes.family_name || '',
        userType: attributes['custom:userType'] || 'Individual',
        status: 'Active',
        verificationStatus: 'Verified',
        profileComplete: attributes['custom:profileComplete'] === 'true'
      });
    } else {
      // Update user with latest Cognito attributes
      if (attributes['custom:userType']) {
        user.userType = attributes['custom:userType'];
      }

      if (attributes['custom:profileComplete']) {
        user.profileComplete = attributes['custom:profileComplete'] === 'true';
      }
    }

    // Update last login time
    user.lastLogin = new Date();
    await user.save();

    // Prepare response with Cognito attributes
    const userTypeFromCognito = attributes['custom:userType'] || user.userType;
    const profileCompleteFromCognito = attributes['custom:profileComplete'] === 'true';

    logger.debug('User type from Cognito:', userTypeFromCognito);
    logger.debug('Profile complete from Cognito:', profileCompleteFromCognito);

    res.status(200).json({
      message: 'Login successful',
      success: true,
      tokens: {
        accessToken: authResult.AuthenticationResult.AccessToken,
        idToken: authResult.AuthenticationResult.IdToken,
        refreshToken: authResult.AuthenticationResult.RefreshToken
      },
      user: {
        id: user._id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        userType: userTypeFromCognito,
        profileComplete: profileCompleteFromCognito,
        cognitoAttributes: attributes
      }
    });
  } catch (error) {
    logger.error('Error logging in:', error);

    // Handle specific Cognito errors
    if (error.code === 'NotAuthorizedException') {
      return res.status(401).json({
        message: 'Incorrect username or password',
        success: false,
        error: error.message
      });
    }

    if (error.code === 'UserNotConfirmedException') {
      return res.status(401).json({
        message: 'User is not confirmed',
        success: false,
        error: error.message,
        needsConfirmation: true
      });
    }

    res.status(401).json({
      message: 'Login failed',
      success: false,
      error: error.message
    });
  }
});

// Forgot password
router.post('/forgot-password', async (req, res) => {
  try {
    const { username } = req.body;

    // Check if user exists in our database
    const user = await User.findOne({ email: username });
    if (!user) {
      // We'll still try with Cognito in case the user exists there but not in our DB
      logger.info('User not found in database, trying Cognito');
    }

    // Initiate forgot password flow in Cognito
    const params = {
      ClientId: userPoolConfig.ClientId,
      Username: username
    };

    const result = await cognitoIdentityServiceProvider.forgotPassword(params).promise();

    res.status(200).json({
      message: 'Password reset code sent',
      success: true,
      destination: result.CodeDeliveryDetails.Destination,
      deliveryMedium: result.CodeDeliveryDetails.DeliveryMedium,
      attributeName: result.CodeDeliveryDetails.AttributeName
    });
  } catch (error) {
    logger.error('Error initiating password reset:', error);

    // Handle specific Cognito errors
    if (error.code === 'UserNotFoundException') {
      return res.status(404).json({
        message: 'User not found',
        success: false,
        error: error.message
      });
    }

    if (error.code === 'LimitExceededException') {
      return res.status(429).json({
        message: 'Too many requests, please try again later',
        success: false,
        error: error.message
      });
    }

    res.status(500).json({
      message: 'Error initiating password reset',
      success: false,
      error: error.message
    });
  }
});

// Reset password (legacy Cognito method)
router.post('/reset-password', async (req, res) => {
  try {
    const { username, code, newPassword } = req.body;

    // Confirm forgot password with Cognito
    const params = {
      ClientId: userPoolConfig.ClientId,
      Username: username,
      ConfirmationCode: code,
      Password: newPassword
    };

    await cognitoIdentityServiceProvider.confirmForgotPassword(params).promise();

    res.status(200).json({
      message: 'Password reset successful',
      success: true
    });
  } catch (error) {
    logger.error('Error resetting password:', error);

    // Handle specific Cognito errors
    if (error.code === 'CodeMismatchException') {
      return res.status(400).json({
        message: 'Invalid verification code',
        success: false,
        error: error.message
      });
    }

    if (error.code === 'ExpiredCodeException') {
      return res.status(400).json({
        message: 'Verification code has expired',
        success: false,
        error: error.message
      });
    }

    if (error.code === 'InvalidPasswordException') {
      return res.status(400).json({
        message: 'Password does not meet requirements',
        success: false,
        error: error.message
      });
    }

    res.status(500).json({
      message: 'Error resetting password',
      success: false,
      error: error.message
    });
  }
});

// New password reset with email tokens
router.post('/request-password-reset', async (req, res) => {
  try {
    const { email } = req.body;
    const passwordResetService = require('../services/passwordResetService');

    if (!email) {
      return res.status(400).json({
        success: false,
        message: 'Email is required'
      });
    }

    const result = await passwordResetService.initiatePasswordReset(email, 'user');

    res.status(200).json({
      success: result.success,
      message: result.message
    });

  } catch (error) {
    logger.error('Error requesting password reset:', error);
    res.status(500).json({
      success: false,
      message: 'Error processing password reset request'
    });
  }
});

// Reset password using token
router.post('/reset-password-with-token', async (req, res) => {
  try {
    const { token, newPassword, confirmPassword } = req.body;
    const passwordResetService = require('../services/passwordResetService');

    if (!token || !newPassword || !confirmPassword) {
      return res.status(400).json({
        success: false,
        message: 'Token, new password, and confirmation password are required'
      });
    }

    const result = await passwordResetService.resetPassword(token, newPassword, confirmPassword);

    if (result.success) {
      res.status(200).json({
        success: true,
        message: result.message
      });
    } else {
      res.status(400).json({
        success: false,
        message: result.message
      });
    }

  } catch (error) {
    logger.error('Error resetting password with token:', error);
    res.status(500).json({
      success: false,
      message: 'Error resetting password'
    });
  }
});

// Verify reset token
router.post('/verify-reset-token', async (req, res) => {
  try {
    const { token } = req.body;
    const passwordResetService = require('../services/passwordResetService');

    if (!token) {
      return res.status(400).json({
        success: false,
        message: 'Token is required'
      });
    }

    const verification = passwordResetService.verifyResetToken(token);

    if (verification.valid) {
      res.status(200).json({
        success: true,
        message: 'Token is valid',
        data: {
          email: verification.email,
          timestamp: verification.timestamp
        }
      });
    } else {
      res.status(400).json({
        success: false,
        message: 'Invalid or expired token'
      });
    }

  } catch (error) {
    logger.error('Error verifying reset token:', error);
    res.status(500).json({
      success: false,
      message: 'Error verifying token'
    });
  }
});

// Update user attributes in Cognito
router.post('/update-cognito-attributes', async (req, res) => {
  try {
    const { username, attributes } = req.body;

    if (!username || !attributes) {
      return res.status(400).json({
        success: false,
        message: 'Username and attributes are required'
      });
    }

    // Prepare attributes for update
    const userAttributes = Object.entries(attributes).map(([key, value]) => ({
      Name: key.startsWith('custom:') ? key : `custom:${key}`,
      Value: value.toString()
    }));

    // Update user attributes in Cognito
    const params = {
      UserPoolId: userPoolConfig.UserPoolId,
      Username: username,
      UserAttributes: userAttributes
    };

    await cognitoIdentityServiceProvider.adminUpdateUserAttributes(params).promise();

    res.status(200).json({
      success: true,
      message: 'User attributes updated successfully'
    });
  } catch (error) {
    logger.error('Error updating user attributes in Cognito:', error);
    res.status(500).json({
      success: false,
      message: 'Error updating user attributes in Cognito',
      error: error.message
    });
  }
});

// Register user in MongoDB after Cognito registration
router.post('/register-mongodb', async (req, res) => {
  try {
    logger.info('Received request to /register-mongodb');
    logger.debug('Request body:', req.body);

    const { cognitoId, email, firstName, lastName, phone, userType = 'Individual' } = req.body;

    if (!cognitoId || !email) {
      logger.warn('Missing required fields: cognitoId or email');
      return res.status(400).json({
        success: false,
        message: 'Cognito ID and email are required'
      });
    }

    // Create new user in MongoDB with initial status Pending and Unverified
    const user = new User({
      cognitoId,
      email,
      firstName: firstName || '',
      lastName: lastName || '',
      phone: phone || '',
      userType,
      status: 'Pending',
      verificationStatus: 'Unverified',
      profileComplete: false
    });

    await user.save();
    logger.info('Created new user in MongoDB:', user._id);

    res.status(201).json({
      success: true,
      message: 'User registered in MongoDB successfully',
      data: {
        id: user._id,
        email: user.email,
        userType: user.userType
      }
    });
  } catch (error) {
    logger.error('Error registering user in MongoDB:', error);
    res.status(500).json({
      success: false,
      message: 'Error registering user in MongoDB',
      error: error.message
    });
  }
});

// Update user type in MongoDB
router.post('/update-user-type', async (req, res) => {
  try {
    logger.info('Received request to /update-user-type');
    logger.debug('Request body:', req.body);

    const { userType, email, cognitoId } = req.body;

    if (!userType) {
      logger.warn('Missing required field: userType');
      return res.status(400).json({
        success: false,
        message: 'User type is required'
      });
    }

    if (!email && !cognitoId) {
      logger.warn('Missing required fields: email or cognitoId');
      return res.status(400).json({
        success: false,
        message: 'Email or Cognito ID is required'
      });
    }

    // Find user by Cognito ID or email
    let user;

    if (cognitoId) {
      user = await User.findOne({ cognitoId });
      logger.debug('Searched for user by cognitoId:', cognitoId, 'Found:', !!user);
    }

    if (!user && email) {
      user = await User.findOne({ email });
      logger.debug('Searched for user by email:', email, 'Found:', !!user);
    }

    if (!user) {
      logger.info('User not found, creating new user');
      // Create new user
      // All user types start with incomplete profiles and must go through their respective info pages
      const isProfileComplete = false;

      user = new User({
        cognitoId: cognitoId || null,
        email,
        userType,
        status: 'Pending',
        verificationStatus: 'Unverified',
        profileComplete: isProfileComplete
      });

      logger.info(`User created with userType: ${userType}, profileComplete: ${isProfileComplete}`);
    } else {
      logger.info('User found, updating user type');
      // Update user type
      user.userType = userType;

      // Reset profile completion status when user type changes
      // All users must complete their respective info pages
      user.profileComplete = false;
      logger.info(`Profile completion reset for user type change to ${userType}`);
    }

    await user.save();
    logger.info('User saved with type:', userType);

    // Update Cognito attributes if cognitoId is available
    if (cognitoId) {
      try {
        const userAttributes = [
          { Name: 'custom:userType', Value: userType },
          { Name: 'custom:profileComplete', Value: 'false' }
        ];

        logger.info(`Setting profileComplete to false in Cognito for ${userType} - user must complete info page`);

        const params = {
          UserPoolId: userPoolConfig.UserPoolId,
          Username: cognitoId,
          UserAttributes: userAttributes
        };

        await cognitoIdentityServiceProvider.adminUpdateUserAttributes(params).promise();
        logger.info('Cognito attributes updated successfully');
      } catch (cognitoError) {
        logger.warn('Failed to update Cognito attributes:', cognitoError.message);
        // Don't fail the request if Cognito update fails
      }
    }

    res.status(200).json({
      success: true,
      message: 'User type updated successfully',
      data: {
        id: user._id,
        email: user.email,
        userType: user.userType,
        profileComplete: user.profileComplete
      }
    });
  } catch (error) {
    logger.error('Error updating user type in MongoDB:', error);
    res.status(500).json({
      success: false,
      message: 'Error updating user type in MongoDB',
      error: error.message
    });
  }
});

// Update verification status in MongoDB
router.post('/update-verification-status', async (req, res) => {
  try {
    logger.info('Received request to /update-verification-status');
    logger.debug('Request body:', req.body);

    const { email, cognitoId, status, verificationStatus } = req.body;

    if (!email && !cognitoId) {
      logger.warn('Missing required fields: email or cognitoId');
      return res.status(400).json({
        success: false,
        message: 'Email or Cognito ID is required'
      });
    }

    // Find user by Cognito ID or email
    let user;

    if (cognitoId) {
      user = await User.findOne({ cognitoId });
      logger.debug('Searched for user by cognitoId:', cognitoId, 'Found:', !!user);
    }

    if (!user && email) {
      user = await User.findOne({ email });
      logger.debug('Searched for user by email:', email, 'Found:', !!user);
    }

    if (!user) {
      logger.warn('User not found');
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    logger.info('User found, updating verification status');

    // Update status if provided
    if (status) {
      user.status = status;
    }

    // Update verification status if provided
    if (verificationStatus) {
      user.verificationStatus = verificationStatus;
    }

    await user.save();
    logger.info('User saved with status:', status, 'and verificationStatus:', verificationStatus);

    res.status(200).json({
      success: true,
      message: 'User verification status updated successfully',
      data: {
        id: user._id,
        email: user.email,
        userType: user.userType,
        status: user.status,
        verificationStatus: user.verificationStatus
      }
    });
  } catch (error) {
    logger.error('Error updating user verification status in MongoDB:', error);
    res.status(500).json({
      success: false,
      message: 'Error updating user verification status in MongoDB',
      error: error.message
    });
  }
});

// Get user type from MongoDB
router.post('/get-user-type', async (req, res) => {
  try {
    logger.info('Received request to /get-user-type');
    logger.debug('Request body:', req.body);

    const { email, cognitoId } = req.body;

    if (!email && !cognitoId) {
      logger.warn('Missing required fields: email or cognitoId');
      return res.status(400).json({
        success: false,
        message: 'Email or Cognito ID is required'
      });
    }

    // Find user by Cognito ID or email
    let user;

    if (cognitoId) {
      user = await User.findOne({ cognitoId });
      logger.debug('Searched for user by cognitoId:', cognitoId, 'Found:', !!user);
    }

    if (!user && email) {
      user = await User.findOne({ email });
      logger.debug('Searched for user by email:', email, 'Found:', !!user);
    }

    if (!user) {
      logger.warn('User not found in MongoDB');
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    logger.info('User found with type:', user.userType);

    // If we found the user type in MongoDB, also try to update Cognito to sync them
    if (user.userType && cognitoId) {
      try {
        logger.info('Syncing user type to Cognito:', user.userType);

        const userAttributes = [
          { Name: 'custom:userType', Value: user.userType.toLowerCase() },
          { Name: 'custom:profileComplete', Value: user.profileComplete ? 'true' : 'false' }
        ];

        logger.info(`Syncing profileComplete status (${user.profileComplete}) to Cognito for ${user.userType}`);

        const params = {
          UserPoolId: userPoolConfig.UserPoolId,
          Username: cognitoId,
          UserAttributes: userAttributes
        };

        await cognitoIdentityServiceProvider.adminUpdateUserAttributes(params).promise();
        logger.info('Cognito attributes synced successfully from MongoDB');
      } catch (cognitoError) {
        logger.warn('Failed to sync Cognito attributes from MongoDB:', cognitoError.message);
        // Don't fail the request if Cognito sync fails
      }
    }

    res.status(200).json({
      success: true,
      userType: user.userType,
      profileComplete: user.profileComplete,
      data: {
        id: user._id,
        email: user.email,
        userType: user.userType,
        profileComplete: user.profileComplete
      }
    });
  } catch (error) {
    logger.error('Error getting user type from MongoDB:', error);
    res.status(500).json({
      success: false,
      message: 'Error getting user type from MongoDB',
      error: error.message
    });
  }
});

// Check user status after Cognito authentication
router.post('/check-user-status', async (req, res) => {
  try {
    const { email, cognitoId } = req.body;

    if (!email) {
      return res.status(400).json({
        success: false,
        message: 'Email is required'
      });
    }

    logger.info(`Checking user status for: ${email}`);

    // Get user from our database
    let user = await User.findOne({ email: email });

    if (!user) {
      logger.warn(`User not found in database: ${email}`);
      return res.status(404).json({
        success: false,
        message: 'User not found in database',
        error: 'USER_NOT_FOUND'
      });
    }

    // Check user status from database
    const userStatus = user.status;
    logger.info(`User status for ${email}: ${userStatus}`);

    // Check if user is suspended, inactive, or pending
    if (['Suspended', 'Inactive', 'Pending'].includes(user.status)) {
      logger.warn(`User with status '${user.status}' attempted to access after Cognito auth: ${email}`);

      // Log the status check attempt
      const UserActivity = require('../models/UserActivity');
      await UserActivity.logActivity({
        userId: user._id,
        activityType: `${user.status}LoginAttempt`,
        description: `User with status '${user.status}' passed Cognito auth but blocked by status check`,
        details: {
          email: email,
          status: user.status,
          cognitoId: cognitoId,
          attemptedAt: new Date(),
          checkType: 'post-cognito-status-check'
        },
        severity: 'High',
        isSystemGenerated: true
      });

      // Return appropriate error based on status
      let message, errorCode;
      switch (user.status) {
        case 'Suspended':
          message = 'Account is suspended. Please contact support for assistance.';
          errorCode = 'ACCOUNT_SUSPENDED';
          break;
        case 'Inactive':
          message = 'Account is inactive. Please contact support to reactivate your account.';
          errorCode = 'ACCOUNT_INACTIVE';
          break;
        case 'Pending':
          message = 'Account is pending approval. Please wait for admin approval or contact support.';
          errorCode = 'ACCOUNT_PENDING';
          break;
      }

      return res.status(403).json({
        message: message,
        success: false,
        error: errorCode,
        status: user.status,
        supportEmail: '<EMAIL>'
      });
    }

    // User status is Active - allow login
    logger.info(`User status check passed for ${email}: ${userStatus}`);

    res.status(200).json({
      success: true,
      message: 'User status check passed',
      status: user.status,
      user: {
        id: user._id,
        email: user.email,
        status: user.status,
        userType: user.userType
      }
    });

  } catch (error) {
    logger.error('Error checking user status:', error);
    res.status(500).json({
      success: false,
      message: 'Error checking user status',
      error: error.message
    });
  }
});

// Create support ticket for account status issues (no auth required)
router.post('/create-account-status-ticket', async (req, res) => {
  try {
    const { email, subject, message, priority, status, errorCode } = req.body;

    if (!email || !message) {
      return res.status(400).json({
        success: false,
        message: 'Email and message are required'
      });
    }

    logger.info(`Creating account status support ticket for: ${email}`);

    // Find user by email
    const user = await User.findOne({ email: email });

    if (!user) {
      logger.warn(`User not found for support ticket: ${email}`);
      // Still create ticket even if user not found in our system
    }

    // Create support ticket
    const SupportTicket = require('../models/SupportTicket');

    const ticketData = {
      userId: user ? user._id : null,
      subject: subject || `Account Status Issue - ${status || 'Unknown'}`,
      description: message,
      type: 'Account',
      priority: (priority && priority.charAt(0).toUpperCase() + priority.slice(1)) || 'High',
      status: 'Open',
      // Add account status context
      relatedEntities: [{
        entityType: 'AccountStatus',
        entityId: user ? user._id : null
      }],
      comments: [{
        text: `Account Status Support Request

Status: ${status || 'Unknown'}
Error Code: ${errorCode || 'Not provided'}
User Email: ${email}
Priority: ${priority || 'High'}

User Message:
${message}`,
        createdBy: user ? user._id : null,
        isInternal: false,
        createdAt: new Date()
      }]
    };

    const ticket = new SupportTicket(ticketData);
    await ticket.save();

    logger.info(`Account status support ticket created: ${ticket._id} for ${email}`);

    // Log activity if user exists
    if (user) {
      const UserActivity = require('../models/UserActivity');
      await UserActivity.logActivity({
        userId: user._id,
        activityType: 'Other',
        description: 'Account status support ticket created',
        details: {
          ticketId: ticket._id,
          subject: ticket.subject,
          priority: ticket.priority,
          status: status,
          errorCode: errorCode
        },
        severity: 'Medium',
        isSystemGenerated: true
      });
    }

    // Send notification email to support team (optional)
    try {
      const emailService = require('../services/emailService');
      await emailService.sendAccountStatusTicketNotification({
        ticketId: ticket._id,
        userEmail: email,
        subject: ticket.subject,
        message: message,
        priority: ticket.priority,
        accountStatus: status,
        errorCode: errorCode
      });
      logger.info(`Account status ticket notification sent for ticket ${ticket._id}`);
    } catch (emailError) {
      logger.error('Failed to send ticket notification email:', emailError);
      // Don't fail ticket creation if email fails
    }

    res.status(201).json({
      success: true,
      message: 'Support ticket created successfully',
      ticketId: ticket._id,
      ticketNumber: `#${ticket._id.toString().slice(-6).toUpperCase()}`
    });

  } catch (error) {
    logger.error('Error creating account status support ticket:', error);
    res.status(500).json({
      success: false,
      message: 'Error creating support ticket',
      error: error.message
    });
  }
});

module.exports = router;
