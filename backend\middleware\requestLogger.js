const morgan = require('morgan');
const logger = require('../utils/logger');

// Create a custom token for request body
morgan.token('req-body', (req) => {
  if (req.method === 'POST' || req.method === 'PUT' || req.method === 'PATCH') {
    // Create a sanitized copy of the request body
    const sanitizedBody = { ...req.body };
    
    // Remove sensitive fields
    if (sanitizedBody.password) sanitizedBody.password = '********';
    if (sanitizedBody.token) sanitizedBody.token = '********';
    if (sanitizedBody.accessToken) sanitizedBody.accessToken = '********';
    if (sanitizedBody.refreshToken) sanitizedBody.refreshToken = '********';
    if (sanitizedBody.idToken) sanitizedBody.idToken = '********';
    if (sanitizedBody.apiKey) sanitizedBody.apiKey = '********';
    if (sanitizedBody.secretKey) sanitizedBody.secretKey = '********';
    
    // Mask email addresses
    if (sanitizedBody.email) sanitizedBody.email = '****@****.com';
    
    // Mask phone numbers
    if (sanitizedBody.phone) sanitizedBody.phone = '***-***-****';
    if (sanitizedBody.phoneNumber) sanitizedBody.phoneNumber = '***-***-****';
    
    // Mask credit card numbers
    if (sanitizedBody.cardNumber) sanitizedBody.cardNumber = '****-****-****-****';
    
    return JSON.stringify(sanitizedBody);
  }
  return '';
});

// Create a custom format that includes the request body
const morganFormat = ':remote-addr - :remote-user [:date[clf]] ":method :url HTTP/:http-version" :status :res[content-length] ":referrer" ":user-agent" :req-body';

// Create the middleware
const requestLogger = morgan(morganFormat, {
  stream: logger.stream,
  skip: (req, res) => {
    // Skip logging for health check endpoints to reduce noise
    return req.url === '/api/health' || req.url === '/health';
  }
});

module.exports = requestLogger;
