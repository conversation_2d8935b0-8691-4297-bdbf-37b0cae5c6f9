import { Auth } from 'aws-amplify';
import { API_BASE_URL } from '../config/api-config';
import { getItem, STORAGE_KEYS } from '../utils/localStorage';

/**
 * Service for handling dashboard-related operations
 */

/**
 * Get dashboard statistics for the current user
 * @returns {Promise} Promise object representing the dashboard statistics
 */
const getDashboardStats = async () => {
  try {
    // First try to get token from localStorage
    let token = getItem(STORAGE_KEYS.ACCESS_TOKEN);

    // If no token in localStorage, try to get it from the current session
    if (!token) {
      try {
        const session = await Auth.currentSession();
        token = session.getAccessToken().getJwtToken();
      } catch (sessionError) {
        console.error('Error getting current session:', sessionError);
        throw new Error('No access token found. Please log in again.');
      }
    }

    const cognitoId = getItem(STORAGE_KEYS.COGNITO_ID);
    if (!cognitoId) {
      throw new Error('No Cognito ID found. Please log in again.');
    }

    // Get dashboard stats from the backend
    const response = await fetch(`${API_BASE_URL}/api/dashboard/stats/${cognitoId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      }
    });

    if (!response.ok) {
      // If the API endpoint doesn't exist yet, create mock data
      if (response.status === 404) {
        console.log('Dashboard stats API not found, using mock data');
        return getMockDashboardStats();
      }

      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to fetch dashboard statistics');
    }

    const stats = await response.json();
    console.log('Dashboard stats fetched successfully:', stats);
    return stats;
  } catch (error) {
    console.error('Error fetching dashboard stats:', error);
    // Return mock data as fallback
    return getMockDashboardStats();
  }
};

/**
 * Get mock dashboard statistics for development
 * @returns {Object} Mock dashboard statistics
 */
const getMockDashboardStats = async () => {
  // Get the user type from localStorage
  const userType = getItem(STORAGE_KEYS.USER_TYPE) || localStorage.getItem('userType') || 'individual';

  // Create mock data based on user type
  let mockData = {
    invoices: {
      count: Math.floor(Math.random() * 5) + 1,
      recentInvoices: []
    },
    offers: {
      count: Math.floor(Math.random() * 3) + 1, // Ensure at least 1 offer
      pendingOffers: [
        {
          id: 'mock-offer-1',
          name: 'Green Energy Special',
          provider: 'EcoEnergy',
          energyType: 'Electricity',
          price: '€0.15/kWh',
          savings: '15%',
          validUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
        },
        {
          id: 'mock-offer-2',
          name: 'Gas & Electric Bundle',
          provider: 'TotalEnergies',
          energyType: 'Both',
          price: '€0.12/kWh + €0.08/m³',
          savings: '20%',
          validUntil: new Date(Date.now() + 45 * 24 * 60 * 60 * 1000).toISOString()
        }
      ]
    },
    contracts: {
      count: Math.floor(Math.random() * 2) + 1, // Ensure at least 1 contract
      activeContracts: []
    },
    appointments: {
      count: Math.floor(Math.random() * 3),
      upcomingAppointments: []
    }
  };

  // Try to get real counts from all endpoints
  try {
    const cognitoId = getItem(STORAGE_KEYS.COGNITO_ID);
    if (cognitoId) {
      const token = getItem(STORAGE_KEYS.ACCESS_TOKEN);
      if (token) {
        // Fetch all counts in parallel
        const [invoiceResponse, offerResponse, contractResponse, appointmentResponse] = await Promise.allSettled([
          fetch(`${API_BASE_URL}/api/dashboard/invoices/count/${cognitoId}`, {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${token}`
            }
          }),
          fetch(`${API_BASE_URL}/api/dashboard/offers/count/${cognitoId}`, {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${token}`
            }
          }),
          fetch(`${API_BASE_URL}/api/dashboard/contracts/count/${cognitoId}`, {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${token}`
            }
          }),
          fetch(`${API_BASE_URL}/api/dashboard/appointments/count/${cognitoId}`, {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${token}`
            }
          })
        ]);

        // Update invoice count
        if (invoiceResponse.status === 'fulfilled' && invoiceResponse.value.ok) {
          const data = await invoiceResponse.value.json();
          if (data && data.count !== undefined) {
            mockData.invoices.count = data.count;
          }
        }

        // Update offer count
        if (offerResponse.status === 'fulfilled' && offerResponse.value.ok) {
          const data = await offerResponse.value.json();
          if (data && data.count !== undefined) {
            mockData.offers.count = data.count;
          }
        }

        // Update contract count
        if (contractResponse.status === 'fulfilled' && contractResponse.value.ok) {
          const data = await contractResponse.value.json();
          if (data && data.count !== undefined) {
            mockData.contracts.count = data.count;
          }
        }

        // Update appointment count
        if (appointmentResponse.status === 'fulfilled' && appointmentResponse.value.ok) {
          const data = await appointmentResponse.value.json();
          if (data && data.count !== undefined) {
            mockData.appointments.count = data.count;
          }
        }
      }
    }
  } catch (error) {
    console.error('Error fetching real counts:', error);
  }

  return {
    success: true,
    data: mockData
  };
};

/**
 * Get user invoices count
 * @returns {Promise} Promise object representing the invoice count
 */
const getInvoiceCount = async () => {
  try {
    // First try to get token from localStorage
    let token = getItem(STORAGE_KEYS.ACCESS_TOKEN);

    // If no token in localStorage, try to get it from the current session
    if (!token) {
      try {
        const session = await Auth.currentSession();
        token = session.getAccessToken().getJwtToken();
      } catch (sessionError) {
        console.error('Error getting current session:', sessionError);
        throw new Error('No access token found. Please log in again.');
      }
    }

    const cognitoId = getItem(STORAGE_KEYS.COGNITO_ID);
    if (!cognitoId) {
      throw new Error('No Cognito ID found. Please log in again.');
    }

    // Get invoice count from the backend
    const response = await fetch(`${API_BASE_URL}/api/invoices/count/${cognitoId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      }
    });

    if (!response.ok) {
      // If the API endpoint doesn't exist yet, return mock data
      if (response.status === 404) {
        return { count: Math.floor(Math.random() * 5) + 1 };
      }

      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to fetch invoice count');
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching invoice count:', error);
    return { count: Math.floor(Math.random() * 5) + 1 };
  }
};

/**
 * Get offer count for the current user
 * @returns {Promise} Promise object representing the offer count
 */
const getOfferCount = async () => {
  try {
    let token = getItem(STORAGE_KEYS.ACCESS_TOKEN);

    if (!token) {
      try {
        const session = await Auth.currentSession();
        token = session.getAccessToken().getJwtToken();
      } catch (sessionError) {
        console.error('Error getting current session:', sessionError);
        throw new Error('No access token found. Please log in again.');
      }
    }

    const cognitoId = getItem(STORAGE_KEYS.COGNITO_ID);
    if (!cognitoId) {
      throw new Error('No Cognito ID found. Please log in again.');
    }

    const response = await fetch(`${API_BASE_URL}/api/dashboard/offers/count/${cognitoId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      }
    });

    if (!response.ok) {
      if (response.status === 404) {
        // Return mock data with some offers for better UX
        return {
          count: Math.floor(Math.random() * 3) + 1, // Ensure at least 1 offer
          offers: [
            {
              id: 'mock-offer-1',
              name: 'Green Energy Special',
              provider: 'EcoEnergy',
              energyType: 'Electricity',
              price: '€0.15/kWh',
              savings: '15%',
              validUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
            },
            {
              id: 'mock-offer-2',
              name: 'Gas & Electric Bundle',
              provider: 'TotalEnergies',
              energyType: 'Both',
              price: '€0.12/kWh + €0.08/m³',
              savings: '20%',
              validUntil: new Date(Date.now() + 45 * 24 * 60 * 60 * 1000).toISOString()
            }
          ]
        };
      }

      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to fetch offer count');
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching offer count:', error);
    // Return mock data with some offers for better UX
    return {
      count: Math.floor(Math.random() * 3) + 1, // Ensure at least 1 offer
      offers: [
        {
          id: 'mock-offer-1',
          name: 'Green Energy Special',
          provider: 'EcoEnergy',
          energyType: 'Electricity',
          price: '€0.15/kWh',
          savings: '15%',
          validUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
        },
        {
          id: 'mock-offer-2',
          name: 'Gas & Electric Bundle',
          provider: 'TotalEnergies',
          energyType: 'Both',
          price: '€0.12/kWh + €0.08/m³',
          savings: '20%',
          validUntil: new Date(Date.now() + 45 * 24 * 60 * 60 * 1000).toISOString()
        }
      ]
    };
  }
};

/**
 * Get contract count for the current user
 * @returns {Promise} Promise object representing the contract count
 */
const getContractCount = async () => {
  try {
    let token = getItem(STORAGE_KEYS.ACCESS_TOKEN);

    if (!token) {
      try {
        const session = await Auth.currentSession();
        token = session.getAccessToken().getJwtToken();
      } catch (sessionError) {
        console.error('Error getting current session:', sessionError);
        throw new Error('No access token found. Please log in again.');
      }
    }

    const cognitoId = getItem(STORAGE_KEYS.COGNITO_ID);
    if (!cognitoId) {
      throw new Error('No Cognito ID found. Please log in again.');
    }

    const response = await fetch(`${API_BASE_URL}/api/dashboard/contracts/count/${cognitoId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      }
    });

    if (!response.ok) {
      if (response.status === 404) {
        return { count: Math.floor(Math.random() * 2) };
      }

      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to fetch contract count');
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching contract count:', error);
    return { count: Math.floor(Math.random() * 2) };
  }
};

/**
 * Get appointment count for the current user
 * @returns {Promise} Promise object representing the appointment count
 */
const getAppointmentCount = async () => {
  try {
    let token = getItem(STORAGE_KEYS.ACCESS_TOKEN);

    if (!token) {
      try {
        const session = await Auth.currentSession();
        token = session.getAccessToken().getJwtToken();
      } catch (sessionError) {
        console.error('Error getting current session:', sessionError);
        throw new Error('No access token found. Please log in again.');
      }
    }

    const cognitoId = getItem(STORAGE_KEYS.COGNITO_ID);
    if (!cognitoId) {
      throw new Error('No Cognito ID found. Please log in again.');
    }

    const response = await fetch(`${API_BASE_URL}/api/dashboard/appointments/count/${cognitoId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      }
    });

    if (!response.ok) {
      if (response.status === 404) {
        return { count: Math.floor(Math.random() * 3) };
      }

      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to fetch appointment count');
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching appointment count:', error);
    return { count: Math.floor(Math.random() * 3) };
  }
};

const dashboardService = {
  getDashboardStats,
  getInvoiceCount,
  getOfferCount,
  getContractCount,
  getAppointmentCount
};

export default dashboardService;
