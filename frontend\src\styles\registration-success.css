.registration-success-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.success-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 700px;
  overflow: hidden;
  text-align: center;
}

.success-icon {
  padding: 40px 30px 20px;
  position: relative;
}

.success-icon > i {
  font-size: 80px;
  color: #28a745;
  margin-bottom: 20px;
  display: block;
}

.check-mark {
  position: absolute;
  top: 30px;
  right: 30px;
  width: 40px;
  height: 40px;
  background: #28a745;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
  animation: checkmarkPop 0.6s ease-out 0.3s both;
}

.success-card h1 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 2.5rem;
  font-weight: 300;
}

.success-message {
  padding: 0 30px 30px;
}

.success-message > p {
  font-size: 1.1rem;
  color: #495057;
  margin-bottom: 30px;
  line-height: 1.6;
}

.approval-notice,
.immediate-access {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 24px;
  margin: 20px 0;
  display: flex;
  align-items: flex-start;
  gap: 16px;
  text-align: left;
}

.approval-notice {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
}

.immediate-access {
  background: #d4edda;
  border: 1px solid #c3e6cb;
}

.notice-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  flex-shrink: 0;
}

.approval-notice .notice-icon {
  background: #ffc107;
  color: white;
}

.immediate-access .notice-icon {
  background: #28a745;
  color: white;
}

.notice-content h3 {
  margin: 0 0 12px 0;
  color: #2c3e50;
  font-size: 1.2rem;
}

.notice-content p {
  margin: 0 0 12px 0;
  color: #495057;
  line-height: 1.5;
}

.notice-content p:last-child {
  margin-bottom: 0;
}

.next-steps {
  padding: 30px;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
}

.next-steps h3 {
  margin: 0 0 24px 0;
  color: #2c3e50;
  font-size: 1.3rem;
}

.steps-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
  text-align: left;
}

.step {
  display: flex;
  align-items: flex-start;
  gap: 16px;
}

.step-number {
  width: 32px;
  height: 32px;
  background: #007bff;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
  flex-shrink: 0;
}

.step-content h4 {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 1rem;
}

.step-content p {
  margin: 0;
  color: #6c757d;
  font-size: 14px;
  line-height: 1.5;
}

.platform-benefits {
  padding: 30px;
  border-top: 1px solid #e9ecef;
}

.platform-benefits h3 {
  margin: 0 0 24px 0;
  color: #2c3e50;
  font-size: 1.3rem;
}

.benefits-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
}

.benefit-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  text-align: center;
}

.benefit-item i {
  font-size: 24px;
  color: #007bff;
}

.benefit-item span {
  font-size: 12px;
  color: #495057;
  font-weight: 500;
}

.action-buttons {
  padding: 30px;
  border-top: 1px solid #e9ecef;
}

.btn-primary {
  padding: 14px 32px;
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 200px;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 123, 255, 0.3);
}

.support-info {
  padding: 20px 30px;
  background: #e3f2fd;
  border-top: 1px solid #e9ecef;
}

.support-info p {
  margin: 0;
  color: #1976d2;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.support-info a {
  color: #1976d2;
  text-decoration: none;
  font-weight: 500;
}

.support-info a:hover {
  text-decoration: underline;
}

/* Animations */
@keyframes checkmarkPop {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.success-card {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

.success-icon > i {
  animation: bounce 1s ease-out 0.5s;
}

/* Responsive Design */
@media (max-width: 768px) {
  .registration-success-container {
    padding: 10px;
  }

  .success-card {
    margin: 10px;
  }

  .success-card h1 {
    font-size: 2rem;
  }

  .success-icon {
    padding: 30px 20px 15px;
  }

  .success-icon > i {
    font-size: 60px;
  }

  .check-mark {
    width: 32px;
    height: 32px;
    font-size: 16px;
    top: 25px;
    right: 25px;
  }

  .success-message,
  .next-steps,
  .platform-benefits,
  .action-buttons {
    padding: 20px;
  }

  .approval-notice,
  .immediate-access {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .steps-list {
    gap: 16px;
  }

  .step {
    flex-direction: column;
    text-align: center;
    gap: 8px;
  }

  .benefits-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }

  .support-info {
    padding: 15px 20px;
  }

  .support-info p {
    font-size: 13px;
    flex-direction: column;
    gap: 4px;
  }
}

@media (max-width: 480px) {
  .success-card h1 {
    font-size: 1.5rem;
  }

  .success-icon > i {
    font-size: 50px;
  }

  .benefits-grid {
    grid-template-columns: 1fr;
  }

  .btn-primary {
    width: 100%;
    min-width: auto;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .success-card {
    background: #2c3e50;
    color: #ecf0f1;
  }

  .success-card h1,
  .notice-content h3,
  .next-steps h3,
  .platform-benefits h3,
  .step-content h4 {
    color: #ecf0f1;
  }

  .success-message > p,
  .notice-content p {
    color: #bdc3c7;
  }

  .next-steps,
  .platform-benefits {
    background: #34495e;
    border-top-color: #4a5f7a;
  }

  .step-content p {
    color: #95a5a6;
  }

  .benefit-item {
    background: #34495e;
  }

  .benefit-item span {
    color: #bdc3c7;
  }

  .support-info {
    background: #2980b9;
    border-top-color: #3498db;
  }

  .support-info p,
  .support-info a {
    color: #ecf0f1;
  }
}
