.registration-success-container {
  min-height: 100vh;
  background: #000000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.success-card {
  background: #ffffff;
  border-radius: 20px;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
  width: 100%;
  max-width: 800px;
  overflow: hidden;
  text-align: center;
  border: 1px solid #e5e7eb;
}

/* Card Header */
.card-header {
  background: #f8f9fa;
  padding: 30px;
  border-bottom: 1px solid #e5e7eb;
  position: relative;
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 8px;
}

.success-logo {
  height: 65px;
  width: auto;
}

.company-name {
  font-size: 1.8rem;
  font-weight: 700;
  color: #1f2937;
  letter-spacing: 1px;
}

.success-icon {
  position: absolute;
  top: 20px;
  right: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.icon-wrapper {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #10b981 0%, #**********%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 10px 25px rgba(16, 185, 129, 0.3);
}

.icon-wrapper i {
  font-size: 40px;
  color: white;
}

.check-mark {
  position: absolute;
  top: -5px;
  right: -5px;
  width: 32px;
  height: 32px;
  background: #059669;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
  border: 3px solid white;
  animation: checkmarkPop 0.6s ease-out 0.3s both;
}

/* Success Content */
.success-content {
  padding: 40px 40px 30px;
}

.success-content h1 {
  margin: 0 0 20px 0;
  color: #1f2937;
  font-size: 2.5rem;
  font-weight: 600;
  letter-spacing: -0.5px;
}

.welcome-message {
  margin-bottom: 30px;
}

.welcome-message p {
  font-size: 1.2rem;
  color: #6b7280;
  margin: 0;
  line-height: 1.6;
}

/* Status Cards */
.status-card {
  background: #f8f9fa;
  border-radius: 16px;
  padding: 30px;
  margin: 25px 0;
  display: flex;
  align-items: flex-start;
  gap: 20px;
  text-align: left;
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

.approval-status {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  border-color: #f59e0b;
}

.ready-status {
  background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
  border-color: #10b981;
}

.status-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  flex-shrink: 0;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.approval-status .status-icon {
  background: #f59e0b;
  color: white;
}

.ready-status .status-icon {
  background: #10b981;
  color: white;
}

.status-content h3 {
  margin: 0 0 15px 0;
  color: #1f2937;
  font-size: 1.4rem;
  font-weight: 600;
}

.status-content p {
  margin: 0 0 15px 0;
  color: #4b5563;
  line-height: 1.6;
  font-size: 1rem;
}

.email-notification {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-top: 15px;
  padding: 15px;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 10px;
  border-left: 4px solid #f59e0b;
}

.email-notification i {
  color: #f59e0b;
  font-size: 16px;
}

.email-notification span {
  color: #374151;
  font-size: 0.95rem;
}

/* Next Steps Section */
.next-steps-section {
  padding: 40px;
  background: #f9fafb;
  border-top: 1px solid #e5e7eb;
}

.next-steps-section h3 {
  margin: 0 0 35px 0;
  color: #1f2937;
  font-size: 1.8rem;
  font-weight: 600;
  text-align: center;
}

.steps-container {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 25px;
  max-width: 800px;
  margin: 0 auto;
  padding: 0 20px;
  align-items: start;
}

.step-item {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  text-align: left;
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
  gap: 15px;
}

.step-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.step-number {
  width: 40px;
  height: 40px;
  background: #000;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 16px;
  flex-shrink: 0;
  margin-top: 2px;
}

.step-text {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  text-align: left;
}

.step-text h4 {
  margin: 0 0 6px 0;
  color: #1f2937;
  font-size: 1rem;
  font-weight: 600;
  line-height: 1.3;
  text-align: left;
}

.step-text p {
  margin: 0;
  color: #6b7280;
  font-size: 0.9rem;
  line-height: 1.5;
  text-align: left;
}

/* Benefits Section */
.benefits-section {
  padding: 40px;
  background: white;
  border-top: 1px solid #e5e7eb;
}

.benefits-section h3 {
  margin: 0 0 30px 0;
  color: #1f2937;
  font-size: 1.5rem;
  font-weight: 600;
  text-align: center;
}

.benefits-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 20px;
}

.benefit-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  padding: 25px 20px;
  background: #f9fafb;
  border-radius: 12px;
  text-align: center;
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

.benefit-item:hover {
  border-color: #10b981;
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.15);
}

.benefit-item i {
  font-size: 32px;
  color: #10b981;
  margin-bottom: 5px;
}

.benefit-item span {
  font-size: 0.95rem;
  color: #374151;
  font-weight: 600;
  line-height: 1.4;
}

/* Action Section */
.action-section {
  padding: 40px;
  background: #f9fafb;
  border-top: 1px solid #e5e7eb;
  text-align: center;
}

.primary-button {
  display: inline-flex;
  align-items: center;
  gap: 12px;
  padding: 16px 32px;
  background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 220px;
  box-shadow: 0 4px 12px rgba(31, 41, 55, 0.3);
}

.primary-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(31, 41, 55, 0.4);
  background: linear-gradient(135deg, #111827 0%, #000000 100%);
}

.primary-button i {
  font-size: 1rem;
}

/* Footer Info */
.footer-info {
  padding: 25px 40px;
  background: #f3f4f6;
  border-top: 1px solid #e5e7eb;
}

.support-contact {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  color: #6b7280;
  font-size: 0.95rem;
}

.support-contact i {
  color: #10b981;
  font-size: 1.1rem;
}

/* Animations */
@keyframes checkmarkPop {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.success-card {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

.success-icon > i {
  animation: bounce 1s ease-out 0.5s;
}

/* Responsive Design */
@media (max-width: 1024px) and (min-width: 769px) {
  .steps-container {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    padding: 0 15px;
  }
}

@media (max-width: 768px) {
  .registration-success-container {
    padding: 10px;
  }

  .success-card {
    margin: 10px;
  }

  .card-header {
    flex-direction: column;
    gap: 20px;
    text-align: center;
    padding: 30px 30px 60px 30px;
  }

  .success-card h1 {
    font-size: 2rem;
  }

  .success-icon {
    padding: 0;
    top: 15px;
    right: 15px;
  }

  .icon-wrapper {
    width: 60px;
    height: 60px;
  }

  .icon-wrapper i {
    font-size: 30px;
  }

  .check-mark {
    width: 28px;
    height: 28px;
    font-size: 14px;
    top: -5px;
    right: -5px;
  }

  .steps-container {
    grid-template-columns: 1fr;
    gap: 15px;
    padding: 0 10px;
  }

  .step-item {
    flex-direction: row;
    padding: 15px;
    gap: 12px;
  }

  .step-number {
    width: 35px;
    height: 35px;
    font-size: 14px;
    margin-top: 0;
  }

  .step-text h4 {
    font-size: 0.95rem;
    margin-bottom: 4px;
  }

  .step-text p {
    font-size: 0.85rem;
    line-height: 1.4;
  }

  .next-steps-section h3 {
    font-size: 1.5rem;
    margin-bottom: 30px;
  }

  .benefits-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }

  .support-info {
    padding: 15px 20px;
  }

  .support-info p {
    font-size: 13px;
    flex-direction: column;
    gap: 4px;
  }

  .steps-container {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    padding: 0 15px;
  }

  .step-item {
    padding: 25px 15px;
  }

  .step-number {
    width: 45px;
    height: 45px;
    font-size: 18px;
    margin-bottom: 15px;
  }

  .step-text h4 {
    font-size: 0.95rem;
    margin-bottom: 8px;
  }

  .step-text p {
    font-size: 0.85rem;
    line-height: 1.3;
  }
}

@media (max-width: 480px) {
  .success-card h1 {
    font-size: 1.5rem;
  }

  .success-icon > i {
    font-size: 50px;
  }

  .benefits-grid {
    grid-template-columns: 1fr;
  }

  .btn-primary {
    width: 100%;
    min-width: auto;
  }

  .steps-container {
    grid-template-columns: 1fr;
    gap: 20px;
    padding: 0 10px;
  }

  .step-item {
    padding: 20px 15px;
  }

  .step-number {
    width: 40px;
    height: 40px;
    font-size: 16px;
    margin-bottom: 12px;
  }

  .step-text h4 {
    font-size: 0.9rem;
    margin-bottom: 6px;
  }

  .step-text p {
    font-size: 0.8rem;
    line-height: 1.3;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .success-card {
    background: #2c3e50;
    color: #ecf0f1;
  }

  .success-card h1,
  .notice-content h3,
  .next-steps h3,
  .platform-benefits h3,
  .step-content h4 {
    color: #ecf0f1;
  }

  .success-message > p,
  .notice-content p {
    color: #bdc3c7;
  }

  .next-steps,
  .platform-benefits {
    background: #34495e;
    border-top-color: #4a5f7a;
  }

  .step-content p {
    color: #95a5a6;
  }

  .benefit-item {
    background: #34495e;
  }

  .benefit-item span {
    color: #bdc3c7;
  }

  .support-info {
    background: #2980b9;
    border-top-color: #3498db;
  }

  .support-info p,
  .support-info a {
    color: #ecf0f1;
  }
}
