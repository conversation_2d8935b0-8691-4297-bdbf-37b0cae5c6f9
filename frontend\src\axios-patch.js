// This file patches axios to work with Vite in production builds
// It's imported before any other imports in main.jsx

// Define a global _global object to fix the axios issue
window._global = window;

// Ensure it's available in the global scope
if (typeof globalThis !== 'undefined') {
  globalThis._global = window;
}

// Make sure global is defined
if (typeof global === 'undefined') {
  window.global = window;
}

// Export for ESM compatibility
export default window._global;
