import React from 'react';

const SupplierConfirmationStep = ({ formData, onSubmit, onPrev, onCancel }) => {
  const handleSubmit = () => {
    onSubmit();
  };

  return (
    <div className="step-content">
      <div className="stepper-header">
        <h2 className="page-title">Confirm Your Information</h2>
        <p className="page-subtitle">Please review your supplier profile information before submitting</p>
      </div>

      <div className="stepper-form">
        <div className="confirmation-summary">
          <h4>Review your supplier profile details</h4>

          <div className="summary-section">
            <h5>Company Information</h5>
            <div className="summary-row">
              <span className="summary-label">Company Name:</span>
              <span className="summary-value">{formData.companyName || 'Not provided'}</span>
            </div>
            <div className="summary-row">
              <span className="summary-label">License Number:</span>
              <span className="summary-value">{formData.licenseNumber || 'Not provided'}</span>
            </div>
            <div className="summary-row">
              <span className="summary-label">Website:</span>
              <span className="summary-value">{formData.website || 'Not provided'}</span>
            </div>
            <div className="summary-row">
              <span className="summary-label">Phone:</span>
              <span className="summary-value">{formData.phone || 'Not provided'}</span>
            </div>
            <div className="summary-row">
              <span className="summary-label">VAT Number:</span>
              <span className="summary-value">{formData.vatNumber || 'Not provided'}</span>
            </div>
            <div className="summary-row">
              <span className="summary-label">Business Description:</span>
              <span className="summary-value">{formData.businessDescription || 'Not provided'}</span>
            </div>
          </div>

          <div className="summary-section">
            <h5>Business Address</h5>
            <div className="summary-row">
              <span className="summary-label">Street:</span>
              <span className="summary-value">{formData.companyAddress.street || 'Not provided'}</span>
            </div>
            <div className="summary-row">
              <span className="summary-label">City:</span>
              <span className="summary-value">{formData.companyAddress.city || 'Not provided'}</span>
            </div>
            <div className="summary-row">
              <span className="summary-label">Postal Code:</span>
              <span className="summary-value">{formData.companyAddress.postalCode || 'Not provided'}</span>
            </div>
            <div className="summary-row">
              <span className="summary-label">Country:</span>
              <span className="summary-value">{formData.companyAddress.country}</span>
            </div>
          </div>

          <div className="summary-section">
            <h5>Energy Services</h5>
            <div className="summary-row">
              <span className="summary-label">Energy Types:</span>
              <span className="summary-value">
                {formData.energyTypesProvided.length > 0
                  ? formData.energyTypesProvided.join(', ')
                  : 'None selected'}
              </span>
            </div>
            <div className="summary-row">
              <span className="summary-label">Contract Types:</span>
              <span className="summary-value">
                {formData.contractTypes.length > 0
                  ? formData.contractTypes.join(', ')
                  : 'None selected'}
              </span>
            </div>
            <div className="summary-row">
              <span className="summary-label">Service Areas:</span>
              <span className="summary-value">
                {formData.serviceAreas.length > 0
                  ? formData.serviceAreas.join(', ')
                  : 'None selected'}
              </span>
            </div>
          </div>
        </div>
      </div>

      <div className="stepper-buttons">
        <button type="button" className="stepper-button stepper-button-prev" onClick={onPrev}>
          <i className="fas fa-arrow-left"></i> Previous
        </button>
        <button type="button" className="stepper-button stepper-button-next" onClick={handleSubmit}>
          <i className="fas fa-check"></i> Complete Setup
        </button>
      </div>
    </div>
  );
};

export default SupplierConfirmationStep;
