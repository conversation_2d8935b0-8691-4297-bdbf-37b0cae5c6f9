import React from 'react';

const ProfessionalInfoDisplay = ({ professionalInfo, onEdit, userType = 'professional' }) => {
  if (!professionalInfo) return null;

  // Handle different formats of data (API response vs localStorage)
  let info;

  if (typeof professionalInfo === 'string') {
    // Parse the stored JSON if it's a string from localStorage
    info = JSON.parse(professionalInfo);
  } else if (professionalInfo.data) {
    // Handle API response format
    const userData = professionalInfo.data;
    const profileData = userData.profile || {};

    // Combine user data with profile data, giving priority to profile data
    info = {
      ...userData,
      ...profileData,
      // Ensure we preserve nested objects
      companyAddress: profileData.companyAddress || userData.companyAddress || {},
      currentSuppliers: profileData.currentSuppliers || userData.currentSuppliers || [],
      annualConsumption: profileData.annualConsumption || userData.annualConsumption || {}
    };

    console.log('Processed professional info:', info);
  } else {
    // Direct object
    info = professionalInfo;
  }

  // Format the energy types for display
  const formatEnergyTypes = () => {
    const types = [];

    // Handle different data structures
    if (info.energyTypes) {
      // Handle object format (from form)
      if (typeof info.energyTypes === 'object' && !Array.isArray(info.energyTypes)) {
        if (info.energyTypes.electricity) types.push('Electricity');
        if (info.energyTypes.gas) types.push('Gas');
      }
      // Handle array format (from API)
      else if (Array.isArray(info.energyTypes)) {
        info.energyTypes.forEach(type => {
          if (type === 'Electricity' || type === 'electricity') types.push('Electricity');
          if (type === 'Gas' || type === 'gas') types.push('Gas');
          if (type === 'Both' || type === 'both') {
            types.push('Electricity');
            types.push('Gas');
          }
        });
      }
    } else if (info.preferredEnergyTypes) {
      if (Array.isArray(info.preferredEnergyTypes)) {
        if (info.preferredEnergyTypes.includes('electricity') ||
            info.preferredEnergyTypes.includes('Electricity')) {
          types.push('Electricity');
        }
        if (info.preferredEnergyTypes.includes('gas') ||
            info.preferredEnergyTypes.includes('Gas')) {
          types.push('Gas');
        }
      }
    }

    // Remove duplicates
    const uniqueTypes = [...new Set(types)];
    return uniqueTypes.length > 0 ? uniqueTypes.join(' & ') : 'Not specified';
  };

  // Format the date for display
  const formatDate = (dateString) => {
    if (!dateString) return 'Not specified';
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  // Get the appropriate title based on user type
  const getAccountTypeTitle = () => {
    switch(userType) {
      case 'broker':
        return 'Broker Account Information';
      case 'supplier':
        return 'Supplier Account Information';
      default:
        return 'Professional Account Information';
    }
  };

  return (
    <div className="info-display-card enhanced">
      <div className="info-display-header">
        <h2 className="info-display-title">{getAccountTypeTitle()}</h2>
        <button
          className="edit-button"
          onClick={onEdit}
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
            <path d="M12.146.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1 0 .708l-10 10a.5.5 0 0 1-.168.11l-5 2a.5.5 0 0 1-.65-.65l2-5a.5.5 0 0 1 .11-.168l10-10zM11.207 2.5 13.5 4.793 14.793 3.5 12.5 1.207 11.207 2.5zm1.586 3L10.5 3.207 4 9.707V10h.5a.5.5 0 0 1 .5.5v.5h.5a.5.5 0 0 1 .5.5v.5h.293l6.5-6.5zm-9.761 5.175-.106.106-1.528 3.821 3.821-1.528.106-.106A.5.5 0 0 1 5 12.5V12h-.5a.5.5 0 0 1-.5-.5V11h-.5a.5.5 0 0 1-.468-.325z"/>
          </svg>
          Edit
        </button>
      </div>

      <div className="info-display-content">
        <div className="info-display-column">
          <div className="info-display-section">
            <h3 className="info-display-subtitle">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                <path d="M6.5 1A1.5 1.5 0 0 0 5 2.5V3H1.5A1.5 1.5 0 0 0 0 4.5v1.384l7.614 2.03a1.5 1.5 0 0 0 .772 0L16 5.884V4.5A1.5 1.5 0 0 0 14.5 3H11v-.5A1.5 1.5 0 0 0 9.5 1h-3zm0 1h3a.5.5 0 0 1 .5.5V3H6v-.5a.5.5 0 0 1 .5-.5z"/>
                <path d="M0 12.5A1.5 1.5 0 0 0 1.5 14h13a1.5 1.5 0 0 0 1.5-1.5V6.85L8.129 8.947a.5.5 0 0 1-.258 0L0 6.85v5.65z"/>
              </svg>
              Company Information
            </h3>

            <div className="info-display-item">
              <div className="info-display-label">Company Name</div>
              <div className="info-display-value highlight">{info.companyName || 'Not provided'}</div>
            </div>

            <div className="info-display-item">
              <div className="info-display-label">Role</div>
              <div className="info-display-value">{info.role || info.position || 'Not provided'}</div>
            </div>

            <div className="info-display-item">
              <div className="info-display-label">SIRET Number</div>
              <div className="info-display-value">{info.siretNumber || info.siret || 'Not provided'}</div>
            </div>

            <div className="info-display-item">
              <div className="info-display-label">Address</div>
              <div className="info-display-value">
                {/* Handle different address formats */}
                {typeof info.companyAddress === 'string' && <div>{info.companyAddress}</div>}

                {/* Handle nested companyAddress object from API */}
                {info.companyAddress && typeof info.companyAddress === 'object' && (
                  <>
                    {info.companyAddress.street && <div>{info.companyAddress.street}</div>}
                    {info.companyAddress.city && info.companyAddress.postalCode && (
                      <div>{info.companyAddress.city}, {info.companyAddress.postalCode}</div>
                    )}
                    {info.companyAddress.country && <div>{info.companyAddress.country}</div>}
                  </>
                )}

                {/* Fallback to address if companyAddress is not available */}
                {!info.companyAddress && info.address && (
                  <>
                    {info.address.street && <div>{info.address.street}</div>}
                    {info.address.city && info.address.postalCode && (
                      <div>{info.address.city}, {info.address.postalCode}</div>
                    )}
                    {info.address.country && <div>{info.address.country}</div>}
                  </>
                )}

                {!info.companyAddress && !info.address && 'Not provided'}
              </div>
            </div>
          </div>
        </div>

        <div className="info-display-column">
          <div className="info-display-section">
            <h3 className="info-display-subtitle">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                <path d="M2.45 7.4 7.2 1.067a1 1 0 0 1 1.6 0L13.55 7.4a1 1 0 0 1 0 1.2L8.8 14.933a1 1 0 0 1-1.6 0L2.45 8.6a1 1 0 0 1 0-1.2z"/>
                <path d="M7.2 1.067a1 1 0 0 1 1.6 0L13.55 7.4a1 1 0 0 1 0 1.2L8.8 14.933a1 1 0 0 1-1.6 0L2.45 8.6a1 1 0 0 1 0-1.2L7.2 1.067z"/>
                <path d="M6.375 7.125V4.658h1.78c.973 0 1.542.457 1.542 1.237 0 .802-.604 1.23-1.764 1.23H6.375zm0 3.762h1.898c1.184 0 1.81-.48 1.81-1.377 0-.885-.65-1.348-1.886-1.348H6.375v2.725z"/>
              </svg>
              Energy Information
            </h3>

            <div className="info-display-item">
              <div className="info-display-label">Energy Types</div>
              <div className="info-display-value highlight">{formatEnergyTypes()}</div>
            </div>

            <div className="info-display-item">
              <div className="info-display-label">Current Supplier</div>
              <div className="info-display-value">
                {/* Handle different supplier formats */}
                {info.currentSupplier === 'Other' ? info.otherSupplier :
                 info.currentSupplier ||
                 (info.currentSuppliers && Array.isArray(info.currentSuppliers) &&
                  info.currentSuppliers.length > 0 ?
                  info.currentSuppliers.map(s => s.supplierName).join(', ') :
                  'Not provided')}
              </div>
            </div>

            <div className="info-display-item">
              <div className="info-display-label">Contract End Date</div>
              <div className="info-display-value">
                {/* Handle different contract date formats */}
                {info.contractEndDate ? formatDate(info.contractEndDate) :
                 (info.currentSuppliers && Array.isArray(info.currentSuppliers) &&
                  info.currentSuppliers.length > 0 && info.currentSuppliers[0].contractEndDate ?
                  formatDate(info.currentSuppliers[0].contractEndDate) :
                  'Not specified')}
              </div>
            </div>

            <div className="info-display-item">
              <div className="info-display-label">Preferred Contract Length</div>
              <div className="info-display-value">
                {info.preferredContractLength ? `${info.preferredContractLength} months` : 'Not specified'}
              </div>
            </div>

            <div className="info-display-item">
              <div className="info-display-label">Meter Number</div>
              <div className="info-display-value highlight">
                {/* Handle both direct property and nested energyIdentifiers */}
                {info.meterNumber ||
                 info.energyIdentifiers?.pdl ||
                 info.energyIdentifiers?.prm ||
                 info.energyIdentifiers?.rae ||
                 'Not provided'}
              </div>
              <div className="info-display-hint">PDL/PRM/RAE</div>
            </div>
          </div>
        </div>
      </div>

      <div className="info-display-footer">
        <div className="info-display-badge">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
            <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
            <path d="M10.97 4.97a.235.235 0 0 0-.02.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05z"/>
          </svg>
          {userType === 'broker' ? 'Broker account verified' :
           userType === 'supplier' ? 'Supplier account verified' :
           'Business account verified'}
        </div>
      </div>
    </div>
  );
};

export default ProfessionalInfoDisplay;
