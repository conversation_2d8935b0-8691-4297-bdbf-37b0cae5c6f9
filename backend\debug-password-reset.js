const jwt = require('jsonwebtoken');
const axios = require('axios');
const dotenv = require('dotenv');
const path = require('path');

// Load environment variables
dotenv.config({
  path: path.resolve(__dirname, '.env.local'),
  override: false,
});

// Test token from the email
const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************.vT6v8flftwP8xU83gGf8I_GFsEoxp1zRRzeZ_Tqr9JI';

// JWT Secret from environment
const jwtSecret = process.env.JWT_SECRET || 'temporary_jwt_secret_for_development';

console.log('=== Password Reset Token Debug ===');
console.log('JWT_SECRET:', jwtSecret);
console.log('Token:', token);
console.log('');

// 1. Decode token without verification
console.log('1. Decoding token (no verification):');
try {
  const decoded = jwt.decode(token);
  console.log('Decoded payload:', JSON.stringify(decoded, null, 2));
  console.log('Current time:', Date.now());
  console.log('Token exp time:', decoded.exp * 1000);
  console.log('Token expired?', Date.now() > decoded.exp * 1000);
} catch (error) {
  console.error('Error decoding token:', error.message);
}

console.log('\n2. Verifying token with JWT secret:');
try {
  const verified = jwt.verify(token, jwtSecret, {
    issuer: 'energy-platform',
    audience: 'password-reset'
  });
  console.log('✅ Token verification successful:', JSON.stringify(verified, null, 2));
} catch (error) {
  console.error('❌ Token verification failed:', error.message);
  console.error('Error details:', error);
}

console.log('\n3. Testing API endpoint:');
async function testAPI() {
  try {
    const response = await axios.post('http://localhost:3000/api/auth/verify-reset-token', { token });
    console.log('✅ API Response:', JSON.stringify(response.data, null, 2));
  } catch (error) {
    console.error('❌ API Error:', error.response ? JSON.stringify(error.response.data, null, 2) : error.message);
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Headers:', error.response.headers);
    }
  }
}

testAPI();
