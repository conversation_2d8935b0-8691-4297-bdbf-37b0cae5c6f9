.dashboard-content {
  width: 100%;
  max-width: 100%;
  padding: 0;
  margin-top: 5px;
}

/* Filter card */
.filter-card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 1.25rem;
  margin-bottom: 1.25rem;
  width: 100%;
}

/* Table card */
.table-card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 1.25rem;
  margin-bottom: 1.25rem;
  overflow: hidden;
  width: 100%;
}

/* Welcome message */
.welcome-message {
  background-color: #f8f9fa;
  border-left: 4px solid #000;
  padding: 1rem 1.25rem;
  margin-bottom: 1.25rem;
  border-radius: 4px;
  color: #333;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  animation: fadeIn 0.5s ease-in-out;
  width: 100%;
}

@media (max-width: 768px) {
  .filter-card,
  .table-card {
    padding: 1rem;
  }
}

@media (max-width: 480px) {
  .filter-card,
  .table-card {
    padding: 0.875rem;
  }
}
