const dotenv = require('dotenv');
const path = require('path');

// Load environment variables
dotenv.config({
  path: path.resolve(__dirname, '.env.uat'),
  override: false,
});

const emailService = require('./services/emailService');
const logger = require('./utils/logger');

async function testEmailNotifications() {
  console.log('🧪 Testing Email Notification System...\n');

  // Test data
  const testUser = {
    email: '<EMAIL>', // Replace with a real email for testing
    firstName: 'John',
    lastName: 'Doe'
  };

  const testAdmin = {
    firstName: 'Admin',
    lastName: 'User',
    email: '<EMAIL>'
  };

  const userName = `${testUser.firstName} ${testUser.lastName}`;
  const adminName = `${testAdmin.firstName} ${testAdmin.lastName}`;

  console.log('📧 Email Configuration:');
  console.log(`FROM_EMAIL: ${process.env.FROM_EMAIL}`);
  console.log(`COMPANY_NAME: ${process.env.COMPANY_NAME}`);
  console.log(`AWS_REGION: ${process.env.AWS_REGION}`);
  console.log(`FRONTEND_URL: ${process.env.FRONTEND_URL}\n`);

  try {
    // Test 1: User Suspended Email
    console.log('1️⃣ Testing User Suspended Email...');
    try {
      await emailService.sendUserSuspendedEmail(testUser.email, userName, adminName);
      console.log('✅ User suspended email sent successfully\n');
    } catch (error) {
      console.error('❌ Failed to send user suspended email:', error.message);
    }

    // Test 2: User Inactive Email
    console.log('2️⃣ Testing User Inactive Email...');
    try {
      await emailService.sendUserInactiveEmail(testUser.email, userName, adminName);
      console.log('✅ User inactive email sent successfully\n');
    } catch (error) {
      console.error('❌ Failed to send user inactive email:', error.message);
    }

    // Test 3: Admin Password Reset Notification Email
    console.log('3️⃣ Testing Admin Password Reset Notification Email...');
    try {
      await emailService.sendAdminPasswordResetNotificationEmail(testUser.email, userName, adminName);
      console.log('✅ Admin password reset notification email sent successfully\n');
    } catch (error) {
      console.error('❌ Failed to send admin password reset notification email:', error.message);
    }

    // Test 4: Check SES Quota
    console.log('4️⃣ Checking AWS SES Quota...');
    try {
      const quota = await emailService.getSendingQuota();
      console.log('✅ SES Quota Information:');
      console.log(`   Max24HourSend: ${quota.Max24HourSend}`);
      console.log(`   MaxSendRate: ${quota.MaxSendRate}`);
      console.log(`   SentLast24Hours: ${quota.SentLast24Hours}\n`);
    } catch (error) {
      console.error('❌ Failed to get SES quota:', error.message);
    }

    console.log('🎉 Email notification testing completed!');
    console.log('\n📝 Notes:');
    console.log('- Make sure <NAME_EMAIL> with a real email address');
    console.log('- Check your email inbox (and spam folder) for the test emails');
    console.log('- Verify that the email templates look professional and contain correct information');
    console.log('- Ensure the FROM_EMAIL address is verified in AWS SES');

  } catch (error) {
    console.error('💥 Unexpected error during testing:', error);
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  testEmailNotifications()
    .then(() => {
      console.log('\n✨ Test completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Test failed:', error);
      process.exit(1);
    });
}

module.exports = { testEmailNotifications };
