import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import DashboardLayout from '../../components/DashboardLayout';
import Spinner from '../../components/Spinner';
import { showSuccessMessage, showErrorMessage } from '../../utils/toastNotifications';
import logger from '../../utils/logger';
import { loadCSS, unloadCSS, ADMIN_CSS, CSS_IDS } from '../../utils/cssLoader';
import api from '../../services/api';

const ManageBrokers = () => {
  const [loading, setLoading] = useState(true);
  const [brokers, setBrokers] = useState([]);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalUsers: 0,
    hasNextPage: false,
    hasPrevPage: false
  });
  const [filters, setFilters] = useState({
    status: 'all',
    verificationStatus: 'all',
    search: '',
    sortBy: 'createdAt',
    sortOrder: 'desc'
  });
  const [selectedBroker, setSelectedBroker] = useState(null);
  const [showBrokerDetails, setShowBrokerDetails] = useState(false);
  const [showAssignClient, setShowAssignClient] = useState(false);
  const [showVerificationModal, setShowVerificationModal] = useState(false);
  const [clients, setClients] = useState([]);
  const navigate = useNavigate();

  // Dynamically load CSS only for this component
  useEffect(() => {
    loadCSS(ADMIN_CSS.MANAGEMENT, CSS_IDS.ADMIN_MANAGEMENT);

    // Cleanup function to remove CSS when component unmounts
    return () => {
      unloadCSS(CSS_IDS.ADMIN_MANAGEMENT);
    };
  }, []);

  useEffect(() => {
    fetchBrokers();
  }, [filters, pagination.currentPage]);

  const fetchBrokers = async () => {
    try {
      setLoading(true);
      logger.info('Fetching brokers with filters:', filters);

      const queryParams = new URLSearchParams({
        page: pagination.currentPage,
        limit: 10,
        userType: 'Broker',
        ...filters
      });

      const response = await api.get(`/api/admin/brokers?${queryParams}`);
      setBrokers(response.data.data.brokers);
      setPagination(response.data.data.pagination);

      logger.info('Brokers fetched successfully');
    } catch (error) {
      logger.error('Error fetching brokers:', error);
      showErrorMessage('BROKERS_LOAD_FAILED', 'Failed to load brokers');
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = (filterName, value) => {
    setFilters(prev => ({
      ...prev,
      [filterName]: value
    }));
    setPagination(prev => ({ ...prev, currentPage: 1 }));
  };

  const handleStatusUpdate = async (brokerId, newStatus) => {
    try {
      await api.patch(`/api/admin/users/${brokerId}/status`, { status: newStatus });
      showSuccessMessage('BROKER_STATUS_UPDATED', 'Broker status updated successfully');
      fetchBrokers(); // Refresh the list
    } catch (error) {
      logger.error('Error updating broker status:', error);
      showErrorMessage('STATUS_UPDATE_FAILED', 'Failed to update broker status');
    }
  };

  const handleVerificationUpdate = async (brokerId, verificationStatus, notes) => {
    try {
      await api.patch(`/api/admin/brokers/${brokerId}/verification`, { verificationStatus, notes });
      showSuccessMessage('BROKER_VERIFICATION_UPDATED', 'Broker verification updated successfully');
      setShowVerificationModal(false);
      fetchBrokers();
    } catch (error) {
      logger.error('Error updating broker verification:', error);
      showErrorMessage('VERIFICATION_UPDATE_FAILED', 'Failed to update broker verification');
    }
  };

  const handleViewBrokerDetails = async (broker) => {
    try {
      setSelectedBroker(broker);
      setShowBrokerDetails(true);
    } catch (error) {
      logger.error('Error viewing broker details:', error);
      showErrorMessage('BROKER_DETAILS_FAILED', 'Failed to load broker details');
    }
  };

  const handleAssignClient = async (brokerId, clientId) => {
    try {
      await api.post(`/api/admin/brokers/${brokerId}/assign-client`, { clientId, assignmentType: 'manual' });
      showSuccessMessage('CLIENT_ASSIGNED', 'Client assigned to broker successfully');
      setShowAssignClient(false);
    } catch (error) {
      logger.error('Error assigning client:', error);
      showErrorMessage('CLIENT_ASSIGNMENT_FAILED', 'Failed to assign client to broker');
    }
  };

  const fetchClients = async () => {
    try {
      const response = await api.get('/api/admin/users?userType=Individual,Professional&status=Active&limit=100');
      setClients(response.data.data.users);
    } catch (error) {
      logger.error('Error fetching clients:', error);
    }
  };

  const handlePageChange = (newPage) => {
    setPagination(prev => ({ ...prev, currentPage: newPage }));
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getStatusBadgeClass = (status) => {
    switch (status?.toLowerCase()) {
      case 'active': return 'status-badge active';
      case 'inactive': return 'status-badge inactive';
      case 'pending': return 'status-badge pending';
      case 'suspended': return 'status-badge suspended';
      default: return 'status-badge';
    }
  };

  if (loading) {
    return <Spinner fullScreen={true} message="Loading brokers..." size="large" />;
  }

  return (
    <DashboardLayout>
      <div className="admin-management-container">
        {/* Header */}
        <div className="management-header">
          <div className="header-content">
            <h1>Manage Brokers</h1>
            <p>View and manage all brokers in the system</p>
          </div>
          <div className="header-stats">
            <div className="stat-item">
              <span className="stat-value">{pagination.totalUsers}</span>
              <span className="stat-label">Total Brokers</span>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="management-filters">
          <div className="filter-group">
            <label>Status:</label>
            <select 
              value={filters.status} 
              onChange={(e) => handleFilterChange('status', e.target.value)}
            >
              <option value="all">All Status</option>
              <option value="Active">Active</option>
              <option value="Inactive">Inactive</option>
              <option value="Pending">Pending</option>
              <option value="Suspended">Suspended</option>
            </select>
          </div>

          <div className="filter-group">
            <label>Verification:</label>
            <select 
              value={filters.verificationStatus} 
              onChange={(e) => handleFilterChange('verificationStatus', e.target.value)}
            >
              <option value="all">All Verification</option>
              <option value="Verified">Verified</option>
              <option value="Pending">Pending</option>
              <option value="Rejected">Rejected</option>
            </select>
          </div>

          <div className="filter-group search-group">
            <label>Search:</label>
            <input
              type="text"
              placeholder="Search by email or name..."
              value={filters.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
            />
          </div>
        </div>

        {/* Brokers Table */}
        <div className="management-table-container">
          <table className="management-table">
            <thead>
              <tr>
                <th>Broker</th>
                <th>License & Performance</th>
                <th>Status</th>
                <th>Verification</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {brokers.map(broker => (
                <tr key={broker._id}>
                  <td>
                    <div className="user-info">
                      <div className="user-icon">
                        <i className="fas fa-handshake"></i>
                      </div>
                      <div className="user-details">
                        <div className="user-name">
                          {broker.firstName} {broker.lastName}
                        </div>
                        <div className="user-email">{broker.email}</div>
                        <div className="user-meta">
                          Joined {formatDate(broker.createdAt)}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td>
                    <div className="broker-performance">
                      <div className="license-info">
                        <span className="license-label">License:</span>
                        <span className="license-number">
                          {broker.profile?.licenseNumber || 'Not provided'}
                        </span>
                      </div>
                      <div className="kpi-summary">
                        <div className="kpi-item">
                          <span className="kpi-value">{broker.kpis?.totalContracts || 0}</span>
                          <span className="kpi-label">Contracts</span>
                        </div>
                        <div className="kpi-item">
                          <span className="kpi-value">{broker.kpis?.conversionRate || 0}%</span>
                          <span className="kpi-label">Conversion</span>
                        </div>
                        <div className="kpi-item">
                          <span className="kpi-value">€{(broker.kpis?.totalCommissions || 0).toLocaleString()}</span>
                          <span className="kpi-label">Commissions</span>
                        </div>
                      </div>
                    </div>
                  </td>
                  <td>
                    <span className={getStatusBadgeClass(broker.status)}>
                      {broker.status || 'Unknown'}
                    </span>
                  </td>
                  <td>
                    <span className={getStatusBadgeClass(broker.verificationStatus)}>
                      {broker.verificationStatus || 'Unknown'}
                    </span>
                  </td>
                  <td>
                    <div className="action-buttons">
                      <button
                        className="action-btn view"
                        onClick={() => handleViewBrokerDetails(broker)}
                        title="View Details"
                      >
                        <i className="fas fa-eye"></i>
                      </button>
                      <button
                        className="action-btn edit"
                        onClick={() => {
                          setSelectedBroker(broker);
                          setShowVerificationModal(true);
                        }}
                        title="Update Verification"
                      >
                        <i className="fas fa-certificate"></i>
                      </button>
                      <button
                        className="action-btn assign"
                        onClick={() => {
                          setSelectedBroker(broker);
                          setShowAssignClient(true);
                          fetchClients();
                        }}
                        title="Assign Client"
                      >
                        <i className="fas fa-user-plus"></i>
                      </button>
                      <select
                        value={broker.status || 'Active'}
                        onChange={(e) => handleStatusUpdate(broker._id, e.target.value)}
                        className="status-select"
                      >
                        <option value="Active">Active</option>
                        <option value="Inactive">Inactive</option>
                        <option value="Pending">Pending</option>
                        <option value="Suspended">Suspended</option>
                      </select>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>

          {brokers.length === 0 && (
            <div className="empty-state">
              <i className="fas fa-handshake"></i>
              <h3>No brokers found</h3>
              <p>No brokers match your current filters.</p>
            </div>
          )}
        </div>

        {/* Pagination */}
        <div className="management-pagination">
          <button 
            disabled={!pagination.hasPrevPage}
            onClick={() => handlePageChange(pagination.currentPage - 1)}
            className="pagination-btn"
          >
            <i className="fas fa-chevron-left"></i>
            Previous
          </button>
          
          <span className="pagination-info">
            Page {pagination.currentPage} of {pagination.totalPages}
          </span>
          
          <button 
            disabled={!pagination.hasNextPage}
            onClick={() => handlePageChange(pagination.currentPage + 1)}
            className="pagination-btn"
          >
            Next
            <i className="fas fa-chevron-right"></i>
          </button>
        </div>

        {/* Broker Details Modal */}
        {showBrokerDetails && selectedBroker && (
          <div className="modal-overlay" onClick={() => setShowBrokerDetails(false)}>
            <div className="modal-content large" onClick={(e) => e.stopPropagation()}>
              <div className="modal-header">
                <h3>Broker Details - {selectedBroker.firstName} {selectedBroker.lastName}</h3>
                <button
                  className="modal-close"
                  onClick={() => setShowBrokerDetails(false)}
                >
                  <i className="fas fa-times"></i>
                </button>
              </div>
              <div className="modal-body">
                <div className="broker-details-content">
                  <div className="details-section">
                    <h4>Basic Information</h4>
                    <div className="details-grid">
                      <div className="detail-item">
                        <label>Email:</label>
                        <span>{selectedBroker.email}</span>
                      </div>
                      <div className="detail-item">
                        <label>Phone:</label>
                        <span>{selectedBroker.phone || 'Not provided'}</span>
                      </div>
                      <div className="detail-item">
                        <label>Company:</label>
                        <span>{selectedBroker.profile?.companyName || 'Not provided'}</span>
                      </div>
                      <div className="detail-item">
                        <label>License Number:</label>
                        <span>{selectedBroker.profile?.licenseNumber || 'Not provided'}</span>
                      </div>
                    </div>
                  </div>

                  <div className="details-section">
                    <h4>Performance KPIs</h4>
                    <div className="kpi-grid">
                      <div className="kpi-card">
                        <span className="kpi-value">{selectedBroker.kpis?.totalQuotes || 0}</span>
                        <span className="kpi-label">Total Quotes</span>
                      </div>
                      <div className="kpi-card">
                        <span className="kpi-value">{selectedBroker.kpis?.totalContracts || 0}</span>
                        <span className="kpi-label">Total Contracts</span>
                      </div>
                      <div className="kpi-card">
                        <span className="kpi-value">{selectedBroker.kpis?.conversionRate || 0}%</span>
                        <span className="kpi-label">Conversion Rate</span>
                      </div>
                      <div className="kpi-card">
                        <span className="kpi-value">€{(selectedBroker.kpis?.totalCommissions || 0).toLocaleString()}</span>
                        <span className="kpi-label">Total Commissions</span>
                      </div>
                      <div className="kpi-card">
                        <span className="kpi-value">{selectedBroker.kpis?.activeClients || 0}</span>
                        <span className="kpi-label">Active Clients</span>
                      </div>
                      <div className="kpi-card">
                        <span className="kpi-value">{selectedBroker.kpis?.monthlyContracts || 0}</span>
                        <span className="kpi-label">Monthly Contracts</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Verification Modal */}
        {showVerificationModal && selectedBroker && (
          <div className="modal-overlay" onClick={() => setShowVerificationModal(false)}>
            <div className="modal-content" onClick={(e) => e.stopPropagation()}>
              <div className="modal-header">
                <h3>Update Broker Verification</h3>
                <button
                  className="modal-close"
                  onClick={() => setShowVerificationModal(false)}
                >
                  <i className="fas fa-times"></i>
                </button>
              </div>
              <div className="modal-body">
                <form onSubmit={(e) => {
                  e.preventDefault();
                  const formData = new FormData(e.target);
                  handleVerificationUpdate(
                    selectedBroker._id,
                    formData.get('verificationStatus'),
                    formData.get('notes')
                  );
                }}>
                  <div className="form-group">
                    <label>Verification Status:</label>
                    <select name="verificationStatus" required>
                      <option value="Verified">Verified</option>
                      <option value="Rejected">Rejected</option>
                    </select>
                  </div>
                  <div className="form-group">
                    <label>Notes:</label>
                    <textarea
                      name="notes"
                      rows="4"
                      placeholder="Add verification notes..."
                    ></textarea>
                  </div>
                  <div className="modal-actions">
                    <button
                      type="button"
                      className="btn-cancel"
                      onClick={() => setShowVerificationModal(false)}
                    >
                      Cancel
                    </button>
                    <button type="submit" className="btn-confirm">
                      Update Verification
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        )}

        {/* Assign Client Modal */}
        {showAssignClient && selectedBroker && (
          <div className="modal-overlay" onClick={() => setShowAssignClient(false)}>
            <div className="modal-content" onClick={(e) => e.stopPropagation()}>
              <div className="modal-header">
                <h3>Assign Client to Broker</h3>
                <button
                  className="modal-close"
                  onClick={() => setShowAssignClient(false)}
                >
                  <i className="fas fa-times"></i>
                </button>
              </div>
              <div className="modal-body">
                <form onSubmit={(e) => {
                  e.preventDefault();
                  const formData = new FormData(e.target);
                  handleAssignClient(selectedBroker._id, formData.get('clientId'));
                }}>
                  <div className="form-group">
                    <label>Select Client:</label>
                    <select name="clientId" required>
                      <option value="">Choose a client...</option>
                      {clients.map(client => (
                        <option key={client._id} value={client._id}>
                          {client.firstName} {client.lastName} ({client.email})
                        </option>
                      ))}
                    </select>
                  </div>
                  <div className="modal-actions">
                    <button
                      type="button"
                      className="btn-cancel"
                      onClick={() => setShowAssignClient(false)}
                    >
                      Cancel
                    </button>
                    <button type="submit" className="btn-confirm">
                      Assign Client
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        )}
      </div>
    </DashboardLayout>
  );
};

export default ManageBrokers;
