import React, { useEffect } from 'react';
import { Link } from 'react-router-dom';
import logoImage from '../assets/logo.jpeg';
import HomeHeader from '../components/HomeHeader';
import Footer from '../components/Footer';
import { clearAll } from '../utils/localStorage';
import { useForceScrollToTop } from '../utils/scrollToTop';
import '../styles/app.css';
import '../styles/pages.css';
import '../styles/home.css';

const HomePage = () => {
  // Clear localStorage when component mounts
  useEffect(() => {
    // Force scroll to top when page loads (aggressive)
    useForceScrollToTop();

    // Clear all localStorage data when visiting the homepage
    console.log('HomePage: Clearing all localStorage data');
    clearAll();

    // NUCLEAR OPTION: Force black buttons with maximum override
    const forceBlackButtons = () => {
      // Remove any existing override styles first
      const existingStyles = document.querySelectorAll('#homepage-button-override, #homepage-button-critical');
      existingStyles.forEach(style => style.remove());

      // Create new style with maximum specificity
      const style = document.createElement('style');
      style.id = 'homepage-button-nuclear';
      style.textContent = `
        /* NUCLEAR BUTTON OVERRIDE - MAXIMUM SPECIFICITY */
        html body div#root div.home-container .btn-primary,
        html body div#root div.home-container .btn.btn-primary,
        html body div#root div.home-container a.btn-primary,
        html body div#root div.home-container a.btn.btn-primary,
        html body div#root .hero-cta .btn-primary,
        html body div#root .hero-cta .btn.btn-primary,
        html body div#root .hero-cta a.btn-primary,
        html body div#root .hero-cta a.btn.btn-primary,
        html body div#root .cta-section .btn-primary,
        html body div#root .cta-section .btn.btn-primary,
        html body div#root .cta-section a.btn-primary,
        html body div#root .cta-section a.btn.btn-primary,
        html body div#root .home-auth-buttons .btn-primary,
        html body div#root .home-auth-buttons .btn.btn-primary,
        html body div#root .home-auth-buttons a.btn-primary,
        html body div#root .home-auth-buttons a.btn.btn-primary {
          background-color: #000000 !important;
          background-image: none !important;
          background: #000000 !important;
          color: #ffffff !important;
          border: 2px solid #000000 !important;
          border-color: #000000 !important;
          box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15) !important;
        }

        html body div#root div.home-container .btn-primary:hover,
        html body div#root div.home-container .btn.btn-primary:hover,
        html body div#root div.home-container a.btn-primary:hover,
        html body div#root div.home-container a.btn.btn-primary:hover,
        html body div#root .hero-cta .btn-primary:hover,
        html body div#root .hero-cta .btn.btn-primary:hover,
        html body div#root .hero-cta a.btn-primary:hover,
        html body div#root .hero-cta a.btn.btn-primary:hover,
        html body div#root .cta-section .btn-primary:hover,
        html body div#root .cta-section .btn.btn-primary:hover,
        html body div#root .cta-section a.btn-primary:hover,
        html body div#root .cta-section a.btn.btn-primary:hover,
        html body div#root .home-auth-buttons .btn-primary:hover,
        html body div#root .home-auth-buttons .btn.btn-primary:hover,
        html body div#root .home-auth-buttons a.btn-primary:hover,
        html body div#root .home-auth-buttons a.btn.btn-primary:hover {
          background-color: #333333 !important;
          background-image: none !important;
          background: #333333 !important;
          color: #ffffff !important;
          border-color: #333333 !important;
          border: 2px solid #333333 !important;
          transform: translateY(-2px) !important;
          box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2) !important;
        }
      `;
      document.head.appendChild(style);

      // Force styles with JavaScript - NUCLEAR APPROACH
      const forceButtonStyles = () => {
        const selectors = [
          '.home-container .btn-primary',
          '.hero-cta .btn-primary',
          '.home-auth-buttons .btn-primary',
          '.cta-section .btn-primary',
          'a.btn-primary',
          '.btn.btn-primary'
        ];

        selectors.forEach(selector => {
          const buttons = document.querySelectorAll(selector);
          buttons.forEach(button => {
            // Remove all existing classes that might cause blue color
            button.classList.remove('btn-info', 'btn-primary-blue', 'btn-blue');

            // Force black styles with maximum priority
            button.style.setProperty('background-color', '#000000', 'important');
            button.style.setProperty('background-image', 'none', 'important');
            button.style.setProperty('background', '#000000', 'important');
            button.style.setProperty('color', '#ffffff', 'important');
            button.style.setProperty('border', '2px solid #000000', 'important');
            button.style.setProperty('border-color', '#000000', 'important');
            button.style.setProperty('box-shadow', '0 4px 15px rgba(0, 0, 0, 0.15)', 'important');

            console.log('NUCLEAR: Forced black style on button:', button, selector);
          });
        });
      };

      // Apply styles multiple times to ensure they stick
      forceButtonStyles();
      setTimeout(forceButtonStyles, 50);
      setTimeout(forceButtonStyles, 100);
      setTimeout(forceButtonStyles, 200);
      setTimeout(forceButtonStyles, 500);
      setTimeout(forceButtonStyles, 1000);

      return style;
    };

    const style = forceBlackButtons();
    console.log('HomePage: NUCLEAR button override applied');

    // Cleanup function
    return () => {
      if (style && style.parentNode) {
        style.parentNode.removeChild(style);
        console.log('HomePage: Removed nuclear button styles');
      }
    };
  }, []);

  return (
    <div className="home-container">
      {/* Header */}
      <HomeHeader />

      {/* Hero Section */}
      <section className="hero-section">
        <div className="hero-content">
          <h1 className="hero-title" style={{ whiteSpace: 'nowrap' }}>Welcome to My Energy Bill</h1>
          <p className="hero-subtitle">
            Your trusted platform to compare and optimize your energy contracts — for individuals and businesses.
          </p>
          <div className="hero-intro">
            <p>
              My Energy Bill is the easiest way to upload your electricity or gas bill and receive customized offers — independently selected by certified experts.
              We help you compare transparently, reduce your costs, and switch contracts with zero hassle.
              Everything is 100% digital, free, and guided by a dedicated advisor.
            </p>
          </div>
          <div className="hero-cta">
            <Link
              to="/signup"
              className="btn btn-black btn-large"
              style={{
                backgroundColor: '#000000',
                color: '#ffffff',
                border: '2px solid #000000',
                backgroundImage: 'none',
                padding: '1rem 2rem',
                borderRadius: '6px',
                textDecoration: 'none',
                display: 'inline-flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontWeight: '700',
                letterSpacing: '1px',
                transition: 'all 0.3s ease',
                marginRight: '1rem',
                marginBottom: '0.5rem'
              }}
            >
              Upload my first bill
            </Link>
            <Link
              to="/how-it-works"
              className="btn btn-outline btn-large"
              style={{
                backgroundColor: 'transparent',
                color: '#000000',
                border: '2px solid #000000',
                backgroundImage: 'none',
                padding: '1rem 2rem',
                borderRadius: '6px',
                textDecoration: 'none',
                display: 'inline-flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontWeight: '700',
                letterSpacing: '1px',
                transition: 'all 0.3s ease',
                marginRight: '1rem',
                marginBottom: '0.5rem'
              }}
            >
              See how it works
            </Link>
            <Link
              to="/contact"
              className="btn btn-outline btn-large"
              style={{
                backgroundColor: 'transparent',
                color: '#000000',
                border: '2px solid #000000',
                backgroundImage: 'none',
                padding: '1rem 2rem',
                borderRadius: '6px',
                textDecoration: 'none',
                display: 'inline-flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontWeight: '700',
                letterSpacing: '1px',
                transition: 'all 0.3s ease',
                marginBottom: '0.5rem'
              }}
            >
              Contact us
            </Link>
          </div>
        </div>
        <div className="hero-image">
          <div className="hero-image-placeholder">
            <svg xmlns="http://www.w3.org/2000/svg" width="240" height="240" fill="currentColor" viewBox="0 0 16 16">
              <path d="M3.5 6.5A.5.5 0 0 1 4 7v1a4 4 0 0 0 8 0V7a.5.5 0 0 1 1 0v1a5 5 0 0 1-4.5 4.975V15h3a.5.5 0 0 1 0 1h-7a.5.5 0 0 1 0-1h3v-2.025A5 5 0 0 1 3 8V7a.5.5 0 0 1 .5-.5z"/>
              <path d="M10 8a2 2 0 1 1-4 0V3a2 2 0 1 1 4 0v5zM8 0a3 3 0 0 0-3 3v5a3 3 0 0 0 6 0V3a3 3 0 0 0-3-3z"/>
            </svg>
            <div className="energy-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="120" height="120" fill="currentColor" viewBox="0 0 16 16">
                <path d="M11.251.068a.5.5 0 0 1 .227.58L9.677 6.5H13a.5.5 0 0 1 .364.843l-8 8.5a.5.5 0 0 1-.842-.49L6.323 9.5H3a.5.5 0 0 1-.364-.843l8-8.5a.5.5 0 0 1 .615-.09z"/>
              </svg>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="features-section" id="features">
        <h2 className="section-title">Why Choose MY ENERGY BILL?</h2>
        <div className="features-grid">
          <div className="feature-card">
            <div className="feature-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" fill="currentColor" viewBox="0 0 16 16">
                <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
              </svg>
            </div>
            <h3 className="feature-title">Save Money</h3>
            <p className="feature-description">
              Compare offers from multiple suppliers to find the best rates for your electricity and gas needs.
            </p>
          </div>
          <div className="feature-card">
            <div className="feature-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" fill="currentColor" viewBox="0 0 16 16">
                <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
                <path d="M10.97 4.97a.235.235 0 0 0-.02.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05z"/>
              </svg>
            </div>
            <h3 className="feature-title">Easy Process</h3>
            <p className="feature-description">
              Simply upload your current energy bill and receive personalized offers within 24 hours.
            </p>
          </div>
          <div className="feature-card">
            <div className="feature-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" fill="currentColor" viewBox="0 0 16 16">
                <path d="M8 1a5 5 0 0 0-5 5v1h1a1 1 0 0 1 1 1v3a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V6a6 6 0 1 1 12 0v6a2.5 2.5 0 0 1-2.5 2.5H9.366a1 1 0 0 1-.866.5h-1a1 1 0 1 1 0-2h1a1 1 0 0 1 .866.5H11.5A1.5 1.5 0 0 0 13 12h-1a1 1 0 0 1-1-1V8a1 1 0 0 1 1-1h1V6a5 5 0 0 0-5-5z"/>
              </svg>
            </div>
            <h3 className="feature-title">Expert Support</h3>
            <p className="feature-description">
              Our energy experts are available to help you understand your options and make the best choice.
            </p>
          </div>
          <div className="feature-card">
            <div className="feature-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" fill="currentColor" viewBox="0 0 16 16">
                <path d="M2 2a2 2 0 0 0-2 2v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2H2zm6 2.5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 0 1h-3a.5.5 0 0 1-.5-.5zM2 5.5a.5.5 0 0 1 .5-.5h6a.5.5 0 0 1 0 1h-6a.5.5 0 0 1-.5-.5zm0 2a.5.5 0 0 1 .5-.5h6a.5.5 0 0 1 0 1h-6a.5.5 0 0 1-.5-.5zm0 2a.5.5 0 0 1 .5-.5h6a.5.5 0 0 1 0 1h-6a.5.5 0 0 1-.5-.5zm8-4a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 0 1h-1a.5.5 0 0 1-.5-.5zm0 2a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 0 1h-1a.5.5 0 0 1-.5-.5zm0 2a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 0 1h-1a.5.5 0 0 1-.5-.5z"/>
              </svg>
            </div>
            <h3 className="feature-title">Transparent Offers</h3>
            <p className="feature-description">
              All offers are presented clearly with no hidden fees or complicated terms.
            </p>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="how-it-works-section" id="how-it-works">
        <h2 className="section-title">How It Works</h2>
        <div className="steps-container">
          <div className="step-card">
            <div
              className="step-number"
              style={{
                backgroundColor: '#000000',
                color: '#ffffff',
                border: '2px solid #000000',
                backgroundImage: 'none'
              }}
            >
              1
            </div>
            <h3 className="step-title">Create an Account</h3>
            <p className="step-description">
              Sign up for a free account as an individual or business customer.
            </p>
          </div>
          <div className="step-card">
            <div
              className="step-number"
              style={{
                backgroundColor: '#000000',
                color: '#ffffff',
                border: '2px solid #000000',
                backgroundImage: 'none'
              }}
            >
              2
            </div>
            <h3 className="step-title">Upload Your Bill</h3>
            <p className="step-description">
              Upload your current energy bill so we can analyze your usage patterns.
            </p>
          </div>
          <div className="step-card">
            <div
              className="step-number"
              style={{
                backgroundColor: '#000000',
                color: '#ffffff',
                border: '2px solid #000000',
                backgroundImage: 'none'
              }}
            >
              3
            </div>
            <h3 className="step-title">Receive Offers</h3>
            <p className="step-description">
              Get personalized offers from top energy suppliers within 24 hours.
            </p>
          </div>
          <div className="step-card">
            <div
              className="step-number"
              style={{
                backgroundColor: '#000000',
                color: '#ffffff',
                border: '2px solid #000000',
                backgroundImage: 'none'
              }}
            >
              4
            </div>
            <h3 className="step-title">Choose & Save</h3>
            <p className="step-description">
              Select the best offer and start saving on your energy bills.
            </p>
          </div>
        </div>
      </section>


      {/* CTA Section */}
      <section className="cta-section">
        <div className="cta-content">
          <h2 className="cta-title">Ready to Optimize Your Energy Bills?</h2>
          <p className="cta-description">
            Upload your bill today and discover how much you can save with personalized offers from certified energy experts.
          </p>
          <Link
            to="/signup"
            className="btn btn-black btn-large"
            style={{
              backgroundColor: '#000000',
              color: '#ffffff',
              border: '2px solid #000000',
              backgroundImage: 'none',
              padding: '1rem 2rem',
              borderRadius: '6px',
              textDecoration: 'none',
              display: 'inline-flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontWeight: '700',
              letterSpacing: '1px',
              transition: 'all 0.3s ease'
            }}
          >
            Upload My Bill
          </Link>
        </div>
      </section>

      {/* Footer */}
      <Footer />
    </div>
  );
};

export default HomePage;
