import React, { useEffect } from 'react';
import { Link } from 'react-router-dom';
import logoImage from '../assets/logo.jpeg';
import HomeHeader from '../components/HomeHeader';
import Footer from '../components/Footer';
import { clearAll } from '../utils/localStorage';
import { useForceScrollToTop } from '../utils/scrollToTop';
import '../styles/app.css';
import '../styles/pages.css';
import '../styles/home.css';

const HomePage = () => {
  // Clear localStorage when component mounts
  useEffect(() => {
    // Force scroll to top when page loads (aggressive)
    useForceScrollToTop();

    // Clear all localStorage data when visiting the homepage
    console.log('HomePage: Clearing all localStorage data');
    clearAll();

    // NUCLEAR OPTION: Force black buttons with maximum override
    const forceBlackButtons = () => {
      // Remove any existing override styles first
      const existingStyles = document.querySelectorAll('#homepage-button-override, #homepage-button-critical');
      existingStyles.forEach(style => style.remove());

      // Create new style with maximum specificity
      const style = document.createElement('style');
      style.id = 'homepage-button-nuclear';
      style.textContent = `
        /* NUCLEAR BUTTON OVERRIDE - MAXIMUM SPECIFICITY */
        html body div#root div.home-container .btn-primary,
        html body div#root div.home-container .btn.btn-primary,
        html body div#root div.home-container a.btn-primary,
        html body div#root div.home-container a.btn.btn-primary,
        html body div#root .hero-cta .btn-primary,
        html body div#root .hero-cta .btn.btn-primary,
        html body div#root .hero-cta a.btn-primary,
        html body div#root .hero-cta a.btn.btn-primary,
        html body div#root .cta-section .btn-primary,
        html body div#root .cta-section .btn.btn-primary,
        html body div#root .cta-section a.btn-primary,
        html body div#root .cta-section a.btn.btn-primary,
        html body div#root .home-auth-buttons .btn-primary,
        html body div#root .home-auth-buttons .btn.btn-primary,
        html body div#root .home-auth-buttons a.btn-primary,
        html body div#root .home-auth-buttons a.btn.btn-primary {
          background-color: #000000 !important;
          background-image: none !important;
          background: #000000 !important;
          color: #ffffff !important;
          border: 2px solid #000000 !important;
          border-color: #000000 !important;
          box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15) !important;
        }

        html body div#root div.home-container .btn-primary:hover,
        html body div#root div.home-container .btn.btn-primary:hover,
        html body div#root div.home-container a.btn-primary:hover,
        html body div#root div.home-container a.btn.btn-primary:hover,
        html body div#root .hero-cta .btn-primary:hover,
        html body div#root .hero-cta .btn.btn-primary:hover,
        html body div#root .hero-cta a.btn-primary:hover,
        html body div#root .hero-cta a.btn.btn-primary:hover,
        html body div#root .cta-section .btn-primary:hover,
        html body div#root .cta-section .btn.btn-primary:hover,
        html body div#root .cta-section a.btn-primary:hover,
        html body div#root .cta-section a.btn.btn-primary:hover,
        html body div#root .home-auth-buttons .btn-primary:hover,
        html body div#root .home-auth-buttons .btn.btn-primary:hover,
        html body div#root .home-auth-buttons a.btn-primary:hover,
        html body div#root .home-auth-buttons a.btn.btn-primary:hover {
          background-color: #333333 !important;
          background-image: none !important;
          background: #333333 !important;
          color: #ffffff !important;
          border-color: #333333 !important;
          border: 2px solid #333333 !important;
          transform: translateY(-2px) !important;
          box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2) !important;
        }
      `;
      document.head.appendChild(style);

      // Force styles with JavaScript - NUCLEAR APPROACH
      const forceButtonStyles = () => {
        const selectors = [
          '.home-container .btn-primary',
          '.hero-cta .btn-primary',
          '.home-auth-buttons .btn-primary',
          '.cta-section .btn-primary',
          'a.btn-primary',
          '.btn.btn-primary'
        ];

        selectors.forEach(selector => {
          const buttons = document.querySelectorAll(selector);
          buttons.forEach(button => {
            // Remove all existing classes that might cause blue color
            button.classList.remove('btn-info', 'btn-primary-blue', 'btn-blue');

            // Force black styles with maximum priority
            button.style.setProperty('background-color', '#000000', 'important');
            button.style.setProperty('background-image', 'none', 'important');
            button.style.setProperty('background', '#000000', 'important');
            button.style.setProperty('color', '#ffffff', 'important');
            button.style.setProperty('border', '2px solid #000000', 'important');
            button.style.setProperty('border-color', '#000000', 'important');
            button.style.setProperty('box-shadow', '0 4px 15px rgba(0, 0, 0, 0.15)', 'important');

            console.log('NUCLEAR: Forced black style on button:', button, selector);
          });
        });
      };

      // Apply styles multiple times to ensure they stick
      forceButtonStyles();
      setTimeout(forceButtonStyles, 50);
      setTimeout(forceButtonStyles, 100);
      setTimeout(forceButtonStyles, 200);
      setTimeout(forceButtonStyles, 500);
      setTimeout(forceButtonStyles, 1000);

      return style;
    };

    const style = forceBlackButtons();
    console.log('HomePage: NUCLEAR button override applied');

    // Cleanup function
    return () => {
      if (style && style.parentNode) {
        style.parentNode.removeChild(style);
        console.log('HomePage: Removed nuclear button styles');
      }
    };
  }, []);

  return (
    <div className="home-container">
      {/* Header */}
      <HomeHeader />

      {/* Hero Section */}
      <section className="hero-section">
        <div className="hero-content">
          <h1 className="hero-title">Welcome to My Energy Bill</h1>
          <p className="hero-subtitle">
            Your trusted platform to compare and optimize your energy contracts — for individuals and businesses.
          </p>
          <div className="hero-intro">
            <p>
              My Energy Bill is the easiest way to upload your electricity or gas bill and receive customized offers — independently selected by certified experts.
              We help you compare transparently, reduce your costs, and switch contracts with zero hassle.
              Everything is 100% digital, free, and guided by a dedicated advisor.
            </p>
          </div>
          <div className="hero-cta">
            <Link
              to="/signup"
              className="btn btn-black btn-large"
              style={{
                backgroundColor: '#000000',
                color: '#ffffff',
                border: '2px solid #000000',
                backgroundImage: 'none',
                padding: '1rem 2rem',
                borderRadius: '6px',
                textDecoration: 'none',
                display: 'inline-flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontWeight: '700',
                letterSpacing: '1px',
                transition: 'all 0.3s ease',
                marginRight: '1rem',
                marginBottom: '0.5rem'
              }}
            >
              Upload my first bill
            </Link>
            <Link
              to="/how-it-works"
              className="btn btn-outline btn-large"
              style={{
                backgroundColor: 'transparent',
                color: '#000000',
                border: '2px solid #000000',
                backgroundImage: 'none',
                padding: '1rem 2rem',
                borderRadius: '6px',
                textDecoration: 'none',
                display: 'inline-flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontWeight: '700',
                letterSpacing: '1px',
                transition: 'all 0.3s ease',
                marginRight: '1rem',
                marginBottom: '0.5rem'
              }}
            >
              See how it works
            </Link>
            <Link
              to="/contact"
              className="btn btn-outline btn-large"
              style={{
                backgroundColor: 'transparent',
                color: '#000000',
                border: '2px solid #000000',
                backgroundImage: 'none',
                padding: '1rem 2rem',
                borderRadius: '6px',
                textDecoration: 'none',
                display: 'inline-flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontWeight: '700',
                letterSpacing: '1px',
                transition: 'all 0.3s ease',
                marginBottom: '0.5rem'
              }}
            >
              Contact us
            </Link>
          </div>
        </div>
        <div className="hero-image">
          <div className="hero-image-placeholder">
            <svg xmlns="http://www.w3.org/2000/svg" width="240" height="240" fill="currentColor" viewBox="0 0 16 16">
              <path d="M3.5 6.5A.5.5 0 0 1 4 7v1a4 4 0 0 0 8 0V7a.5.5 0 0 1 1 0v1a5 5 0 0 1-4.5 4.975V15h3a.5.5 0 0 1 0 1h-7a.5.5 0 0 1 0-1h3v-2.025A5 5 0 0 1 3 8V7a.5.5 0 0 1 .5-.5z"/>
              <path d="M10 8a2 2 0 1 1-4 0V3a2 2 0 1 1 4 0v5zM8 0a3 3 0 0 0-3 3v5a3 3 0 0 0 6 0V3a3 3 0 0 0-3-3z"/>
            </svg>
            <div className="energy-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="120" height="120" fill="currentColor" viewBox="0 0 16 16">
                <path d="M11.251.068a.5.5 0 0 1 .227.58L9.677 6.5H13a.5.5 0 0 1 .364.843l-8 8.5a.5.5 0 0 1-.842-.49L6.323 9.5H3a.5.5 0 0 1-.364-.843l8-8.5a.5.5 0 0 1 .615-.09z"/>
              </svg>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="features-section" id="features">
        <h2 className="section-title">Why Choose MY ENERGY BILL?</h2>
        <div className="features-list">
          <div className="feature-item">
            <div className="feature-icon-large">
              <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" fill="currentColor" viewBox="0 0 16 16">
                <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
                <path d="M10.97 4.97a.235.235 0 0 0-.02.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05z"/>
              </svg>
            </div>
            <div className="feature-content">
              <h3 className="feature-title-large">Transparent and stress-free process</h3>
              <p className="feature-description-large">
                Our platform is designed to make energy contract comparison simple, honest, and 100% digital — with no hidden fees or small print.
              </p>
            </div>
          </div>

          <div className="feature-item">
            <div className="feature-icon-large">
              <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" fill="currentColor" viewBox="0 0 16 16">
                <path d="M8 1a2.5 2.5 0 0 1 2.5 2.5V4h-5v-.5A2.5 2.5 0 0 1 8 1zm3.5 3v-.5a3.5 3.5 0 1 0-7 0V4H1v10a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V4h-3.5zM2 5h12v9a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V5z"/>
              </svg>
            </div>
            <div className="feature-content">
              <h3 className="feature-title-large">Truly personalized service</h3>
              <p className="feature-description-large">
                We take the time to understand your energy needs and constraints. Each offer you receive is reviewed and selected by an expert — not just a machine.
              </p>
            </div>
          </div>

          <div className="feature-item">
            <div className="feature-icon-large">
              <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" fill="currentColor" viewBox="0 0 16 16">
                <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
              </svg>
            </div>
            <div className="feature-content">
              <h3 className="feature-title-large">No cost to you</h3>
              <p className="feature-description-large">
                Our services are completely free for clients. You get professional support without paying anything upfront or afterwards.
              </p>
            </div>
          </div>

          <div className="feature-item">
            <div className="feature-icon-large">
              <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" fill="currentColor" viewBox="0 0 16 16">
                <path d="M8.93 6.588l-2.29.287-.082.38.45.083c.294.07.352.176.288.469l-.738 3.468c-.194.897.105 1.319.808 1.319.545 0 1.178-.252 1.465-.598l.088-.416c-.2.176-.492.246-.686.246-.275 0-.375-.193-.304-.533L8.93 6.588zM9 4.5a1 1 0 1 1-2 0 1 1 0 0 1 2 0z"/>
              </svg>
            </div>
            <div className="feature-content">
              <h3 className="feature-title-large">Human guidance every step of the way</h3>
              <p className="feature-description-large">
                From your first upload to your final signature, a dedicated advisor is always available to assist, clarify, and follow up.
              </p>
            </div>
          </div>

          <div className="feature-item">
            <div className="feature-icon-large">
              <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" fill="currentColor" viewBox="0 0 16 16">
                <path d="M7 14s-1 0-1-1 1-4 5-4 5 3 5 4-1 1-1 1H7zm4-6a3 3 0 1 0 0-6 3 3 0 0 0 0 6z"/>
                <path fillRule="evenodd" d="M5.216 14A2.238 2.238 0 0 1 5 13c0-1.355.68-2.75 1.936-3.72A6.325 6.325 0 0 0 5 9c-4 0-5 3-5 4s1 1 1 1h4.216z"/>
                <path d="M4.5 8a2.5 2.5 0 1 0 0-5 2.5 2.5 0 0 0 0 5z"/>
              </svg>
            </div>
            <div className="feature-content">
              <h3 className="feature-title-large">For individuals and businesses</h3>
              <p className="feature-description-large">
                Whether you're managing a household or running a company, we help you secure the best energy deal with full independence from suppliers.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="how-it-works-section" id="how-it-works">
        <h2 className="section-title">How It Works</h2>
        <div className="steps-container">
          <div className="step-card">
            <div
              className="step-number"
              style={{
                backgroundColor: '#000000',
                color: '#ffffff',
                border: '2px solid #000000',
                backgroundImage: 'none'
              }}
            >
              1
            </div>
            <h3 className="step-title">Upload your bill</h3>
            <p className="step-description">
              Send us a PDF or a photo of your electricity or gas invoice — residential or business.
            </p>
          </div>
          <div className="step-card">
            <div
              className="step-number"
              style={{
                backgroundColor: '#000000',
                color: '#ffffff',
                border: '2px solid #000000',
                backgroundImage: 'none'
              }}
            >
              2
            </div>
            <h3 className="step-title">Expert analysis</h3>
            <p className="step-description">
              Our energy experts and certified partners analyze your bill and compare dozens of market offers independently.
            </p>
          </div>
          <div className="step-card">
            <div
              className="step-number"
              style={{
                backgroundColor: '#000000',
                color: '#ffffff',
                border: '2px solid #000000',
                backgroundImage: 'none'
              }}
            >
              3
            </div>
            <h3 className="step-title">Receive your tailored offers</h3>
            <p className="step-description">
              You receive clear, customized proposals that match your energy usage, pricing structure, and needs.
            </p>
          </div>
          <div className="step-card">
            <div
              className="step-number"
              style={{
                backgroundColor: '#000000',
                color: '#ffffff',
                border: '2px solid #000000',
                backgroundImage: 'none'
              }}
            >
              4
            </div>
            <h3 className="step-title">Sign the best offer — with no hidden fees</h3>
            <p className="step-description">
              You choose and sign the offer that suits you best. Everything is transparent and digital.
            </p>
          </div>
          <div className="step-card">
            <div
              className="step-number"
              style={{
                backgroundColor: '#000000',
                color: '#ffffff',
                border: '2px solid #000000',
                backgroundImage: 'none'
              }}
            >
              5
            </div>
            <h3 className="step-title">Stay supported with a personal advisor</h3>
            <p className="step-description">
              Even after signing, your dedicated advisor remains available to answer any questions or assist with future needs.
            </p>
          </div>
        </div>
      </section>


      {/* CTA Section */}
      <section className="cta-section">
        <div className="cta-content">
          <h2 className="cta-title">Ready to Optimize Your Energy Bills?</h2>
          <p className="cta-description">
            Upload your bill today and discover how much you can save with personalized offers from certified energy experts.
          </p>
          <Link
            to="/signup"
            className="btn btn-black btn-large"
            style={{
              backgroundColor: '#000000',
              color: '#ffffff',
              border: '2px solid #000000',
              backgroundImage: 'none',
              padding: '1rem 2rem',
              borderRadius: '6px',
              textDecoration: 'none',
              display: 'inline-flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontWeight: '700',
              letterSpacing: '1px',
              transition: 'all 0.3s ease'
            }}
          >
            Upload My Bill
          </Link>
        </div>
      </section>

      {/* Footer */}
      <Footer />
    </div>
  );
};

export default HomePage;
