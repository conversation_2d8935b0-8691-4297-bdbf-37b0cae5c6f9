import { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import IndividualInfoWizard from '../components/IndividualInfoWizard';
import '../styles/horizontal-layout.css';
import { Auth } from 'aws-amplify';
import { trackNavigation } from '../utils/navigationTracker';
import { setItem, getItem, removeItem, STORAGE_KEYS } from '../utils/localStorage';
import Spinner from '../components/Spinner';
import profileService from '../services/profile.service';
import userService from '../services/user.service';
import { getLoadingMessage } from '../utils/loadingMessages';
import { showSuccessMessage, showErrorMessage } from '../utils/toastNotifications';
import { useForceScrollToTopAuthenticated } from '../utils/scrollToTop';
import { API_BASE_URL } from '../config/api-config';

const IndividualInfoPage = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [userData, setUserData] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);



  useEffect(() => {
    // Force scroll to top when page loads (SUPER AGGRESSIVE for authenticated pages)
    useForceScrollToTopAuthenticated();

    const fetchUserData = async () => {
      try {
        // First check if we have data in location state
        if (location.state?.userData) {
          console.log('Using user data from location state');
          setUserData(location.state.userData);
          return;
        }

        // Try to fetch from the database first (database-first approach)
        console.log('Fetching individual data from database');
        try {
          const individualData = await profileService.fetchUserDataByType('individual');
          if (individualData) {
            console.log('Individual data fetched from database:', individualData);
            setUserData(individualData);
            return;
          }
        } catch (dbError) {
          console.warn('Error fetching individual data from database:', dbError);
          // Continue to fallback options
        }

        // Fallback to localStorage
        const storedUserData = getItem(STORAGE_KEYS.USER_DATA, true);
        if (storedUserData) {
          console.log('Using user data from localStorage');
          setUserData(storedUserData);
          return;
        }

        // Last resort: get basic user data from Cognito
        console.log('Fetching basic user data from Cognito');
        const user = await Auth.currentAuthenticatedUser();
        const basicUserData = {
          firstName: user.attributes.given_name || '',
          lastName: user.attributes.family_name || '',
          email: user.attributes.email || '',
          phoneNumber: user.attributes.phone_number || '',
          cognitoId: user.attributes.sub || '',
          userType: 'individual'
        };

        console.log('Using basic user data from Cognito:', basicUserData);
        setUserData(basicUserData);
      } catch (error) {
        console.error('Error fetching user data:', error);

        // For testing purposes, create mock user data if all else fails
        const mockUserData = {
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          phoneNumber: '+1555-123-4567',
          userType: 'individual'
        };
        setUserData(mockUserData);
      }
    };

    fetchUserData();
  }, [location]);

  const handleSubmit = async (formData) => {
    try {
      setIsSubmitting(true);
      console.log('Individual info form submitted', formData);

      // Set a flag to prevent redirection loops
      sessionStorage.setItem('completing_individual_info', 'true');

      // 1. Store the individual info in localStorage with detailed logging
      console.log('Before localStorage update:', {
        userType: getItem(STORAGE_KEYS.USER_TYPE),
        profileCompletion: getItem(STORAGE_KEYS.PROFILE_COMPLETION),
        userData: getItem(STORAGE_KEYS.USER_DATA, true)
      });

      // Store authentication state in localStorage
      setItem(STORAGE_KEYS.IS_AUTHENTICATED, 'true');

      // Also update localStorage with user type and profile completion
      // This ensures localStorage is in sync with Cognito
      setItem(STORAGE_KEYS.USER_TYPE, 'individual');
      setItem(STORAGE_KEYS.PROFILE_COMPLETION, 'true');

      // We don't need to store the actual form data in localStorage anymore
      // as the dashboard will fetch it from the API
      // We can now remove userData and any individual info as it's no longer needed
      // Use our safe removeItem function for all localStorage operations
      try {
        console.log('Safely removing user data from localStorage');

        // Remove userData using our safe utility function
        removeItem(STORAGE_KEYS.USER_DATA);

        // Safely remove legacy keys with try-catch for each operation
        try {
          removeItem('individualInfo');
        } catch (e) {
          console.log('Error removing individualInfo, skipping:', e);
        }

        try {
          removeItem('individualInfoCompleted');
        } catch (e) {
          console.log('Error removing individualInfoCompleted, skipping:', e);
        }

        console.log('Successfully removed user data from localStorage');
      } catch (error) {
        console.error('Error during localStorage cleanup:', error);
        // Continue execution even if there's an error
      }

      console.log('After localStorage update:', {
        userType: getItem(STORAGE_KEYS.USER_TYPE),
        profileCompletion: getItem(STORAGE_KEYS.PROFILE_COMPLETION),
        userData: getItem(STORAGE_KEYS.USER_DATA, true)
      });

      // 2. Update Cognito attributes using the profile service
      try {
        // First try to update user type directly
        try {
          const user = await Auth.currentAuthenticatedUser();
          await Auth.updateUserAttributes(user, {
            'custom:userType': 'individual'
          });
          console.log('Successfully updated userType in Cognito');

          // Also try to update profileCompletion directly
          try {
            await Auth.updateUserAttributes(user, {
              'custom:profileCompletion': 'true'
            });
            console.log('Successfully updated profileCompletion in Cognito');
          } catch (profileError) {
            console.error('Error updating profileCompletion directly:', profileError);
          }
        } catch (userTypeError) {
          console.error('Error updating userType in Cognito:', userTypeError);
        }

        // Then use the profile service to update profileComplete
        const updateResult = await profileService.updateProfileCompletionInCognito(true);
        console.log('Profile completion update result:', updateResult);

        if (updateResult.success === false) {
          console.warn('Profile completion update was not successful, will try again');

          // Try one more time with a delay
          setTimeout(async () => {
            try {
              const retryResult = await profileService.updateProfileCompletionInCognito(true);
              console.log('Profile completion retry result:', retryResult);
            } catch (retryError) {
              console.error('Error in profile completion retry:', retryError);
            }
          }, 1000);
        }
      } catch (error) {
        console.error('Error updating Cognito attributes:', error);
      }

      // 3. Create or update individual profile in MongoDB
      try {
        // Format the data according to the backend's expectations
        const profileData = {
          address: {
            street: formData.streetAddress,
            city: formData.city,
            postalCode: formData.postalCode,
            country: formData.country
          },
          energyIdentifiers: {
            pdl: formData.meterNumber // Using meterNumber as PDL for now
          },
          preferredContactMethod: 'Email', // Default value
          authorizeDataAccess: formData.authorizeDataAccess || false
        };

        console.log('Submitting individual profile data:', profileData);

        // Call the user service to create/update the individual profile
        const profileResponse = await userService.updateIndividualProfile(profileData);
        console.log('Individual profile update response:', profileResponse);
      } catch (error) {
        console.error('Error creating/updating individual profile:', error);
      }

      // 4. Update profile completion status in MongoDB
      try {
        const response = await fetch(`${API_BASE_URL}/api/users/update-profile-completion`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            email: userData.email,
            profileComplete: true,
            userType: 'individual'
          })
        });

        if (response.ok) {
          console.log('Successfully updated MongoDB profile completion status');
        } else {
          console.error('Failed to update MongoDB profile completion status:', await response.text());
        }
      } catch (error) {
        console.error('Error updating MongoDB profile completion status:', error);
      }

      // 5. Navigate to upload first invoice page with success message using React Router
      console.log('Navigating to upload first invoice page...');

      // Track this navigation event
      trackNavigation(
        '/individual-info',
        '/upload-first-invoice',
        'react-router-navigate',
        {
          action: 'form-submission-success',
          userType: 'individual',
          individualInfoCompleted: true
        }
      );

      // Clear the flag before navigation
      sessionStorage.removeItem('completing_individual_info');

      // Make sure we have the essential data in localStorage before navigating
      // These are the only two values we need to check in the dashboard
      setItem(STORAGE_KEYS.USER_TYPE, 'individual');
      setItem(STORAGE_KEYS.PROFILE_COMPLETION, 'true');

      // Set a flag to indicate this is the first time flow
      localStorage.setItem('first_time_flow', 'true');

      // Remove any undefined keys that might exist
      try {
        removeItem('undefined');
      } catch (e) {
        console.log('Error removing undefined key, skipping:', e);
      }

      // Show success message
      showSuccessMessage('INDIVIDUAL_INFO_SAVED');

      // Use navigate with replace to prevent back button issues
      navigate('/upload-first-invoice', {
        state: {
          message: 'Personal information saved successfully! Please upload your first energy bill.'
        },
        replace: true
      });

    } catch (error) {
      console.error('Error in form submission:', error);
      showErrorMessage('PROFILE_SAVE_FAILED', 'Failed to save personal information. Please try again.');

      // Track this navigation event (error case)
      trackNavigation(
        '/individual-info',
        '/upload-first-invoice',
        'react-router-navigate',
        {
          action: 'form-submission-error',
          userType: 'individual',
          individualInfoCompleted: true,
          error: error.message
        }
      );

      // Clear the flag in case of error
      sessionStorage.removeItem('completing_individual_info');

      // Set a flag to indicate this is the first time flow
      localStorage.setItem('first_time_flow', 'true');

      // Even in case of error, try to navigate to upload first invoice page
      navigate('/upload-first-invoice', {
        state: {
          message: 'Personal information saved successfully! Please upload your first energy bill.'
        },
        replace: true
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    // Navigate back to user type selection
    navigate('/user-type');
  };

  return (
    <div className="fullscreen-container">
      {isSubmitting && (
        <Spinner
          fullScreen={true}
          message={getLoadingMessage('INDIVIDUAL_INFO')}
          size="large"
          color="#3498db"
        />
      )}

      <div className="content-wrapper" style={{ width: '100%', maxWidth: '100%' }}>
        <h1 className="page-title">Personal Information</h1>
        <p className="page-subtitle">
          Please provide your personal details to complete your account setup. This information will help us provide you with the most accurate energy offers.
        </p>

        <div className="wizard-fullscreen">
          {userData ? (
            <IndividualInfoWizard
              onSubmit={handleSubmit}
              onCancel={handleCancel}
              userData={userData}
            />
          ) : (
            <div className="loading-overlay">
              <div className="loading-content">
                <div className="loading-spinner"></div>
                <p>Loading your information...</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default IndividualInfoPage;
