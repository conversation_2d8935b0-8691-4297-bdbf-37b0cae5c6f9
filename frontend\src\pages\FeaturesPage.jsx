import React, { useEffect } from 'react';
import { Link } from 'react-router-dom';
import logoImage from '../assets/logo.jpeg';
import HomeHeader from '../components/HomeHeader';
import Footer from '../components/Footer';
import { useForceScrollToTop } from '../utils/scrollToTop';
import '../styles/app.css';
import '../styles/pages.css';

const FeaturesPage = () => {
  // Force scroll to top when page loads
  useEffect(() => {
    useForceScrollToTop();
  }, []);

  return (
    <div className="home-container">
      {/* Header */}
      <HomeHeader />

      {/* Hero Section */}
      <section className="page-hero">
        <div className="page-hero-content">
          <h1 className="page-hero-title">Our Features</h1>
          <p className="page-hero-subtitle">
            Discover the powerful tools and services that make My Energy Bill the leading platform for energy savings.
          </p>
        </div>
      </section>

      {/* Main Features Section */}
      <section className="features-main-section">
        <div className="features-container">
          <div className="feature-highlight">
            <div className="feature-content">
              <h2 className="section-title">Bill Analysis</h2>
              <p className="feature-description">
                Our advanced algorithms analyze your energy bills to identify potential savings and recommend the best tariffs for your consumption patterns.
              </p>
              <ul className="feature-list">
                <li>Upload your bill in seconds</li>
                <li>AI-powered consumption analysis</li>
                <li>Personalized savings recommendations</li>
                <li>Historical usage tracking</li>
              </ul>
            </div>
            <div className="feature-image">
              <div className="image-placeholder">
                <svg xmlns="http://www.w3.org/2000/svg" width="80" height="80" fill="currentColor" viewBox="0 0 16 16">
                  <path d="M5 10.5a.5.5 0 0 1 .5-.5h2a.5.5 0 0 1 0 1h-2a.5.5 0 0 1-.5-.5zm0-2a.5.5 0 0 1 .5-.5h5a.5.5 0 0 1 0 1h-5a.5.5 0 0 1-.5-.5zm0-2a.5.5 0 0 1 .5-.5h5a.5.5 0 0 1 0 1h-5a.5.5 0 0 1-.5-.5zm0-2a.5.5 0 0 1 .5-.5h5a.5.5 0 0 1 0 1h-5a.5.5 0 0 1-.5-.5z"/>
                  <path d="M3 0h10a2 2 0 0 1 2 2v12a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2v-1h1v1a1 1 0 0 0 1 1h10a1 1 0 0 0 1-1V2a1 1 0 0 0-1-1H3a1 1 0 0 0-1 1v1H1V2a2 2 0 0 1 2-2z"/>
                  <path d="M1 5v-.5a.5.5 0 0 1 1 0V5h.5a.5.5 0 0 1 0 1h-2a.5.5 0 0 1 0-1H1zm0 3v-.5a.5.5 0 0 1 1 0V8h.5a.5.5 0 0 1 0 1h-2a.5.5 0 0 1 0-1H1zm0 3v-.5a.5.5 0 0 1 1 0v.5h.5a.5.5 0 0 1 0 1h-2a.5.5 0 0 1 0-1H1z"/>
                </svg>
                <p>Bill Analysis Illustration</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Supplier Comparison Section */}
      <section className="features-section bg-light">
        <div className="features-container">
          <div className="feature-highlight reverse">
            <div className="feature-image">
              <div className="image-placeholder">
                <svg xmlns="http://www.w3.org/2000/svg" width="80" height="80" fill="currentColor" viewBox="0 0 16 16">
                  <path fillRule="evenodd" d="M0 0h1v15h15v1H0V0Zm10 3.5a.5.5 0 0 1 .5-.5h4a.5.5 0 0 1 .5.5v4a.5.5 0 0 1-1 0V4.9l-3.613 4.417a.5.5 0 0 1-.74.037L7.06 6.767l-3.656 5.027a.5.5 0 0 1-.808-.588l4-5.5a.5.5 0 0 1 .758-.06l2.609 2.61L13.445 4H10.5a.5.5 0 0 1-.5-.5Z"/>
                </svg>
                <p>Comparison Tool Illustration</p>
              </div>
            </div>
            <div className="feature-content">
              <h2 className="section-title">Supplier Comparison</h2>
              <p className="feature-description">
                Compare offers from multiple energy suppliers in one place. Our platform provides side-by-side comparisons of rates, contract terms, and customer reviews.
              </p>
              <ul className="feature-list">
                <li>Compare offers from 30+ suppliers</li>
                <li>Transparent pricing information</li>
                <li>Customer satisfaction ratings</li>
                <li>Green energy options highlighted</li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Contract Management Section */}
      <section className="features-section">
        <div className="features-container">
          <div className="feature-highlight">
            <div className="feature-content">
              <h2 className="section-title">Contract Management</h2>
              <p className="feature-description">
                Keep track of your energy contracts, renewal dates, and terms all in one place. We'll notify you when it's time to consider switching to a better deal.
              </p>
              <ul className="feature-list">
                <li>Contract expiration alerts</li>
                <li>Digital contract storage</li>
                <li>Renewal recommendations</li>
                <li>Price change notifications</li>
              </ul>
            </div>
            <div className="feature-image">
              <div className="image-placeholder">
                <svg xmlns="http://www.w3.org/2000/svg" width="80" height="80" fill="currentColor" viewBox="0 0 16 16">
                  <path d="M8 3.5a.5.5 0 0 0-1 0V9a.5.5 0 0 0 .252.434l3.5 2a.5.5 0 0 0 .496-.868L8 8.71V3.5z"/>
                  <path d="M8 16A8 8 0 1 0 8 0a8 8 0 0 0 0 16zm7-8A7 7 0 1 1 1 8a7 7 0 0 1 14 0z"/>
                </svg>
                <p>Contract Management Illustration</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Additional Features Section */}
      <section className="additional-features-section bg-light">
        <div className="features-container">
          <h2 className="section-title text-center">More Powerful Features</h2>
          <div className="features-grid">
            <div className="feature-card">
              <div className="feature-icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="currentColor" viewBox="0 0 16 16">
                  <path d="M11.251.068a.5.5 0 0 1 .227.58L9.677 6.5H13a.5.5 0 0 1 .364.843l-8 8.5a.5.5 0 0 1-.842-.49L6.323 9.5H3a.5.5 0 0 1-.364-.843l8-8.5a.5.5 0 0 1 .615-.09z"/>
                </svg>
              </div>
              <h3>Energy Usage Insights</h3>
              <p>Gain valuable insights into your energy consumption patterns and learn how to reduce your usage.</p>
            </div>

            <div className="feature-card">
              <div className="feature-icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="currentColor" viewBox="0 0 16 16">
                  <path d="M8 16A8 8 0 1 0 8 0a8 8 0 0 0 0 16zm.93-9.412-1 4.705c-.07.34.029.533.304.533.194 0 .487-.07.686-.246l-.088.416c-.287.346-.92.598-1.465.598-.703 0-1.002-.422-.808-1.319l.738-3.468c.064-.293.006-.399-.287-.47l-.451-.081.082-.381 2.29-.287zM8 5.5a1 1 0 1 1 0-2 1 1 0 0 1 0 2z"/>
                </svg>
              </div>
              <h3>Expert Advice</h3>
              <p>Access personalized recommendations from energy experts to optimize your energy usage and costs.</p>
            </div>

            <div className="feature-card">
              <div className="feature-icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="currentColor" viewBox="0 0 16 16">
                  <path d="M2 2a2 2 0 0 0-2 2v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2H2zm6 2.5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 0 1h-3a.5.5 0 0 1-.5-.5zm-6 2A.5.5 0 0 1 2.5 6h11a.5.5 0 0 1 0 1h-11a.5.5 0 0 1-.5-.5zm0 2a.5.5 0 0 1 .5-.5h6a.5.5 0 0 1 0 1h-6a.5.5 0 0 1-.5-.5zm0 2a.5.5 0 0 1 .5-.5h6a.5.5 0 0 1 0 1h-6a.5.5 0 0 1-.5-.5zM0 14a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-2H0v2z"/>
                </svg>
              </div>
              <h3>Paperless Billing</h3>
              <p>Manage all your energy bills digitally in one secure location, reducing paper waste and staying organized.</p>
            </div>

            <div className="feature-card">
              <div className="feature-icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="currentColor" viewBox="0 0 16 16">
                  <path d="M8 0a8 8 0 1 0 0 16A8 8 0 0 0 8 0zm0 14.5a6.5 6.5 0 1 1 0-13 6.5 6.5 0 0 1 0 13z"/>
                  <path d="M7 5.5a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-1a.5.5 0 0 1-.5-.5v-1z"/>
                  <path d="M7.5 7.5A.5.5 0 0 1 8 7h1a.5.5 0 0 1 .5.5v4a.5.5 0 0 1-.5.5H8a.5.5 0 0 1-.5-.5v-4z"/>
                </svg>
              </div>
              <h3>Market Updates</h3>
              <p>Stay informed about energy market trends, price changes, and new supplier offerings that could benefit you.</p>
            </div>

            <div className="feature-card">
              <div className="feature-icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="currentColor" viewBox="0 0 16 16">
                  <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
                  <path d="M10.97 4.97a.235.235 0 0 0-.02.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05z"/>
                </svg>
              </div>
              <h3>Seamless Switching</h3>
              <p>Switch energy suppliers with just a few clicks. We handle all the paperwork and communication for you.</p>
            </div>

            <div className="feature-card">
              <div className="feature-icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="currentColor" viewBox="0 0 16 16">
                  <path d="M8 1a2.5 2.5 0 0 1 2.5 2.5V4h-5v-.5A2.5 2.5 0 0 1 8 1zm3.5 3v-.5a3.5 3.5 0 1 0-7 0V4H1v10a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V4h-3.5zM2 5h12v9a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V5z"/>
                </svg>
              </div>
              <h3>Special Offers</h3>
              <p>Access exclusive deals and promotions from energy suppliers that aren't available to the general public.</p>
            </div>
          </div>
        </div>
      </section>

      {/* Business Features Section */}
      <section className="business-features-section">
        <div className="features-container">
          <h2 className="section-title text-center">For Business Customers</h2>
          <p className="section-subtitle text-center">
            Specialized features designed for businesses of all sizes to optimize energy costs and improve sustainability.
          </p>

          <div className="business-features-grid">
            <div className="business-feature">
              <h3>Multi-site Management</h3>
              <p>Manage energy contracts for multiple business locations from a single dashboard.</p>
            </div>

            <div className="business-feature">
              <h3>Custom Energy Solutions</h3>
              <p>Get tailored energy solutions based on your business's specific consumption patterns and needs.</p>
            </div>

            <div className="business-feature">
              <h3>Consumption Analytics</h3>
              <p>Advanced analytics to identify peak usage times and opportunities for energy efficiency improvements.</p>
            </div>

            <div className="business-feature">
              <h3>Sustainability Reporting</h3>
              <p>Generate reports on your business's carbon footprint and progress toward sustainability goals.</p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="cta-section">
        <div className="cta-content">
          <h2 className="cta-title">Ready to Experience These Features?</h2>
          <p className="cta-description">
            Create your account today and start exploring all the powerful tools My Energy Bill has to offer.
          </p>
          <Link to="/signup" className="btn btn-primary btn-large">Sign Up</Link>
        </div>
      </section>

      {/* Footer */}
      <Footer />
    </div>
  );
};

export default FeaturesPage;
