import React, { useEffect } from 'react';
import { Link } from 'react-router-dom';
import logoImage from '../assets/logo.jpeg';
import HomeHeader from '../components/HomeHeader';
import Footer from '../components/Footer';
import { useForceScrollToTop } from '../utils/scrollToTop';
import '../styles/app.css';
import '../styles/pages.css';

const FeaturesPage = () => {
  // Force scroll to top when page loads
  useEffect(() => {
    useForceScrollToTop();
  }, []);

  return (
    <div className="home-container">
      {/* Header */}
      <HomeHeader />

      {/* Hero Section */}
      <section className="page-hero">
        <div className="page-hero-content">
          <h1 className="page-hero-title">⚙️ Features</h1>
          <p className="page-hero-subtitle">
            Discover the powerful tools and services that make My Energy Bill the leading platform for energy savings.
          </p>
        </div>
      </section>

      {/* Main Features Section */}
      <section className="features-main-section">
        <div className="features-container">
          <div className="feature-highlight">
            <div className="feature-content">
              <h2 className="section-title">Tailored bill analysis</h2>
              <p className="feature-description">
                Upload your electricity or gas bill and let our team review it manually and thoroughly to extract the key data needed for your comparison.
              </p>
            </div>
            <div className="feature-image">
              <div className="image-placeholder">
                <svg xmlns="http://www.w3.org/2000/svg" width="80" height="80" fill="currentColor" viewBox="0 0 16 16">
                  <path d="M5 10.5a.5.5 0 0 1 .5-.5h2a.5.5 0 0 1 0 1h-2a.5.5 0 0 1-.5-.5zm0-2a.5.5 0 0 1 .5-.5h5a.5.5 0 0 1 0 1h-5a.5.5 0 0 1-.5-.5zm0-2a.5.5 0 0 1 .5-.5h5a.5.5 0 0 1 0 1h-5a.5.5 0 0 1-.5-.5zm0-2a.5.5 0 0 1 .5-.5h5a.5.5 0 0 1 0 1h-5a.5.5 0 0 1-.5-.5z"/>
                  <path d="M3 0h10a2 2 0 0 1 2 2v12a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2v-1h1v1a1 1 0 0 0 1 1h10a1 1 0 0 0 1-1V2a1 1 0 0 0-1-1H3a1 1 0 0 0-1 1v1H1V2a2 2 0 0 1 2-2z"/>
                  <path d="M1 5v-.5a.5.5 0 0 1 1 0V5h.5a.5.5 0 0 1 0 1h-2a.5.5 0 0 1 0-1H1zm0 3v-.5a.5.5 0 0 1 1 0V8h.5a.5.5 0 0 1 0 1h-2a.5.5 0 0 1 0-1H1zm0 3v-.5a.5.5 0 0 1 1 0v.5h.5a.5.5 0 0 1 0 1h-2a.5.5 0 0 1 0-1H1z"/>
                </svg>
                <p>Bill Analysis Illustration</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Independent Offer Comparison Section */}
      <section className="features-section bg-light">
        <div className="features-container">
          <div className="feature-highlight reverse">
            <div className="feature-image">
              <div className="image-placeholder">
                <svg xmlns="http://www.w3.org/2000/svg" width="80" height="80" fill="currentColor" viewBox="0 0 16 16">
                  <path fillRule="evenodd" d="M0 0h1v15h15v1H0V0Zm10 3.5a.5.5 0 0 1 .5-.5h4a.5.5 0 0 1 .5.5v4a.5.5 0 0 1-1 0V4.9l-3.613 4.417a.5.5 0 0 1-.74.037L7.06 6.767l-3.656 5.027a.5.5 0 0 1-.808-.588l4-5.5a.5.5 0 0 1 .758-.06l2.609 2.61L13.445 4H10.5a.5.5 0 0 1-.5-.5Z"/>
                </svg>
                <p>Comparison Tool Illustration</p>
              </div>
            </div>
            <div className="feature-content">
              <h2 className="section-title">Independent offer comparison</h2>
              <p className="feature-description">
                Our experts compare a wide range of offers from trusted suppliers — always based on your actual needs, not automated algorithms.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Clear Recommendations Section */}
      <section className="features-section">
        <div className="features-container">
          <div className="feature-highlight">
            <div className="feature-content">
              <h2 className="section-title">Clear, personalized recommendations</h2>
              <p className="feature-description">
                Receive well-structured, easy-to-read comparison tables that make your decision straightforward. Each proposal is reviewed by a real advisor before being sent to you.
              </p>
            </div>
            <div className="feature-image">
              <div className="image-placeholder">
                <svg xmlns="http://www.w3.org/2000/svg" width="80" height="80" fill="currentColor" viewBox="0 0 16 16">
                  <path d="M2 2a2 2 0 0 0-2 2v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2H2zm6 2.5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 0 1h-3a.5.5 0 0 1-.5-.5zm-6 2A.5.5 0 0 1 2.5 6h11a.5.5 0 0 1 0 1h-11a.5.5 0 0 1-.5-.5zm0 2a.5.5 0 0 1 .5-.5h6a.5.5 0 0 1 0 1h-6a.5.5 0 0 1-.5-.5zm0 2a.5.5 0 0 1 .5-.5h6a.5.5 0 0 1 0 1h-6a.5.5 0 0 1-.5-.5z"/>
                </svg>
                <p>Recommendations Illustration</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Guided Contract Switching Section */}
      <section className="features-section bg-light">
        <div className="features-container">
          <div className="feature-highlight">
            <div className="feature-content">
              <h2 className="section-title">Guided contract switching</h2>
              <p className="feature-description">
                Once you've chosen the right offer, we assist you through the signing process and ensure everything runs smoothly — from provider contact to follow-up.
              </p>
            </div>
            <div className="feature-image">
              <div className="image-placeholder">
                <svg xmlns="http://www.w3.org/2000/svg" width="80" height="80" fill="currentColor" viewBox="0 0 16 16">
                  <path d="M8 1a2.5 2.5 0 0 1 2.5 2.5V4h-5v-.5A2.5 2.5 0 0 1 8 1zm3.5 3v-.5a3.5 3.5 0 1 0-7 0V4H1v10a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V4h-3.5zM2 5h12v9a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V5z"/>
                </svg>
                <p>Contract Switching Illustration</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Dedicated Support Section */}
      <section className="features-section">
        <div className="features-container">
          <div className="feature-highlight reverse">
            <div className="feature-image">
              <div className="image-placeholder">
                <svg xmlns="http://www.w3.org/2000/svg" width="80" height="80" fill="currentColor" viewBox="0 0 16 16">
                  <path d="M8 16A8 8 0 1 0 8 0a8 8 0 0 0 0 16zm.93-9.412-1 4.705c-.07.34.029.533.304.533.194 0 .487-.07.686-.246l-.088.416c-.287.346-.92.598-1.465.598-.703 0-1.002-.422-.808-1.319l.738-3.468c.064-.293.006-.399-.287-.47l-.451-.081.082-.381 2.29-.287zM8 5.5a1 1 0 1 1 0-2 1 1 0 0 1 0 2z"/>
                </svg>
                <p>Support Illustration</p>
              </div>
            </div>
            <div className="feature-content">
              <h2 className="section-title">Dedicated support</h2>
              <p className="feature-description">
                At every step, you can rely on a real person to answer your questions, validate the details, and make sure you're fully informed and comfortable.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="cta-section">
        <div className="cta-content">
          <h2 className="cta-title">Ready to Experience These Features?</h2>
          <p className="cta-description">
            Create your account today and start exploring all the powerful tools My Energy Bill has to offer.
          </p>
          <Link to="/signup" className="btn btn-primary btn-large">Sign Up</Link>
        </div>
      </section>

      {/* Footer */}
      <Footer />
    </div>
  );
};

export default FeaturesPage;
