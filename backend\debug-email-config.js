const dotenv = require('dotenv');
const path = require('path');

// Load environment variables
dotenv.config({
  path: path.resolve(__dirname, '.env.uat'),
  override: false,
});

console.log('🔍 DEBUG: Environment Configuration Check');
console.log('==========================================');

console.log('📧 Email Configuration:');
console.log(`FROM_EMAIL: ${process.env.FROM_EMAIL || 'NOT SET'}`);
console.log(`COMPANY_NAME: ${process.env.COMPANY_NAME || 'NOT SET'}`);
console.log(`FRONTEND_URL: ${process.env.FRONTEND_URL || 'NOT SET'}`);

console.log('\n🔧 AWS Configuration:');
console.log(`AWS_REGION: ${process.env.AWS_REGION || 'NOT SET'}`);
console.log(`AWS_ACCESS_KEY_ID: ${process.env.AWS_ACCESS_KEY_ID ? 'SET' : 'NOT SET'}`);
console.log(`AWS_SECRET_ACCESS_KEY: ${process.env.AWS_SECRET_ACCESS_KEY ? 'SET' : 'NOT SET'}`);

console.log('\n🏗️ Other Configuration:');
console.log(`NODE_ENV: ${process.env.NODE_ENV || 'NOT SET'}`);
console.log(`PORT: ${process.env.PORT || 'NOT SET'}`);

// Test email service initialization
console.log('\n📬 Testing Email Service Initialization:');
try {
  const emailService = require('./services/emailService');
  console.log('✅ Email service loaded successfully');
  
  // Check if we can access the properties
  console.log(`📧 From Email: ${emailService.fromEmail || 'NOT SET'}`);
  console.log(`🏢 Company Name: ${emailService.companyName || 'NOT SET'}`);
  console.log(`🌐 Frontend URL: ${emailService.frontendUrl || 'NOT SET'}`);
  
} catch (error) {
  console.error('❌ Failed to load email service:', error.message);
}

// Test AWS SES configuration
console.log('\n☁️ Testing AWS SES Configuration:');
try {
  const AWS = require('aws-sdk');
  
  const ses = new AWS.SES({
    region: process.env.AWS_REGION || 'eu-west-1',
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
  });
  
  console.log('✅ AWS SES client initialized successfully');
  console.log(`📍 Region: ${ses.config.region}`);
  
  // Test SES quota (this will fail if credentials are wrong)
  ses.getSendQuota().promise()
    .then(quota => {
      console.log('✅ AWS SES connection successful');
      console.log(`📊 Quota - Max24HourSend: ${quota.Max24HourSend}, MaxSendRate: ${quota.MaxSendRate}, SentLast24Hours: ${quota.SentLast24Hours}`);
    })
    .catch(error => {
      console.error('❌ AWS SES connection failed:', error.message);
      console.error('   This could be due to:');
      console.error('   - Invalid AWS credentials');
      console.error('   - Incorrect AWS region');
      console.error('   - SES not enabled in the region');
      console.error('   - Network connectivity issues');
    });
    
} catch (error) {
  console.error('❌ Failed to initialize AWS SES:', error.message);
}

console.log('\n🔍 Debug completed. Check the output above for any issues.');
