import React, { useState } from 'react';

const BrokerConfirmationStep = ({ formData, onSubmit, onPrev, onCancel }) => {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      await onSubmit();
    } catch (error) {
      console.error('Error submitting broker profile:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const formatAddress = () => {
    const { street, city, postalCode, country } = formData.companyAddress;
    const parts = [street, city, postalCode, country].filter(Boolean);
    return parts.length > 0 ? parts.join(', ') : 'Not provided';
  };

  const formatCommission = () => {
    const { type, rate } = formData.commissionStructure;
    if (!rate) return 'Not specified';

    if (type === 'percentage') {
      return `${rate}% of contract value`;
    } else if (type === 'fixed') {
      return `€${rate} per contract`;
    } else {
      return `Tiered structure (base: ${rate}%)`;
    }
  };

  return (
    <div>
      <div className="stepper-header">
        <h3 className="page-title">Confirm Your Information</h3>
        <p className="page-subtitle">
          Please review your broker profile information before submitting.
        </p>
      </div>

      <div className="confirmation-summary">

        {/* Company Information */}
        <div className="summary-section">
          <h4 className="summary-section-title">Company Information</h4>
          <div className="summary-grid">
            <div className="summary-item">
              <span className="summary-label">Company Name:</span>
              <span className="summary-value">{formData.companyName || 'Not provided'}</span>
            </div>
            <div className="summary-item">
              <span className="summary-label">License Number:</span>
              <span className="summary-value">{formData.licenseNumber || 'Not provided'}</span>
            </div>
            <div className="summary-item">
              <span className="summary-label">Website:</span>
              <span className="summary-value">{formData.website || 'Not provided'}</span>
            </div>
            <div className="summary-item">
              <span className="summary-label">Phone:</span>
              <span className="summary-value">{formData.phone || 'Not provided'}</span>
            </div>
            <div className="summary-item">
              <span className="summary-label">Experience:</span>
              <span className="summary-value">
                {formData.yearsOfExperience ? `${formData.yearsOfExperience} years` : 'Not specified'}
              </span>
            </div>
            <div className="summary-item">
              <span className="summary-label">VAT Number:</span>
              <span className="summary-value">{formData.vatNumber || 'Not provided'}</span>
            </div>
          </div>

          {formData.businessDescription && (
            <div className="summary-item" style={{ marginTop: '1rem' }}>
              <span className="summary-label">Business Description:</span>
              <span className="summary-value">{formData.businessDescription}</span>
            </div>
          )}

          <div className="summary-item" style={{ marginTop: '1rem' }}>
            <span className="summary-label">Business Address:</span>
            <span className="summary-value">{formatAddress()}</span>
          </div>
        </div>

        {/* Specializations */}
        <div className="summary-section">
          <h4 className="summary-section-title">Specializations</h4>
          <div className="summary-item">
            <span className="summary-label">Areas of Expertise:</span>
            <span className="summary-value">
              {formData.specializations.length > 0
                ? formData.specializations.join(', ')
                : 'None selected'
              }
            </span>
          </div>
          <div className="summary-item">
            <span className="summary-label">Service Areas:</span>
            <span className="summary-value">
              {formData.serviceAreas.length > 0
                ? formData.serviceAreas.join(', ')
                : 'None selected'
              }
            </span>
          </div>
        </div>

        {/* Commission Structure */}
        <div className="summary-section">
          <h4 className="summary-section-title">Commission Structure</h4>
          <div className="summary-item">
            <span className="summary-label">Commission Type:</span>
            <span className="summary-value">
              {formData.commissionStructure.type.charAt(0).toUpperCase() +
               formData.commissionStructure.type.slice(1)}
            </span>
          </div>
          <div className="summary-item">
            <span className="summary-label">Commission Rate:</span>
            <span className="summary-value">{formatCommission()}</span>
          </div>
        </div>
      </div>

      <div className="stepper-buttons">
        <button type="button" className="stepper-button stepper-button-prev" onClick={onPrev} disabled={isSubmitting}>
          Back
        </button>
        <button
          type="button"
          className="stepper-button stepper-button-next"
          onClick={handleSubmit}
          disabled={isSubmitting}
        >
          {isSubmitting ? (
            <>
              <i className="fas fa-spinner fa-spin" style={{ marginRight: '0.5rem' }}></i>
              Setting up profile...
            </>
          ) : (
            <>
              <i className="fas fa-check" style={{ marginRight: '0.5rem' }}></i>
              Complete Setup
            </>
          )}
        </button>
      </div>
    </div>
  );
};

export default BrokerConfirmationStep;
