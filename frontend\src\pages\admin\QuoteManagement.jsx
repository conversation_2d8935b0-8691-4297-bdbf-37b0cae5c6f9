import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import DashboardLayout from '../../components/DashboardLayout';
import Spinner from '../../components/Spinner';
import { showSuccessMessage, showErrorMessage } from '../../utils/toastNotifications';
import logger from '../../utils/logger';
import '../../styles/admin-management.css';
import { API_BASE_URL } from '../../config/api-config';

const QuoteManagement = () => {
  const [loading, setLoading] = useState(true);
  const [quoteRequests, setQuoteRequests] = useState([]);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalCount: 0,
    hasNextPage: false,
    hasPrevPage: false
  });
  const [filters, setFilters] = useState({
    status: 'all',
    priority: 'all',
    energyType: 'all',
    search: ''
  });
  const [selectedRequest, setSelectedRequest] = useState(null);
  const [showQuoteDetails, setShowQuoteDetails] = useState(false);
  const [showQuoteEditor, setShowQuoteEditor] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    fetchQuoteRequests();
  }, [filters, pagination.currentPage]);

  const fetchQuoteRequests = async () => {
    try {
      setLoading(true);
      logger.info('Fetching quote requests with filters:', filters);

      const queryParams = new URLSearchParams({
        page: pagination.currentPage,
        limit: 10,
        ...filters
      });

      const response = await fetch(`${API_BASE_URL}s/api/admin/quotes?${queryParams}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch quote requests');
      }

      const data = await response.json();
      setQuoteRequests(data.data.requests);
      setPagination(data.data.pagination);
      
      logger.info('Quote requests fetched successfully');
    } catch (error) {
      logger.error('Error fetching quote requests:', error);
      showErrorMessage('QUOTES_LOAD_FAILED', 'Failed to load quote requests');
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = (filterName, value) => {
    setFilters(prev => ({
      ...prev,
      [filterName]: value
    }));
    setPagination(prev => ({ ...prev, currentPage: 1 }));
  };

  const handleStatusUpdate = async (requestId, newStatus) => {
    try {
      const response = await fetch(`${API_BASE_URL}s/api/admin/quotes/${requestId}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
        },
        body: JSON.stringify({ status: newStatus })
      });

      if (!response.ok) {
        throw new Error('Failed to update quote request status');
      }

      showSuccessMessage('QUOTE_STATUS_UPDATED', 'Quote request status updated successfully');
      fetchQuoteRequests();
    } catch (error) {
      logger.error('Error updating quote request status:', error);
      showErrorMessage('STATUS_UPDATE_FAILED', 'Failed to update quote request status');
    }
  };

  const handleViewQuoteDetails = async (request) => {
    setSelectedRequest(request);
    setShowQuoteDetails(true);
  };

  const handleEditQuotes = (request) => {
    setSelectedRequest(request);
    setShowQuoteEditor(true);
  };

  const handlePageChange = (newPage) => {
    setPagination(prev => ({ ...prev, currentPage: newPage }));
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusBadgeClass = (status) => {
    switch (status?.toLowerCase()) {
      case 'pending': return 'status-badge pending';
      case 'processing': return 'status-badge processing';
      case 'quotes_available': return 'status-badge active';
      case 'sent_to_client': return 'status-badge sent';
      case 'approved': return 'status-badge active';
      case 'rejected': return 'status-badge inactive';
      case 'expired': return 'status-badge suspended';
      default: return 'status-badge';
    }
  };

  const getPriorityBadgeClass = (priority) => {
    switch (priority?.toLowerCase()) {
      case 'low': return 'priority-badge low';
      case 'normal': return 'priority-badge normal';
      case 'high': return 'priority-badge high';
      case 'urgent': return 'priority-badge urgent';
      default: return 'priority-badge normal';
    }
  };

  const getEnergyTypeIcon = (energyType) => {
    switch (energyType?.toLowerCase()) {
      case 'electricity': return 'fas fa-bolt';
      case 'gas': return 'fas fa-fire';
      case 'both': return 'fas fa-home';
      default: return 'fas fa-question';
    }
  };

  if (loading) {
    return <Spinner fullScreen={true} message="Loading quote requests..." size="large" />;
  }

  return (
    <DashboardLayout>
      <div className="admin-management-container">
        {/* Header */}
        <div className="management-header">
          <div className="header-content">
            <button 
              className="back-button"
              onClick={() => navigate('/dashboard')}
            >
              <i className="fas fa-arrow-left"></i>
              Back to Dashboard
            </button>
            <h1>Quote & Comparison Management</h1>
            <p>Manage quote requests, modify offers, and approve comparisons</p>
          </div>
          <div className="header-stats">
            <div className="stat-item">
              <span className="stat-value">{pagination.totalCount}</span>
              <span className="stat-label">Total Requests</span>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="management-filters">
          <div className="filter-group">
            <label>Status:</label>
            <select 
              value={filters.status} 
              onChange={(e) => handleFilterChange('status', e.target.value)}
            >
              <option value="all">All Status</option>
              <option value="Pending">Pending</option>
              <option value="Processing">Processing</option>
              <option value="Quotes_Available">Quotes Available</option>
              <option value="Sent_to_Client">Sent to Client</option>
              <option value="Approved">Approved</option>
              <option value="Rejected">Rejected</option>
              <option value="Expired">Expired</option>
            </select>
          </div>

          <div className="filter-group">
            <label>Priority:</label>
            <select 
              value={filters.priority} 
              onChange={(e) => handleFilterChange('priority', e.target.value)}
            >
              <option value="all">All Priorities</option>
              <option value="Low">Low</option>
              <option value="Normal">Normal</option>
              <option value="High">High</option>
              <option value="Urgent">Urgent</option>
            </select>
          </div>

          <div className="filter-group">
            <label>Energy Type:</label>
            <select 
              value={filters.energyType} 
              onChange={(e) => handleFilterChange('energyType', e.target.value)}
            >
              <option value="all">All Types</option>
              <option value="Electricity">Electricity</option>
              <option value="Gas">Gas</option>
              <option value="Both">Both</option>
            </select>
          </div>

          <div className="filter-group search-group">
            <label>Search:</label>
            <input
              type="text"
              placeholder="Search by client name or email..."
              value={filters.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
            />
          </div>
        </div>

        {/* Quote Requests Table */}
        <div className="management-table-container">
          <table className="management-table">
            <thead>
              <tr>
                <th>Client</th>
                <th>Energy Type</th>
                <th>Priority</th>
                <th>Status</th>
                <th>Quotes</th>
                <th>Created</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {quoteRequests.map(request => (
                <tr key={request._id}>
                  <td>
                    <div className="user-info">
                      <div className="user-icon">
                        <i className="fas fa-user"></i>
                      </div>
                      <div className="user-details">
                        <div className="user-name">
                          {request.userId?.firstName} {request.userId?.lastName}
                        </div>
                        <div className="user-email">{request.userId?.email}</div>
                      </div>
                    </div>
                  </td>
                  <td>
                    <div className="energy-type">
                      <i className={getEnergyTypeIcon(request.energyType)}></i>
                      <span>{request.energyType}</span>
                    </div>
                  </td>
                  <td>
                    <span className={getPriorityBadgeClass(request.priority)}>
                      {request.priority}
                    </span>
                  </td>
                  <td>
                    <span className={getStatusBadgeClass(request.status)}>
                      {request.status?.replace('_', ' ')}
                    </span>
                  </td>
                  <td>
                    <span className="quotes-count">
                      {request.quotes?.length || 0} quotes
                    </span>
                  </td>
                  <td>{formatDate(request.createdAt)}</td>
                  <td>
                    <div className="action-buttons">
                      <button
                        className="action-btn view"
                        onClick={() => handleViewQuoteDetails(request)}
                        title="View Details"
                      >
                        <i className="fas fa-eye"></i>
                      </button>
                      <button
                        className="action-btn edit"
                        onClick={() => handleEditQuotes(request)}
                        title="Edit Quotes"
                      >
                        <i className="fas fa-edit"></i>
                      </button>
                      <select
                        value={request.status}
                        onChange={(e) => handleStatusUpdate(request._id, e.target.value)}
                        className="status-select"
                      >
                        <option value="Pending">Pending</option>
                        <option value="Processing">Processing</option>
                        <option value="Quotes_Available">Quotes Available</option>
                        <option value="Sent_to_Client">Sent to Client</option>
                        <option value="Approved">Approved</option>
                        <option value="Rejected">Rejected</option>
                      </select>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        <div className="management-pagination">
          <button 
            disabled={!pagination.hasPrevPage}
            onClick={() => handlePageChange(pagination.currentPage - 1)}
            className="pagination-btn"
          >
            <i className="fas fa-chevron-left"></i>
            Previous
          </button>
          
          <span className="pagination-info">
            Page {pagination.currentPage} of {pagination.totalPages}
          </span>
          
          <button 
            disabled={!pagination.hasNextPage}
            onClick={() => handlePageChange(pagination.currentPage + 1)}
            className="pagination-btn"
          >
            Next
            <i className="fas fa-chevron-right"></i>
          </button>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default QuoteManagement;
