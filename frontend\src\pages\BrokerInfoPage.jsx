import { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';

import { Auth } from 'aws-amplify';
import { setItem, getItem, STORAGE_KEYS } from '../utils/localStorage';
import { trackNavigation } from '../utils/navigationTracker';
import brokerService from '../services/broker.service';
import profileService from '../services/profile.service';
import logger from '../utils/logger';
import BrokerInfoWizard from '../components/BrokerInfoWizard';
import Spinner from '../components/Spinner';
import '../styles/horizontal-layout.css';
import { getLoadingMessage } from '../utils/loadingMessages';
import { showSuccessMessage, showErrorMessage, showInfoMessage } from '../utils/toastNotifications';
import { useForceScrollToTopAuthenticated } from '../utils/scrollToTop';
import { API_BASE_URL } from '../config/api-config';

const BrokerInfoPage = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [loading, setLoading] = useState(false);
  const [userData, setUserData] = useState(null);

  useEffect(() => {
    // Force scroll to top when page loads (SUPER AGGRESSIVE for authenticated pages)
    useForceScrollToTopAuthenticated();

    const fetchUserData = async () => {
      try {
        // First check if we have data in location state
        if (location.state?.userData) {
          console.log('Using user data from location state');
          setUserData(location.state.userData);
          return;
        }

        // Try to fetch from the database first (database-first approach)
        console.log('Fetching broker data from database');
        try {
          const brokerData = await profileService.fetchUserDataByType('broker');
          if (brokerData) {
            console.log('Broker data fetched from database:', brokerData);
            setUserData(brokerData);
            return;
          }
        } catch (dbError) {
          console.warn('Error fetching broker data from database:', dbError);
          // Continue to fallback options
        }

        // Fallback to localStorage
        const storedUserData = getItem(STORAGE_KEYS.USER_DATA, true);
        if (storedUserData) {
          console.log('Using user data from localStorage');
          setUserData(storedUserData);
          return;
        }

        // Last resort: create minimal user data from available localStorage
        const userType = getItem(STORAGE_KEYS.USER_TYPE);
        const cognitoId = getItem(STORAGE_KEYS.COGNITO_ID);

        if (userType === 'broker') {
          // Get basic user data from Cognito
          try {
            const user = await Auth.currentAuthenticatedUser();
            const minimalUserData = {
              firstName: user.attributes.given_name || '',
              lastName: user.attributes.family_name || '',
              email: user.attributes.email || '',
              phoneNumber: user.attributes.phone_number || '',
              cognitoId: user.attributes.sub || cognitoId || '',
              userType: 'broker'
            };
            setUserData(minimalUserData);
            logger.info('Broker info page loaded with minimal user data from Cognito');
          } catch (cognitoError) {
            logger.error('Error getting user from Cognito:', cognitoError);
            // Create very basic user data
            const basicUserData = {
              userType: 'broker',
              cognitoId: cognitoId || '',
              email: '',
            };
            setUserData(basicUserData);
            logger.info('Broker info page loaded with basic user data');
          }
        } else {
          logger.warn('No user data found, redirecting to user type selection');
          navigate('/user-type');
        }
      } catch (error) {
        logger.error('Error fetching broker user data:', error);

        // For testing purposes, create mock user data if all else fails
        const mockUserData = {
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          phoneNumber: '+1555-123-4567',
          userType: 'broker'
        };
        setUserData(mockUserData);
      }
    };

    fetchUserData();

    // Track navigation
    trackNavigation(
      'previous-page',
      '/broker-info',
      'component-mount',
      {
        component: 'BrokerInfoPage',
        userType: 'broker'
      }
    );
  }, [location, navigate]);

  const handleSubmit = async (formData) => {

    try {
      setLoading(true);
      logger.info('Submitting broker profile information');

      // Validate required fields
      if (!formData.companyName || !formData.licenseNumber) {
        showErrorMessage('VALIDATION_FAILED', 'Please fill in all required fields');
        return;
      }

      if (formData.specializations.length === 0) {
        showErrorMessage('VALIDATION_FAILED', 'Please select at least one specialization');
        return;
      }

      // Update user attributes in Cognito to mark profile as complete
      try {
        await Auth.currentAuthenticatedUser().then(async (user) => {
          await Auth.updateUserAttributes(user, {
            'custom:profileCompletion': 'true'
          });
          logger.info('Broker profile completion status updated in Cognito');
        });
      } catch (cognitoError) {
        logger.warn('Could not update Cognito attributes:', cognitoError);
      }

      // Store profile completion in localStorage
      setItem(STORAGE_KEYS.PROFILE_COMPLETION, 'true');
      localStorage.setItem('profileCompletion', 'true');
      localStorage.setItem('brokerInfoCompleted', 'true');

      // Save broker profile data
      try {
        const response = await brokerService.updateProfile(formData);
        if (response.success) {
          logger.info('Broker profile saved successfully');
          // Success message is already shown in the main success handler
        }
      } catch (profileError) {
        logger.warn('Could not save broker profile to backend:', profileError);
        // Continue anyway since this might be in development mode
      }

      // Update profile completion status in MongoDB
      try {
        const response = await fetch(`${API_BASE_URL}/api/users/update-profile-completion`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            email: userData.email,
            profileComplete: true,
            userType: 'broker'
          })
        });

        if (response.ok) {
          logger.info('Successfully updated MongoDB profile completion status for broker');
        } else {
          logger.error('Failed to update MongoDB profile completion status:', await response.text());
        }
      } catch (error) {
        logger.error('Error updating MongoDB profile completion status:', error);
      }



      // Track successful completion
      trackNavigation(
        '/broker-info',
        '/dashboard',
        'form-submission-success',
        {
          action: 'broker-profile-completed',
          userType: 'broker'
        }
      );

      // Show success message
      showSuccessMessage('BROKER_INFO_SAVED');

      // Navigate directly to dashboard (skip invoice upload)
      logger.info('Broker profile completed, navigating to dashboard');
      navigate('/dashboard', {
        state: {
          message: 'Welcome! Your broker profile has been set up successfully. You can now start managing clients and energy deals.'
        },
        replace: true
      });

    } catch (error) {
      logger.error('Error submitting broker profile:', error);
      showErrorMessage('PROFILE_SAVE_FAILED', 'Failed to save broker profile. Please try again.');

      trackNavigation(
        '/broker-info',
        '/broker-info',
        'form-submission-error',
        {
          action: 'broker-profile-error',
          error: error.message
        }
      );
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    logger.info('Broker info form cancelled');
    navigate('/user-type');
  };

  if (!userData) {
    return (
      <div className="fullscreen-container">
        <div className="loading-overlay">
          <div className="loading-content">
            <div className="loading-spinner"></div>
            <p>Loading...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="fullscreen-container">
      {loading && (
        <Spinner
          fullScreen={true}
          message={getLoadingMessage('BROKER_INFO')}
          size="large"
          color="#3498db"
        />
      )}

      <div className="content-wrapper" style={{ width: '100%', maxWidth: '100%' }}>
        <h1 className="page-title">Broker Information</h1>
        <p className="page-subtitle">
          Set up your energy broker profile to start helping clients find the best energy deals
        </p>

        <div className="wizard-fullscreen">
          <BrokerInfoWizard
            onSubmit={handleSubmit}
            onCancel={handleCancel}
            userData={userData}
          />
        </div>
      </div>
    </div>
  );
};

export default BrokerInfoPage;
