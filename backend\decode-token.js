const jwt = require('jsonwebtoken');

// The access token from the test
const accessToken = '***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************';

const idToken = 'eyJraWQiOiJtcWRyQ0Y2cWZFaXVkU3FlSVNJMFhDK3lcL1BUb0JMd01SSFV0XC9oSkNsaTQ9IiwiYWxnIjoiUlMyNTYifQ.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.jx5mzA0e--EcFvzjZh-FjZM9nqnlaExURtxcgCbJt2_E3b1sFho_J0Bb60ujtZQ1dvqzQaDS6w2G_O72w19RGUYBBbOO9odZo8BJjOYqzcHJ3MBs6ydaul3nCbKP2CbBEG5sMVCtPgLnn1k25Z9PF4GjLZ8D6NEhoODi7hMGdL7Csu6nWTm0g_gp-qcLXFt7aFVetH2fWiHaJHWR5FD1O6q_3JF_OzHBRvdMS0R8-rLMuF4usrqktHR_wHZm9E482XssutS0jmKfKv5C8HkCO0nEEb6TdusFC4Qj_K7tcqraUv9vZxMNox3FNiCKe1IM7D7dAnMGzizhrLYEBJiL4w';

console.log('=== ACCESS TOKEN ===');
try {
  const accessDecoded = jwt.decode(accessToken);
  console.log('Access Token Payload:', JSON.stringify(accessDecoded, null, 2));
} catch (error) {
  console.error('Error decoding access token:', error);
}

console.log('\n=== ID TOKEN ===');
try {
  const idDecoded = jwt.decode(idToken);
  console.log('ID Token Payload:', JSON.stringify(idDecoded, null, 2));
} catch (error) {
  console.error('Error decoding ID token:', error);
}
