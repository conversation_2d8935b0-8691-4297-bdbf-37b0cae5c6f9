const express = require('express');
const router = express.Router();
const User = require('../../models/User');
const Contract = require('../../models/Contract');
const QuoteRequest = require('../../models/QuoteRequest');
const Offer = require('../../models/Offer');
const UserActivity = require('../../models/UserActivity');
const Notification = require('../../models/Notification');
const logger = require('../../utils/logger');
const { authMiddleware } = require('../../middleware/auth');

// Apply auth middleware to all admin routes
router.use(authMiddleware);

// Middleware to check if user is admin
const requireAdmin = async (req, res, next) => {
  try {
    const userEmail = req.user?.email;
    if (!userEmail) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    const user = await User.findOne({ email: userEmail });
    if (!user || user.userType !== 'Admin') {
      return res.status(403).json({
        success: false,
        message: 'Admin access required'
      });
    }

    req.adminUser = user;
    next();
  } catch (error) {
    logger.error('Admin middleware error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// Get comprehensive platform analytics
router.get('/overview', requireAdmin, async (req, res) => {
  try {
    const { period = '30days' } = req.query;

    let startDate;
    switch (period) {
      case '7days':
        startDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '30days':
        startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
        break;
      case '90days':
        startDate = new Date(Date.now() - 90 * 24 * 60 * 60 * 1000);
        break;
      case '1year':
        startDate = new Date(Date.now() - 365 * 24 * 60 * 60 * 1000);
        break;
      default:
        startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    }

    const [
      userStats,
      contractStats,
      quoteStats,
      savingsStats,
      conversionStats,
      revenueStats
    ] = await Promise.all([
      getUserStats(startDate),
      getContractStats(startDate),
      getQuoteStats(startDate),
      getSavingsStats(startDate),
      getConversionStats(startDate),
      getRevenueStats(startDate)
    ]);

    res.json({
      success: true,
      data: {
        period,
        userStats,
        contractStats,
        quoteStats,
        savingsStats,
        conversionStats,
        revenueStats
      }
    });

  } catch (error) {
    logger.error('Error fetching analytics overview:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch analytics overview'
    });
  }
});

// Get user analytics
router.get('/users', requireAdmin, async (req, res) => {
  try {
    const { period = '30days' } = req.query;
    let startDate = getStartDate(period);

    const [
      registrationTrend,
      userTypeBreakdown,
      activityStats,
      retentionStats,
      geographicDistribution
    ] = await Promise.all([
      getRegistrationTrend(startDate),
      getUserTypeBreakdown(startDate),
      getUserActivityStats(startDate),
      getUserRetentionStats(startDate),
      getGeographicDistribution()
    ]);

    res.json({
      success: true,
      data: {
        period,
        registrationTrend,
        userTypeBreakdown,
        activityStats,
        retentionStats,
        geographicDistribution
      }
    });

  } catch (error) {
    logger.error('Error fetching user analytics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch user analytics'
    });
  }
});

// Get contract analytics
router.get('/contracts', requireAdmin, async (req, res) => {
  try {
    const { period = '30days' } = req.query;
    let startDate = getStartDate(period);

    const [
      contractTrend,
      contractsByType,
      contractsBySupplier,
      averageContractValue,
      contractDuration,
      statusDistribution
    ] = await Promise.all([
      getContractTrend(startDate),
      getContractsByType(startDate),
      getContractsBySupplier(startDate),
      getAverageContractValue(startDate),
      getContractDuration(startDate),
      getContractStatusDistribution()
    ]);

    res.json({
      success: true,
      data: {
        period,
        contractTrend,
        contractsByType,
        contractsBySupplier,
        averageContractValue,
        contractDuration,
        statusDistribution
      }
    });

  } catch (error) {
    logger.error('Error fetching contract analytics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch contract analytics'
    });
  }
});

// Get broker performance analytics
router.get('/brokers', requireAdmin, async (req, res) => {
  try {
    const { period = '30days' } = req.query;
    let startDate = getStartDate(period);

    const [
      topBrokers,
      brokerPerformance,
      commissionStats,
      clientAcquisition
    ] = await Promise.all([
      getTopBrokers(startDate),
      getBrokerPerformance(startDate),
      getCommissionStats(startDate),
      getClientAcquisition(startDate)
    ]);

    res.json({
      success: true,
      data: {
        period,
        topBrokers,
        brokerPerformance,
        commissionStats,
        clientAcquisition
      }
    });

  } catch (error) {
    logger.error('Error fetching broker analytics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch broker analytics'
    });
  }
});

// Get supplier analytics
router.get('/suppliers', requireAdmin, async (req, res) => {
  try {
    const { period = '30days' } = req.query;
    let startDate = getStartDate(period);

    const [
      topSuppliers,
      supplierPerformance,
      offerStats,
      marketShare
    ] = await Promise.all([
      getTopSuppliers(startDate),
      getSupplierPerformance(startDate),
      getOfferStats(startDate),
      getMarketShare(startDate)
    ]);

    res.json({
      success: true,
      data: {
        period,
        topSuppliers,
        supplierPerformance,
        offerStats,
        marketShare
      }
    });

  } catch (error) {
    logger.error('Error fetching supplier analytics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch supplier analytics'
    });
  }
});

// Helper functions
function getStartDate(period) {
  switch (period) {
    case '7days':
      return new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
    case '30days':
      return new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    case '90days':
      return new Date(Date.now() - 90 * 24 * 60 * 60 * 1000);
    case '1year':
      return new Date(Date.now() - 365 * 24 * 60 * 60 * 1000);
    default:
      return new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
  }
}

async function getUserStats(startDate) {
  const [totalUsers, newUsers, activeUsers, usersByType] = await Promise.all([
    User.countDocuments(),
    User.countDocuments({ createdAt: { $gte: startDate } }),
    User.countDocuments({ 
      lastLogin: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }
    }),
    User.aggregate([
      { $group: { _id: '$userType', count: { $sum: 1 } } }
    ])
  ]);

  return {
    totalUsers,
    newUsers,
    activeUsers,
    usersByType
  };
}

async function getContractStats(startDate) {
  const [totalContracts, newContracts, activeContracts, totalValue] = await Promise.all([
    Contract.countDocuments(),
    Contract.countDocuments({ createdAt: { $gte: startDate } }),
    Contract.countDocuments({ status: 'Active' }),
    Contract.aggregate([
      { $group: { _id: null, total: { $sum: '$contractDetails.estimatedAnnualCost' } } }
    ])
  ]);

  return {
    totalContracts,
    newContracts,
    activeContracts,
    totalValue: totalValue[0]?.total || 0
  };
}

async function getQuoteStats(startDate) {
  const [totalQuotes, newQuotes, conversionRate] = await Promise.all([
    QuoteRequest.countDocuments(),
    QuoteRequest.countDocuments({ createdAt: { $gte: startDate } }),
    calculateConversionRate(startDate)
  ]);

  return {
    totalQuotes,
    newQuotes,
    conversionRate
  };
}

async function getSavingsStats(startDate) {
  const savingsData = await Contract.aggregate([
    { $match: { createdAt: { $gte: startDate } } },
    {
      $group: {
        _id: null,
        totalSavings: { $sum: '$estimatedSavings.amount' },
        averageSavings: { $avg: '$estimatedSavings.amount' },
        count: { $sum: 1 }
      }
    }
  ]);

  return savingsData[0] || { totalSavings: 0, averageSavings: 0, count: 0 };
}

async function getConversionStats(startDate) {
  const [quotes, contracts] = await Promise.all([
    QuoteRequest.countDocuments({ createdAt: { $gte: startDate } }),
    Contract.countDocuments({ createdAt: { $gte: startDate } })
  ]);

  const conversionRate = quotes > 0 ? ((contracts / quotes) * 100).toFixed(1) : 0;
  
  return {
    quotes,
    contracts,
    conversionRate: parseFloat(conversionRate)
  };
}

async function getRevenueStats(startDate) {
  const revenueData = await Contract.aggregate([
    { $match: { createdAt: { $gte: startDate } } },
    {
      $group: {
        _id: null,
        totalRevenue: { $sum: '$contractDetails.estimatedAnnualCost' },
        averageRevenue: { $avg: '$contractDetails.estimatedAnnualCost' },
        count: { $sum: 1 }
      }
    }
  ]);

  return revenueData[0] || { totalRevenue: 0, averageRevenue: 0, count: 0 };
}

async function calculateConversionRate(startDate) {
  const [quotes, contracts] = await Promise.all([
    QuoteRequest.countDocuments({ createdAt: { $gte: startDate } }),
    Contract.countDocuments({ createdAt: { $gte: startDate } })
  ]);

  return quotes > 0 ? ((contracts / quotes) * 100).toFixed(1) : 0;
}

async function getRegistrationTrend(startDate) {
  return await User.aggregate([
    { $match: { createdAt: { $gte: startDate } } },
    {
      $group: {
        _id: {
          year: { $year: '$createdAt' },
          month: { $month: '$createdAt' },
          day: { $dayOfMonth: '$createdAt' }
        },
        count: { $sum: 1 }
      }
    },
    { $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 } }
  ]);
}

async function getUserTypeBreakdown(startDate) {
  return await User.aggregate([
    { $match: { createdAt: { $gte: startDate } } },
    { $group: { _id: '$userType', count: { $sum: 1 } } }
  ]);
}

async function getUserActivityStats(startDate) {
  return await UserActivity.aggregate([
    { $match: { createdAt: { $gte: startDate } } },
    { $group: { _id: '$activityType', count: { $sum: 1 } } }
  ]);
}

async function getUserRetentionStats(startDate) {
  const activeUsers = await User.countDocuments({
    lastLogin: { $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) }
  });
  
  const totalUsers = await User.countDocuments();
  
  return {
    activeUsers,
    totalUsers,
    retentionRate: totalUsers > 0 ? ((activeUsers / totalUsers) * 100).toFixed(1) : 0
  };
}

async function getGeographicDistribution() {
  // This would need to be implemented based on user profile data
  return [];
}

async function getContractTrend(startDate) {
  return await Contract.aggregate([
    { $match: { createdAt: { $gte: startDate } } },
    {
      $group: {
        _id: {
          year: { $year: '$createdAt' },
          month: { $month: '$createdAt' },
          day: { $dayOfMonth: '$createdAt' }
        },
        count: { $sum: 1 },
        totalValue: { $sum: '$contractDetails.estimatedAnnualCost' }
      }
    },
    { $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 } }
  ]);
}

async function getContractsByType(startDate) {
  return await Contract.aggregate([
    { $match: { createdAt: { $gte: startDate } } },
    { $group: { _id: '$contractDetails.energyType', count: { $sum: 1 } } }
  ]);
}

async function getContractsBySupplier(startDate) {
  return await Contract.aggregate([
    { $match: { createdAt: { $gte: startDate } } },
    {
      $group: {
        _id: '$supplierId',
        count: { $sum: 1 },
        totalValue: { $sum: '$contractDetails.estimatedAnnualCost' }
      }
    },
    {
      $lookup: {
        from: 'users',
        localField: '_id',
        foreignField: '_id',
        as: 'supplier'
      }
    },
    { $unwind: '$supplier' },
    { $sort: { count: -1 } },
    { $limit: 10 }
  ]);
}

async function getAverageContractValue(startDate) {
  const result = await Contract.aggregate([
    { $match: { createdAt: { $gte: startDate } } },
    {
      $group: {
        _id: null,
        average: { $avg: '$contractDetails.estimatedAnnualCost' },
        min: { $min: '$contractDetails.estimatedAnnualCost' },
        max: { $max: '$contractDetails.estimatedAnnualCost' }
      }
    }
  ]);

  return result[0] || { average: 0, min: 0, max: 0 };
}

async function getContractDuration(startDate) {
  return await Contract.aggregate([
    { $match: { createdAt: { $gte: startDate } } },
    {
      $addFields: {
        durationMonths: {
          $divide: [
            { $subtract: ['$endDate', '$startDate'] },
            1000 * 60 * 60 * 24 * 30
          ]
        }
      }
    },
    {
      $group: {
        _id: null,
        averageDuration: { $avg: '$durationMonths' },
        minDuration: { $min: '$durationMonths' },
        maxDuration: { $max: '$durationMonths' }
      }
    }
  ]);
}

async function getContractStatusDistribution() {
  return await Contract.aggregate([
    { $group: { _id: '$status', count: { $sum: 1 } } }
  ]);
}

async function getTopBrokers(startDate) {
  return await Contract.aggregate([
    { $match: { createdAt: { $gte: startDate }, brokerId: { $exists: true } } },
    {
      $group: {
        _id: '$brokerId',
        contractsCount: { $sum: 1 },
        totalValue: { $sum: '$contractDetails.estimatedAnnualCost' },
        totalCommissions: { $sum: '$commissions.brokerCommission' }
      }
    },
    {
      $lookup: {
        from: 'users',
        localField: '_id',
        foreignField: '_id',
        as: 'broker'
      }
    },
    { $unwind: '$broker' },
    { $sort: { contractsCount: -1 } },
    { $limit: 10 }
  ]);
}

async function getBrokerPerformance(startDate) {
  return await Contract.aggregate([
    { $match: { createdAt: { $gte: startDate }, brokerId: { $exists: true } } },
    {
      $group: {
        _id: {
          year: { $year: '$createdAt' },
          month: { $month: '$createdAt' }
        },
        brokersActive: { $addToSet: '$brokerId' },
        totalContracts: { $sum: 1 },
        totalValue: { $sum: '$contractDetails.estimatedAnnualCost' }
      }
    },
    {
      $addFields: {
        brokersCount: { $size: '$brokersActive' }
      }
    },
    { $sort: { '_id.year': 1, '_id.month': 1 } }
  ]);
}

async function getCommissionStats(startDate) {
  const result = await Contract.aggregate([
    { $match: { createdAt: { $gte: startDate }, brokerId: { $exists: true } } },
    {
      $group: {
        _id: null,
        totalCommissions: { $sum: '$commissions.brokerCommission' },
        paidCommissions: {
          $sum: {
            $cond: [
              '$commissions.commissionPaid',
              '$commissions.brokerCommission',
              0
            ]
          }
        },
        averageCommission: { $avg: '$commissions.brokerCommission' },
        count: { $sum: 1 }
      }
    }
  ]);

  return result[0] || { totalCommissions: 0, paidCommissions: 0, averageCommission: 0, count: 0 };
}

async function getClientAcquisition(startDate) {
  return await Contract.aggregate([
    { $match: { createdAt: { $gte: startDate }, brokerId: { $exists: true } } },
    {
      $group: {
        _id: {
          brokerId: '$brokerId',
          year: { $year: '$createdAt' },
          month: { $month: '$createdAt' }
        },
        newClients: { $addToSet: '$userId' }
      }
    },
    {
      $addFields: {
        clientCount: { $size: '$newClients' }
      }
    },
    {
      $group: {
        _id: {
          year: '$_id.year',
          month: '$_id.month'
        },
        totalNewClients: { $sum: '$clientCount' },
        brokersActive: { $addToSet: '$_id.brokerId' }
      }
    },
    { $sort: { '_id.year': 1, '_id.month': 1 } }
  ]);
}

async function getTopSuppliers(startDate) {
  return await Contract.aggregate([
    { $match: { createdAt: { $gte: startDate } } },
    {
      $group: {
        _id: '$supplierId',
        contractsCount: { $sum: 1 },
        totalValue: { $sum: '$contractDetails.estimatedAnnualCost' }
      }
    },
    {
      $lookup: {
        from: 'users',
        localField: '_id',
        foreignField: '_id',
        as: 'supplier'
      }
    },
    { $unwind: '$supplier' },
    { $sort: { contractsCount: -1 } },
    { $limit: 10 }
  ]);
}

async function getSupplierPerformance(startDate) {
  return await Contract.aggregate([
    { $match: { createdAt: { $gte: startDate } } },
    {
      $group: {
        _id: {
          supplierId: '$supplierId',
          year: { $year: '$createdAt' },
          month: { $month: '$createdAt' }
        },
        contractsCount: { $sum: 1 },
        totalValue: { $sum: '$contractDetails.estimatedAnnualCost' }
      }
    },
    {
      $group: {
        _id: {
          year: '$_id.year',
          month: '$_id.month'
        },
        suppliersActive: { $addToSet: '$_id.supplierId' },
        totalContracts: { $sum: '$contractsCount' },
        totalValue: { $sum: '$totalValue' }
      }
    },
    {
      $addFields: {
        suppliersCount: { $size: '$suppliersActive' }
      }
    },
    { $sort: { '_id.year': 1, '_id.month': 1 } }
  ]);
}

async function getOfferStats(startDate) {
  const [totalOffers, newOffers, acceptedOffers] = await Promise.all([
    Offer.countDocuments(),
    Offer.countDocuments({ createdAt: { $gte: startDate } }),
    Offer.countDocuments({
      createdAt: { $gte: startDate },
      status: 'Accepted'
    })
  ]);

  const acceptanceRate = newOffers > 0 ? ((acceptedOffers / newOffers) * 100).toFixed(1) : 0;

  return {
    totalOffers,
    newOffers,
    acceptedOffers,
    acceptanceRate: parseFloat(acceptanceRate)
  };
}

async function getMarketShare(startDate) {
  return await Contract.aggregate([
    { $match: { createdAt: { $gte: startDate } } },
    {
      $group: {
        _id: '$supplierId',
        contractsCount: { $sum: 1 },
        totalValue: { $sum: '$contractDetails.estimatedAnnualCost' }
      }
    },
    {
      $lookup: {
        from: 'users',
        localField: '_id',
        foreignField: '_id',
        as: 'supplier'
      }
    },
    { $unwind: '$supplier' },
    {
      $project: {
        supplierName: { $concat: ['$supplier.firstName', ' ', '$supplier.lastName'] },
        contractsCount: 1,
        totalValue: 1
      }
    },
    { $sort: { totalValue: -1 } }
  ]);
}

module.exports = router;
