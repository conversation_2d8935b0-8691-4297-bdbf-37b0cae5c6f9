import React, { useState, useEffect } from 'react';
import SupplierCompanyInfoStep from './wizard-steps/SupplierCompanyInfoStep';
import SupplierServicesStep from './wizard-steps/SupplierServicesStep';
import SupplierAreasStep from './wizard-steps/SupplierAreasStep';
import SupplierConfirmationStep from './wizard-steps/SupplierConfirmationStep';
import StepperProgress from './StepperProgress';
import { useForceScrollToTopAuthenticated } from '../utils/scrollToTop';
import '../styles/stepper.css';

const SupplierInfoWizard = ({ onSubmit, onCancel, userData }) => {
  const [currentStep, setCurrentStep] = useState(1);

  // Initialize form data with default values
  const [formData, setFormData] = useState({
    companyName: '',
    licenseNumber: '',
    website: '',
    phone: '',
    vatNumber: '',
    businessDescription: '',
    companyAddress: {
      street: '',
      city: '',
      postalCode: '',
      country: 'France'
    },
    energyTypesProvided: [],
    serviceAreas: [],
    contractTypes: []
  });

  const totalSteps = 3;
  const stepLabels = ['Company Info', 'Energy & Meter', 'Confirmation'];

  // Pre-populate form with user data if available
  useEffect(() => {
    if (userData) {
      setFormData(prev => ({
        ...prev,
        // Pre-populate any available data from userData
        companyName: userData.companyName || '',
        phone: userData.phoneNumber || '',
        // Add other mappings as needed
      }));
    }
  }, [userData]);

  // Force scroll to top whenever step changes
  useEffect(() => {
    console.log('🔄 SupplierInfoWizard: Step changed to:', currentStep);
    useForceScrollToTopAuthenticated();
  }, [currentStep]);

  const handleNextStep = () => {
    console.log('🔄 SupplierInfoWizard: Moving to next step');
    setCurrentStep(prev => Math.min(prev + 1, totalSteps));

    // Force scroll to top when moving to next step
    setTimeout(() => {
      useForceScrollToTopAuthenticated();
    }, 50); // Small delay to ensure state update completes
  };

  const handlePrevStep = () => {
    console.log('🔄 SupplierInfoWizard: Moving to previous step');
    setCurrentStep(prev => Math.max(prev - 1, 1));

    // Force scroll to top when moving to previous step
    setTimeout(() => {
      useForceScrollToTopAuthenticated();
    }, 50); // Small delay to ensure state update completes
  };

  const handleChange = (name, value) => {
    if (name.includes('.')) {
      // Handle nested object properties
      const [parent, child] = name.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  const handleMultiSelectChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: prev[field].includes(value)
        ? prev[field].filter(item => item !== value)
        : [...prev[field], value]
    }));
  };

  const handleSubmit = () => {
    onSubmit(formData);
  };

  return (
    <div className="stepper-container">
      <div className="stepper-content">
        <StepperProgress
          currentStep={currentStep}
          steps={stepLabels}
        />

        <div className="stepper-form">
        {currentStep === 1 && (
          <SupplierCompanyInfoStep
            formData={formData}
            onChange={handleChange}
            onNext={handleNextStep}
            onCancel={onCancel}
          />
        )}

        {currentStep === 2 && (
          <SupplierServicesStep
            formData={formData}
            onChange={handleChange}
            onMultiSelectChange={handleMultiSelectChange}
            onNext={handleNextStep}
            onPrev={handlePrevStep}
          />
        )}

        {currentStep === 3 && (
          <SupplierConfirmationStep
            formData={formData}
            onChange={handleChange}
            onSubmit={handleSubmit}
            onPrev={handlePrevStep}
            onCancel={onCancel}
          />
        )}
        </div>
      </div>
    </div>
  );
};

export default SupplierInfoWizard;
