#!/bin/bash
# Script to build and run the application in a Linux container

echo "Building and running the application in Docker containers..."

# Build and start the containers
docker-compose up --build -d

# Check if the containers are running
if [ $? -eq 0 ]; then
  echo "Application is now running!"
  echo "Frontend: http://localhost:8080"
  echo "Backend: http://localhost:3000"
  echo "MongoDB: mongodb://localhost:27017"
else
  echo "Failed to start the application."
  exit 1
fi
