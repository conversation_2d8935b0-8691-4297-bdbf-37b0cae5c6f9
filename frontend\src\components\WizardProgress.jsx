import React from 'react';

const WizardProgress = ({ currentStep, stepLabels = [] }) => {
  // Default step labels if none provided
  const defaultLabels = ['Personal Info', 'Address & Meter', 'Confirmation'];
  const labels = stepLabels.length ? stepLabels : defaultLabels;

  return (
    <div className="wizard-container">
      <div className="stepper-wrapper">
        {/* Step 1 */}
        <div className={`stepper-item ${currentStep >= 1 ? 'completed' : ''} ${currentStep === 1 ? 'active' : ''}`}>
          <div className="step-counter">
            {currentStep > 1 ? (
              <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="currentColor" viewBox="0 0 16 16">
                <path d="M13.854 3.646a.5.5 0 0 1 0 .708l-7 7a.5.5 0 0 1-.708 0l-3.5-3.5a.5.5 0 1 1 .708-.708L6.5 10.293l6.646-6.647a.5.5 0 0 1 .708 0z"/>
              </svg>
            ) : 1}
          </div>
          <div className="step-name">{labels[0]}</div>
        </div>

        {/* Step 2 */}
        <div className={`stepper-item ${currentStep >= 2 ? 'completed' : ''} ${currentStep === 2 ? 'active' : ''}`}>
          <div className="step-counter">
            {currentStep > 2 ? (
              <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="currentColor" viewBox="0 0 16 16">
                <path d="M13.854 3.646a.5.5 0 0 1 0 .708l-7 7a.5.5 0 0 1-.708 0l-3.5-3.5a.5.5 0 1 1 .708-.708L6.5 10.293l6.646-6.647a.5.5 0 0 1 .708 0z"/>
              </svg>
            ) : 2}
          </div>
          <div className="step-name">{labels[1]}</div>
        </div>

        {/* Step 3 */}
        <div className={`stepper-item ${currentStep >= 3 ? 'completed' : ''} ${currentStep === 3 ? 'active' : ''}`}>
          <div className="step-counter">
            {currentStep > 3 ? (
              <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="currentColor" viewBox="0 0 16 16">
                <path d="M13.854 3.646a.5.5 0 0 1 0 .708l-7 7a.5.5 0 0 1-.708 0l-3.5-3.5a.5.5 0 1 1 .708-.708L6.5 10.293l6.646-6.647a.5.5 0 0 1 .708 0z"/>
              </svg>
            ) : 3}
          </div>
          <div className="step-name">{labels[2]}</div>
        </div>
      </div>
    </div>
  );
};

export default WizardProgress;
