const express = require('express');
const router = express.Router();
const DocumentTemplate = require('../../models/DocumentTemplate');
const User = require('../../models/User');
const UserActivity = require('../../models/UserActivity');
const logger = require('../../utils/logger');
const { authMiddleware } = require('../../middleware/auth');
const multer = require('multer');
const path = require('path');

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, 'uploads/templates/');
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({ 
  storage: storage,
  fileFilter: function (req, file, cb) {
    // Accept only specific file types
    const allowedTypes = ['.pdf', '.docx', '.html', '.txt'];
    const ext = path.extname(file.originalname).toLowerCase();
    if (allowedTypes.includes(ext)) {
      cb(null, true);
    } else {
      cb(new Error('Invalid file type. Only PDF, DOCX, HTML, and TXT files are allowed.'));
    }
  },
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB limit
  }
});

// Apply auth middleware to all admin routes
router.use(authMiddleware);

// Middleware to check if user is admin
const requireAdmin = async (req, res, next) => {
  try {
    const userEmail = req.user?.email;
    if (!userEmail) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    const user = await User.findOne({ email: userEmail });
    if (!user || user.userType !== 'Admin') {
      return res.status(403).json({
        success: false,
        message: 'Admin access required'
      });
    }

    req.adminUser = user;
    next();
  } catch (error) {
    logger.error('Admin middleware error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// Get all document templates
router.get('/', requireAdmin, async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      templateType,
      status,
      search
    } = req.query;

    const filter = {};
    
    if (templateType && templateType !== 'all') {
      filter.templateType = templateType;
    }
    
    if (status && status !== 'all') {
      filter.status = status;
    }
    
    if (search) {
      filter.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }

    const skip = (parseInt(page) - 1) * parseInt(limit);

    const templates = await DocumentTemplate.find(filter)
      .populate('createdBy', 'firstName lastName email')
      .populate('lastModifiedBy', 'firstName lastName email')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    const totalTemplates = await DocumentTemplate.countDocuments(filter);
    const totalPages = Math.ceil(totalTemplates / parseInt(limit));

    res.json({
      success: true,
      data: {
        templates,
        pagination: {
          currentPage: parseInt(page),
          totalPages,
          totalTemplates,
          hasNextPage: parseInt(page) < totalPages,
          hasPrevPage: parseInt(page) > 1
        }
      }
    });

  } catch (error) {
    logger.error('Error fetching document templates:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch document templates'
    });
  }
});

// Create new document template
router.post('/', requireAdmin, upload.single('templateFile'), async (req, res) => {
  try {
    const {
      name,
      description,
      templateType,
      content,
      variables,
      settings
    } = req.body;

    // Parse JSON fields
    let parsedVariables = [];
    let parsedSettings = {};

    try {
      if (variables) parsedVariables = JSON.parse(variables);
      if (settings) parsedSettings = JSON.parse(settings);
    } catch (parseError) {
      return res.status(400).json({
        success: false,
        message: 'Invalid JSON format in variables or settings'
      });
    }

    const templateData = {
      name,
      description,
      templateType,
      content: content || '',
      variables: parsedVariables,
      settings: parsedSettings,
      createdBy: req.adminUser._id,
      lastModifiedBy: req.adminUser._id,
      status: 'Active'
    };

    // If file was uploaded, store file information
    if (req.file) {
      templateData.fileInfo = {
        originalName: req.file.originalname,
        fileName: req.file.filename,
        filePath: req.file.path,
        fileSize: req.file.size,
        mimeType: req.file.mimetype
      };
    }

    const template = new DocumentTemplate(templateData);
    await template.save();

    // Log activity
    await UserActivity.logActivity({
      userId: req.adminUser._id,
      activityType: 'Other',
      description: `Created document template: ${name}`,
      details: {
        templateId: template._id,
        templateType,
        hasFile: !!req.file
      },
      severity: 'Normal',
      isSystemGenerated: true
    });

    logger.info(`Admin ${req.adminUser.email} created document template: ${name}`);

    res.json({
      success: true,
      message: 'Document template created successfully',
      data: template
    });

  } catch (error) {
    logger.error('Error creating document template:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create document template'
    });
  }
});

// Get specific template details
router.get('/:templateId', requireAdmin, async (req, res) => {
  try {
    const { templateId } = req.params;

    const template = await DocumentTemplate.findById(templateId)
      .populate('createdBy', 'firstName lastName email')
      .populate('lastModifiedBy', 'firstName lastName email');

    if (!template) {
      return res.status(404).json({
        success: false,
        message: 'Document template not found'
      });
    }

    res.json({
      success: true,
      data: template
    });

  } catch (error) {
    logger.error('Error fetching template details:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch template details'
    });
  }
});

// Update document template
router.put('/:templateId', requireAdmin, upload.single('templateFile'), async (req, res) => {
  try {
    const { templateId } = req.params;
    const {
      name,
      description,
      templateType,
      content,
      variables,
      settings,
      status
    } = req.body;

    const template = await DocumentTemplate.findById(templateId);
    if (!template) {
      return res.status(404).json({
        success: false,
        message: 'Document template not found'
      });
    }

    // Parse JSON fields
    let parsedVariables = template.variables;
    let parsedSettings = template.settings;

    try {
      if (variables) parsedVariables = JSON.parse(variables);
      if (settings) parsedSettings = JSON.parse(settings);
    } catch (parseError) {
      return res.status(400).json({
        success: false,
        message: 'Invalid JSON format in variables or settings'
      });
    }

    // Update template data
    const updateData = {
      name: name || template.name,
      description: description || template.description,
      templateType: templateType || template.templateType,
      content: content !== undefined ? content : template.content,
      variables: parsedVariables,
      settings: parsedSettings,
      lastModifiedBy: req.adminUser._id,
      lastModifiedAt: new Date()
    };

    if (status) {
      updateData.status = status;
    }

    // If new file was uploaded, update file information
    if (req.file) {
      updateData.fileInfo = {
        originalName: req.file.originalname,
        fileName: req.file.filename,
        filePath: req.file.path,
        fileSize: req.file.size,
        mimeType: req.file.mimetype
      };
    }

    const updatedTemplate = await DocumentTemplate.findByIdAndUpdate(
      templateId,
      updateData,
      { new: true }
    );

    // Log activity
    await UserActivity.logActivity({
      userId: req.adminUser._id,
      activityType: 'TemplateUpdated',
      description: `Updated document template: ${updatedTemplate.name}`,
      details: {
        templateId,
        changes: Object.keys(updateData),
        hasNewFile: !!req.file
      },
      severity: 'Normal',
      isSystemGenerated: true
    });

    logger.info(`Admin ${req.adminUser.email} updated document template: ${updatedTemplate.name}`);

    res.json({
      success: true,
      message: 'Document template updated successfully',
      data: updatedTemplate
    });

  } catch (error) {
    logger.error('Error updating document template:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update document template'
    });
  }
});

// Delete document template
router.delete('/:templateId', requireAdmin, async (req, res) => {
  try {
    const { templateId } = req.params;

    const template = await DocumentTemplate.findByIdAndDelete(templateId);

    if (!template) {
      return res.status(404).json({
        success: false,
        message: 'Document template not found'
      });
    }

    // Log activity
    await UserActivity.logActivity({
      userId: req.adminUser._id,
      activityType: 'TemplateDeleted',
      description: `Deleted document template: ${template.name}`,
      details: {
        templateId,
        templateType: template.templateType
      },
      severity: 'High',
      isSystemGenerated: true
    });

    logger.info(`Admin ${req.adminUser.email} deleted document template: ${template.name}`);

    res.json({
      success: true,
      message: 'Document template deleted successfully'
    });

  } catch (error) {
    logger.error('Error deleting document template:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete document template'
    });
  }
});

module.exports = router;
