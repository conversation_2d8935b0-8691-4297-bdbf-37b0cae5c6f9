# Sample Energy Invoices for Testing

This folder contains sample energy invoices from different providers that can be used to test the invoice upload and data extraction functionality.

## Sample Invoices

### 1. EDF Invoice
- **Provider**: EDF (Électricité de France)
- **Invoice Number**: EDF-2023-12345
- **Date**: 15/05/2023
- **PDL Number**: 12345678901234
- **Amount**: 140.27 €
- **Consumption**: 500 kWh

### 2. Engie Invoice
- **Provider**: ENGIE
- **Invoice Number**: ENGIE-2023-67890
- **Date**: 10/06/2023
- **PDL Number**: 98765432109876
- **PCE Number (Gas)**: 87654321098765
- **Amount**: 158.38 €
- **Consumption (Electricity)**: 330 kWh
- **Consumption (Gas)**: 560 kWh

### 3. TotalEnergies Invoice
- **Provider**: TotalEnergies
- **Invoice Number**: TE-2023-54321
- **Date**: 05/07/2023
- **PDL Number**: 56789012345678
- **Amount**: 122.18 €
- **Consumption**: 450 kWh

## File Formats

Each invoice is available in multiple formats:
- **HTML**: Source files used to generate the other formats
- **PDF**: For testing PDF uploads
- **PNG**: For testing image uploads

## How to Generate PDF and PNG Files

To create PDF and PNG versions of the sample invoices:

### Creating PDF Files

1. Open the HTML files in a web browser:
   - `edf-sample-invoice.html`
   - `engie-sample-invoice.html`
   - `totalenergies-sample-invoice.html`

2. Use the browser's print function (Ctrl+P or Cmd+P)

3. Select "Save as PDF" as the destination

4. Save each file to the `pdf` folder with the appropriate name:
   - `edf-sample-invoice.pdf`
   - `engie-sample-invoice.pdf`
   - `totalenergies-sample-invoice.pdf`

### Creating PNG Files

1. Open the HTML files in a web browser

2. Take a screenshot of each invoice displayed in the browser
   - You can use the Windows Snipping Tool, Snip & Sketch, or any screenshot utility

3. Save each screenshot to the `png` folder with the appropriate name:
   - `edf-sample-invoice.png`
   - `engie-sample-invoice.png`
   - `totalenergies-sample-invoice.png`

## Testing Instructions

1. Use these sample invoices to test the file upload functionality
2. Verify that the AWS Textract extraction correctly identifies:
   - Invoice number
   - Invoice date
   - Provider name
   - PDL/PRM/PCE number
   - Amount
   - Consumption

## Expected Extraction Results

When uploading these invoices, the system should automatically extract the key fields listed above for each invoice. The extraction results should match the values specified in this README.
