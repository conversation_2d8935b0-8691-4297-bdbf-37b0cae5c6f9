import React from 'react';
import styled from 'styled-components';
import AuthPageContainer from './AuthPageContainer';

const Title = styled.h1`
  font-size: 32px;
  font-weight: 900;
  margin-bottom: 5px;
  color: #000000;
  text-shadow: 1px 1px 3px rgba(255, 255, 255, 0.8);
  text-align: center;

  @media (max-width: 768px) {
    font-size: 28px;
  }
`;

const SubTitle = styled.p`
  font-size: 17px;
  font-weight: 600;
  color: #000000;
  text-shadow: 0.5px 0.5px 2px rgba(255, 255, 255, 0.8);
  margin: 0 0 20px 0;
  text-align: center;

  @media (max-width: 768px) {
    font-size: 16px;
  }
`;

const FormElementsContainer = styled.div`
  padding: 0;
  margin-bottom: 10px;
  background-color: transparent;
`;

const InputWrapper = styled.div`
  margin-bottom: 15px;
  display: flex;
  flex-direction: column;
  width: 100%;
`;

const InputContainer = styled.div`
  background-color: white;
  border-radius: 10px;
  display: flex;
  align-items: center;
  padding: 18px 20px;
  border: none;
  margin-bottom: 8px;
  width: 100%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
`;

const IconSpan = styled.span`
  margin-right: 10px;
  color: #000;
`;

const StyledInput = styled.input`
  border: none;
  outline: none;
  width: 100%;
  background-color: transparent;
  font-size: 16px;
  color: #000;
  padding: 0;
  margin: 0;

  &::placeholder {
    color: rgba(0, 0, 0, 0.6);
  }
`;

const VerifyButton = styled.button`
  background-color: #1E3D5C;
  color: white;
  border: none;
  border-radius: 10px;
  padding: 18px;
  width: 100%;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  margin-bottom: 15px;
  margin-top: 10px;
  text-transform: uppercase;
  transition: all 0.3s;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);

  &:hover {
    background-color: #15304a;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.25);
  }
`;

const ResendButton = styled.button`
  background: transparent;
  border: none;
  color: #000;
  cursor: pointer;
  font-size: 14px;
  margin-top: 10px;
  text-decoration: underline;
  width: 100%;
  text-align: center;
  padding: 5px;

  &:hover {
    color: #333;
  }
`;

const VerificationForm = ({ verificationCode, setVerificationCode, handleVerification, handleResendCode }) => {
  return (
    <AuthPageContainer>
      <form onSubmit={handleVerification}>
        <Title>Verify Your Account</Title>
        <SubTitle>We've sent a verification code to your email</SubTitle>

        <FormElementsContainer>
          <InputWrapper>
            <InputContainer>
              <IconSpan>
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" viewBox="0 0 16 16">
                  <path d="M8 1a2 2 0 0 1 2 2v4H6V3a2 2 0 0 1 2-2zm3 6V3a3 3 0 0 0-6 0v4a2 2 0 0 0-2 2v5a2 2 0 0 0 2 2h6a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2z"/>
                </svg>
              </IconSpan>
              <StyledInput
                type="text"
                id="verificationCode"
                placeholder="Enter verification code"
                value={verificationCode}
                onChange={(e) => setVerificationCode(e.target.value)}
                required
              />
            </InputContainer>
          </InputWrapper>

          <VerifyButton type="submit">
            Verify Account
          </VerifyButton>

          <ResendButton type="button" onClick={handleResendCode}>
            Resend Code
          </ResendButton>
        </FormElementsContainer>
      </form>
    </AuthPageContainer>
  );
};

export default VerificationForm;
