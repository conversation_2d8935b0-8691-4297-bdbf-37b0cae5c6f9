/**
 * Centralized loading messages for different pages and actions
 */

export const LOADING_MESSAGES = {
  // Authentication
  LOGIN: 'Signing you in...',
  SIGNUP: 'Creating your account...',
  VERIFICATION: 'Verifying your account...',
  LOGOUT: 'Signing you out...',
  CHECKING_AUTH: 'Checking your authentication...',
  FORGOT_PASSWORD: 'Sending reset instructions...',
  RESET_PASSWORD: 'Resetting your password...',

  // Profile & User Info
  LOADING_PROFILE: 'Loading your profile...',
  SAVING_PROFILE: 'Saving your profile...',
  UPDATING_PROFILE: 'Updating your profile information...',
  SAVING_USER_TYPE: 'Saving your account type...',
  INDIVIDUAL_INFO: 'Saving your personal information...',
  PROFESSIONAL_INFO: 'Saving your professional information...',
  BROKER_INFO: 'Saving your broker information...',
  SUPPLIER_INFO: 'Saving your supplier information...',
  USER_TYPE_SELECTION: 'Loading your account information...',

  // Dashboard
  LOADING_DASHBOARD: 'Loading your dashboard...',
  LOADING_STATS: 'Loading dashboard statistics...',
  LOADING_OVERVIEW: 'Loading overview data...',

  // Invoice & File Upload
  UPLOADING_INVOICE: 'Uploading your energy bill...',
  PROCESSING_INVOICE: 'Processing your energy bill...',
  EXTRACTING_DATA: 'Extracting bill information...',
  ANALYZING_BILL: 'Analyzing your energy usage...',
  UPLOADING_FILE: 'Uploading file...',
  PROCESSING_FILE: 'Processing file...',

  // Offers & Contracts
  LOADING_OFFERS: 'Loading available offers...',
  CREATING_OFFER: 'Creating new offer...',
  ACCEPTING_OFFER: 'Processing your offer acceptance...',
  LOADING_CONTRACTS: 'Loading your contracts...',
  GENERATING_CONTRACT: 'Generating contract...',

  // Clients & Broker Operations
  LOADING_CLIENTS: 'Loading client information...',
  ADDING_CLIENT: 'Adding new client...',
  UPDATING_CLIENT: 'Updating client information...',
  LOADING_DEALS: 'Loading deals...',
  PROCESSING_DEAL: 'Processing deal...',

  // Supplier Operations
  LOADING_SUPPLIER_DATA: 'Loading supplier dashboard...',
  CREATING_TARIFF: 'Creating new tariff...',
  UPDATING_TARIFF: 'Updating tariff...',
  LOADING_CUSTOMERS: 'Loading customer data...',

  // Analytics & Reports
  LOADING_ANALYTICS: 'Loading analytics data...',
  GENERATING_REPORT: 'Generating report...',
  EXPORTING_DATA: 'Exporting data...',

  // General
  LOADING: 'Loading...',
  SAVING: 'Saving...',
  PROCESSING: 'Processing...',
  SUBMITTING: 'Submitting...',
  DELETING: 'Deleting...',
  UPDATING: 'Updating...',
  FETCHING_DATA: 'Fetching data...',
  INITIALIZING: 'Initializing...',
  REDIRECTING: 'Redirecting...',
  VALIDATING: 'Validating...',

  // Error Recovery
  RETRYING: 'Retrying...',
  REFRESHING: 'Refreshing...',
  RECONNECTING: 'Reconnecting...',

  // Page Specific
  USER_TYPE_SELECTION: 'Loading user type selection...',
  FIRST_INVOICE_UPLOAD: 'Preparing invoice upload...',
  SETTINGS: 'Loading settings...',
  NOTIFICATIONS: 'Loading notifications...',
  PROFILE_PAGE: 'Loading profile page...',
  ACCOUNT_VERIFICATION: 'Loading your account...'
};

/**
 * Get loading message by key with fallback
 * @param {string} key - The message key
 * @param {string} fallback - Fallback message if key not found
 * @returns {string} Loading message
 */
export const getLoadingMessage = (key, fallback = 'Loading...') => {
  return LOADING_MESSAGES[key] || fallback;
};

/**
 * Get contextual loading message based on page and action
 * @param {string} page - Current page/route
 * @param {string} action - Current action being performed
 * @returns {string} Contextual loading message
 */
export const getContextualLoadingMessage = (page, action = 'loading') => {
  const pageActionMap = {
    '/login': {
      loading: LOADING_MESSAGES.LOGIN,
      submitting: LOADING_MESSAGES.LOGIN
    },
    '/signup': {
      loading: LOADING_MESSAGES.SIGNUP,
      submitting: LOADING_MESSAGES.SIGNUP,
      verifying: LOADING_MESSAGES.VERIFICATION
    },
    '/forgot-password': {
      loading: LOADING_MESSAGES.FORGOT_PASSWORD,
      submitting: LOADING_MESSAGES.FORGOT_PASSWORD
    },
    '/user-type': {
      loading: LOADING_MESSAGES.USER_TYPE_SELECTION
    },
    '/individual-info': {
      loading: LOADING_MESSAGES.LOADING_PROFILE,
      submitting: LOADING_MESSAGES.INDIVIDUAL_INFO,
      saving: LOADING_MESSAGES.INDIVIDUAL_INFO
    },
    '/professional-info': {
      loading: LOADING_MESSAGES.LOADING_PROFILE,
      submitting: LOADING_MESSAGES.PROFESSIONAL_INFO,
      saving: LOADING_MESSAGES.PROFESSIONAL_INFO
    },
    '/broker-info': {
      loading: LOADING_MESSAGES.LOADING_PROFILE,
      submitting: LOADING_MESSAGES.BROKER_INFO,
      saving: LOADING_MESSAGES.BROKER_INFO
    },
    '/supplier-info': {
      loading: LOADING_MESSAGES.LOADING_PROFILE,
      submitting: LOADING_MESSAGES.SUPPLIER_INFO,
      saving: LOADING_MESSAGES.SUPPLIER_INFO
    },
    '/dashboard': {
      loading: LOADING_MESSAGES.LOADING_DASHBOARD,
      stats: LOADING_MESSAGES.LOADING_STATS,
      overview: LOADING_MESSAGES.LOADING_OVERVIEW
    },
    '/upload-first-invoice': {
      loading: LOADING_MESSAGES.FIRST_INVOICE_UPLOAD,
      uploading: LOADING_MESSAGES.UPLOADING_INVOICE,
      processing: LOADING_MESSAGES.PROCESSING_INVOICE,
      extracting: LOADING_MESSAGES.EXTRACTING_DATA
    },
    '/offers': {
      loading: LOADING_MESSAGES.LOADING_OFFERS,
      accepting: LOADING_MESSAGES.ACCEPTING_OFFER
    },
    '/create-offer': {
      loading: LOADING_MESSAGES.CREATING_OFFER,
      submitting: LOADING_MESSAGES.CREATING_OFFER
    },
    '/clients': {
      loading: LOADING_MESSAGES.LOADING_CLIENTS,
      adding: LOADING_MESSAGES.ADDING_CLIENT
    },
    '/analytics': {
      loading: LOADING_MESSAGES.LOADING_ANALYTICS,
      generating: LOADING_MESSAGES.GENERATING_REPORT
    },
    '/profile': {
      loading: LOADING_MESSAGES.PROFILE_PAGE,
      saving: LOADING_MESSAGES.UPDATING_PROFILE
    },
    '/settings': {
      loading: LOADING_MESSAGES.SETTINGS,
      saving: LOADING_MESSAGES.SAVING
    }
  };

  const pageMessages = pageActionMap[page];
  if (pageMessages && pageMessages[action]) {
    return pageMessages[action];
  }

  // Fallback to general action messages
  return LOADING_MESSAGES[action.toUpperCase()] || LOADING_MESSAGES.LOADING;
};
