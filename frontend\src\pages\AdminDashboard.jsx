import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import DashboardLayout from '../components/DashboardLayout';
import Spinner from '../components/Spinner';
import { showSuccessMessage, showErrorMessage } from '../utils/toastNotifications';
import logger from '../utils/logger';
import { loadCSS, unloadCSS, ADMIN_CSS, CSS_IDS } from '../utils/cssLoader';
import api from '../services/api';
import '../styles/admin-dashboard.css';

const AdminDashboard = () => {
  const [loading, setLoading] = useState(true);
  const [dashboardData, setDashboardData] = useState({
    totalUsers: 0,
    totalBrokers: 0,
    totalSuppliers: 0,
    totalClients: 0,
    totalContracts: 0,
    totalInvoices: 0,
    totalOffers: 0,
    recentActivities: [],
    systemStats: {}
  });
  const navigate = useNavigate();

  // Dynamically load CSS only for this component
  useEffect(() => {
    loadCSS(ADMIN_CSS.DASHBOARD, CSS_IDS.ADMIN_DASHBOARD);

    // Cleanup function to remove CSS when component unmounts
    return () => {
      unloadCSS(CSS_IDS.ADMIN_DASHBOARD);
    };
  }, []);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      logger.info('Fetching admin dashboard data...');

      // Fetch dashboard statistics using API service (will automatically use ID token for admin routes)
      const statsResponse = await api.get('/api/admin/dashboard-stats');
      const statsData = statsResponse.data;

      // Fetch recent activities
      let recentActivities = [];
      try {
        const activitiesResponse = await api.get('/api/admin/recent-activities');
        recentActivities = activitiesResponse.data.data || [];
      } catch (error) {
        logger.warn('Failed to fetch recent activities:', error.message);
      }

      // Fetch system status
      let systemStats = {
        serverUptime: '99.9%',
        activeUsers: 0,
        pendingApprovals: 0,
        systemHealth: 'Good'
      };

      try {
        const systemResponse = await api.get('/api/admin/system-status');
        systemStats = systemResponse.data.data || systemStats;
      } catch (error) {
        logger.warn('Failed to fetch system status:', error.message);
      }

      // Combine all data
      const dashboardData = {
        totalUsers: statsData.data.totalUsers || 0,
        totalBrokers: statsData.data.totalBrokers || 0,
        totalSuppliers: statsData.data.totalSuppliers || 0,
        totalClients: statsData.data.totalClients || 0,
        totalContracts: statsData.data.totalContracts || 0,
        totalInvoices: statsData.data.totalInvoices || 0,
        totalOffers: statsData.data.totalOffers || 0,
        recentActivities,
        systemStats
      };

      setDashboardData(dashboardData);
      setLoading(false);
      logger.info('Admin dashboard data loaded successfully');

    } catch (error) {
      logger.error('Error fetching admin dashboard data:', error);
      showErrorMessage('DASHBOARD_LOAD_FAILED', 'Failed to load dashboard data');

      // Fallback to empty data
      setDashboardData({
        totalUsers: 0,
        totalBrokers: 0,
        totalSuppliers: 0,
        totalClients: 0,
        totalContracts: 0,
        totalInvoices: 0,
        totalOffers: 0,
        recentActivities: [],
        systemStats: {
          serverUptime: 'N/A',
          activeUsers: 0,
          pendingApprovals: 0,
          systemHealth: 'Unknown'
        }
      });
      setLoading(false);
    }
  };

  // Navigation handlers
  const handleManageUsers = () => navigate('/admin/users');
  const handleManageBrokers = () => navigate('/admin/brokers');
  const handleManageSuppliers = () => navigate('/admin/suppliers');
  const handleManageContracts = () => navigate('/admin/contracts');
  const handleQuoteManagement = () => navigate('/admin/quotes');
  const handleDocumentTemplates = () => navigate('/admin/templates');
  const handleNotificationManagement = () => navigate('/admin/notifications');
  const handleAnalytics = () => navigate('/admin/analytics');
  const handleInvitationManagement = () => navigate('/admin/invitations');
  const handleSystemSettings = () => navigate('/admin/settings');
  const handleViewReports = () => navigate('/admin/reports');

  const formatTimestamp = (timestamp) => {
    const now = new Date();
    const diff = now - timestamp;
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    return `${days}d ago`;
  };

  const getSeverityIcon = (severity) => {
    switch (severity) {
      case 'success': return 'fas fa-check-circle';
      case 'warning': return 'fas fa-exclamation-triangle';
      case 'error': return 'fas fa-times-circle';
      default: return 'fas fa-info-circle';
    }
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="admin-dashboard-loading">
          <Spinner size="large" message="Loading admin dashboard..." />
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="admin-dashboard">
        {/* Header */}
        <div className="admin-header">
          <div className="admin-header-content">
            <h1>System Administration</h1>
            <p>Manage users, brokers, suppliers, and monitor system performance</p>
          </div>
          <div className="admin-header-actions">
            <button className="btn-admin-action" onClick={handleSystemSettings}>
              <i className="fas fa-cog"></i>
              System Settings
            </button>
            <button className="btn-admin-action primary" onClick={handleViewReports}>
              <i className="fas fa-chart-bar"></i>
              View Reports
            </button>
          </div>
        </div>

        {/* Stats Grid */}
        <div className="admin-stats-grid">
          <div className="stat-card users" onClick={handleManageUsers}>
            <div className="stat-icon">
              <i className="fas fa-users"></i>
            </div>
            <div className="stat-content">
              <h3>{dashboardData.totalUsers.toLocaleString()}</h3>
              <p>Total Users</p>
            </div>
            <div className="stat-action">
              <i className="fas fa-arrow-right"></i>
            </div>
          </div>

          <div className="stat-card brokers" onClick={handleManageBrokers}>
            <div className="stat-icon">
              <i className="fas fa-handshake"></i>
            </div>
            <div className="stat-content">
              <h3>{dashboardData.totalBrokers}</h3>
              <p>Active Brokers</p>
            </div>
            <div className="stat-action">
              <i className="fas fa-arrow-right"></i>
            </div>
          </div>

          <div className="stat-card suppliers" onClick={handleManageSuppliers}>
            <div className="stat-icon">
              <i className="fas fa-industry"></i>
            </div>
            <div className="stat-content">
              <h3>{dashboardData.totalSuppliers}</h3>
              <p>Suppliers</p>
            </div>
            <div className="stat-action">
              <i className="fas fa-arrow-right"></i>
            </div>
          </div>

          <div className="stat-card contracts" onClick={handleManageContracts}>
            <div className="stat-icon">
              <i className="fas fa-file-contract"></i>
            </div>
            <div className="stat-content">
              <h3>{dashboardData.totalContracts.toLocaleString()}</h3>
              <p>Active Contracts</p>
            </div>
            <div className="stat-action">
              <i className="fas fa-arrow-right"></i>
            </div>
          </div>

          <div className="stat-card invoices">
            <div className="stat-icon">
              <i className="fas fa-file-invoice"></i>
            </div>
            <div className="stat-content">
              <h3>{dashboardData.totalInvoices.toLocaleString()}</h3>
              <p>Total Invoices</p>
            </div>
          </div>

          <div className="stat-card offers" onClick={handleQuoteManagement}>
            <div className="stat-icon">
              <i className="fas fa-tags"></i>
            </div>
            <div className="stat-content">
              <h3>{dashboardData.totalOffers}</h3>
              <p>Active Offers</p>
            </div>
            <div className="stat-action">
              <i className="fas fa-arrow-right"></i>
            </div>
          </div>

          <div className="stat-card quotes" onClick={handleQuoteManagement}>
            <div className="stat-icon">
              <i className="fas fa-calculator"></i>
            </div>
            <div className="stat-content">
              <h3>{dashboardData.totalQuotes || 0}</h3>
              <p>Quote Requests</p>
            </div>
            <div className="stat-action">
              <i className="fas fa-arrow-right"></i>
            </div>
          </div>

          <div className="stat-card notifications" onClick={handleNotificationManagement}>
            <div className="stat-icon">
              <i className="fas fa-bell"></i>
            </div>
            <div className="stat-content">
              <h3>{dashboardData.totalNotifications || 0}</h3>
              <p>Notifications</p>
            </div>
            <div className="stat-action">
              <i className="fas fa-arrow-right"></i>
            </div>
          </div>
        </div>

        {/* Main Content Grid */}
        <div className="admin-content-grid">
          {/* Recent Activities */}
          <div className="admin-section">
            <div className="section-header">
              <h2>Recent Activities</h2>
              <button className="btn-view-all">View All</button>
            </div>
            <div className="activities-list">
              {dashboardData.recentActivities.map(activity => (
                <div key={activity.id} className={`activity-item ${activity.severity}`}>
                  <div className="activity-icon">
                    <i className={getSeverityIcon(activity.severity)}></i>
                  </div>
                  <div className="activity-content">
                    <p>{activity.message}</p>
                    <span className="activity-time">{formatTimestamp(activity.timestamp)}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* System Status */}
          <div className="admin-section">
            <div className="section-header">
              <h2>System Status</h2>
              <span className="status-indicator good">
                <i className="fas fa-circle"></i>
                {dashboardData.systemStats.systemHealth}
              </span>
            </div>
            <div className="system-stats">
              <div className="system-stat">
                <span className="stat-label">Server Uptime</span>
                <span className="stat-value">{dashboardData.systemStats.serverUptime}</span>
              </div>
              <div className="system-stat">
                <span className="stat-label">Active Users</span>
                <span className="stat-value">{dashboardData.systemStats.activeUsers}</span>
              </div>
              <div className="system-stat">
                <span className="stat-label">Pending Approvals</span>
                <span className="stat-value warning">{dashboardData.systemStats.pendingApprovals}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="admin-quick-actions">
          <h2>Quick Actions</h2>
          <div className="quick-actions-grid">
            <button className="quick-action-btn" onClick={handleManageUsers}>
              <i className="fas fa-user-plus"></i>
              <span>Manage Users</span>
            </button>
            <button className="quick-action-btn" onClick={handleManageBrokers}>
              <i className="fas fa-user-tie"></i>
              <span>Manage Brokers</span>
            </button>
            <button className="quick-action-btn" onClick={handleManageSuppliers}>
              <i className="fas fa-building"></i>
              <span>Manage Suppliers</span>
            </button>
            <button className="quick-action-btn" onClick={handleInvitationManagement}>
              <i className="fas fa-envelope"></i>
              <span>Send Invitations</span>
            </button>
            <button className="quick-action-btn" onClick={handleQuoteManagement}>
              <i className="fas fa-calculator"></i>
              <span>Quote System</span>
            </button>
            <button className="quick-action-btn" onClick={handleDocumentTemplates}>
              <i className="fas fa-file-alt"></i>
              <span>Templates</span>
            </button>
            <button className="quick-action-btn" onClick={handleNotificationManagement}>
              <i className="fas fa-bell"></i>
              <span>Notifications</span>
            </button>
            <button className="quick-action-btn" onClick={handleAnalytics}>
              <i className="fas fa-chart-line"></i>
              <span>Analytics</span>
            </button>
            <button className="quick-action-btn" onClick={() => navigate('/admin/analytics')}>
              <i className="fas fa-download"></i>
              <span>Export Data</span>
            </button>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default AdminDashboard;
