// Mock data for electricity requests/tickets
export const mockTickets = [
  {
    id: 1,
    title: 'Power Outage',
    description: 'Complete power outage in my apartment since 2 hours ago',
    status: 'In Progress',
    priority: 'High',
    createdAt: '2023-04-23T14:30:00',
    updatedAt: '2023-04-23T15:45:00',
    meterNumber: 'E-12345678',
    address: '123 Main St, Apt 4B',
    contactPhone: '************'
  },
  {
    id: 2,
    title: 'Flickering Lights',
    description: 'Lights have been flickering in the kitchen for the past 3 days',
    status: 'Pending',
    priority: 'Medium',
    createdAt: '2023-04-22T09:15:00',
    updatedAt: '2023-04-22T10:20:00',
    meterNumber: 'E-87654321',
    address: '456 Oak Ave, Unit 7',
    contactPhone: '************'
  },
  {
    id: 3,
    title: 'Meter Reading Dispute',
    description: 'I believe my meter reading is incorrect. The bill is much higher than usual.',
    status: 'Resolved',
    priority: 'Low',
    createdAt: '2023-04-20T11:00:00',
    updatedAt: '2023-04-21T16:30:00',
    meterNumber: 'E-55556666',
    address: '789 Pine St, Apt 12C',
    contactPhone: '************'
  },
  {
    id: 4,
    title: 'New Connection Request',
    description: 'I need a new electricity connection for my newly constructed house',
    status: 'Pending',
    priority: 'Medium',
    createdAt: '2023-04-19T13:45:00',
    updatedAt: '2023-04-19T14:30:00',
    meterNumber: 'N/A',
    address: '321 Maple Rd, House 5',
    contactPhone: '************'
  },
  {
    id: 5,
    title: 'Voltage Fluctuation',
    description: 'Experiencing severe voltage fluctuations that are damaging my appliances',
    status: 'In Progress',
    priority: 'High',
    createdAt: '2023-04-18T16:20:00',
    updatedAt: '2023-04-19T09:10:00',
    meterNumber: 'E-11223344',
    address: '567 Elm St, Apt 3A',
    contactPhone: '************'
  }
];

// Status options for tickets
export const statusOptions = [
  'Pending',
  'In Progress',
  'Resolved',
  'Cancelled'
];

// Priority options for tickets
export const priorityOptions = [
  'Low',
  'Medium',
  'High',
  'Critical'
];
