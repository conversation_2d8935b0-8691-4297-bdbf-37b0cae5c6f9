<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Account Status UI Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .account-status-card {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            border: 2px solid #fed7d7;
            overflow: hidden;
        }
        
        .status-header {
            text-align: center;
            padding: 40px 30px 20px;
            background: rgba(255, 255, 255, 0.9);
        }
        
        .status-icon {
            font-size: 48px;
            margin-bottom: 16px;
        }
        
        .status-title {
            font-size: 28px;
            font-weight: 600;
            margin: 0;
            color: #ff3b30;
        }
        
        .status-content {
            padding: 20px 30px;
        }
        
        .status-actions {
            padding: 20px 30px 30px;
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
        }
        
        .btn-primary {
            background: #007aff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            flex: 1;
            min-width: 160px;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 122, 255, 0.3);
        }
        
        .contact-modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.75);
            backdrop-filter: blur(8px);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            padding: 20px;
            animation: modalOverlayFadeIn 0.3s ease-out;
        }

        @keyframes modalOverlayFadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .contact-modal {
            background: white;
            border-radius: 20px;
            max-width: 500px;
            width: 100%;
            max-height: 85vh;
            position: relative;
            box-shadow:
                0 25px 50px rgba(0, 0, 0, 0.25),
                0 0 0 1px rgba(255, 255, 255, 0.1);
            animation: modalSlideIn 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(30px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .contact-modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 24px 32px;
            background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
            border-bottom: 1px solid #e2e8f0;
            flex-shrink: 0;
        }

        .contact-modal-header h2 {
            margin: 0;
            font-size: 22px;
            font-weight: 600;
            color: #2d3748;
        }

        .close-modal {
            background: #f7fafc;
            border: 2px solid #e2e8f0;
            font-size: 18px;
            color: #718096;
            cursor: pointer;
            padding: 0;
            border-radius: 12px;
            transition: all 0.2s ease;
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
        }

        .close-modal:hover {
            background: #edf2f7;
            border-color: #cbd5e0;
            color: #4a5568;
            transform: scale(1.05);
        }

        .modal-content-wrapper {
            flex: 1;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
        }
        

        
        .support-ticket-form {
            padding: 32px;
            background: #ffffff;
            flex: 1;
            overflow-y: auto;
        }

        /* Form Introduction */
        .form-intro {
            text-align: center;
            margin-bottom: 32px;
            padding-bottom: 24px;
            border-bottom: 1px solid #e2e8f0;
        }

        .form-intro-icon {
            font-size: 48px;
            margin-bottom: 16px;
        }

        .form-intro h3 {
            margin: 0 0 12px 0;
            font-size: 24px;
            font-weight: 600;
            color: #2d3748;
        }

        .form-intro p {
            margin: 0;
            font-size: 16px;
            color: #4a5568;
            line-height: 1.5;
        }

        /* Submission Status */
        .submission-status {
            margin: 24px 0;
            padding: 20px;
            border-radius: 12px;
            border: 2px solid;
            animation: slideIn 0.3s ease-out;
        }

        .submission-status.success {
            background: linear-gradient(135deg, #f0fff4 0%, #e6fffa 100%);
            border-color: #38a169;
            color: #2d3748;
        }

        .submission-status.error {
            background: linear-gradient(135deg, #fff5f5 0%, #fed7d7 100%);
            border-color: #e53e3e;
            color: #2d3748;
        }

        .success-message, .error-message {
            display: flex;
            align-items: flex-start;
            gap: 16px;
        }

        .success-icon, .error-icon {
            font-size: 24px;
            flex-shrink: 0;
            margin-top: 2px;
        }

        .success-text, .error-text {
            flex: 1;
        }

        .success-text strong, .error-text strong {
            display: block;
            font-size: 16px;
            margin-bottom: 8px;
        }

        .success-text p, .error-text p {
            margin: 4px 0;
            font-size: 14px;
            line-height: 1.4;
        }

        /* Loading Spinner */
        .loading-spinner {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: #ffffff;
            animation: spin 1s ease-in-out infinite;
            margin-right: 8px;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .form-group {
            margin-bottom: 24px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2d3748;
            font-size: 15px;
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 14px 16px;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            font-size: 15px;
            font-family: inherit;
            background: #ffffff;
            color: #2d3748;
            transition: all 0.2s ease;
            box-sizing: border-box;
        }

        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: #007aff;
            box-shadow: 0 0 0 4px rgba(0, 122, 255, 0.15);
            background: #fafbff;
        }

        .form-group input::placeholder,
        .form-group textarea::placeholder {
            color: #a0aec0;
            font-style: italic;
        }

        .form-group textarea {
            resize: vertical;
            min-height: 120px;
            line-height: 1.5;
        }

        .form-group select {
            cursor: pointer;
            appearance: none;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 12px center;
            background-repeat: no-repeat;
            background-size: 16px;
            padding-right: 40px;
        }
        
        .ticket-form-actions {
            display: flex;
            gap: 16px;
            margin-top: 32px;
            padding-top: 24px;
            border-top: 1px solid #e2e8f0;
        }

        .ticket-form-actions .btn-primary {
            flex: 2;
            background: linear-gradient(135deg, #007aff 0%, #0056cc 100%);
            padding: 16px 24px;
            font-size: 16px;
            font-weight: 600;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 122, 255, 0.3);
            transition: all 0.2s ease;
            color: white;
            border: none;
            cursor: pointer;
        }

        .ticket-form-actions .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 122, 255, 0.4);
        }

        .btn-secondary {
            flex: 1;
            background: #f7fafc;
            color: #4a5568;
            border: 2px solid #e2e8f0;
            padding: 16px 24px;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .btn-secondary:hover {
            background: #edf2f7;
            border-color: #cbd5e0;
            transform: translateY(-1px);
        }
        
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="account-status-card">
        <div class="status-header">
            <div class="status-icon">🚫</div>
            <h1 class="status-title">Account Suspended</h1>
        </div>
        
        <div class="status-content">
            <p>Your account has been suspended by our administrative team.</p>
            <div style="background: #f7fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 16px; margin: 20px 0;">
                <strong>Message:</strong> Account is suspended. Please contact support for assistance.
            </div>
        </div>
        
        <div class="status-actions">
            <button class="btn-primary" onclick="showContactModal()">
                🎫 Contact Support
            </button>
        </div>
    </div>
    
    <div id="contactModal" class="contact-modal-overlay hidden">
        <div class="contact-modal">
            <div class="contact-modal-header">
                <h2>🎫 Contact Support</h2>
                <button class="close-modal" onclick="hideContactModal()" title="Close">×</button>
            </div>

            <div class="modal-content-wrapper">
                <div class="support-ticket-form">
                    <div class="form-intro">
                        <div class="form-intro-icon">🎫</div>
                        <h3>Create Support Ticket</h3>
                        <p>Get professional help with your account status issue. We'll respond within 24 hours.</p>
                    </div>

                    <form>
                        <div class="form-group">
                            <label for="ticket-subject">Subject *</label>
                            <input type="text" id="ticket-subject" placeholder="Account Status Issue - ACCOUNT_SUSPENDED" required>
                        </div>

                        <div class="form-group">
                            <label for="ticket-message">Detailed Description *</label>
                            <textarea id="ticket-message" rows="5" placeholder="Please describe your account status issue in detail. Include any relevant information that might help us resolve your case quickly..." required></textarea>
                        </div>

                        <div class="form-group">
                            <label for="ticket-priority">Priority Level</label>
                            <select id="ticket-priority">
                                <option value="high">🔴 High - Account Access Issue</option>
                                <option value="medium">🟡 Medium - General Inquiry</option>
                                <option value="low">🟢 Low - Information Request</option>
                            </select>
                        </div>

                        <!-- Submission Status Demo -->
                        <div id="submissionStatus" class="submission-status success" style="display: none;">
                            <div class="success-message">
                                <div class="success-icon">✅</div>
                                <div class="success-text">
                                    <strong>Ticket Created Successfully!</strong>
                                    <p>Ticket Number: <strong>#ABC123</strong></p>
                                    <p>We'll respond within 24 hours to your email.</p>
                                </div>
                            </div>
                        </div>

                        <div class="ticket-form-actions">
                            <button type="submit" class="btn-primary" id="submitBtn">
                                🎫 Create Support Ticket
                            </button>
                            <button type="button" class="btn-secondary" onclick="hideContactModal()">Cancel</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function showContactModal() {
            document.getElementById('contactModal').classList.remove('hidden');
        }

        function hideContactModal() {
            document.getElementById('contactModal').classList.add('hidden');
            // Reset form and status
            document.getElementById('submissionStatus').style.display = 'none';
            document.getElementById('submitBtn').disabled = false;
            document.getElementById('submitBtn').innerHTML = '🎫 Create Support Ticket';
        }

        // Demo form submission with loading states
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.querySelector('form');
            const submitBtn = document.getElementById('submitBtn');
            const statusDiv = document.getElementById('submissionStatus');
            const messageTextarea = document.getElementById('ticket-message');

            // Enable/disable submit button based on message content
            messageTextarea.addEventListener('input', function() {
                const hasContent = this.value.trim().length >= 10;
                submitBtn.disabled = !hasContent;
            });

            form.addEventListener('submit', function(e) {
                e.preventDefault();

                // Show loading state
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<span class="loading-spinner"></span>Creating Ticket...';

                // Simulate API call
                setTimeout(() => {
                    // Show success status
                    statusDiv.style.display = 'block';
                    statusDiv.className = 'submission-status success';
                    statusDiv.innerHTML = `
                        <div class="success-message">
                            <div class="success-icon">✅</div>
                            <div class="success-text">
                                <strong>Ticket Created Successfully!</strong>
                                <p>Ticket Number: <strong>#${Math.random().toString(36).substr(2, 6).toUpperCase()}</strong></p>
                                <p>We'll respond within 24 hours to your email.</p>
                            </div>
                        </div>
                    `;

                    // Reset button
                    submitBtn.innerHTML = '✅ Ticket Created';

                    // Auto-close after 3 seconds
                    setTimeout(() => {
                        hideContactModal();
                        form.reset();
                    }, 3000);

                }, 2000); // 2 second delay to show loading
            });
        });
    </script>
</body>
</html>
