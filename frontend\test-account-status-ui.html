<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Account Status UI Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .account-status-card {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            border: 2px solid #fed7d7;
            overflow: hidden;
        }
        
        .status-header {
            text-align: center;
            padding: 40px 30px 20px;
            background: rgba(255, 255, 255, 0.9);
        }
        
        .status-icon {
            font-size: 48px;
            margin-bottom: 16px;
        }
        
        .status-title {
            font-size: 28px;
            font-weight: 600;
            margin: 0;
            color: #ff3b30;
        }
        
        .status-content {
            padding: 20px 30px;
        }
        
        .status-actions {
            padding: 20px 30px 30px;
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
        }
        
        .btn-primary {
            background: #007aff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            flex: 1;
            min-width: 160px;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 122, 255, 0.3);
        }
        
        .contact-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            padding: 20px;
        }
        
        .contact-modal-content {
            background: white;
            border-radius: 16px;
            max-width: 500px;
            width: 100%;
            max-height: 90vh;
            overflow-y: auto;
        }
        
        .contact-options {
            display: flex;
            justify-content: center;
            padding: 30px;
        }
        
        .contact-option-card.single-option {
            max-width: 400px;
            width: 100%;
            border: 2px solid #007aff;
            background: linear-gradient(135deg, #f0f9ff 0%, #ffffff 100%);
            border-radius: 12px;
            padding: 24px;
            text-align: center;
            transition: all 0.2s ease;
        }
        
        .contact-option-icon {
            font-size: 48px;
            margin-bottom: 16px;
        }
        
        .contact-option-btn.primary {
            background: linear-gradient(135deg, #007aff 0%, #0056cc 100%);
            padding: 14px 32px;
            font-size: 16px;
            box-shadow: 0 4px 12px rgba(0, 122, 255, 0.3);
            color: white;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-top: 16px;
            width: 100%;
        }
        
        .support-ticket-form {
            padding: 30px;
            border-top: 1px solid #e2e8f0;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2d3748;
        }
        
        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.2s ease;
        }
        
        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: #007aff;
        }
        
        .ticket-form-actions {
            display: flex;
            gap: 12px;
            margin-top: 24px;
        }
        
        .btn-secondary {
            background: #f7fafc;
            color: #4a5568;
            border: 2px solid #e2e8f0;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            flex: 1;
        }
        
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="account-status-card">
        <div class="status-header">
            <div class="status-icon">🚫</div>
            <h1 class="status-title">Account Suspended</h1>
        </div>
        
        <div class="status-content">
            <p>Your account has been suspended by our administrative team.</p>
            <div style="background: #f7fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 16px; margin: 20px 0;">
                <strong>Message:</strong> Account is suspended. Please contact support for assistance.
            </div>
        </div>
        
        <div class="status-actions">
            <button class="btn-primary" onclick="showContactModal()">
                🎫 Contact Support
            </button>
        </div>
    </div>
    
    <div id="contactModal" class="contact-modal hidden">
        <div class="contact-modal-content">
            <div class="contact-options">
                <div class="contact-option-card single-option">
                    <div class="contact-option-icon">🎫</div>
                    <h3>Create Support Ticket</h3>
                    <p>Get help with your account status issue</p>
                    <p style="color: #718096;">Professional support with tracking</p>
                    <p style="color: #718096; font-size: 14px;">We'll respond within 24 hours</p>
                    <button class="contact-option-btn primary" onclick="showTicketForm()">
                        Create Support Ticket
                    </button>
                </div>
            </div>
            
            <div id="ticketForm" class="support-ticket-form hidden">
                <h3>Create Support Ticket</h3>
                <form>
                    <div class="form-group">
                        <label for="ticket-subject">Subject</label>
                        <input type="text" id="ticket-subject" placeholder="Account Status Issue - ACCOUNT_SUSPENDED">
                    </div>
                    
                    <div class="form-group">
                        <label for="ticket-message">Message</label>
                        <textarea id="ticket-message" rows="4" placeholder="Please describe your issue in detail..." required></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="ticket-priority">Priority</label>
                        <select id="ticket-priority">
                            <option value="high">High - Account Access Issue</option>
                            <option value="medium">Medium</option>
                            <option value="low">Low</option>
                        </select>
                    </div>
                    
                    <div class="ticket-form-actions">
                        <button type="submit" class="btn-primary">Create Ticket</button>
                        <button type="button" class="btn-secondary" onclick="hideContactModal()">Cancel</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <script>
        function showContactModal() {
            document.getElementById('contactModal').classList.remove('hidden');
        }
        
        function hideContactModal() {
            document.getElementById('contactModal').classList.add('hidden');
            document.getElementById('ticketForm').classList.add('hidden');
        }
        
        function showTicketForm() {
            document.getElementById('ticketForm').classList.remove('hidden');
            document.getElementById('ticketForm').scrollIntoView({ behavior: 'smooth' });
        }
    </script>
</body>
</html>
