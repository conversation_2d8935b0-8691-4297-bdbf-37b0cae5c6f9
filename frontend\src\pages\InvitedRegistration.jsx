import { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { showSuccessMessage, showErrorMessage } from '../utils/toastNotifications';
import logger from '../utils/logger';
import Spinner from '../components/Spinner';
import '../styles/invited-registration.css';
import { API_BASE_URL } from '../config/api-config';

const InvitedRegistration = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [invitationData, setInvitationData] = useState(null);
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    phone: '',
    password: '',
    confirmPassword: ''
  });
  const [errors, setErrors] = useState({});

  const token = searchParams.get('token');
  const userType = searchParams.get('type');

  useEffect(() => {
    if (!token || !userType) {
      showErrorMessage('INVALID_INVITATION', 'Invalid invitation link');
      navigate('/');
      return;
    }

    validateInvitation();
  }, [token, userType]);

  const validateInvitation = async () => {
    try {
      setLoading(true);
      
      const response = await fetch(`${API_BASE_URL}/api/invitations/validate/${token}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Invalid invitation');
      }

      const data = await response.json();
      setInvitationData(data.data);
      
      // Pre-fill form with invitation data
      setFormData(prev => ({
        ...prev,
        firstName: data.data.inviteeDetails?.firstName || '',
        lastName: data.data.inviteeDetails?.lastName || '',
        phone: data.data.inviteeDetails?.phone || ''
      }));

      logger.info('Invitation validated successfully');
    } catch (error) {
      logger.error('Error validating invitation:', error);
      showErrorMessage('INVITATION_VALIDATION_FAILED', error.message);
      navigate('/');
    } finally {
      setLoading(false);
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.firstName.trim()) {
      newErrors.firstName = 'First name is required';
    }

    if (!formData.lastName.trim()) {
      newErrors.lastName = 'Last name is required';
    }

    if (!formData.password) {
      newErrors.password = 'Password is required';
    } else if (formData.password.length < 8) {
      newErrors.password = 'Password must be at least 8 characters';
    }

    if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match';
    }

    if (formData.phone && !/^\+?[\d\s\-\(\)]+$/.test(formData.phone)) {
      newErrors.phone = 'Please enter a valid phone number';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setSubmitting(true);
    
    try {
      const response = await fetch(`${API_BASE_URL}/api/invitations/accept/${token}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          firstName: formData.firstName,
          lastName: formData.lastName,
          phone: formData.phone,
          password: formData.password
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Registration failed');
      }

      const data = await response.json();
      
      showSuccessMessage('REGISTRATION_SUCCESS', 'Account created successfully! Please wait for admin approval.');
      
      // Redirect to a success page or login
      navigate('/registration-success', { 
        state: { 
          userType: invitationData.userType,
          email: invitationData.email,
          requiresApproval: true
        }
      });

    } catch (error) {
      logger.error('Error accepting invitation:', error);
      showErrorMessage('REGISTRATION_FAILED', error.message);
    } finally {
      setSubmitting(false);
    }
  };

  const getDaysRemaining = () => {
    if (!invitationData?.expiresAt) return 0;
    const now = new Date();
    const expiry = new Date(invitationData.expiresAt);
    const diffTime = expiry - now;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return Math.max(0, diffDays);
  };

  if (loading) {
    return <Spinner fullScreen={true} message="Validating invitation..." size="large" />;
  }

  if (!invitationData) {
    return (
      <div className="error-page">
        <h2>Invalid Invitation</h2>
        <p>This invitation link is invalid or has expired.</p>
        <button onClick={() => navigate('/')}>Go Home</button>
      </div>
    );
  }

  const userTypeDisplay = invitationData.userType === 'Broker' ? 'Energy Broker' : 'Energy Supplier';
  const daysRemaining = getDaysRemaining();

  return (
    <div className="invited-registration-container">
      <div className="registration-card">
        <div className="card-header">
          <div className="invitation-badge">
            <i className={`fas fa-${invitationData.userType === 'Broker' ? 'handshake' : 'industry'}`}></i>
          </div>
          <h1>Complete Your Registration</h1>
          <p>You've been invited to join as a <strong>{userTypeDisplay}</strong></p>
        </div>

        <div className="invitation-info">
          <div className="info-item">
            <span className="label">Email:</span>
            <span className="value">{invitationData.email}</span>
          </div>
          <div className="info-item">
            <span className="label">Invited by:</span>
            <span className="value">
              {invitationData.invitedBy ? 
                `${invitationData.invitedBy.firstName} ${invitationData.invitedBy.lastName}` :
                'Platform Administrator'
              }
            </span>
          </div>
          {daysRemaining > 0 && (
            <div className="info-item">
              <span className="label">Expires in:</span>
              <span className={`value ${daysRemaining <= 1 ? 'urgent' : ''}`}>
                {daysRemaining} day{daysRemaining !== 1 ? 's' : ''}
              </span>
            </div>
          )}
        </div>

        <form onSubmit={handleSubmit} className="registration-form">
          <div className="form-row">
            <div className="form-group">
              <label>First Name *</label>
              <input
                type="text"
                value={formData.firstName}
                onChange={(e) => setFormData({...formData, firstName: e.target.value})}
                className={errors.firstName ? 'error' : ''}
                placeholder="Enter your first name"
              />
              {errors.firstName && <span className="error-message">{errors.firstName}</span>}
            </div>

            <div className="form-group">
              <label>Last Name *</label>
              <input
                type="text"
                value={formData.lastName}
                onChange={(e) => setFormData({...formData, lastName: e.target.value})}
                className={errors.lastName ? 'error' : ''}
                placeholder="Enter your last name"
              />
              {errors.lastName && <span className="error-message">{errors.lastName}</span>}
            </div>
          </div>

          <div className="form-group">
            <label>Phone Number</label>
            <input
              type="tel"
              value={formData.phone}
              onChange={(e) => setFormData({...formData, phone: e.target.value})}
              className={errors.phone ? 'error' : ''}
              placeholder="Enter your phone number"
            />
            {errors.phone && <span className="error-message">{errors.phone}</span>}
          </div>

          <div className="form-group">
            <label>Password *</label>
            <input
              type="password"
              value={formData.password}
              onChange={(e) => setFormData({...formData, password: e.target.value})}
              className={errors.password ? 'error' : ''}
              placeholder="Create a secure password"
            />
            {errors.password && <span className="error-message">{errors.password}</span>}
          </div>

          <div className="form-group">
            <label>Confirm Password *</label>
            <input
              type="password"
              value={formData.confirmPassword}
              onChange={(e) => setFormData({...formData, confirmPassword: e.target.value})}
              className={errors.confirmPassword ? 'error' : ''}
              placeholder="Confirm your password"
            />
            {errors.confirmPassword && <span className="error-message">{errors.confirmPassword}</span>}
          </div>

          <div className="form-actions">
            <button 
              type="submit" 
              className="btn-primary"
              disabled={submitting}
            >
              {submitting ? 'Creating Account...' : 'Complete Registration'}
            </button>
          </div>
        </form>

        <div className="registration-note">
          <p>
            <i className="fas fa-info-circle"></i>
            After registration, your account will be reviewed by our admin team. 
            You'll receive an email notification once your account is approved.
          </p>
        </div>
      </div>
    </div>
  );
};

export default InvitedRegistration;
