.support-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.support-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 30px;
  color: #000;
}

.support-tabs {
  display: flex;
  margin-bottom: 30px;
  border-bottom: 1px solid #eee;
}

.support-tab {
  padding: 12px 20px;
  background: none;
  border: none;
  font-size: 16px;
  font-weight: 500;
  color: #666;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 3px solid transparent;
  display: flex;
  align-items: center;
}

.support-tab i {
  margin-right: 8px;
}

.support-tab:hover {
  color: #333;
}

.support-tab.active {
  color: #000;
  border-bottom-color: #000;
}

.support-content {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  padding: 30px;
  border: 1px solid #f0f0f0;
}

/* FAQ Section */
.faq-search {
  position: relative;
  margin-bottom: 30px;
}

.faq-search input {
  width: 100%;
  padding: 12px 20px;
  padding-right: 40px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 16px;
  transition: border-color 0.2s ease;
}

.faq-search input:focus {
  border-color: #000;
  outline: none;
}

.faq-search i {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #666;
}

.no-results {
  text-align: center;
  padding: 30px;
}

.no-results p {
  font-size: 16px;
  color: #666;
  margin-bottom: 15px;
}

.no-results button {
  padding: 8px 16px;
  background-color: #f5f5f5;
  color: #333;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.no-results button:hover {
  background-color: #eee;
}

.faq-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.faq-item {
  border: 1px solid #eee;
  border-radius: 8px;
  overflow: hidden;
}

.faq-question {
  padding: 15px 20px;
  background-color: #f9f9f9;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
}

.faq-question h3 {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.faq-question i {
  color: #666;
  transition: transform 0.2s ease;
}

.faq-item.active .faq-question i {
  transform: rotate(180deg);
}

.faq-answer {
  padding: 15px 20px;
  border-top: 1px solid #eee;
}

.faq-answer p {
  font-size: 15px;
  color: #666;
  line-height: 1.6;
  margin: 0;
}

/* Contact Section */
.contact-section {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 30px;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.contact-method {
  display: flex;
  align-items: flex-start;
  gap: 15px;
}

.contact-method i {
  font-size: 24px;
  color: #000;
  margin-top: 5px;
}

.contact-method h3 {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0 0 5px 0;
}

.contact-method p {
  font-size: 15px;
  color: #666;
  margin: 0;
}

.contact-hours {
  font-size: 13px;
  color: #888;
  margin-top: 5px !important;
}

.contact-form-container {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 20px;
}

.contact-form-container h2 {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin: 0 0 20px 0;
}

.contact-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.form-group label {
  font-size: 14px;
  font-weight: 500;
  color: #666;
}

.form-group input,
.form-group textarea,
.form-group select {
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 15px;
  transition: border-color 0.2s ease;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  border-color: #000;
  outline: none;
}

.submit-btn {
  padding: 12px 20px;
  background: linear-gradient(to right, #000, #333);
  color: #fff;
  border: none;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: 10px;
}

.submit-btn:hover {
  opacity: 0.9;
  transform: translateY(-2px);
}

/* Tickets Section */
.tickets-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.tickets-header h2 {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.new-ticket-btn {
  padding: 8px 16px;
  background: linear-gradient(to right, #000, #333);
  color: #fff;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: all 0.2s ease;
}

.new-ticket-btn i {
  margin-right: 8px;
}

.new-ticket-btn:hover {
  opacity: 0.9;
  transform: translateY(-2px);
}

.tickets-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.ticket-item {
  display: flex;
  align-items: center;
  padding: 15px;
  border: 1px solid #eee;
  border-radius: 8px;
  transition: transform 0.2s ease;
}

.ticket-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.ticket-status {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  margin-right: 15px;
  min-width: 80px;
  text-align: center;
}

.ticket-status.open {
  background-color: #e3f2fd;
  color: #1565c0;
}

.ticket-status.closed {
  background-color: #f5f5f5;
  color: #616161;
}

.ticket-status.in-progress {
  background-color: #fff8e1;
  color: #ff8f00;
}

.ticket-info {
  flex: 1;
}

.ticket-subject {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 5px 0;
}

.ticket-message {
  font-size: 14px;
  color: #666;
  margin: 0 0 10px 0;
  line-height: 1.5;
}

.ticket-meta {
  display: flex;
  gap: 15px;
}

.ticket-id,
.ticket-date {
  font-size: 12px;
  color: #888;
}

.ticket-actions {
  margin-left: 15px;
}

.view-ticket-btn {
  padding: 6px 12px;
  background-color: #f5f5f5;
  color: #333;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.view-ticket-btn:hover {
  background-color: #eee;
}

/* Responsive styles */
@media (max-width: 992px) {
  .contact-section {
    grid-template-columns: 1fr;
  }
  
  .contact-info {
    order: 2;
  }
  
  .contact-form-container {
    order: 1;
  }
}

@media (max-width: 768px) {
  .support-tabs {
    flex-wrap: wrap;
  }
  
  .support-tab {
    flex: 1;
    min-width: 120px;
    justify-content: center;
  }
  
  .ticket-item {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .ticket-status {
    margin-right: 0;
    margin-bottom: 10px;
  }
  
  .ticket-actions {
    margin-left: 0;
    margin-top: 15px;
    align-self: flex-end;
  }
}
