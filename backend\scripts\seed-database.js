/**
 * <PERSON><PERSON>t to seed the database with initial data
 * Run with: node scripts/seed-database.js
 */

const mongoose = require('mongoose');
const dotenv = require('dotenv');
const logger = require('../utils/logger');
const path = require('path');

const {
  User,
  IndividualProfile,
  ProfessionalProfile,
  EnergyRequest
} = require('../models');

// Load environment variables
const cliEnv = process.argv[2];
process.env.NODE_ENV = cliEnv || process.env.NODE_ENV || 'local';

const envFile = `.env.${process.env.NODE_ENV}`;

dotenv.config({
  path: path.resolve(__dirname, `../${envFile}`),
  override: false,
});

console.log(`Loaded environment: ${envFile}`);

// Get MongoDB URI from environment variables
const MONGODB_URI = process.env.MONGODB_URI;

if (!MONGODB_URI) {
  logger.error('Error: MONGODB_URI is not defined in the environment variables');
  process.exit(1);
}

// Sample data
const sampleUsers = [
  {
    email: '<EMAIL>',
    firstName: 'John',
    lastName: 'Doe',
    phone: '+33123456789',
    userType: 'Individual',
    status: 'Active',
    profileComplete: true,
    verificationStatus: 'Verified'
  },
  {
    email: '<EMAIL>',
    firstName: 'Jane',
    lastName: 'Smith',
    phone: '+33987654321',
    userType: 'Professional',
    status: 'Active',
    profileComplete: true,
    verificationStatus: 'Verified'
  },
  {
    email: '<EMAIL>',
    firstName: 'Robert',
    lastName: 'Johnson',
    phone: '+33456789123',
    userType: 'Broker',
    status: 'Active',
    profileComplete: true,
    verificationStatus: 'Verified'
  },
  {
    email: '<EMAIL>',
    firstName: 'Emily',
    lastName: 'Brown',
    phone: '+33789123456',
    userType: 'Supplier',
    status: 'Active',
    profileComplete: true,
    verificationStatus: 'Verified'
  },
  {
    email: '<EMAIL>',
    firstName: 'Admin',
    lastName: 'User',
    phone: '+33654321987',
    userType: 'Admin',
    status: 'Active',
    profileComplete: true,
    verificationStatus: 'Verified'
  }
];

// Connect to MongoDB
logger.info('Connecting to MongoDB...');
mongoose.connect(MONGODB_URI)
  .then(async () => {
    logger.info('✅ MongoDB connection successful!');

    // Clear existing data
    logger.info('Clearing existing data...');
    await User.deleteMany({});
    await IndividualProfile.deleteMany({});
    await ProfessionalProfile.deleteMany({});
    await EnergyRequest.deleteMany({});

    // Create users
    logger.info('Creating sample users...');
    const createdUsers = await User.insertMany(sampleUsers);

    // Map users by email for easy reference
    const userMap = createdUsers.reduce((map, user) => {
      map[user.email] = user;
      return map;
    }, {});

    // Create individual profile
    logger.info('Creating individual profile...');
    const individualProfile = await IndividualProfile.create({
      userId: userMap['<EMAIL>']._id,
      address: {
        street: '123 Rue de Paris',
        city: 'Paris',
        postalCode: '75001',
        country: 'France'
      },
      energyIdentifiers: {
        pdl: 'PDL123456789',
        prm: 'PRM987654321'
      },
      currentSupplier: 'EDF',
      consumptionDetails: {
        annualConsumption: 3500,
        averageMonthlyBill: 85
      },
      preferredContactMethod: 'Email'
    });

    // Create professional profile
    logger.info('Creating professional profile...');
    const professionalProfile = await ProfessionalProfile.create({
      userId: userMap['<EMAIL>']._id,
      companyName: 'Tech Solutions SARL',
      companyAddress: {
        street: '456 Avenue des Champs-Élysées',
        city: 'Paris',
        postalCode: '75008',
        country: 'France'
      },
      siretNumber: '12345678901234',
      vatNumber: 'FR12345678901',
      companyRole: 'Technology',
      businessType: 'SARL',
      energyTypes: ['Electricity', 'Gas'],
      numberOfLocations: 3,
      annualConsumption: {
        electricity: 25000,
        gas: 15000
      },
      currentSuppliers: [
        {
          energyType: 'Electricity',
          supplierName: 'EDF',
          contractEndDate: new Date('2023-12-31')
        },
        {
          energyType: 'Gas',
          supplierName: 'Engie',
          contractEndDate: new Date('2023-10-15')
        }
      ]
    });

    // Create energy requests
    logger.info('Creating energy requests...');
    const energyRequests = await EnergyRequest.insertMany([
      {
        userId: userMap['<EMAIL>']._id,
        userType: 'Individual',
        requestType: 'Electricity',
        status: 'Submitted',
        consumptionDetails: {
          annualConsumption: 3500,
          averageMonthlyBill: 85
        },
        currentSupplier: 'EDF',
        currentContractEndDate: new Date('2023-12-31'),
        preferredDuration: '12 months',
        additionalRequirements: 'Looking for green energy options'
      },
      {
        userId: userMap['<EMAIL>']._id,
        userType: 'Professional',
        requestType: 'Both',
        status: 'UnderReview',
        consumptionDetails: {
          annualConsumption: 25000,
          averageMonthlyBill: 450
        },
        currentSupplier: 'EDF & Engie',
        currentContractEndDate: new Date('2023-10-15'),
        preferredDuration: '24 months',
        additionalRequirements: 'Need competitive rates for multiple locations',
        assignedBroker: userMap['<EMAIL>']._id
      }
    ]);

    logger.info('✅ Database seeded successfully!');
    logger.info(`Created ${createdUsers.length} users`);
    logger.info(`Created ${energyRequests.length} energy requests`);

    // Close the connection
    await mongoose.connection.close();
    logger.info('Connection closed');
    process.exit(0);
  })
  .catch(err => {
    logger.error('❌ Error seeding database:', err);
    process.exit(1);
  });
