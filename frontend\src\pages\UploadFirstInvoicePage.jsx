import { useState, useEffect, useRef } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { setItem, getItem, STORAGE_KEYS } from '../utils/localStorage';
import { trackNavigation } from '../utils/navigationTracker';
import Spinner from '../components/Spinner';
import InvoiceUpload from '../components/InvoiceUpload';
import invoiceService from '../services/invoice.service';
import { getLoadingMessage } from '../utils/loadingMessages';
import { showSuccessMessage, showErrorMessage, showInfoMessage } from '../utils/toastNotifications';
import { useForceScrollToTopAuthenticated } from '../utils/scrollToTop';
import '../styles/upload-invoice.css';

// DEBUG FLAG - Set to false to disable debug mode
const DEBUG_MODE = false;

const UploadFirstInvoicePage = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user } = useAuth();
  const [uploading, setUploading] = useState(false);
  const [showExtraction, setShowExtraction] = useState(false);
  const [debugInfo, setDebugInfo] = useState([]);
  const [multiFileMode, setMultiFileMode] = useState(false);
  const userType = getItem(STORAGE_KEYS.USER_TYPE) || 'user';
  const invoiceUploadRef = useRef(null);

  // Debug logger function
  const logDebug = (message) => {
    if (DEBUG_MODE) {
      const timestamp = new Date().toISOString();
      const logMessage = `${timestamp}: ${message}`;
      console.log(`DEBUG: ${logMessage}`);
      setDebugInfo(prev => [...prev, logMessage]);
    }
  };

  // Log initial render and force scroll to top
  useEffect(() => {
    // Force scroll to top when page loads (SUPER AGGRESSIVE for authenticated pages)
    useForceScrollToTopAuthenticated();

    logDebug(`Component mounted at path: ${location.pathname}`);
    logDebug(`User type: ${userType}`);
    logDebug(`Initial uploading state: ${uploading}`);
    logDebug(`Initial showExtraction state: ${showExtraction}`);

    // Prevent navigation away from this page
    const handleBeforeUnload = (e) => {
      if (uploading || showExtraction) {
        e.preventDefault();
        e.returnValue = '';
        return '';
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [location.pathname, userType, uploading, showExtraction]);

  // Handle successful upload
  const handleUploadSuccess = (result) => {
    logDebug(`Upload success callback called with result: ${JSON.stringify(result)}`);

    // CRITICAL FIX: Always set uploading to false first to hide the spinner
    setUploading(false);

    // Check if this is the final submission after data extraction
    const isFinalSubmission = result && result.message === 'Invoice processed successfully';

    if (isFinalSubmission) {
      logDebug('Final submission detected, preparing to navigate to dashboard');

      // Save the invoice details to localStorage for future reference
      if (result.invoice) {
        logDebug(`Saving invoice details to localStorage: ${JSON.stringify(result.invoice)}`);

        // Store the invoice ID for future reference
        setItem('lastInvoiceId', result.invoice.id);

        // Store the complete invoice details
        const invoiceDetails = {
          id: result.invoice.id,
          originalFilename: result.invoice.originalFilename,
          s3Key: result.invoice.s3Key,
          publicUrl: result.invoice.publicUrl,
          status: result.invoice.status,
          metadata: result.metadata,
          updatedAt: result.invoice.updatedAt
        };

        setItem('lastInvoiceDetails', JSON.stringify(invoiceDetails));

        logDebug(`Invoice details saved to localStorage`);
      } else {
        logDebug('No invoice details found in result');
      }

      // Set a flag in localStorage to indicate the user has uploaded their first invoice
      setItem('firstInvoiceUploaded', 'true');

      // Set the flag indicating the user has completed the first-time flow
      localStorage.setItem('firstTimeFlowCompleted', 'true');

      // Clear the first-time flow flag
      localStorage.removeItem('first_time_flow');

      // IMPORTANT: Reset all states before navigation
      setUploading(false);
      setShowExtraction(false);

      // Show success message
      showSuccessMessage('INVOICE_PROCESSED');

      // Track this navigation event
      trackNavigation(
        '/upload-first-invoice',
        '/dashboard',
        'react-router-navigate',
        {
          action: 'first-invoice-uploaded',
          userType: userType,
          invoiceId: result.invoice ? result.invoice.id : null
        }
      );

      logDebug('Navigating to dashboard now');

      // Navigate to dashboard
      navigate('/dashboard', {
        state: {
          message: 'Your first energy bill has been uploaded successfully! You can now explore energy offers.',
          invoiceId: result.invoice ? result.invoice.id : null
        },
        replace: true
      });
    } else {
      // For any other response, including data extraction, don't navigate
      logDebug('Not navigating to dashboard - waiting for user to confirm data');

      // Force show extraction form
      setShowExtraction(true);

      // IMPORTANT: Reset uploading state
      setUploading(false);

      // Log the result for debugging
      if (result && result.message === 'Data extracted successfully') {
        logDebug(`Data extracted successfully: ${JSON.stringify(result.extractedData)}`);

        // Store the temporary invoice ID for later use
        if (result.invoice && result.invoice.id) {
          localStorage.setItem('tempInvoiceId', result.invoice.id);
          logDebug(`Stored temporary invoice ID: ${result.invoice.id}`);
        }
      } else {
        logDebug(`Unexpected response: ${JSON.stringify(result)}`);
      }
    }
  };

  // Set uploading state when the upload starts
  const handleUploadStart = () => {
    logDebug('Upload started, showing spinner');

    // Only set uploading to true if we're not already showing the extraction form
    if (!showExtraction) {
      setUploading(true);
    } else {
      // If we're already showing the extraction form, don't set uploading to true
      logDebug('Not setting uploading to true because extraction form is already showing');
    }

    // Don't reset extraction form flag when starting a new upload if it's already true
    if (!showExtraction) {
      setShowExtraction(false);
    }
  };

  // Handle upload error
  const handleUploadError = (error) => {
    logDebug(`Upload error: ${error.message}`);
    showErrorMessage('UPLOAD_FAILED', error.message);
    setUploading(false);
  };

  // Force show extraction form for testing
  const forceShowExtraction = () => {
    logDebug('Forcing extraction form to show');

    // Create a dummy invoice ID for testing
    const dummyInvoiceId = 'dummy-' + Date.now();
    localStorage.setItem('tempInvoiceId', dummyInvoiceId);
    logDebug(`Created dummy invoice ID: ${dummyInvoiceId}`);

    setShowExtraction(true);
    setUploading(false);
  };

  return (
    <div className="fullscreen-container upload-first-invoice-page">
      {/* Only show the spinner at the page level, not in the component */}
      {uploading && (
        <Spinner
          fullScreen={true}
          message={getLoadingMessage('PROCESSING_INVOICE')}
          size="large"
          color="#3498db"
        />
      )}

      <div className="upload-first-invoice-container">
        <h1 className="page-title">Upload First Invoice</h1>

        {/* Debug panel - only shown in debug mode */}
        {DEBUG_MODE && (
          <div style={{
            position: 'fixed',
            top: '10px',
            right: '10px',
            width: '300px',
            maxHeight: '300px',
            overflowY: 'auto',
            backgroundColor: 'rgba(0,0,0,0.8)',
            color: '#00ff00',
            padding: '10px',
            fontSize: '12px',
            fontFamily: 'monospace',
            zIndex: 9999,
            borderRadius: '5px'
          }}>
            <h3 style={{ margin: '0 0 10px 0', color: '#fff' }}>Debug Info</h3>
            <div>
              <button
                onClick={forceShowExtraction}
                style={{
                  backgroundColor: '#00ff00',
                  color: '#000',
                  border: 'none',
                  padding: '5px 10px',
                  marginBottom: '10px',
                  cursor: 'pointer',
                  borderRadius: '3px'
                }}
              >
                Force Show Extraction Form
              </button>
            </div>
            <div>
              <p><strong>States:</strong></p>
              <p>uploading: {uploading.toString()}</p>
              <p>showExtraction: {showExtraction.toString()}</p>
            </div>
            <div>
              <p><strong>Log:</strong></p>
              {debugInfo.map((log, index) => (
                <div key={index} style={{ marginBottom: '5px', borderBottom: '1px solid #333', paddingBottom: '5px' }}>
                  {log}
                </div>
              ))}
            </div>
          </div>
        )}

        <div className="upload-first-invoice-content">
          <div className="upload-icon">
            <svg width="120" height="120" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M70 25H45L30 40V85H70V25Z" stroke="#333" strokeWidth="2.5" />
              <path d="M45 25V40H30" stroke="#333" strokeWidth="2.5" />
              <path d="M40 55H60" stroke="#333" strokeWidth="2.5" />
              <path d="M40 65H60" stroke="#333" strokeWidth="2.5" />
              <path d="M40 75H50" stroke="#333" strokeWidth="2.5" />

              {/* Energy symbol */}
              <path d="M20 60L25 45H35L30 55H40L25 75L28 60H20Z" fill="#000000" stroke="#333" strokeWidth="1.5" />

              {/* Euro symbol */}
              <path d="M75 60C72 60 70 58 70 55H80M70 50H78M75 65C78 65 80 63 80 60" stroke="#2ecc71" strokeWidth="2.5" />

              {/* Scale balance */}
              <path d="M45 40C45 40 40 45 40 50C40 55 50 55 50 50C50 45 45 40 45 40Z" fill="rgba(0, 0, 0, 0.2)" />
              <circle cx="45" cy="55" r="10" stroke="#333" strokeWidth="2.5" fill="rgba(46, 204, 113, 0.2)" />
              <path d="M42 55L45 58L48 55" stroke="#333" strokeWidth="2.5" />
              <path d="M45 52V58" stroke="#333" strokeWidth="2.5" />
            </svg>
          </div>

          <p className="upload-description">
            Upload your first {userType === 'individual' ? 'personal' : 'business'} electricity and/or gas bill.
            This will help us analyze your energy usage and find the best deals tailored to your needs.
          </p>

          {/* Upload mode toggle */}
          <div className="upload-mode-toggle" style={{
            textAlign: 'center',
            marginBottom: '20px',
            padding: '15px',
            backgroundColor: '#f8f9fa',
            borderRadius: '8px',
            border: '1px solid #e9ecef'
          }}>
            <p style={{ margin: '0 0 10px 0', fontWeight: '500', color: '#333' }}>
              Upload Mode:
            </p>
            <div style={{ display: 'flex', justifyContent: 'center', gap: '10px' }}>
              <button
                onClick={() => setMultiFileMode(false)}
                style={{
                  padding: '8px 16px',
                  border: '2px solid',
                  borderColor: !multiFileMode ? '#000' : '#ccc',
                  backgroundColor: !multiFileMode ? '#000' : 'white',
                  color: !multiFileMode ? 'white' : '#333',
                  borderRadius: '4px',
                  cursor: 'pointer',
                  fontWeight: '500',
                  transition: 'all 0.2s ease'
                }}
              >
                📄 Single File
              </button>
              <button
                onClick={() => setMultiFileMode(true)}
                style={{
                  padding: '8px 16px',
                  border: '2px solid',
                  borderColor: multiFileMode ? '#000' : '#ccc',
                  backgroundColor: multiFileMode ? '#000' : 'white',
                  color: multiFileMode ? 'white' : '#333',
                  borderRadius: '4px',
                  cursor: 'pointer',
                  fontWeight: '500',
                  transition: 'all 0.2s ease'
                }}
              >
                📑 Multi-Page Invoice
              </button>
            </div>
            <p style={{
              margin: '10px 0 0 0',
              fontSize: '0.85rem',
              color: '#666',
              fontStyle: 'italic'
            }}>
              {multiFileMode
                ? 'Perfect for invoices with multiple pages - upload all pages together'
                : 'Standard mode for single-page invoices or PDFs'
              }
            </p>
          </div>

          {/* Use the InvoiceUpload component with ref to access its methods */}
          <InvoiceUpload
            ref={invoiceUploadRef}
            onUploadSuccess={handleUploadSuccess}
            onUploadError={handleUploadError}
            onUploadStart={handleUploadStart}
            forceShowExtraction={showExtraction}
            allowMultipleFiles={multiFileMode}
          />

          {/* Confirm and Continue button */}
          {showExtraction && (
            <div style={{ textAlign: 'center', marginTop: '20px', marginBottom: '30px', paddingBottom: '20px' }}>
              <button
                onClick={() => {
                  logDebug('Confirm and Continue button clicked');
                  setUploading(true);

                  // Check if we have a reference to the InvoiceUpload component
                  if (invoiceUploadRef.current) {
                    logDebug('Calling handleFinalSubmit method on InvoiceUpload component');

                    // Call the handleFinalSubmit method directly
                    invoiceUploadRef.current.handleFinalSubmit();
                  } else {
                    logDebug('No reference to InvoiceUpload component, using fallback approach');

                    // Get the invoice ID from localStorage
                    let invoiceId = localStorage.getItem('tempInvoiceId');

                    if (!invoiceId) {
                      logDebug('No invoice ID found in localStorage, creating a dummy one');
                      // Create a dummy invoice ID for testing
                      invoiceId = 'dummy-' + Date.now();
                      localStorage.setItem('tempInvoiceId', invoiceId);
                      logDebug(`Created dummy invoice ID: ${invoiceId}`);
                    }

                    logDebug(`Using invoice ID: ${invoiceId}`);

                    // Get form values directly from the DOM
                    const formValues = {
                      invoiceDate: document.querySelector('input[name="invoiceDate"]')?.value || new Date().toISOString().split('T')[0],
                      invoiceNumber: document.querySelector('input[name="invoiceNumber"]')?.value || 'TEST-123456',
                      provider: document.querySelector('input[name="provider"]')?.value || 'Test Provider',
                      energyType: document.querySelector('select[name="energyType"]')?.value || 'electricity',
                      pointOfDelivery: document.querySelector('input[name="pointOfDelivery"]')?.value || '12345678901234',
                      amount: document.querySelector('input[name="amount"]')?.value || '123.45',
                      currency: document.querySelector('select[name="currency"]')?.value || 'EUR',
                      consumption: document.querySelector('input[name="consumption"]')?.value || '500',
                      notes: document.querySelector('textarea[name="notes"]')?.value || ''
                    };

                    logDebug(`Form values: ${JSON.stringify(formValues)}`);

                    logDebug('Creating invoice record in MongoDB with file details and metadata');

                    // Get the file details from localStorage if available
                    let fileDetails = null;
                    try {
                      const fileDetailsStr = localStorage.getItem('tempFileDetails');
                      if (fileDetailsStr) {
                        fileDetails = JSON.parse(fileDetailsStr);
                        logDebug(`Retrieved file details from localStorage: ${JSON.stringify(fileDetails)}`);
                      }
                    } catch (e) {
                      logDebug(`Error parsing file details from localStorage: ${e.message}`);
                    }

                    // If no file details in localStorage, create a dummy file
                    if (!fileDetails) {
                      fileDetails = {
                        name: 'test-invoice.pdf',
                        size: 1024 * 1024, // 1MB
                        type: 'application/pdf'
                      };
                      logDebug(`Using dummy file details: ${JSON.stringify(fileDetails)}`);
                    }

                    // Add status to the form values
                    const finalFormValues = {
                      ...formValues,
                      status: 'processed'
                    };

                    logDebug(`Creating invoice record with metadata: ${JSON.stringify(finalFormValues)}`);

                    // Create the invoice record with file details and metadata
                    invoiceService.createInvoiceRecord(fileDetails, finalFormValues)
                      .then(result => {
                        logDebug(`Successfully created invoice record: ${JSON.stringify(result)}`);

                        // Clean up localStorage
                        localStorage.removeItem('tempFileDetails');
                        localStorage.removeItem('tempInvoiceId');

                        // Trigger the success callback
                        handleUploadSuccess({
                          message: 'Invoice processed successfully',
                          invoice: result.invoice,
                          metadata: finalFormValues
                        });
                      })
                      .catch(error => {
                        logDebug(`Error saving invoice data: ${error.message}`);
                        setUploading(false);
                        showErrorMessage('PROCESSING_FAILED', `Error: ${error.message}`);
                      });
                  }
                }}
                style={{
                  backgroundColor: 'black',
                  color: 'white',
                  padding: '12px 24px',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: 'pointer',
                  fontWeight: 'bold',
                  transition: 'background-color 0.3s ease',
                  boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
                }}
                onMouseOver={(e) => {
                  e.currentTarget.style.backgroundColor = '#333';
                  e.currentTarget.style.boxShadow = '0 4px 8px rgba(0,0,0,0.2)';
                }}
                onMouseOut={(e) => {
                  e.currentTarget.style.backgroundColor = 'black';
                  e.currentTarget.style.boxShadow = '0 2px 4px rgba(0,0,0,0.1)';
                }}
              >
                ✓ Confirm and Continue
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default UploadFirstInvoicePage;
