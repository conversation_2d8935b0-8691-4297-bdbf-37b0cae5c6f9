const dotenv = require('dotenv');
const path = require('path');

// Load environment variables
dotenv.config({
  path: path.resolve(__dirname, '.env.uat'),
  override: false,
});

const { cognitoIdentityServiceProvider, userPoolConfig } = require('./config/cognito');
const logger = require('./utils/logger');

async function setupCognitoCustomAttributes() {
  console.log('🔧 Setting up Cognito Custom Attributes...\n');

  console.log('📧 Configuration:');
  console.log(`User Pool ID: ${userPoolConfig.UserPoolId}`);
  console.log(`AWS Region: ${process.env.AWS_REGION}\n`);

  try {
    // First, let's check current user pool configuration
    console.log('🔍 Checking current User Pool configuration...');
    
    const userPool = await cognitoIdentityServiceProvider.describeUserPool({
      UserPoolId: userPoolConfig.UserPoolId
    }).promise();

    console.log('✅ User Pool found:', userPool.UserPool.Name);
    
    // Check existing custom attributes
    const existingAttributes = userPool.UserPool.Schema || [];
    const customAttributes = existingAttributes.filter(attr => attr.Name.startsWith('custom:'));
    
    console.log('\n📋 Existing custom attributes:');
    if (customAttributes.length === 0) {
      console.log('   No custom attributes found');
    } else {
      customAttributes.forEach(attr => {
        console.log(`   - ${attr.Name} (${attr.AttributeDataType})`);
      });
    }

    // Check if our required attributes exist
    const requiredAttributes = ['custom:status', 'custom:profileCompletion'];
    const missingAttributes = requiredAttributes.filter(reqAttr => 
      !customAttributes.some(existingAttr => existingAttr.Name === reqAttr)
    );

    if (missingAttributes.length === 0) {
      console.log('\n✅ All required custom attributes already exist!');
      return;
    }

    console.log('\n⚠️ Missing custom attributes:', missingAttributes);
    console.log('\n🚨 IMPORTANT: Custom attributes cannot be added to existing User Pools!');
    console.log('📝 You have two options:');
    console.log('1. Create a new User Pool with custom attributes');
    console.log('2. Use a workaround with existing attributes or external storage');
    
    console.log('\n💡 Recommended Workaround:');
    console.log('Since we cannot add custom attributes to existing User Pool,');
    console.log('we should modify the code to:');
    console.log('1. Store status only in MongoDB (primary source)');
    console.log('2. Check status from MongoDB during login');
    console.log('3. Skip Cognito custom attribute sync');

    console.log('\n🔧 Alternative: Use existing Cognito attributes');
    console.log('We could potentially use:');
    console.log('- User status in Cognito (enabled/disabled)');
    console.log('- Custom user attributes if any exist');

    // Check if we can at least disable users in Cognito
    console.log('\n🧪 Testing Cognito user enable/disable functionality...');
    
    // This is a safer approach - we can enable/disable users in Cognito
    console.log('✅ We can use Cognito\'s built-in user enable/disable feature');
    console.log('   - Suspended users: adminDisableUser()');
    console.log('   - Active users: adminEnableUser()');

  } catch (error) {
    console.error('❌ Error setting up Cognito attributes:', error.message);
    
    if (error.code === 'UserPoolNotFound') {
      console.log('\n💡 User Pool not found. Please check:');
      console.log('- COGNITO_USER_POOL_ID in environment variables');
      console.log('- AWS region configuration');
      console.log('- AWS credentials and permissions');
    } else if (error.code === 'AccessDenied') {
      console.log('\n💡 Access denied. Please check:');
      console.log('- AWS credentials have Cognito permissions');
      console.log('- IAM user has cognito-idp:DescribeUserPool permission');
    }
  }
}

// Run the setup if this file is executed directly
if (require.main === module) {
  setupCognitoCustomAttributes()
    .then(() => {
      console.log('\n✨ Cognito attribute setup completed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Setup failed:', error);
      process.exit(1);
    });
}

module.exports = { setupCognitoCustomAttributes };
