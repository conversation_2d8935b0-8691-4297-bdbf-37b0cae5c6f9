/* ========== HOME PAGE STYLES ========== */

/* Home Container */
.home-container {
  width: 100%;
  max-width: 100%;
  margin: 0;
  padding: 0;
  background-color: #f9f9f9;
  color: #333;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  overflow-x: hidden;
  box-sizing: border-box;
}

/* Home Header */
.home-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 5%;
  background-color: #fff;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  position: sticky;
  top: 0;
  z-index: 100;
  min-height: 70px;
  box-sizing: border-box;
  gap: 2rem;
}

.home-logo-container {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  z-index: 101;
  flex: 1;
  min-width: 0;
}

.home-logo {
  width: 60px;
  height: 60px;
  object-fit: contain;
  flex-shrink: 0;
}

/* Mobile menu toggle button */
.home-menu-toggle {
  display: none;
  background-color: rgba(0, 0, 0, 0.08);
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  position: relative;
  width: 42px;
  height: 42px;
  z-index: 102;
  margin-left: 1rem;
  border-radius: 6px;
  flex-shrink: 0;
  transition: background-color 0.2s ease;
}

.home-menu-toggle:hover {
  background-color: rgba(0, 0, 0, 0.12);
}

.home-menu-toggle.visible {
  display: flex !important;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.home-menu-toggle span {
  display: block;
  width: 22px;
  height: 2.5px;
  background-color: #000;
  margin: 3px 0;
  transition: all 0.3s ease;
  border-radius: 1px;
  position: relative;
}

.home-menu-toggle span.open:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
}

.home-menu-toggle span.open:nth-child(2) {
  opacity: 0;
}

.home-menu-toggle span.open:nth-child(3) {
  transform: rotate(-45deg) translate(5px, -5px);
}

.hidden {
  display: none !important;
}

/* Desktop navigation */
.home-nav {
  display: flex;
  gap: 2rem;
  margin-right: 2rem;
}

.home-nav-link {
  color: #555;
  text-decoration: none;
  font-weight: 500;
  font-size: 1rem;
  transition: color 0.2s ease;
  position: relative;
}

.home-nav-link:hover {
  color: #000;
}

.home-nav-link.active {
  color: #000;
}

.home-nav-link.active::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: #000;
}

.home-auth-buttons {
  display: flex;
  gap: 1rem;
  flex-shrink: 0;
}

/* ========== HOME PAGE BUTTON STYLES ========== */
/* Primary buttons on homepage */
.home-container .btn-primary,
.home-container .btn.btn-primary,
.home-auth-buttons .btn-primary,
.hero-cta .btn-primary,
.cta-section .btn-primary {
  background-color: #000;
  background-image: none;
  color: #fff;
  border: 2px solid #000;
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 600;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  cursor: pointer;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.home-container .btn-primary:hover,
.home-container .btn.btn-primary:hover,
.home-auth-buttons .btn-primary:hover,
.hero-cta .btn-primary:hover,
.cta-section .btn-primary:hover {
  background-color: #333;
  background-image: none;
  color: #fff;
  border-color: #333;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
}

/* Outline buttons on homepage */
.home-container .btn-outline,
.home-container .btn.btn-outline,
.home-auth-buttons .btn-outline,
.hero-cta .btn-outline {
  background-color: transparent;
  background-image: none;
  color: #000;
  border: 2px solid #000;
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 600;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  cursor: pointer;
}

.home-container .btn-outline:hover,
.home-container .btn.btn-outline:hover,
.home-auth-buttons .btn-outline:hover,
.hero-cta .btn-outline:hover {
  background-color: #000;
  color: #fff;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

/* Mobile navigation menu */
.home-mobile-menu {
  display: flex;
  position: fixed;
  top: 70px;
  left: 0;
  width: 100%;
  background-color: #fff;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  padding: 1rem;
  z-index: 100;
  flex-direction: column;
  gap: 1rem;
}

.mobile-menu-header {
  display: flex;
  justify-content: flex-end;
  padding: 0 0.5rem 0.5rem 0;
  border-bottom: 1px solid #eee;
  margin-bottom: 0.5rem;
}

.mobile-menu-close {
  background: none;
  border: none;
  cursor: pointer;
  color: #333;
  padding: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.mobile-menu-close:hover {
  background-color: rgba(0, 0, 0, 0.05);
  color: #000;
}

/* Hero Section */
.hero-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 5rem 5%;
  background: linear-gradient(135deg, #f5f7fa 0%, #e4e8f0 100%);
}

.hero-content {
  max-width: 600px;
}

.hero-title {
  font-size: 3rem;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 1.5rem;
  color: #000;
}

.hero-subtitle {
  font-size: 1.25rem;
  line-height: 1.6;
  color: #555;
  margin-bottom: 2rem;
}

.hero-cta {
  display: flex;
  gap: 1rem;
}

.hero-image {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.hero-image-placeholder {
  width: 400px;
  height: 400px;
  background-color: rgba(0, 0, 0, 0.03);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  color: rgba(0, 0, 0, 0.1);
}

.energy-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #000;
  opacity: 0.8;
}

/* Features Section */
.features-section {
  padding: 3rem 5% 5rem;
  background-color: #fff;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  text-align: center;
  margin: 0 0 2rem 0;
  color: #000;
}

/* When section has subtitle, adjust spacing */
.section-title:has(+ .section-subtitle) {
  margin-bottom: 1.5rem;
}

.section-title + .section-subtitle {
  margin-top: 0;
}

.section-subtitle {
  font-size: 1.2rem;
  line-height: 1.6;
  color: #64748b;
  margin: 0 auto 2rem;
  max-width: 600px;
  text-align: center;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 2rem;
  max-width: 1200px;
  margin: 2rem auto 0;
  align-items: stretch;
  padding: 0 2rem;
  justify-content: center;
}

/* For sections with 6 cards, use 3 columns */
.additional-features-section .features-grid {
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
}

@media (max-width: 1024px) {
  .features-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
    padding: 0 3rem;
    max-width: 800px;
  }
}

@media (max-width: 768px) {
  .features-grid {
    grid-template-columns: 1fr !important;
    gap: 1.5rem !important;
    padding: 0 2rem !important;
    max-width: 400px !important;
    margin: 2rem auto 0 !important;
  }
}

@media (max-width: 600px) {
  .features-grid {
    grid-template-columns: 1fr !important;
    gap: 1.25rem !important;
    padding: 0 1.5rem !important;
    max-width: 350px !important;
  }
}

/* Force single column on all mobile devices - HIGHEST PRIORITY */
@media screen and (max-width: 767px) {
  .features-section .features-grid,
  .how-it-works-section .steps-container,
  .testimonials-section .testimonials-container {
    display: grid !important;
    grid-template-columns: 1fr !important;
    grid-template-rows: auto !important;
    width: 100% !important;
    max-width: 400px !important;
    margin: 2rem auto 0 !important;
    padding: 0 2rem !important;
    gap: 1.5rem !important;
  }
}

/* Additional mobile override for very specific targeting */
@media only screen and (max-width: 768px) {
  body .home-container .features-section .features-grid {
    grid-template-columns: 1fr !important;
  }

  body .home-container .how-it-works-section .steps-container {
    grid-template-columns: 1fr !important;
  }

  body .home-container .testimonials-section .testimonials-container {
    grid-template-columns: 1fr !important;
  }
}

.feature-card {
  background-color: #f9f9f9;
  border-radius: 12px;
  padding: 2rem;
  text-align: center;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 300px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  justify-content: flex-start;
  align-items: center;
  position: relative;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.feature-icon {
  width: 80px;
  height: 80px;
  background-color: #000;
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  flex-shrink: 0;
}

.feature-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #000;
  text-align: center;
  line-height: 1.3;
  min-height: 2.6rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.feature-description {
  font-size: 1rem;
  line-height: 1.6;
  color: #555;
  text-align: center;
  flex-grow: 1;
  display: block;
  margin: 0;
  padding: 0;
}

/* How It Works Section */
.how-it-works-section {
  padding: 3rem 5% 5rem;
  background-color: #f5f7fa;
}

.steps-container {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 2rem;
  max-width: 1200px;
  margin: 2rem auto 0;
  align-items: stretch;
  padding: 0 2rem;
  justify-content: center;
}

@media (max-width: 1024px) {
  .steps-container {
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
    padding: 0 3rem;
    max-width: 800px;
  }
}

@media (max-width: 768px) {
  .steps-container {
    grid-template-columns: 1fr !important;
    gap: 1.5rem !important;
    padding: 0 2rem !important;
    max-width: 400px !important;
  }
}

.step-card {
  background-color: #fff;
  border-radius: 12px;
  padding: 2rem 1.5rem;
  text-align: center;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 300px;
  justify-content: space-between;
  align-items: center;
}

.step-number {
  width: 45px;
  height: 45px;
  background-color: #000;
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0 auto 1.5rem;
  flex-shrink: 0;
}

.step-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #000;
  text-align: center;
  line-height: 1.3;
  min-height: 2.6rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.step-description {
  font-size: 0.9rem;
  line-height: 1.5;
  color: #555;
  text-align: center;
  flex-grow: 1;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  margin-top: auto;
}

/* Testimonials Section */
.testimonials-section {
  padding: 3rem 5% 5rem;
  background-color: #fff;
}

.testimonials-container {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
  max-width: 1000px;
  margin: 2rem auto 0;
  align-items: stretch;
  padding: 0 1rem;
}

@media (max-width: 1024px) {
  .testimonials-container {
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
    padding: 0 2rem;
  }
}

@media (max-width: 768px) {
  .testimonials-container {
    grid-template-columns: 1fr !important;
    gap: 1.5rem !important;
    padding: 0 2rem !important;
    max-width: 400px !important;
  }
}

.testimonial-card {
  background-color: #f9f9f9;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 250px;
  justify-content: space-between;
  align-items: stretch;
}

.testimonial-content {
  margin-bottom: 1.5rem;
  flex-grow: 1;
  display: flex;
  align-items: center;
}

.testimonial-content p {
  font-size: 1rem;
  line-height: 1.6;
  color: #333;
  font-style: italic;
}

.testimonial-author {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.testimonial-avatar {
  width: 50px;
  height: 50px;
  background-color: #000;
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  font-weight: 600;
}

.testimonial-info h4 {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 0.25rem;
  color: #000;
}

.testimonial-info p {
  font-size: 0.875rem;
  color: #666;
  margin: 0;
}

/* CTA Section */
.cta-section {
  padding: 3rem 5% 5rem;
  background: linear-gradient(135deg, #000 0%, #333 100%);
  color: #fff;
  text-align: center;
}

.cta-content {
  max-width: 800px;
  margin: 0 auto;
}

.cta-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
}

.cta-description {
  font-size: 1.25rem;
  line-height: 1.6;
  margin-bottom: 2rem;
  opacity: 0.9;
}

/* Footer */
.home-footer {
  background-color: #f5f7fa;
  padding: 5rem 5% 2rem;
}

.footer-content {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: 3rem;
  margin-bottom: 3rem;
}

.footer-logo {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 0.5rem;
}

.footer-logo-img {
  width: 70px;
  height: 70px;
  object-fit: contain;
  background-color: #eaedf0;
  border-radius: 6px;
  transition: all 0.2s ease;
  padding: 6px;
}

.footer-logo-img:hover {
  background-color: #e0e4e8;
}

.footer-links {
  display: flex;
  flex-wrap: wrap;
  gap: 3rem;
}

.footer-column {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.footer-column h4 {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #000;
}

.footer-column a {
  color: #555;
  text-decoration: none;
  font-size: 0.9rem;
  transition: color 0.2s ease;
}

.footer-column a:hover {
  color: #000;
}

.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 2rem;
  border-top: 1px solid #eaeaea;
}

.footer-bottom p {
  font-size: 0.875rem;
  color: #666;
  margin: 0;
}

.footer-social {
  display: flex;
  gap: 1rem;
}

.footer-social a {
  color: #666;
  transition: color 0.2s ease;
}

.footer-social a:hover {
  color: #000;
}

/* Button Styles - Override global theme */
.home-container .btn,
.home-page .btn,
.btn {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 0.75rem 1.5rem !important;
  border-radius: 6px !important;
  font-weight: 600 !important;
  font-size: 1rem !important;
  text-decoration: none !important;
  transition: all 0.2s ease !important;
  cursor: pointer !important;
  white-space: nowrap !important; /* Prevent text from wrapping */
  margin: 0.5rem !important;
  height: auto !important;
  min-height: 48px !important;
}

.home-container .btn-primary,
.home-page .btn-primary,
.btn-primary {
  background-color: #000 !important;
  color: #fff !important;
  border: 2px solid #000 !important;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15) !important;
}

.home-container .btn-primary:hover,
.home-page .btn-primary:hover,
.btn-primary:hover {
  background-color: #333 !important;
  border-color: #333 !important;
  color: #fff !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2) !important;
}

.home-container .btn-outline,
.home-page .btn-outline,
.btn-outline {
  background-color: transparent !important;
  color: #000 !important;
  border: 2px solid #000 !important;
}

.home-container .btn-outline:hover,
.home-page .btn-outline:hover,
.btn-outline:hover {
  background-color: rgba(0, 0, 0, 0.05) !important;
  color: #000 !important;
}

.home-container .btn-large,
.home-page .btn-large,
.btn-large {
  padding: 1rem 2rem !important;
  font-size: 1.125rem !important;
}

/* Contact Page Styles */
/* Contact Information Section */
.contact-info-section {
  padding: 4rem 5%;
  background-color: #fff;
}

.contact-info-container {
  max-width: 1200px;
  margin: 0 auto;
}

.contact-methods-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.contact-method-card {
  background-color: #f9f9f9;
  border-radius: 12px;
  padding: 2rem;
  text-align: center;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.contact-method-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.contact-title {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: #000;
}

.contact-description {
  font-size: 1rem;
  line-height: 1.6;
  color: #555;
  margin-bottom: 1.5rem;
}

.contact-methods {
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
  margin-bottom: 1.5rem;
}

.contact-method {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.contact-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 64px;
  height: 64px;
  background-color: #000;
  color: #fff;
  border-radius: 50%;
  margin: 0 auto 1.5rem;
}

.contact-method-card h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #000;
}

.contact-method-card p {
  font-size: 1rem;
  color: #555;
  margin-bottom: 0.5rem;
  line-height: 1.5;
}

.contact-hours {
  font-size: 0.875rem;
  color: #777;
  font-style: italic;
}

/* Contact Form Section */
.contact-form-section {
  padding: 4rem 5%;
  background-color: #f9f9f9;
}

.contact-form-container-new {
  max-width: 800px;
  margin: 0 auto;
}

.contact-form-wrapper {
  background-color: #fff;
  border-radius: 12px;
  padding: 3rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  margin-top: 2rem;
}

.contact-form-container {
  flex: 1;
  min-width: 300px;
  background-color: #fff;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.contact-form h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: #000;
  text-align: center;
}

.contact-form .form-group {
  margin-bottom: 1.25rem;
}

.contact-form .form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  font-size: 0.95rem;
  color: #333;
}

.contact-form .form-input {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #999;
  border-radius: 6px;
  font-size: 1rem;
  transition: all 0.2s ease;
  background-color: #fff;
  color: #000;
  box-sizing: border-box;
}

/* Contact form and invoice form input styling only */
.contact-form .form-input,
.invoice-form .form-input {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #999;
  border-radius: 6px;
  font-size: 1rem;
  transition: all 0.2s ease;
  background-color: #fff !important;
  color: #000 !important;
  box-sizing: border-box;
}

.contact-form .form-input:focus,
.invoice-form .form-input:focus {
  outline: none;
  border-color: #000;
  box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.05);
}

/* Specific styling for invoice upload forms only */
.metadata-section input[type="text"],
.metadata-section input[type="email"],
.metadata-section input[type="tel"],
.metadata-section input[type="number"],
.metadata-section input[type="date"],
.metadata-section select,
.metadata-section textarea {
  background-color: #fff !important;
  color: #000 !important;
  border: 1px solid #999 !important;
  padding: 0.75rem !important;
  font-size: 1rem !important;
}

.metadata-section input[type="text"]:focus,
.metadata-section input[type="email"]:focus,
.metadata-section input[type="tel"]:focus,
.metadata-section input[type="number"]:focus,
.metadata-section input[type="date"]:focus,
.metadata-section select:focus,
.metadata-section textarea:focus {
  border-color: #000 !important;
  outline: none !important;
}

/* Also target the form-input class specifically in metadata section */
.metadata-section .form-input {
  background-color: #fff !important;
  color: #000 !important;
  border: 1px solid #999 !important;
  padding: 0.75rem !important;
  font-size: 1rem !important;
}

.metadata-section .form-input:focus {
  border-color: #000 !important;
  outline: none !important;
}

.contact-form .form-input:focus {
  outline: none;
  border-color: #000;
  box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.05);
}

.form-error-message {
  background-color: #fff0f0;
  color: #e53e3e;
  padding: 0.75rem;
  border-radius: 6px;
  margin-bottom: 1.5rem;
  text-align: center;
  font-size: 0.9rem;
}

.form-textarea {
  width: 100% !important;
  padding: 0.75rem !important;
  border: 2px solid #999 !important;
  border-radius: 6px !important;
  font-size: 1rem !important;
  transition: all 0.2s ease !important;
  resize: vertical !important;
  background-color: #fff !important;
  color: #333 !important;
  font-family: inherit !important;
  box-sizing: border-box !important;
  min-height: 120px !important;
}

.contact-form .form-textarea {
  width: 100% !important;
  padding: 0.75rem !important;
  border: 2px solid #999 !important;
  border-radius: 6px !important;
  font-size: 1rem !important;
  transition: all 0.2s ease !important;
  resize: vertical !important;
  background-color: #fff !important;
  color: #333 !important;
  font-family: inherit !important;
  box-sizing: border-box !important;
  min-height: 120px !important;
}

.form-textarea:focus {
  outline: none !important;
  border-color: #000 !important;
  box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.05) !important;
}

.contact-form .form-textarea:focus {
  outline: none !important;
  border-color: #000 !important;
  box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.05) !important;
}

.btn-block {
  width: 100%;
  margin-top: 1rem;
}

.form-success {
  text-align: center;
  padding: 2rem 0;
}

.success-icon {
  width: 80px;
  height: 80px;
  background-color: #ebfbee;
  color: #38a169;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
}

.form-success h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #000;
}

.form-success p {
  font-size: 1rem;
  color: #555;
  margin-bottom: 1.5rem;
}

.map-section {
  padding: 3rem 5%;
  background-color: #fff;
}

.map-placeholder {
  width: 100%;
  height: 400px;
  background-color: #f0f0f0;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #888;
  max-width: 1200px;
  margin: 0 auto;
}

.map-icon {
  margin-bottom: 1rem;
}

/* Mobile navigation styles */
.mobile-nav {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.mobile-nav-link {
  color: #333;
  text-decoration: none;
  font-weight: 500;
  font-size: 1rem;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #eee;
  transition: all 0.2s ease;
}

.mobile-nav-link:hover {
  background-color: #f5f5f5;
  padding-left: 1.25rem;
}

.mobile-nav-link.active {
  color: #000;
  font-weight: 600;
  background-color: #f0f0f0;
  border-left: 3px solid #000;
  padding-left: calc(1rem - 3px);
}

.mobile-auth {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-top: 1rem;
  padding: 0 1rem;
}

/* Responsive Styles */
@media (min-width: 1200px) {
  .contact-container {
    gap: 4rem;
    max-width: 1600px;
  }

  .contact-info,
  .contact-form-container {
    padding: 3rem;
    max-width: 750px;
  }
}

@media (max-width: 1024px) {
  .hero-title {
    font-size: 2.5rem;
  }

  .hero-image-placeholder {
    width: 350px;
    height: 350px;
  }

  .contact-container {
    justify-content: center;
    gap: 2rem;
  }

  .contact-info,
  .contact-form-container {
    max-width: 500px;
  }
}

@media (max-width: 768px) {
  .home-header {
    padding: 0.75rem 1rem;
    min-height: 64px;
    height: auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-sizing: border-box;
    position: sticky;
    top: 0;
    background-color: #fff;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  }

  .home-logo-container {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 0.25rem;
    z-index: 101;
    min-width: 0;
    margin-right: 1rem;
  }

  .home-logo {
    width: 56px;
    height: 56px;
  }

  /* Contact page responsive styles */
  .contact-methods-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .contact-form-wrapper {
    padding: 2rem;
  }

  .contact-info-section,
  .contact-form-section {
    padding: 3rem 5%;
  }

  /* Hide desktop navigation and auth buttons */
  .desktop-nav, .desktop-auth {
    display: none;
  }

  /* Show mobile menu toggle */
  .home-menu-toggle {
    display: flex !important;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    position: relative;
    margin-left: 0;
    margin-right: 0;
    flex-shrink: 0;
    z-index: 102;
    width: 40px;
    height: 40px;
  }

  .home-menu-toggle span {
    width: 20px;
    height: 2px;
    margin: 2.5px 0;
  }

  /* Mobile menu positioning */
  .home-mobile-menu {
    position: fixed;
    top: 64px;
    left: 0;
    width: 100%;
    z-index: 100;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  /* Mobile card adjustments - Single column layout */
  .feature-card {
    padding: 2rem 1.5rem !important;
    min-height: 280px !important;
    border-radius: 12px !important;
    margin: 0 !important;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08) !important;
  }

  .step-card,
  .testimonial-card {
    padding: 2rem 1.5rem;
    min-height: 280px;
  }

  .feature-icon {
    width: 70px !important;
    height: 70px !important;
    margin: 0 auto 1.25rem !important;
  }

  .step-number {
    width: 45px;
    height: 45px;
    margin-bottom: 1.25rem;
    font-size: 1.2rem;
  }

  .feature-title {
    font-size: 1.1rem !important;
    margin-bottom: 1rem !important;
    min-height: 2.4rem !important;
    line-height: 1.2 !important;
  }

  .step-title {
    font-size: 1.1rem;
    margin-bottom: 1rem;
    min-height: 2.4rem;
  }

  .feature-description {
    font-size: 0.95rem !important;
    line-height: 1.5 !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  .step-description {
    font-size: 0.95rem;
    line-height: 1.5;
  }

  .hero-section {
    flex-direction: column;
    text-align: center;
    gap: 3rem;
  }

  .hero-cta {
    justify-content: center;
  }

  .hero-image-placeholder {
    width: 300px;
    height: 300px;
  }

  .section-title {
    font-size: 2rem;
  }

  /* Grid responsive rules are now handled in individual grid sections */

  .footer-content {
    flex-direction: column;
    gap: 2rem;
  }

  .footer-bottom {
    flex-direction: column;
    gap: 1rem;
  }
}

@media (max-width: 480px) {
  .home-logo-container {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 0.2rem;
    margin-right: 0.75rem;
  }

  .home-logo {
    width: 52px;
    height: 52px;
  }

  .home-header {
    padding: 0.6rem 0.75rem;
    min-height: 60px;
    height: auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-sizing: border-box;
  }

  .home-menu-toggle {
    margin-left: 0;
    margin-right: 0;
    width: 36px;
    height: 36px;
    flex-shrink: 0;
  }

  .home-menu-toggle span {
    width: 18px;
    height: 2px;
    margin: 2px 0;
  }

  .home-mobile-menu {
    top: 60px;
  }

  /* Additional mobile refinements for very small screens */
  .feature-card {
    padding: 1.75rem 1.25rem !important;
    min-height: 260px !important;
  }

  .feature-icon {
    width: 65px !important;
    height: 65px !important;
    margin: 0 auto 1rem !important;
  }

  .feature-title {
    font-size: 1rem !important;
    min-height: 2.2rem !important;
    margin-bottom: 0.75rem !important;
  }

  .feature-description {
    font-size: 0.9rem !important;
    line-height: 1.4 !important;
  }

  .mobile-nav-link {
    padding: 0.75rem 1rem;
  }

  .mobile-auth {
    padding: 0 1rem;
  }

  .mobile-auth .btn {
    width: 100%;
    margin-bottom: 0.5rem;
  }

  .btn-block {
    width: 100%;
    display: block;
    text-align: center;
  }

  .btn {
    padding: 0.75rem 1rem;
  }

  .hero-title {
    font-size: 2rem;
  }

  .hero-subtitle {
    font-size: 1rem;
  }

  .hero-cta {
    flex-direction: column;
    gap: 1rem;
    width: 100%;
  }

  .hero-cta .btn {
    width: 100%;
  }

  .hero-image-placeholder {
    width: 250px;
    height: 250px;
  }

  /* Contact page mobile styles */
  .contact-form-wrapper {
    padding: 1.5rem;
  }

  .contact-info-section,
  .contact-form-section {
    padding: 2rem 5%;
  }

  .contact-method-card {
    padding: 1.5rem;
  }
}

@media (max-width: 320px) {
  .home-logo-container {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 0.15rem;
    margin-right: 0.5rem;
  }

  .home-logo {
    width: 48px;
    height: 48px;
  }

  .home-header {
    padding: 0.5rem 0.5rem;
    min-height: 56px;
    height: auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-sizing: border-box;
  }

  .home-menu-toggle {
    width: 34px;
    height: 34px;
    margin-left: 0;
    margin-right: 0;
    flex-shrink: 0;
  }

  .home-menu-toggle span {
    width: 16px;
    height: 1.5px;
    margin: 2px 0;
  }

  .home-mobile-menu {
    top: 56px;
  }

  .mobile-nav-link {
    padding: 0.5rem 0.75rem;
    font-size: 0.9rem;
  }
}

/* ========== COMPREHENSIVE MOBILE RESPONSIVENESS FIXES FOR HOME PAGES ========== */

/* Global mobile fixes for home pages */
@media screen and (max-width: 768px) {
  /* Ensure all home page containers respect viewport width */
  .home-container,
  .home-header,
  .hero-section,
  .features-section,
  .how-it-works-section,
  .testimonials-section,
  .footer {
    width: 100% !important;
    max-width: 100% !important;
    overflow-x: hidden !important;
    box-sizing: border-box !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
  }

  /* Fix home header mobile responsiveness */
  .home-header {
    padding: 1rem !important;
    min-height: 60px !important;
  }

  /* Fix hero section mobile responsiveness */
  .hero-section {
    padding: 2rem 1rem !important;
    flex-direction: column !important;
    text-align: center !important;
  }

  .hero-content {
    width: 100% !important;
    max-width: 100% !important;
    margin-bottom: 2rem !important;
  }

  .hero-title {
    font-size: 2rem !important;
    line-height: 1.2 !important;
    margin-bottom: 1rem !important;
  }

  .hero-subtitle {
    font-size: 1rem !important;
    margin-bottom: 1.5rem !important;
  }

  .hero-image {
    width: 100% !important;
    max-width: 100% !important;
  }

  .hero-image-placeholder {
    width: 100% !important;
    max-width: 300px !important;
    height: 250px !important;
    margin: 0 auto !important;
  }

  /* Fix features section mobile responsiveness */
  .features-section,
  .how-it-works-section,
  .testimonials-section {
    padding: 2rem 1rem !important;
  }

  .section-title {
    font-size: 1.8rem !important;
    margin-bottom: 1rem !important;
  }

  .section-subtitle {
    font-size: 1rem !important;
    margin-bottom: 1.5rem !important;
    padding: 0 1rem !important;
  }

  /* Fix grid layouts mobile responsiveness */
  .features-grid,
  .steps-container,
  .testimonials-container {
    grid-template-columns: 1fr !important;
    gap: 1.5rem !important;
    padding: 0 !important;
    margin: 1rem auto 0 !important;
    width: 100% !important;
    max-width: 100% !important;
  }

  /* Fix card mobile responsiveness */
  .feature-card,
  .step-card,
  .testimonial-card {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
    padding: 1.5rem !important;
    overflow-x: hidden !important;
    box-sizing: border-box !important;
  }

  /* Fix navigation mobile responsiveness */
  .home-nav {
    display: none !important;
  }

  .home-menu-toggle {
    display: flex !important;
  }

  .home-mobile-menu {
    width: 100% !important;
    max-width: 100% !important;
    left: 0 !important;
    right: 0 !important;
    overflow-x: hidden !important;
    box-sizing: border-box !important;
  }

  /* Fix button mobile responsiveness */
  .btn,
  .hero-cta .btn {
    width: 100% !important;
    max-width: 300px !important;
    margin: 0.5rem auto !important;
    display: block !important;
  }

  .hero-cta {
    flex-direction: column !important;
    align-items: center !important;
    gap: 1rem !important;
  }

  /* Fix any remaining overflow issues */
  .home-container * {
    max-width: 100% !important;
    box-sizing: border-box !important;
  }

  /* Fix specific elements that might cause overflow */
  .home-logo-container,
  .home-auth-buttons {
    overflow-x: hidden !important;
    max-width: 100% !important;
  }

  .home-auth-buttons {
    gap: 0.5rem !important;
  }

  .home-auth-buttons .btn {
    padding: 0.5rem 1rem !important;
    font-size: 0.9rem !important;
    min-width: auto !important;
    width: auto !important;
  }
}

/* ========== HOMEPAGE BUTTON OVERRIDES ========== */
/* Ensure homepage buttons are always black, never blue - Maximum specificity */
.home-container .hero-cta .btn-primary,
.home-container .hero-cta .btn,
.hero-cta .btn-primary,
.hero-cta .btn,
body .home-container .btn-primary,
body .home-container .btn,
html body .home-container .btn-primary,
html body .home-container .btn,
.home-container button.btn-primary,
.home-container button.btn,
body .home-container button.btn-primary,
body .home-container button.btn,
html body .home-container button.btn-primary,
html body .home-container button.btn,
.home-page .btn-primary,
.home-page .btn,
body .home-page .btn-primary,
body .home-page .btn,
html body .home-page .btn-primary,
html body .home-page .btn {
  background-color: #000000 !important;
  color: #ffffff !important;
  border: 2px solid #000000 !important;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15) !important;
  background-image: none !important;
  background-gradient: none !important;
}

.home-container .hero-cta .btn-primary:hover,
.home-container .hero-cta .btn:hover,
.hero-cta .btn-primary:hover,
.hero-cta .btn:hover,
body .home-container .btn-primary:hover,
body .home-container .btn:hover,
html body .home-container .btn-primary:hover,
html body .home-container .btn:hover,
.home-container button.btn-primary:hover,
.home-container button.btn:hover,
body .home-container button.btn-primary:hover,
body .home-container button.btn:hover,
html body .home-container button.btn-primary:hover,
html body .home-container button.btn:hover,
.home-page .btn-primary:hover,
.home-page .btn:hover,
body .home-page .btn-primary:hover,
body .home-page .btn:hover,
html body .home-page .btn-primary:hover,
html body .home-page .btn:hover {
  background-color: #333333 !important;
  color: #ffffff !important;
  border-color: #333333 !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2) !important;
  background-image: none !important;
  background-gradient: none !important;
}

.home-container .hero-cta .btn-outline,
.hero-cta .btn-outline,
body .home-container .btn-outline,
html body .home-container .btn-outline {
  background-color: transparent !important;
  color: #000000 !important;
  border: 2px solid #000000 !important;
}

.home-container .hero-cta .btn-outline:hover,
.hero-cta .btn-outline:hover,
body .home-container .btn-outline:hover,
html body .home-container .btn-outline:hover {
  background-color: #000000 !important;
  color: #ffffff !important;
  border-color: #000000 !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2) !important;
}

/* ========== FINAL OVERRIDE - HIGHEST PRIORITY ========== */
/* This must be the last CSS rule to ensure it overrides everything */
.home-container .btn-primary,
.home-container .btn,
.home-page .btn-primary,
.home-page .btn,
.hero-cta .btn-primary,
.hero-cta .btn {
  background-color: #000000 !important;
  color: #ffffff !important;
  border: 2px solid #000000 !important;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15) !important;
  background-image: none !important;
}

.home-container .btn-primary:hover,
.home-container .btn:hover,
.home-page .btn-primary:hover,
.home-page .btn:hover,
.hero-cta .btn-primary:hover,
.hero-cta .btn:hover {
  background-color: #333333 !important;
  color: #ffffff !important;
  border-color: #333333 !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2) !important;
  background-image: none !important;
}
