const mongoose = require('mongoose');

const brokerProfileSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    unique: true
  },
  companyName: {
    type: String,
    required: true,
    trim: true
  },
  companyAddress: {
    street: {
      type: String,
      trim: true
    },
    city: {
      type: String,
      trim: true
    },
    postalCode: {
      type: String,
      trim: true
    },
    country: {
      type: String,
      default: 'France',
      trim: true
    }
  },
  licenseNumber: {
    type: String,
    trim: true
  },
  specializations: [{
    type: String,
    enum: ['Residential', 'Commercial', 'Industrial', 'Renewable']
  }],
  commissionStructure: {
    percentageRate: Number,
    fixedFee: Number,
    tieredRates: [{
      threshold: Number,
      rate: Number
    }]
  },
  supplierPartnerships: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }],
  verificationDocuments: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Document'
  }],
  verificationStatus: {
    type: String,
    enum: ['Pending', 'Verified', 'Rejected'],
    default: 'Pending'
  },
  verificationNotes: {
    type: String
  }
}, {
  timestamps: true
});

// Create indexes
brokerProfileSchema.index({ userId: 1 });
brokerProfileSchema.index({ verificationStatus: 1 });
brokerProfileSchema.index({ companyName: 1 });

const BrokerProfile = mongoose.model('BrokerProfile', brokerProfileSchema);

module.exports = BrokerProfile;
