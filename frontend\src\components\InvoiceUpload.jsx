import { useState, useRef, useEffect, forwardRef, useImperativeHandle } from 'react';
import invoiceService from '../services/invoice.service';
import { showSuccessMessage, showErrorMessage, showInfoMessage } from '../utils/toastNotifications';

/**
 * Component for uploading invoices (single or multiple files)
 */
const InvoiceUpload = forwardRef(({ onUploadSuccess, onUploadError, onUploadStart, forceShowExtraction, allowMultipleFiles = false }, ref) => {
  // Single file states
  const [file, setFile] = useState(null);
  const [preview, setPreview] = useState(null);

  // Multi-file states
  const [files, setFiles] = useState([]);
  const [previews, setPreviews] = useState([]);

  // Common states
  const [uploading, setUploading] = useState(false);
  const [extracting, setExtracting] = useState(false);
  const [error, setError] = useState(null);
  const [extractedFields, setExtractedFields] = useState([]);
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);
  const [metadata, setMetadata] = useState({
    invoiceDate: '',
    invoiceNumber: '',
    provider: '',
    energyType: 'electricity',
    pointOfDelivery: '',
    amount: '',
    currency: 'EUR',
    consumption: '',
    notes: ''
  });

  // Handle window resize for responsive layout
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  // If forceShowExtraction is true, populate the form with dummy data
  useEffect(() => {
    if (forceShowExtraction && extractedFields.length === 0) {
      console.log('Force showing extraction form with dummy data');

      // Create dummy data
      const dummyData = {
        invoiceNumber: 'TEST-123456',
        invoiceDate: new Date().toISOString().split('T')[0],
        provider: 'Test Provider',
        energyType: 'electricity',
        amount: '123.45',
        pointOfDelivery: '12345678901234',
        consumption: '500'
      };

      // Update the form with dummy data
      setMetadata({
        ...metadata,
        invoiceNumber: dummyData.invoiceNumber,
        invoiceDate: dummyData.invoiceDate,
        provider: dummyData.provider,
        energyType: dummyData.energyType,
        amount: dummyData.amount,
        pointOfDelivery: dummyData.pointOfDelivery,
        consumption: dummyData.consumption
      });

      // Mark all fields as extracted
      setExtractedFields(['invoiceNumber', 'invoiceDate', 'provider', 'energyType', 'amount', 'pointOfDelivery', 'consumption']);
    }
  }, [forceShowExtraction]);

  const fileInputRef = useRef(null);

  // Expose methods to parent component
  useImperativeHandle(ref, () => ({
    handleFinalSubmit: () => handleFinalSubmit()
  }));

  // Handle file selection (single or multiple)
  const handleFileChange = (e) => {
    if (allowMultipleFiles) {
      // Handle multiple files
      const selectedFiles = Array.from(e.target.files);

      if (selectedFiles.length === 0) return;

      // Validate file types
      const validTypes = ['.pdf', '.jpg', '.jpeg', '.png'];
      const invalidFiles = selectedFiles.filter(file => {
        const extension = '.' + file.name.split('.').pop().toLowerCase();
        return !validTypes.includes(extension);
      });

      if (invalidFiles.length > 0) {
        setError(`Invalid file types: ${invalidFiles.map(f => f.name).join(', ')}. Please use PDF, JPG, or PNG files.`);
        return;
      }

      // Check if mixing PDFs with images
      const pdfFiles = selectedFiles.filter(file => file.type === 'application/pdf');
      const imageFiles = selectedFiles.filter(file => file.type.startsWith('image/'));

      if (pdfFiles.length > 0 && imageFiles.length > 0) {
        setError('Please upload either PDF files OR image files, not both types together.');
        return;
      }

      // Limit to 5 files maximum
      if (selectedFiles.length > 5) {
        setError('Maximum 5 files allowed. Please select fewer files.');
        return;
      }

      setFiles(selectedFiles);
      setError(null);

      // Create previews for image files
      const newPreviews = [];
      selectedFiles.forEach((file, index) => {
        if (file.type.startsWith('image/')) {
          const reader = new FileReader();
          reader.onload = () => {
            newPreviews[index] = reader.result;
            if (newPreviews.filter(p => p).length === imageFiles.length) {
              setPreviews([...newPreviews]);
            }
          };
          reader.readAsDataURL(file);
        } else {
          newPreviews[index] = null;
        }
      });

      if (imageFiles.length === 0) {
        setPreviews(new Array(selectedFiles.length).fill(null));
      }
    } else {
      // Handle single file (existing logic)
      const selectedFile = e.target.files[0];

      if (selectedFile) {
        setFile(selectedFile);

        // Create a preview for images
        if (selectedFile.type.startsWith('image/')) {
          const reader = new FileReader();
          reader.onload = () => {
            setPreview(reader.result);
          };
          reader.readAsDataURL(selectedFile);
        } else {
          // For non-image files, just show the file name
          setPreview(null);
        }

        setError(null);
      }
    }
  };

  // Remove a file from multi-file selection
  const removeFile = (indexToRemove) => {
    if (!allowMultipleFiles) return;

    const newFiles = files.filter((_, index) => index !== indexToRemove);
    const newPreviews = previews.filter((_, index) => index !== indexToRemove);

    setFiles(newFiles);
    setPreviews(newPreviews);

    if (newFiles.length === 0) {
      setError(null);
    }
  };

  // Handle metadata changes
  const handleMetadataChange = (e) => {
    const { name, value } = e.target;
    setMetadata(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    // Validate files based on mode
    if (allowMultipleFiles) {
      if (files.length === 0) {
        setError('Please select at least one file to upload');
        return;
      }
    } else {
      if (!file) {
        setError('Please select a file to upload');
        return;
      }
    }

    try {
      // IMPORTANT: Only set local states if we don't have a parent handler
      // This prevents double spinners
      if (!onUploadStart) {
        setUploading(true);
        setExtracting(true);
      } else {
        // If parent is handling loading state, don't show local spinners
        setUploading(false);
        setExtracting(false);
      }

      setError(null);
      setExtractedFields([]);

      // Notify parent component that upload has started
      // This will show the spinner in the parent component
      if (onUploadStart) {
        onUploadStart();
      }

      // Log upload details
      if (allowMultipleFiles) {
        console.log(`Uploading ${files.length} files:`, files.map(f => ({ name: f.name, type: f.type, size: f.size })));
      } else {
        console.log('Uploading file:', file.name, file.type, file.size);
      }

      // Get user info from localStorage for debugging
      const cognitoId = localStorage.getItem('cognitoId');
      const userType = localStorage.getItem('userType');
      console.log('User info from localStorage:', { cognitoId, userType });

      // Log all localStorage keys for debugging
      console.log('All localStorage keys:', Object.keys(localStorage));

      // If we don't have user info, show an error
      if (!cognitoId || !userType) {
        console.warn('Missing user information. This will cause the file(s) to be uploaded to an unknown path.');
      }

      // Upload the file(s) with metadata
      const result = allowMultipleFiles
        ? await invoiceService.uploadMultipleInvoices(files, metadata)
        : await invoiceService.uploadInvoice(file, metadata);

      // Store the invoice ID in localStorage and component state for later use
      if (result.invoice && result.invoice.id) {
        localStorage.setItem('tempInvoiceId', result.invoice.id);
        console.log('Stored invoice ID in localStorage:', result.invoice.id);

        // Also store the invoice ID in the file object for reference
        if (file) {
          file.invoiceId = result.invoice.id;
          console.log('Stored invoice ID in file object:', result.invoice.id);
        }
      }

      // Store the file details in localStorage for later use
      if (result.file) {
        localStorage.setItem('tempFileDetails', JSON.stringify({
          name: result.file.originalname || file.name,
          size: result.file.size || file.size,
          type: result.file.mimetype || file.type,
          s3Key: result.file.key || '',
          s3Bucket: result.file.bucket || 'energy-app-uat-backend-files',
          publicUrl: result.file.location || '',
          uploadedAt: new Date().toISOString()
        }));
        console.log('Stored file details in localStorage');
      }

      console.log('Upload successful, response:', result);

      // CRITICAL FIX: Force the component to show the extraction form
      // This is a direct fix for the issue where the extraction form isn't showing

      // Check if we have extracted data
      if (!result.extractedData || Object.keys(result.extractedData).length === 0) {
        console.log('No extracted data in result, checking if extraction service is available');

        // Log a warning but don't create dummy data automatically
        console.warn('No extracted data available from the backend. The extraction service might be unavailable.');

        // We'll still show the form but won't pre-fill it with dummy data
        result.extractedData = {};
      }

      console.log('Using extracted data:', result.extractedData);

      // Track which fields were actually extracted
      const extractedFieldsList = [];

      // Check each field in the extracted data
      const fieldsToCheck = ['invoiceNumber', 'invoiceDate', 'provider', 'amount', 'pointOfDelivery', 'consumption'];

      fieldsToCheck.forEach(field => {
        // Check if the field exists and has a non-empty value
        if (
          result.extractedData[field] &&
          result.extractedData[field].toString().trim() !== '' &&
          result.extractedData[field].toString().trim() !== 'null' &&
          result.extractedData[field].toString().trim() !== 'undefined'
        ) {
          console.log(`Field ${field} extracted with value: ${result.extractedData[field]}`);
          extractedFieldsList.push(field);
        } else {
          console.log(`Field ${field} not extracted or empty`);
        }
      });

      // Update the form with extracted data - only use actual extracted values
      const updatedMetadata = { ...metadata };

      // Only update fields that were actually extracted
      if (extractedFieldsList.includes('invoiceNumber')) {
        updatedMetadata.invoiceNumber = result.extractedData.invoiceNumber;
      }

      if (extractedFieldsList.includes('invoiceDate')) {
        updatedMetadata.invoiceDate = result.extractedData.invoiceDate;
      }

      if (extractedFieldsList.includes('provider')) {
        updatedMetadata.provider = result.extractedData.provider;
      }

      if (extractedFieldsList.includes('amount')) {
        updatedMetadata.amount = result.extractedData.amount;
      }

      if (extractedFieldsList.includes('pointOfDelivery')) {
        updatedMetadata.pointOfDelivery = result.extractedData.pointOfDelivery;
      }

      if (extractedFieldsList.includes('consumption')) {
        updatedMetadata.consumption = result.extractedData.consumption;
      }

      console.log('Updated metadata with extracted values:', updatedMetadata);

      console.log('Setting metadata state:', updatedMetadata);
      console.log('Setting extractedFields state:', extractedFieldsList);

      // Update state
      setMetadata(updatedMetadata);
      setExtractedFields(extractedFieldsList);

      // Show a success message about extraction
      setError(null);

      // Don't reset the form yet since we want to show the extracted data
      setUploading(false);
      setExtracting(false);

      // IMPORTANT: Delay the success callback to ensure the form is rendered first
      setTimeout(() => {
        // Call onUploadSuccess with a partial success indicator
        // This will prevent navigation to the dashboard
        if (onUploadSuccess) {
          onUploadSuccess({
            message: 'Data extracted successfully',
            extractedData: result.extractedData,
            file: result.file,
            invoice: result.invoice
          });
        }
      }, 100);

      // Store the file details and extracted data in localStorage for later use
      // CRITICAL FIX: Also store the invoice ID that was created during upload
      try {
        console.log('Storing file details and extracted data in localStorage');

        // Get the file details from the result
        const fileDetails = {
          name: result.file.originalname,
          size: result.file.size,
          type: result.file.mimetype,
          s3Key: result.file.key,
          s3Bucket: result.file.bucket,
          publicUrl: result.file.location
        };

        // Store the file details in localStorage for later use
        localStorage.setItem('tempFileDetails', JSON.stringify(fileDetails));

        // CRITICAL FIX: Store the invoice ID that was created during upload
        // This prevents duplicate creation in handleFinalSubmit
        let invoiceId = null;
        if (result.invoice) {
          // Try different possible ID fields
          invoiceId = result.invoice.id || result.invoice._id || result.invoice.invoiceId;

          if (invoiceId) {
            console.log('Storing invoice ID for later update:', invoiceId);
            localStorage.setItem('tempInvoiceId', invoiceId);
          } else {
            console.warn('No invoice ID found in upload result. Available fields:', Object.keys(result.invoice));
            console.warn('Full invoice object:', result.invoice);
          }
        } else {
          console.warn('No invoice object found in upload result:', result);
        }

        // Create a metadata object from the extracted data
        const extractedMetadata = {
          invoiceDate: result.extractedData?.invoiceDate || '',
          invoiceNumber: result.extractedData?.invoiceNumber || '',
          provider: result.extractedData?.provider || '',
          energyType: result.extractedData?.energyType || metadata.energyType || 'electricity',
          pointOfDelivery: result.extractedData?.pointOfDelivery || '',
          amount: result.extractedData?.amount || '',
          currency: metadata.currency || 'EUR',
          consumption: result.extractedData?.consumption || '',
          notes: metadata.notes || ''
        };

        // CRITICAL FIX: Update the form metadata state with extracted data
        console.log('Updating form metadata with extracted data:', extractedMetadata);
        setMetadata(extractedMetadata);

        // CRITICAL FIX: Mark fields as extracted if they have values
        const fieldsWithData = [];
        if (result.extractedData?.invoiceDate) fieldsWithData.push('invoiceDate');
        if (result.extractedData?.invoiceNumber) fieldsWithData.push('invoiceNumber');
        if (result.extractedData?.provider) fieldsWithData.push('provider');
        if (result.extractedData?.energyType) fieldsWithData.push('energyType');
        if (result.extractedData?.pointOfDelivery) fieldsWithData.push('pointOfDelivery');
        if (result.extractedData?.amount) fieldsWithData.push('amount');
        if (result.extractedData?.consumption) fieldsWithData.push('consumption');

        console.log('Fields with extracted data:', fieldsWithData);
        setExtractedFields(fieldsWithData);

        // Store the extracted metadata in localStorage for later use
        localStorage.setItem('tempExtractedMetadata', JSON.stringify(extractedMetadata));

        console.log('File details and extracted data stored in localStorage');
        console.log('File details:', fileDetails);
        console.log('Extracted metadata:', extractedMetadata);
      } catch (error) {
        console.error('Error storing file details and extracted data:', error);
        // Don't throw the error, just log it
        // We'll still show the extraction form
      }

      // IMPORTANT: We're returning early here to prevent the code below from executing
      // This ensures the form stays in the extraction state
      return;
    } catch (error) {
      console.error('Error uploading invoice:', error);

      // Provide a more user-friendly error message
      let errorMessage = 'Failed to upload invoice';

      // Check if it's a response error with specific error data
      if (error.response && error.response.data) {
        const errorData = error.response.data;

        // Handle specific error types from backend
        if (errorData.message === 'Unsupported document format' ||
            errorData.error === 'Unsupported document format' ||
            errorData.error === 'Unsupported PDF version' ||
            errorData.error === 'Password-protected PDF') {
          errorMessage = `${errorData.error || errorData.message}\n\n${errorData.message || errorData.details || ''}`;
          if (errorData.supportedFormats) {
            errorMessage += `\n\nSupported formats: ${errorData.supportedFormats.join(', ')}`;
          }
          // Add helpful suggestions
          errorMessage += '\n\n💡 Quick solutions:\n• Convert PDF to JPG/PNG using online tools\n• Save PDF as "PDF/A" format\n• Remove password protection from PDF\n• Try a different PDF file';
        } else {
          errorMessage = errorData.error || errorData.message || 'Failed to upload invoice';
        }
      } else if (error.message) {
        if (error.message.includes('401')) {
          errorMessage = 'Authentication error. Please log in again.';
        } else if (error.message.includes('404')) {
          errorMessage = 'The upload service is not available. Please try again later.';
        } else if (error.message.includes('413')) {
          errorMessage = 'The file is too large. Maximum size is 10MB.';
        } else if (error.message.includes('415')) {
          errorMessage = 'File type not supported. Please use PDF, JPEG, PNG, or Office documents.';
        } else if (error.message.includes('500')) {
          errorMessage = 'Server error. Please try again later.';
        } else if (error.message.includes('Unsupported document format')) {
          errorMessage = 'This PDF format is not supported for text extraction. Please try converting to JPG/PNG format or use a different PDF file.';
        } else {
          errorMessage = error.message;
        }
      }

      setError(errorMessage);

      // Call the error callback
      if (onUploadError) {
        onUploadError(error);
      }
    } finally {
      // Always reset local loading states
      setUploading(false);
      setExtracting(false);
    }
  };

  // Handle final submission after data extraction
  const handleFinalSubmit = async () => {
    try {
      console.log('handleFinalSubmit called');

      // Set local uploading state to true temporarily
      setUploading(true);
      setError(null);

      // Get the file details from localStorage if available
      let fileDetails = null;
      try {
        const fileDetailsStr = localStorage.getItem('tempFileDetails');
        if (fileDetailsStr) {
          fileDetails = JSON.parse(fileDetailsStr);
          console.log('Retrieved file details from localStorage:', fileDetails);
        }
      } catch (e) {
        console.error('Error parsing file details from localStorage:', e);
      }

      // If no file details in localStorage, use the current file if available
      if (!fileDetails && file) {
        fileDetails = {
          name: file.name,
          size: file.size,
          type: file.type
        };
        console.log('Using current file for details:', fileDetails);
      }

      // If still no file details, create a dummy file
      if (!fileDetails) {
        fileDetails = {
          name: 'test-invoice.pdf',
          size: 1024 * 1024, // 1MB
          type: 'application/pdf'
        };
        console.log('Using dummy file details:', fileDetails);
      }

      // Try to get the extracted metadata from localStorage
      let extractedMetadata = null;
      try {
        const extractedMetadataStr = localStorage.getItem('tempExtractedMetadata');
        if (extractedMetadataStr) {
          extractedMetadata = JSON.parse(extractedMetadataStr);
          console.log('Retrieved extracted metadata from localStorage:', extractedMetadata);
        }
      } catch (e) {
        console.error('Error parsing extracted metadata from localStorage:', e);
      }

      // Prepare metadata with proper type conversions
      // Use the form values (metadata) as the source of truth, but fall back to extracted values if needed
      const processedMetadata = {
        invoiceDate: metadata.invoiceDate || extractedMetadata?.invoiceDate || null,
        invoiceNumber: metadata.invoiceNumber || extractedMetadata?.invoiceNumber || null,
        provider: metadata.provider || extractedMetadata?.provider || null,
        energyType: metadata.energyType || extractedMetadata?.energyType || 'electricity',
        pointOfDelivery: metadata.pointOfDelivery || extractedMetadata?.pointOfDelivery || null,
        // Convert amount to number if it exists
        amount: metadata.amount ? parseFloat(metadata.amount) :
                extractedMetadata?.amount ? parseFloat(extractedMetadata.amount) : null,
        currency: metadata.currency || extractedMetadata?.currency || 'EUR',
        // Convert consumption to number if it exists
        consumption: metadata.consumption ? parseFloat(metadata.consumption) :
                     extractedMetadata?.consumption ? parseFloat(extractedMetadata.consumption) : null,
        notes: metadata.notes || extractedMetadata?.notes || null,
        // Set status to processed
        status: 'processed'
      };

      console.log('Creating invoice record with metadata:', processedMetadata);

      // ✅ CORRECT APPROACH: The upload API should have created the MongoDB record
      // We should ONLY update it with metadata, NEVER create a new record

      const invoiceId = localStorage.getItem('tempInvoiceId') || (file && file.invoiceId);
      let result;

      console.log('🔍 Looking for invoice ID from localStorage:', invoiceId);
      console.log('📁 File object:', file);

      if (invoiceId && !invoiceId.startsWith('temp-') && !invoiceId.startsWith('dummy-')) {
        // If we have a valid invoice ID, update the existing record
        console.log(`✅ UPDATING existing invoice record with ID: ${invoiceId}`);
        const updateResult = await invoiceService.updateInvoiceMetadata(invoiceId, processedMetadata);
        console.log('✅ Invoice record updated successfully:', updateResult);
        result = updateResult;
      } else {
        // ✅ CORRECT FIX: The upload API should have created a MongoDB record
        // We should NEVER create a new record here - only find and update the existing one
        console.log('🔍 No invoice ID in localStorage. Searching for the record created by upload API...');

        try {
          // Get recent invoices to find the one that was created by the upload API
          const recentInvoices = await invoiceService.getUserInvoices();
          console.log('📋 Fetched recent invoices to find the uploaded file record');

          // Find the most recent invoice that matches our uploaded file
          const matchingInvoice = recentInvoices?.invoices?.find(invoice => {
            const filenameMatch = invoice.originalFilename === fileDetails.name;
            const statusMatch = invoice.status === 'pending';
            const timeMatch = Math.abs(new Date(invoice.createdAt) - new Date()) < 10 * 60 * 1000;

            console.log(`🔍 Checking invoice ${invoice._id || invoice.id}:`, {
              filename: invoice.originalFilename,
              expectedFilename: fileDetails.name,
              filenameMatch,
              status: invoice.status,
              statusMatch,
              createdAt: invoice.createdAt,
              timeMatch,
              timeDiffMinutes: Math.abs(new Date(invoice.createdAt) - new Date()) / (1000 * 60)
            });

            return filenameMatch && statusMatch && timeMatch;
          });

          if (matchingInvoice) {
            console.log('✅ Found the invoice record created by upload API:', matchingInvoice._id || matchingInvoice.id);
            const updateId = matchingInvoice._id || matchingInvoice.id;
            const updateResult = await invoiceService.updateInvoiceMetadata(updateId, processedMetadata);
            console.log('✅ Invoice record updated with metadata successfully:', updateResult);
            result = updateResult;
          } else {
            console.error('🚨 CRITICAL ERROR: Could not find the invoice record that should have been created by upload API!');
            console.error('📋 Available invoices:', recentInvoices?.invoices?.map(inv => ({
              id: inv._id || inv.id,
              filename: inv.originalFilename,
              status: inv.status,
              createdAt: inv.createdAt
            })));
            console.error('🔍 Expected filename:', fileDetails.name);

            // This should never happen if the upload API is working correctly
            throw new Error(`Upload API failed to create invoice record for file: ${fileDetails.name}`);
          }
        } catch (error) {
          console.error('❌ Error finding the invoice record created by upload API:', error);
          throw new Error(`Failed to find and update invoice record: ${error.message}`);
        }
      }
      console.log('Final result:', result);

      // Store the file details in localStorage for reference
      if (result.invoice) {
        localStorage.setItem('lastInvoiceDetails', JSON.stringify({
          id: result.invoice.id,
          originalFilename: result.invoice.originalFilename,
          s3Key: result.invoice.s3Key,
          publicUrl: result.invoice.publicUrl,
          status: result.invoice.status,
          metadata: result.invoice.metadata,
          updatedAt: result.invoice.updatedAt
        }));
      }

      // Remove all temporary data from localStorage
      localStorage.removeItem('tempInvoiceId');
      localStorage.removeItem('tempFileDetails');
      localStorage.removeItem('tempExtractedMetadata');

      // Reset the form
      setFile(null);
      setPreview(null);
      setMetadata({
        invoiceDate: '',
        invoiceNumber: '',
        provider: '',
        energyType: 'electricity',
        pointOfDelivery: '',
        amount: '',
        currency: 'EUR',
        consumption: '',
        notes: ''
      });
      setExtractedFields([]);

      // Reset the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }

      // IMPORTANT: Reset uploading state before calling success callback
      setUploading(false);

      console.log('Calling onUploadSuccess with processed successfully message');
      console.log('Invoice details:', result.invoice);

      // Call the success callback with the current state and include the invoice details
      if (onUploadSuccess) {
        console.log('Calling onUploadSuccess');
        onUploadSuccess({
          message: 'Invoice processed successfully',
          metadata: processedMetadata,
          invoice: result.invoice
        });
      }
    } catch (error) {
      console.error('Error updating invoice metadata:', error);
      setError(error.message || 'Failed to update invoice metadata');

      // Call the error callback
      if (onUploadError) {
        onUploadError(error);
      }

      // Always reset local loading state in case of error
      setUploading(false);
    }
  };

  // Function to check MongoDB status
  const checkMongoDbStatus = async () => {
    try {
      setError(null);
      console.log('Checking MongoDB status...');
      const result = await invoiceService.checkMongoDbStatus();
      console.log('MongoDB status check result:', result);

      if (result.ok) {
        showInfoMessage('PROCESSING', `MongoDB Status: ${result.data?.status || 'OK'}\nConnection: ${result.data?.connection?.state || 'Unknown'}\nCollections: ${result.data?.collections?.length || 0}\nInvoices: ${result.data?.invoices?.count || 0}`);
      } else {
        setError(`MongoDB Status Check Failed: ${result.error || 'Unknown error'}`);
      }
    } catch (error) {
      console.error('Error checking MongoDB status:', error);
      setError(`Error checking MongoDB status: ${error.message}`);
    }
  };

  return (
    <div className="invoice-upload-container">
      <h2>Upload Energy Invoice</h2>



      {error && (
        <div className="error-message" style={{
          whiteSpace: 'pre-line',
          maxWidth: '800px',
          margin: '0 auto 20px auto',
          padding: '15px 20px',
          backgroundColor: '#fff5f5',
          border: '1px solid #fed7d7',
          borderRadius: '8px',
          color: '#c53030',
          fontSize: '14px',
          lineHeight: '1.5'
        }}>
          {error}
        </div>
      )}

      {(extractedFields.length > 0 || forceShowExtraction) && (
        <div className="success-message" style={{
          maxWidth: '850px',
          margin: '0 auto 30px auto',
          padding: '15px 20px',
          backgroundColor: '#f0fff4',
          border: '1px solid #d4edda',
          borderRadius: '8px',
          boxShadow: '0 2px 4px rgba(0, 0, 0, 0.05)'
        }}>
          <div style={{
            display: 'flex',
            flexDirection: 'column',
            width: '100%'
          }}>
            {/* First line - Checkmark and text */}
            <div style={{
              display: 'flex',
              alignItems: 'center',
              marginBottom: '8px',
              width: '100%'
            }}>
              <span style={{ color: '#2ecc71', fontSize: '20px', marginRight: '8px', flexShrink: 0 }}>✓</span>
              <span style={{ fontSize: '14px', fontWeight: '500' }}>
                {extractedFields.length > 0
                  ? "We've automatically extracted the following information from your invoice:"
                  : "Please fill in the invoice details below:"}
              </span>
            </div>

            {/* Second line - Verification message */}
            <div style={{
              fontSize: '14px',
              fontStyle: 'italic',
              marginBottom: '15px',
              width: '100%',
              color: '#555'
            }}>
              {extractedFields.length > 0
                ? "Please verify the extracted information and make any necessary corrections."
                : "Fill in the details from your invoice to continue."}
            </div>

            {/* Third line - Fields in horizontal view */}
            <div style={{
              display: 'flex',
              flexWrap: 'wrap',
              width: '100%',
              marginTop: '10px',
              borderTop: '1px solid rgba(0, 0, 0, 0.05)',
              paddingTop: '10px'
            }}>
              <div style={{
                width: isMobile ? '50%' : '33.33%',
                fontSize: '14px',
                padding: '5px 10px',
                marginBottom: '10px',
                boxSizing: 'border-box'
              }}>
                <span style={{ color: extractedFields.includes('invoiceDate') ? '#2ecc71' : '#999', marginRight: '5px' }}>•</span> Invoice Date
              </div>
              <div style={{
                width: isMobile ? '50%' : '33.33%',
                fontSize: '14px',
                padding: '5px 10px',
                marginBottom: '10px',
                boxSizing: 'border-box'
              }}>
                <span style={{ color: extractedFields.includes('invoiceNumber') ? '#2ecc71' : '#999', marginRight: '5px' }}>•</span> Invoice Number
              </div>
              <div style={{
                width: isMobile ? '50%' : '33.33%',
                fontSize: '14px',
                padding: '5px 10px',
                marginBottom: '10px',
                boxSizing: 'border-box'
              }}>
                <span style={{ color: extractedFields.includes('provider') ? '#2ecc71' : '#999', marginRight: '5px' }}>•</span> Energy Provider
              </div>
              <div style={{
                width: isMobile ? '50%' : '33.33%',
                fontSize: '14px',
                padding: '5px 10px',
                marginBottom: isMobile ? '10px' : '0',
                boxSizing: 'border-box'
              }}>
                <span style={{ color: extractedFields.includes('pointOfDelivery') ? '#2ecc71' : '#999', marginRight: '5px' }}>•</span> PDL/PRM/RAE Number
              </div>
              <div style={{
                width: isMobile ? '50%' : '33.33%',
                fontSize: '14px',
                padding: '5px 10px',
                marginBottom: isMobile ? '10px' : '0',
                boxSizing: 'border-box'
              }}>
                <span style={{ color: extractedFields.includes('amount') ? '#2ecc71' : '#999', marginRight: '5px' }}>•</span> Amount
              </div>
              <div style={{
                width: isMobile ? '50%' : '33.33%',
                fontSize: '14px',
                padding: '5px 10px',
                boxSizing: 'border-box'
              }}>
                <span style={{ color: extractedFields.includes('consumption') ? '#2ecc71' : '#999', marginRight: '5px' }}>•</span> Consumption
              </div>
            </div>
          </div>
        </div>
      )}

      {extractedFields.length > 0 || forceShowExtraction ? (
        <form onSubmit={(e) => { e.preventDefault(); handleFinalSubmit(); }}>
          {/* Metadata section with extracted data */}
          <div className="metadata-section" style={{ maxWidth: '800px', margin: '0 auto' }}>
            <div className="form-grid" style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '20px' }}>
              <div className={`form-group ${extractedFields.includes('invoiceDate') ? 'extracted-field' : ''}`}>
                <label>
                  Invoice Date {extractedFields.includes('invoiceDate') && <span style={{
                    backgroundColor: '#2ecc71',
                    color: 'white',
                    fontSize: '10px',
                    padding: '2px 6px',
                    borderRadius: '10px',
                    marginLeft: '8px',
                    fontWeight: 'bold'
                  }}>Auto-detected</span>}
                </label>
                <input
                  type="date"
                  name="invoiceDate"
                  value={metadata.invoiceDate}
                  onChange={handleMetadataChange}
                  className={`form-input ${extractedFields.includes('invoiceDate') ? 'extracted-input' : ''}`}
                />
              </div>

              <div className={`form-group ${extractedFields.includes('invoiceNumber') ? 'extracted-field' : ''}`}>
                <label>
                  Invoice Number {extractedFields.includes('invoiceNumber') && <span style={{
                    backgroundColor: '#2ecc71',
                    color: 'white',
                    fontSize: '10px',
                    padding: '2px 6px',
                    borderRadius: '10px',
                    marginLeft: '8px',
                    fontWeight: 'bold'
                  }}>Auto-detected</span>}
                </label>
                <input
                  type="text"
                  name="invoiceNumber"
                  value={metadata.invoiceNumber}
                  onChange={handleMetadataChange}
                  className={`form-input ${extractedFields.includes('invoiceNumber') ? 'extracted-input' : ''}`}
                  placeholder="e.g., INV-12345"
                />
              </div>

              <div className={`form-group ${extractedFields.includes('provider') ? 'extracted-field' : ''}`}>
                <label>
                  Energy Provider {extractedFields.includes('provider') && <span style={{
                    backgroundColor: '#2ecc71',
                    color: 'white',
                    fontSize: '10px',
                    padding: '2px 6px',
                    borderRadius: '10px',
                    marginLeft: '8px',
                    fontWeight: 'bold'
                  }}>Auto-detected</span>}
                </label>
                <input
                  type="text"
                  name="provider"
                  value={metadata.provider}
                  onChange={handleMetadataChange}
                  className={`form-input ${extractedFields.includes('provider') ? 'extracted-input' : ''}`}
                  placeholder="e.g., EDF, Engie"
                />
              </div>

              <div className={`form-group ${extractedFields.includes('energyType') ? 'extracted-field' : ''}`}>
                <label>
                  Energy Type {extractedFields.includes('energyType') && <span style={{
                    backgroundColor: '#2ecc71',
                    color: 'white',
                    fontSize: '10px',
                    padding: '2px 6px',
                    borderRadius: '10px',
                    marginLeft: '8px',
                    fontWeight: 'bold'
                  }}>Auto-detected</span>}
                </label>
                <select
                  name="energyType"
                  value={metadata.energyType}
                  onChange={handleMetadataChange}
                  className={`form-input ${extractedFields.includes('energyType') ? 'extracted-input' : ''}`}
                >
                  <option value="electricity">Electricity</option>
                  <option value="gas">Gas</option>
                  <option value="both">Both</option>
                  <option value="other">Other</option>
                </select>
              </div>

              <div className={`form-group ${extractedFields.includes('pointOfDelivery') ? 'extracted-field' : ''}`}>
                <label>
                  PDL/PRM/RAE Number {extractedFields.includes('pointOfDelivery') && <span style={{
                    backgroundColor: '#2ecc71',
                    color: 'white',
                    fontSize: '10px',
                    padding: '2px 6px',
                    borderRadius: '10px',
                    marginLeft: '8px',
                    fontWeight: 'bold'
                  }}>Auto-detected</span>}
                </label>
                <input
                  type="text"
                  name="pointOfDelivery"
                  value={metadata.pointOfDelivery}
                  onChange={handleMetadataChange}
                  className={`form-input ${extractedFields.includes('pointOfDelivery') ? 'extracted-input' : ''}`}
                  placeholder="Point of delivery number"
                />
              </div>

              <div className={`form-group ${extractedFields.includes('amount') ? 'extracted-field' : ''}`}>
                <label>
                  Amount {extractedFields.includes('amount') && <span style={{
                    backgroundColor: '#2ecc71',
                    color: 'white',
                    fontSize: '10px',
                    padding: '2px 6px',
                    borderRadius: '10px',
                    marginLeft: '8px',
                    fontWeight: 'bold'
                  }}>Auto-detected</span>}
                </label>
                <div className="amount-currency">
                  <input
                    type="number"
                    name="amount"
                    value={metadata.amount}
                    onChange={handleMetadataChange}
                    className={`form-input amount-field ${extractedFields.includes('amount') ? 'extracted-input' : ''}`}
                    placeholder="e.g., 120.50"
                    step="0.01"
                  />
                  <select
                    name="currency"
                    value={metadata.currency}
                    onChange={handleMetadataChange}
                    className="form-input currency-field"
                  >
                    <option value="EUR">EUR</option>
                    <option value="USD">USD</option>
                    <option value="GBP">GBP</option>
                  </select>
                </div>
              </div>

              <div className={`form-group ${extractedFields.includes('consumption') ? 'extracted-field' : ''}`}>
                <label>
                  Consumption (kWh) {extractedFields.includes('consumption') && <span style={{
                    backgroundColor: '#2ecc71',
                    color: 'white',
                    fontSize: '10px',
                    padding: '2px 6px',
                    borderRadius: '10px',
                    marginLeft: '8px',
                    fontWeight: 'bold'
                  }}>Auto-detected</span>}
                </label>
                <input
                  type="number"
                  name="consumption"
                  value={metadata.consumption}
                  onChange={handleMetadataChange}
                  className={`form-input ${extractedFields.includes('consumption') ? 'extracted-input' : ''}`}
                  placeholder="e.g., 500"
                  step="0.01"
                />
              </div>

              <div className="form-group full-width">
                <label>
                  Notes
                </label>
                <textarea
                  name="notes"
                  value={metadata.notes}
                  onChange={handleMetadataChange}
                  className="form-input"
                  rows="3"
                  placeholder="Any additional information about this invoice"
                ></textarea>
              </div>
            </div>
          </div>
        </form>
      ) : (
        <form onSubmit={handleSubmit}>
          {/* File upload section */}
          <div className="file-upload-section">
            <label>{allowMultipleFiles ? 'Invoice Files (Multiple Pages)' : 'Invoice File'}</label>
            <div
              className={`upload-area ${allowMultipleFiles ? 'multi-file-upload' : ''}`}
              onClick={() => fileInputRef.current.click()}
            >
              <div className="upload-placeholder">
                <p>
                  {allowMultipleFiles
                    ? 'Click to upload multiple invoice pages or drag and drop files here'
                    : 'Click to upload your energy bill or drag and drop file here'
                  }
                </p>
                {allowMultipleFiles && (
                  <p className="upload-hint">
                    📄 Upload up to 5 files (PDF, JPG, PNG)<br/>
                    💡 For multi-page invoices: upload all pages together
                  </p>
                )}
              </div>
              <input
                type="file"
                style={{ display: 'none' }}
                onChange={handleFileChange}
                accept={allowMultipleFiles ? ".pdf,.jpg,.jpeg,.png" : ".pdf,.jpg,.jpeg,.png,.doc,.docx,.xls,.xlsx,.html"}
                multiple={allowMultipleFiles}
                ref={fileInputRef}
              />
            </div>

            {/* Single file preview */}
            {!allowMultipleFiles && file && (
              <div className="file-preview">
                <p>Selected file: {file.name} ({(file.size / 1024).toFixed(2)} KB)</p>
              </div>
            )}

            {/* Multiple files preview */}
            {allowMultipleFiles && files.length > 0 && (
              <div className="files-preview">
                <h4>Selected Files ({files.length}):</h4>
                <div className="files-grid">
                  {files.map((file, index) => (
                    <div key={index} className="file-preview-item">
                      {previews[index] ? (
                        <img src={previews[index]} alt={`Preview ${index + 1}`} className="file-preview-image" />
                      ) : (
                        <div className="file-preview-placeholder">
                          <span>📄</span>
                          <span>PDF</span>
                        </div>
                      )}
                      <div className="file-info">
                        <p className="file-name">{file.name}</p>
                        <p className="file-size">({(file.size / 1024).toFixed(2)} KB)</p>
                        <button
                          type="button"
                          className="remove-file-btn"
                          onClick={() => removeFile(index)}
                        >
                          ✕
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Submit button */}
            {((allowMultipleFiles && files.length > 0) || (!allowMultipleFiles && file)) && (
              <div className="upload-actions">
                <button type="submit" className="black-button" disabled={uploading}>
                  {uploading ? 'Processing...' :
                    allowMultipleFiles ? `Upload ${files.length} File(s)` : 'Upload Invoice'
                  }
                </button>
              </div>
            )}
          </div>
        </form>
      )}

      {/* Extraction indicator */}
      {extracting && !onUploadStart && (
        <div className="extraction-overlay">
          <div className="extraction-content">
            <div className="extraction-spinner"></div>
            <p>
              {allowMultipleFiles && files.length > 1
                ? `Analyzing your ${files.length} invoice files...`
                : 'Analyzing your invoice...'
              }
            </p>
            {allowMultipleFiles && files.length > 1 && (
              <p className="extraction-detail">Combining text from all pages...</p>
            )}
          </div>
        </div>
      )}
    </div>
  );
});

export default InvoiceUpload;
