import React from 'react';
import styled from 'styled-components';
import logoImage from '../assets/login-logo.png';

const PageContainer = styled.div`
  position: relative;
  min-height: 100vh;
  width: 100%;
  display: flex;
  align-items: center; /* Vertically centers the form */
  justify-content: flex-start;
  font-family: 'Inter', sans-serif;
  overflow: auto;
  background-color: #f5f5f5;
  margin: 0;
  padding: 40px 0; /* Add padding to top and bottom */
  box-sizing: border-box;

  @media (max-width: 768px) {
    background-position: 65% top !important;
    background-size: cover !important;
    min-height: 100vh;
    padding: 30px 0; /* Slightly less padding on mobile */
  }
`;

const FormContainer = styled.div`
  max-width: 400px;
  width: 90%;
  z-index: 2;
  padding: 0;
  background-color: transparent;
  position: relative;
  left: 5%;
  margin: 20px 0; /* Add vertical margin */
  display: flex;
  flex-direction: column;
  justify-content: center;

  @media (max-width: 768px) {
    position: relative;
    left: 0;
    width: 90%;
    margin: 15px auto; /* Add vertical margin on mobile */
  }
`;

const InnerContainer = styled.div`
  width: 100%;
  background-color: rgba(245, 250, 255, 0.85); /* More opaque background */
  border-radius: 15px;
  padding: 25px 20px;
  backdrop-filter: none;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 0;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-height: 300px;
  position: relative; /* Added for z-index to work */
  z-index: 1; /* Ensure proper stacking */

  @media (max-width: 768px) {
    background-color: rgba(245, 250, 255, 0.9); /* Even more opaque on mobile */
    padding: 20px 15px;
    margin-bottom: 0;
  }
`;

const LogoContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15px;
  gap: 0;
  background-color: transparent;
  padding: 0;
  margin-top: 0;
  width: 100%;
  min-height: 80px;

  @media (max-width: 768px) {
    min-height: 65px;
  }
`;

const Logo = styled.img`
  width: 75px;
  height: 75px;
  margin-right: -8px;
  filter: brightness(0);
  object-fit: contain;

  @media (max-width: 768px) {
    width: 60px;
    height: 60px;
  }
`;

const AppTitle = styled.div`
  color: #000000;
  font-weight: 900;
  font-size: 26px;
  line-height: 1.2;
  text-align: left;
  text-shadow: 0px 0px 2px rgba(0, 0, 0, 0.3);
  margin-left: 0;
  padding-left: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;

  @media (max-width: 768px) {
    font-size: 22px;
  }
`;

const AuthPageContainer = ({ children }) => {
  return (
    <PageContainer style={{
      backgroundImage: 'url(/background.png)',
      backgroundPosition: 'center top',
      backgroundSize: 'cover', /* Changed to cover for better image display */
      backgroundRepeat: 'no-repeat',
      backgroundColor: '#f5f5f5',
      width: '100vw',
      minHeight: '100vh',
      maxWidth: '100%',
      margin: 0,
      padding: '40px 0', /* Add padding to top and bottom */
      overflow: 'auto',
      display: 'flex',
      justifyContent: 'flex-start',
      alignItems: 'center', /* Vertically centers the form */
      position: 'relative'
    }}>
      <FormContainer>
        <LogoContainer>
          <Logo src={logoImage} alt="MY ENERGY BILL Logo" />
          <AppTitle>
            <div>MY ENERGY</div>
            <div>BILL</div>
          </AppTitle>
        </LogoContainer>
        <InnerContainer>
          {children}
        </InnerContainer>
      </FormContainer>
    </PageContainer>
  );
};

export default AuthPageContainer;
export {
  PageContainer,
  FormContainer,
  InnerContainer,
  LogoContainer,
  Logo,
  AppTitle
};
