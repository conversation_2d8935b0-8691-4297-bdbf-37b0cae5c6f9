# Admin Actions & User Suspension System

This guide covers the complete admin actions system including email notifications and user suspension blocking.

## 🎯 Overview

The system provides comprehensive admin action capabilities with:
- **Email Notifications**: Professional emails sent to users for admin actions
- **Login Blocking**: Suspended users cannot access the system
- **Activity Logging**: All admin actions and suspension attempts are logged
- **Security**: Multiple layers of protection against suspended user access

## 📧 Email Notifications

### Supported Actions
1. **User Suspension** - Warning email with suspension details
2. **User Inactive Status** - Informational email about status change
3. **Admin Password Reset** - Security notification about password reset

### Email Features
- Professional HTML templates with company branding
- Responsive design for mobile devices
- Security information and timestamps
- Support contact information
- Plain text fallback for accessibility

## 🚫 User Suspension System

### How It Works
1. **Admin Action**: Admin changes user status to "Suspended" in dashboard
2. **Email Notification**: User receives professional suspension notification
3. **Login Blocking**: User cannot log in (blocked at authentication)
4. **API Blocking**: User cannot access any protected resources
5. **Activity Logging**: All suspension attempts are logged for security

### Blocking Points
- **Login Route** (`/auth/login`): Blocks at initial authentication
- **Auth Middleware**: Blocks all API requests for suspended users
- **Frontend**: Handles suspension errors with user-friendly messages

## 🔧 Setup & Configuration

### 1. AWS SES Email Verification

**IMPORTANT**: Before email notifications work, verify the sender email in AWS SES:

```bash
# Run the email verification script
cd backend
node verify-ses-email.js
```

This will:
- Send verification email to `<EMAIL>`
- Check current SES quota and limits
- Provide troubleshooting information

### 2. Environment Variables

Ensure these are set in your environment files:

```bash
# Email Configuration
FROM_EMAIL=<EMAIL>
COMPANY_NAME=My Energy Bill
FRONTEND_URL=https://uat.mafactureenergie.fr

# AWS Configuration
AWS_REGION=eu-west-3
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
```

## 🧪 Testing Guide

### 1. Test Email Notifications

```bash
# Test all email templates
cd backend
node test-email-notifications.js
```

**Before testing**: Update the test email address in the script!

### 2. Test User Suspension Flow

1. **Suspend a User**:
   - Go to Admin Dashboard → Manage Users
   - Find a test user and change status to "Suspended"
   - Check logs for email sending confirmation

2. **Verify Email Sent**:
   - Check the user's email inbox
   - Verify professional appearance and content
   - Confirm support contact information is correct

3. **Test Login Blocking**:
   - Try to log in as the suspended user
   - Should see: "Account is suspended. Please contact support..."
   - Login should be completely blocked

4. **Test API Blocking**:
   - If user has existing session, try accessing protected resources
   - Should receive 403 error with suspension message
   - User should be redirected to login page

### 3. Check Logs

Monitor these log files for admin actions:
```bash
# Application logs
tail -f backend/logs/application-$(date +%Y-%m-%d).log

# Error logs
tail -f backend/logs/error-$(date +%Y-%m-%d).log
```

Look for:
- Admin action confirmations
- Email sending success/failure
- Suspended user login attempts
- API access attempts by suspended users

## 🔍 Troubleshooting

### Email Not Sending

**Error**: `Email address is not verified`
**Solution**: Run `node verify-ses-email.js` and verify the email in AWS SES

**Error**: `MessageRejected` or quota exceeded
**Solution**: Check AWS SES sending limits and quota

### Login Not Blocked

**Check**: User status in database should be exactly "Suspended"
**Check**: Backend logs for suspension check execution
**Check**: Frontend error handling for ACCOUNT_SUSPENDED response

### Frontend Not Showing Suspension Message

**Check**: Browser console for JavaScript errors
**Check**: API response includes `error: 'ACCOUNT_SUSPENDED'`
**Check**: Frontend error handling in Login.jsx and api.js

## 📊 Monitoring & Analytics

### User Activity Logs

All suspension-related activities are logged:
- `SuspendedLoginAttempt`: When suspended user tries to log in
- `SuspendedAccessAttempt`: When suspended user tries to access API

### Admin Action Logs

All admin actions are logged with:
- Admin user who performed the action
- Target user affected
- Timestamp and details
- Email notification status

## 🔐 Security Features

### Multi-Layer Protection
1. **Database Level**: User status checked in all auth flows
2. **API Level**: Auth middleware blocks suspended users
3. **Frontend Level**: Handles suspension responses gracefully
4. **Logging**: All attempts are logged for security monitoring

### Activity Tracking
- Failed login attempts by suspended users
- API access attempts by suspended users
- Admin actions with full audit trail
- Email notification delivery status

## 🚀 Production Deployment

### Pre-Deployment Checklist
- [ ] AWS SES email address verified
- [ ] Environment variables configured
- [ ] Email templates tested
- [ ] Suspension flow tested end-to-end
- [ ] Logging and monitoring configured
- [ ] Support contact information updated

### Post-Deployment Verification
- [ ] Test admin suspension action
- [ ] Verify email delivery
- [ ] Test login blocking
- [ ] Check log files
- [ ] Monitor SES quota usage

## 📞 Support

For technical issues:
- Check application logs first
- Verify AWS SES configuration
- Test with the provided scripts
- Contact development team with specific error messages

For user support:
- Suspended users should contact: <EMAIL>
- Provide clear instructions for account reactivation
- Monitor suspension-related support tickets
