import axios from 'axios';
import { Auth } from 'aws-amplify';
import { getItem, STORAGE_KEYS } from '../utils/localStorage';
import logger from '../utils/logger';
import { API_BASE_URL } from '../config/api-config';

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  }
});

// Request interceptor to add auth token
api.interceptors.request.use(
  async (config) => {
    try {
      // Check if this is an admin route
      const isAdminRoute = config.url?.includes('/admin');

      let token;

      if (isAdminRoute) {
        // For admin routes, use ID token (contains email and user info)
        token = getItem(STORAGE_KEYS.ID_TOKEN);

        if (!token) {
          try {
            const session = await Auth.currentSession();
            token = session.getIdToken().getJwtToken();
            logger.debug('Got ID token from Cognito session for admin route');
          } catch (sessionError) {
            logger.warn('Could not get ID token from Cognito session:', sessionError);
          }
        }
      } else {
        // For regular routes, use access token
        token = getItem(STORAGE_KEYS.ACCESS_TOKEN);

        if (!token) {
          try {
            const session = await Auth.currentSession();
            token = session.getAccessToken().getJwtToken();
            logger.debug('Got access token from Cognito session');
          } catch (sessionError) {
            logger.warn('Could not get access token from Cognito session:', sessionError);
          }
        }
      }

      // Add token to headers if available
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
        logger.debug(`Added ${isAdminRoute ? 'ID' : 'access'} token to request`);
      }

      logger.debug('API Request:', {
        method: config.method?.toUpperCase(),
        url: config.url,
        baseURL: config.baseURL,
        hasAuth: !!token
      });

      return config;
    } catch (error) {
      logger.error('Request interceptor error:', error);
      return config;
    }
  },
  (error) => {
    logger.error('Request interceptor error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    logger.debug('API Response:', {
      status: response.status,
      url: response.config.url,
      method: response.config.method?.toUpperCase()
    });
    return response;
  },
  (error) => {
    logger.error('API Error:', {
      status: error.response?.status,
      statusText: error.response?.statusText,
      url: error.config?.url,
      method: error.config?.method?.toUpperCase(),
      message: error.response?.data?.message || error.message
    });

    // Handle specific error cases
    if (error.response?.status === 401) {
      logger.warn('Unauthorized request - token may be expired');
      // Don't automatically redirect here as it might be handled by the calling component
    }

    if (error.response?.status === 403) {
      logger.warn('Forbidden request - insufficient permissions');
    }

    if (error.response?.status >= 500) {
      logger.error('Server error occurred');
    }

    return Promise.reject(error);
  }
);

export default api;
