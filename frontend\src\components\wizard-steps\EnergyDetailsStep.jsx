import React from 'react';
import { showErrorMessage } from '../../utils/toastNotifications';

const EnergyDetailsStep = ({ formData, onChange, onNext, onPrev }) => {
  const [showOtherSupplier, setShowOtherSupplier] = React.useState(formData.currentSupplier === 'Other');

  // List of common energy suppliers in France
  const suppliers = [
    'EDF',
    'Engie',
    'Total Direct Energie',
    'Eni',
    'Vattenfall',
    'Planète OUI',
    'Ekwateur',
    'Mint Energie',
    'Other'
  ];

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;

    if (name === 'currentSupplier') {
      setShowOtherSupplier(value === 'Other');
      onChange(name, value);
      if (value !== 'Other') {
        onChange('otherSupplier', '');
      }
    } else if (type === 'checkbox' && name.startsWith('energyType-')) {
      const energyType = name.split('-')[1];
      const updatedEnergyTypes = {
        ...formData.energyTypes,
        [energyType]: checked
      };
      onChange('energyTypes', updatedEnergyTypes);
    } else {
      onChange(name, value);
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();

    // Validate required fields
    if (!formData.energyTypes.electricity && !formData.energyTypes.gas) {
      showErrorMessage('VALIDATION_FAILED', 'Please select at least one energy type');
      return;
    }

    if (!formData.currentSupplier) {
      showErrorMessage('VALIDATION_FAILED', 'Please select your current supplier');
      return;
    }

    if (formData.currentSupplier === 'Other' && !formData.otherSupplier.trim()) {
      showErrorMessage('VALIDATION_FAILED', 'Please specify your current supplier');
      return;
    }

    if (!formData.meterNumber.trim()) {
      showErrorMessage('VALIDATION_FAILED', 'PDL/PRM/RAE number is required');
      return;
    } else if (!/^\d{14}$/.test(formData.meterNumber.replace(/\s/g, ''))) {
      showErrorMessage('VALIDATION_FAILED', 'Please enter a valid meter number (exactly 14 digits)');
      return;
    }

    onNext();
  };

  return (
    <div className="wizard-step">
      <h3 className="wizard-title">Energy & Meter Information</h3>
      <form onSubmit={handleSubmit}>
        <div className="form-row">
          {/* Left Column */}
          <div className="form-col">
            <div className="form-group">
              <label>Energy Types <span className="required">*</span></label>
              <div className="checkbox-group">
                <div className="checkbox-item">
                  <input
                    type="checkbox"
                    id="energyType-electricity"
                    name="energyType-electricity"
                    checked={formData.energyTypes.electricity}
                    onChange={handleChange}
                  />
                  <label htmlFor="energyType-electricity">Electricity</label>
                </div>
                <div className="checkbox-item">
                  <input
                    type="checkbox"
                    id="energyType-gas"
                    name="energyType-gas"
                    checked={formData.energyTypes.gas}
                    onChange={handleChange}
                  />
                  <label htmlFor="energyType-gas">Gas</label>
                </div>
              </div>
            </div>

            <div className="form-group">
              <label htmlFor="currentSupplier">Current Supplier <span className="required">*</span></label>
              <select
                id="currentSupplier"
                name="currentSupplier"
                className="form-input"
                value={formData.currentSupplier}
                onChange={handleChange}
                required
              >
                <option value="">Select your current supplier</option>
                {suppliers.map((supplier, index) => (
                  <option key={index} value={supplier}>{supplier}</option>
                ))}
              </select>
            </div>

            {showOtherSupplier && (
              <div className="form-group">
                <label htmlFor="otherSupplier">Specify Supplier <span className="required">*</span></label>
                <input
                  type="text"
                  id="otherSupplier"
                  name="otherSupplier"
                  className="form-input"
                  value={formData.otherSupplier}
                  onChange={handleChange}
                  placeholder="Enter your current supplier"
                  required
                />
              </div>
            )}

            <div className="form-group">
              <label htmlFor="meterNumber">PDL/PRM/RAE Number <span className="required">*</span></label>
              <input
                type="text"
                id="meterNumber"
                name="meterNumber"
                className="form-input"
                value={formData.meterNumber}
                onChange={handleChange}
                placeholder="Enter your 14-digit meter number"
                pattern="[0-9]{14}"
                maxLength="14"
                inputMode="numeric"
                required
              />
              <div className="hint-text">Your meter identification number (exactly 14 digits)</div>
            </div>
          </div>

          {/* Right Column */}
          <div className="form-col">
            <div className="form-group">
              <label htmlFor="contractEndDate">Contract End Date <span className="optional">(Optional)</span></label>
              <input
                type="date"
                id="contractEndDate"
                name="contractEndDate"
                className="form-input"
                value={formData.contractEndDate}
                onChange={handleChange}
              />
            </div>

            <div className="form-group">
              <label htmlFor="preferredContractLength">Preferred Contract Length <span className="optional">(Optional)</span></label>
              <select
                id="preferredContractLength"
                name="preferredContractLength"
                className="form-input"
                value={formData.preferredContractLength}
                onChange={handleChange}
              >
                <option value="">Select preferred contract length</option>
                <option value="12">12 months</option>
                <option value="24">24 months</option>
                <option value="36">36 months</option>
              </select>
            </div>
          </div>
        </div>

        <div className="wizard-buttons">
          <button type="button" className="btn btn-secondary" onClick={onPrev}>
            Back
          </button>
          <button
            type="submit"
            className="btn btn-primary stepper-button-override"
            style={{
              backgroundColor: '#000000',
              borderColor: '#000000',
              color: '#ffffff',
              border: '1px solid #000000'
            }}
          >
            Next
          </button>
        </div>
      </form>
    </div>
  );
};

export default EnergyDetailsStep;
