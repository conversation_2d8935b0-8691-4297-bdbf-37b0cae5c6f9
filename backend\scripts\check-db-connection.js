/**
 * <PERSON><PERSON><PERSON> to check MongoDB connection
 * Run with: node scripts/check-db-connection.js
 */

const mongoose = require('mongoose');
const dotenv = require('dotenv');
const logger = require('../utils/logger');
const path = require('path');

// Load environment variables
const cliEnv = process.argv[2];
process.env.NODE_ENV = cliEnv || process.env.NODE_ENV || 'local';

const envFile = `.env.${process.env.NODE_ENV}`;

dotenv.config({
  path: path.resolve(__dirname, `../${envFile}`),
  override: false,
});

console.log(`Loaded environment: ${envFile}`);

// Get MongoDB URI from environment variables
const MONGODB_URI = process.env.MONGODB_URI;

if (!MONGODB_URI) {
  logger.error('Error: MONGODB_URI is not defined in the environment variables');
  process.exit(1);
}

logger.info('Attempting to connect to MongoDB...');
logger.info(`Connection string: ${MONGODB_URI.replace(/mongodb\+srv:\/\/([^:]+):([^@]+)@/, 'mongodb+srv://****:****@')}`);

// Connect to MongoDB
mongoose.connect(MONGODB_URI)
  .then(() => {
    logger.info('✅ MongoDB connection successful!');

    // List all collections
    return mongoose.connection.db.listCollections().toArray();
  })
  .then(collections => {
    if (collections.length === 0) {
      logger.info('Database is empty. No collections found.');
    } else {
      logger.info('Available collections:');
      collections.forEach(collection => {
        logger.info(`- ${collection.name}`);
      });
    }

    // Close the connection
    return mongoose.connection.close();
  })
  .then(() => {
    logger.info('Connection closed successfully');
    process.exit(0);
  })
  .catch(err => {
    logger.error('❌ MongoDB connection error:', err.message);

    // Provide troubleshooting tips based on error
    if (err.message.includes('ENOTFOUND')) {
      logger.error('\nTroubleshooting tips:');
      logger.error('- Check if the cluster URL is correct');
      logger.error('- Verify that your MongoDB Atlas cluster is running');
    } else if (err.message.includes('Authentication failed')) {
      logger.error('\nTroubleshooting tips:');
      logger.error('- Check if your username and password are correct');
      logger.error('- Verify that the database user has the correct permissions');
    } else if (err.message.includes('timed out')) {
      logger.error('\nTroubleshooting tips:');
      logger.error('- Check your network connection');
      logger.error('- Verify that your IP address is in the MongoDB Atlas allowed list');
    }

    process.exit(1);
  });
