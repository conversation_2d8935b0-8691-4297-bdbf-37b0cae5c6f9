import React from 'react';
import { showErrorMessage } from '../../utils/toastNotifications';

const BrokerSpecializationStep = ({ formData, onChange, onMultiSelectChange, onNext, onPrev }) => {
  const specializationOptions = ['Residential', 'Commercial', 'Industrial', 'Renewable Energy', 'Small Business', 'Large Enterprise'];
  const serviceAreaOptions = ['Paris', 'Lyon', 'Marseille', 'Toulouse', 'Nice', 'Nantes', 'Strasbourg', 'Montpellier', 'Bordeaux', 'Lille'];

  const handleSubmit = (e) => {
    e.preventDefault();

    // Validate required fields
    if (formData.specializations.length === 0) {
      showErrorMessage('VALIDATION_FAILED', 'Please select at least one specialization');
      return;
    }

    onNext();
  };

  return (
    <div>
      <div className="stepper-header">
        <h3 className="page-title">Specializations & Service Areas</h3>
        <p className="page-subtitle">
          Select your areas of expertise and the regions where you provide services.
        </p>
      </div>
      <form onSubmit={handleSubmit}>
        <div className="form-row">
          <div className="form-group">
            <label style={{ marginBottom: '1rem', color: '#000', fontSize: '1.1rem', display: 'block' }}>
              Specializations <span className="required">*</span>
            </label>
            <p style={{ color: '#666', marginBottom: '1rem', fontSize: '0.9rem' }}>
              Select your areas of expertise
            </p>

            <div style={{
              display: 'grid',
              gridTemplateColumns: '1fr',
              gap: '0.75rem',
              marginBottom: '2rem'
            }}>
              {specializationOptions.map(spec => (
                <label
                  key={spec}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.75rem',
                    padding: '1rem',
                    border: '1px solid #ddd',
                    borderRadius: '8px',
                    cursor: 'pointer',
                    backgroundColor: formData.specializations.includes(spec) ? '#f8f9fa' : 'white',
                    borderColor: formData.specializations.includes(spec) ? '#000' : '#ddd',
                    fontSize: '0.95rem'
                  }}
                >
                  <input
                    type="checkbox"
                    checked={formData.specializations.includes(spec)}
                    onChange={() => onMultiSelectChange('specializations', spec)}
                    style={{ marginRight: '0.5rem' }}
                  />
                  <span>{spec}</span>
                </label>
              ))}
            </div>
          </div>

          <div className="form-group">
            <label style={{ marginBottom: '1rem', color: '#000', fontSize: '1.1rem', display: 'block' }}>
              Service Areas
            </label>
            <p style={{ color: '#666', marginBottom: '1rem', fontSize: '0.9rem' }}>
              Select the areas where you provide brokerage services
            </p>

            <div style={{
              display: 'grid',
              gridTemplateColumns: '1fr',
              gap: '0.75rem',
              marginBottom: '2rem'
            }}>
              {serviceAreaOptions.map(area => (
                <label
                  key={area}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.75rem',
                    padding: '1rem',
                    border: '1px solid #ddd',
                    borderRadius: '8px',
                    cursor: 'pointer',
                    backgroundColor: formData.serviceAreas.includes(area) ? '#f8f9fa' : 'white',
                    borderColor: formData.serviceAreas.includes(area) ? '#000' : '#ddd',
                    fontSize: '0.95rem'
                  }}
                >
                  <input
                    type="checkbox"
                    checked={formData.serviceAreas.includes(area)}
                    onChange={() => onMultiSelectChange('serviceAreas', area)}
                    style={{ marginRight: '0.5rem' }}
                  />
                  <span>{area}</span>
                </label>
              ))}
            </div>
          </div>
        </div>

        <div className="stepper-buttons">
          <button type="button" className="stepper-button stepper-button-prev" onClick={onPrev}>
            Back
          </button>
          <button type="submit" className="stepper-button stepper-button-next">
            Next
          </button>
        </div>
      </form>
    </div>
  );
};

export default BrokerSpecializationStep;
