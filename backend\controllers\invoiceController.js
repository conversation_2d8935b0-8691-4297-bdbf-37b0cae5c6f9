const AWS = require('aws-sdk');
const multer = require('multer');
const multerS3 = require('multer-s3');
const path = require('path');
const Invoice = require('../models/Invoice');
const User = require('../models/User');
const util = require('util');
const logger = require('../utils/logger');
const pdf2pic = require('pdf2pic');
const sharp = require('sharp');
const pdfParse = require('pdf-parse');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');

// Configure AWS SDK
// In AWS environments (EC2, ECS, Lambda), this will automatically use the instance role
// In local development, it will use the credentials from environment variables
const awsConfig = {
  region: process.env.AWS_REGION || 'eu-west-3'
};

// Only add credentials if they're provided (not needed when using IAM roles in AWS)
if (process.env.AWS_ACCESS_KEY_ID && process.env.AWS_SECRET_ACCESS_KEY) {
  awsConfig.accessKeyId = process.env.AWS_ACCESS_KEY_ID;
  awsConfig.secretAccessKey = process.env.AWS_SECRET_ACCESS_KEY;
}

AWS.config.update(awsConfig);

// Create S3 instance
const s3 = new AWS.S3();

// Create Textract instance
const textract = new AWS.Textract();

/**
 * Convert PDF to images using pdf2pic
 * @param {Buffer} pdfBuffer - PDF file buffer
 * @returns {Promise<Buffer[]>} - Array of image buffers
 */
const convertPdfToImages = async (pdfBuffer) => {
  try {
    console.log('Converting PDF to images...');

    // Create a temporary file for the PDF
    const tempDir = path.join(__dirname, '../temp');
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }

    const tempPdfPath = path.join(tempDir, `temp-${uuidv4()}.pdf`);
    fs.writeFileSync(tempPdfPath, pdfBuffer);

    // Configure pdf2pic with optimized settings for Textract
    const convert = pdf2pic.fromPath(tempPdfPath, {
      density: 200,           // Reduced DPI for better compatibility
      saveFilename: "page",
      savePath: tempDir,
      format: "png",
      width: 1654,           // A4 width at 200 DPI (more compatible)
      height: 2339,          // A4 height at 200 DPI
      quality: 100           // High quality
    });

    // Convert first page (most invoices are single page)
    const result = await convert(1, { responseType: "buffer" });

    // Clean up temp PDF file
    fs.unlinkSync(tempPdfPath);

    if (result && result.buffer) {
      console.log('PDF converted to image successfully, buffer size:', result.buffer.length);

      // Validate the image buffer
      if (result.buffer.length < 100) {
        throw new Error('Converted image buffer is too small');
      }

      // Optionally process with Sharp to ensure proper format
      try {
        const processedBuffer = await sharp(result.buffer)
          .png({ quality: 100 })
          .toBuffer();

        console.log('Image processed with Sharp, final size:', processedBuffer.length);
        return [processedBuffer];
      } catch (sharpError) {
        console.log('Sharp processing failed, using original buffer:', sharpError.message);
        return [result.buffer];
      }
    } else {
      throw new Error('Failed to convert PDF to image - no buffer returned');
    }

  } catch (error) {
    console.error('Error converting PDF to image:', error);
    throw error;
  }
};

/**
 * Process image files using synchronous Textract APIs
 * @param {Buffer} fileBuffer - Image file buffer
 * @returns {Promise<Object>} - Extracted data
 */
const processImageFile = async (fileBuffer) => {
  console.log('K. Processing image with synchronous Textract APIs...');

  try {
    // First try DetectDocumentText (simpler, faster)
    console.log('L. Trying DetectDocumentText for image...');
    const detectParams = {
      Document: {
        Bytes: fileBuffer
      }
    };

    const detectResponse = await textract.detectDocumentText(detectParams).promise();
    console.log('M. DetectDocumentText successful, blocks count:', detectResponse.Blocks?.length || 0);
    return processTextractResponseForImages(detectResponse);

  } catch (detectError) {
    console.error('N. DetectDocumentText failed for image:', detectError.message);
    console.error('N. Error details:', {
      code: detectError.code,
      statusCode: detectError.statusCode,
      message: detectError.message,
      bufferSize: fileBuffer.length
    });

    // Try AnalyzeDocument as fallback for better extraction
    try {
      console.log('O. Trying AnalyzeDocument as fallback for image...');
      const analyzeParams = {
        Document: {
          Bytes: fileBuffer
        },
        FeatureTypes: ['FORMS', 'TABLES']
      };

      const analyzeResponse = await textract.analyzeDocument(analyzeParams).promise();
      console.log('P. AnalyzeDocument successful, blocks count:', analyzeResponse.Blocks?.length || 0);
      return processTextractResponseForImages(analyzeResponse);

    } catch (analyzeError) {
      console.error('Q. AnalyzeDocument also failed for image:', analyzeError.message);
      console.error('Q. Error details:', {
        code: analyzeError.code,
        statusCode: analyzeError.statusCode,
        message: analyzeError.message,
        bufferSize: fileBuffer.length
      });

      // Provide specific error messages
      if (analyzeError.code === 'InvalidParameterException') {
        return {
          error: 'Invalid image format',
          message: 'The image format is not valid for text extraction. Please ensure you are using a clear JPG or PNG image.'
        };
      } else if (analyzeError.code === 'UnsupportedDocumentException') {
        return {
          error: 'Unsupported image',
          message: 'This image format is not supported. Please try with a different JPG or PNG image.'
        };
      } else {
        return {
          error: 'Image processing failed',
          message: 'Unable to extract text from this image. Please ensure the image is clear and contains readable text, or try a different image format (JPG, PNG).'
        };
      }
    }
  }
};

/**
 * Process Textract response for image files
 * @param {Object} response - Textract response (DetectDocumentText or AnalyzeDocument)
 * @returns {Object} - Extracted invoice data
 */
const processTextractResponseForImages = (response) => {
  console.log('Processing Textract response for image file');

  // Extract text lines from Textract blocks
  const allText = response.Blocks
    .filter(block => block.BlockType === 'LINE')
    .map(block => block.Text);

  console.log('Extracted text lines from Textract:', allText.length);
  console.log('Sample text lines:', allText.slice(0, 10));

  // Use the same field extraction logic as PDF processing
  return extractInvoiceFieldsFromText(allText);
};

/**
 * Process PDF files using direct buffer approach (simpler and more reliable)
 * @param {Buffer} fileBuffer - PDF file buffer
 * @returns {Promise<Object>} - Extracted data
 */
const processPdfWithBuffer = async (fileBuffer) => {
  console.log('R. Processing PDF with direct text extraction using pdf-parse...');

  try {
    // Use pdf-parse library to extract text directly from PDF buffer
    console.log('S. Extracting text from PDF using pdf-parse library...');

    const pdfData = await pdfParse(fileBuffer);
    console.log('T. PDF text extraction successful, text length:', pdfData.text.length);
    console.log('U. PDF metadata:', {
      pages: pdfData.numpages,
      info: pdfData.info
    });

    if (!pdfData.text || pdfData.text.trim().length === 0) {
      console.log('V. No text found in PDF, trying PDF-to-image conversion...');
      return await fallbackToPdfToImage(fileBuffer);
    }

    // Split text into lines for processing
    const textLines = pdfData.text.split('\n').map(line => line.trim()).filter(line => line.length > 0);
    console.log('W. Extracted text lines:', textLines.length);
    console.log('X. Sample text lines:', textLines.slice(0, 10));

    // Use the same field extraction logic as Textract
    return extractInvoiceFieldsFromText(textLines);

  } catch (parseError) {
    console.error('Y. PDF text extraction failed:', parseError.message);
    console.error('Y. Error details:', {
      message: parseError.message,
      stack: parseError.stack
    });

    // If PDF parsing fails, try PDF-to-image conversion as fallback
    console.log('Z. PDF text extraction failed, trying PDF-to-image conversion...');
    return await fallbackToPdfToImage(fileBuffer);
  }
};

/**
 * Extract invoice fields from text lines using pattern matching
 * @param {Array<string>} textLines - Array of text lines from PDF
 * @returns {Object} - Extracted invoice data
 */
const extractInvoiceFieldsFromText = (textLines) => {
  console.log('CC. Extracting invoice fields from text lines...');

  const extractedData = {
    invoiceNumber: '',
    invoiceDate: '',
    provider: '',
    amount: '',
    currency: 'EUR',
    consumption: '',
    pointOfDelivery: '',
    energyType: 'electricity',
    notes: ''
  };

  // Join all text for easier searching
  const fullText = textLines.join(' ').toLowerCase();

  try {
    // Log the full text for debugging
    console.log('DD. Full text for extraction (first 500 chars):', fullText.substring(0, 500));
    console.log('DD. Text lines count:', textLines.length);
    console.log('DD. Sample text lines:', textLines.slice(0, 5));

    // Extract invoice number - improved patterns with better filtering
    const invoicePatterns = [
      // French patterns with specific labels
      /(?:facture|n°|numero|numéro)\s*:?\s*([a-z0-9\-_\/\.]+)/gi,
      /(?:référence|reference|réf)\s*:?\s*([a-z0-9\-_\/\.]+)/gi,
      /(?:invoice|number)\s*:?\s*([a-z0-9\-_\/\.]+)/gi,
      // Pattern for invoice-like codes (letters followed by numbers)
      /\b([a-z]{1,4}[0-9]{6,})\b/gi,
      // Pattern for pure numeric invoice numbers (8+ digits)
      /\b([0-9]{8,12})\b/g,
      // Pattern for mixed alphanumeric codes with specific structure
      /\b([0-9]{4,}[a-z]{1,3}[0-9]{2,})\b/gi,
      /\b([a-z]{2,3}[0-9]{4,}[a-z0-9]*)\b/gi
    ];

    // Words to exclude from invoice number extraction
    const excludeWords = [
      'client', 'customer', 'name', 'nom', 'address', 'adresse', 'email', 'phone', 'telephone',
      'date', 'total', 'amount', 'montant', 'period', 'periode', 'consumption', 'consommation',
      'electricity', 'electricite', 'gas', 'gaz', 'energy', 'energie', 'service', 'company',
      'societe', 'contact', 'support', 'help', 'aide', 'website', 'site', 'www', 'http',
      'page', 'document', 'facture', 'invoice', 'bill', 'statement', 'releve'
    ];

    for (const pattern of invoicePatterns) {
      const matches = fullText.match(pattern);
      if (matches && matches.length > 0) {
        // Get the first match that looks like an invoice number
        for (const match of matches) {
          const cleanMatch = match.replace(/^(facture|n°|numero|numéro|référence|reference|réf|invoice|number)\s*:?\s*/gi, '').trim();

          // Check if the match is long enough and not an excluded word
          if (cleanMatch && cleanMatch.length >= 6) {
            const isExcluded = excludeWords.some(word =>
              cleanMatch.toLowerCase().includes(word.toLowerCase())
            );

            if (!isExcluded) {
              // Additional validation: should contain at least some numbers
              if (/[0-9]/.test(cleanMatch)) {
                extractedData.invoiceNumber = cleanMatch.toUpperCase();
                console.log('DD. Found invoice number:', extractedData.invoiceNumber, 'from pattern:', pattern);
                break;
              }
            } else {
              console.log('DD. Skipping excluded word:', cleanMatch);
            }
          }
        }
        if (extractedData.invoiceNumber) break;
      }
    }

    // If no invoice number found with patterns, try line-by-line analysis
    if (!extractedData.invoiceNumber) {
      console.log('DD. No invoice number found with patterns, trying line-by-line analysis...');

      for (const line of textLines) {
        const lowerLine = line.toLowerCase();

        // Look for lines that contain invoice-related keywords
        if (lowerLine.includes('facture') || lowerLine.includes('invoice') ||
            lowerLine.includes('n°') || lowerLine.includes('ref') ||
            lowerLine.includes('référence')) {

          // Extract potential invoice numbers from this line
          const lineMatches = line.match(/([a-z0-9]{6,})/gi);
          if (lineMatches) {
            for (const match of lineMatches) {
              const isExcluded = excludeWords.some(word =>
                match.toLowerCase().includes(word.toLowerCase())
              );

              if (!isExcluded && /[0-9]/.test(match) && match.length >= 6) {
                extractedData.invoiceNumber = match.toUpperCase();
                console.log('DD. Found invoice number from line analysis:', extractedData.invoiceNumber, 'in line:', line);
                break;
              }
            }
          }

          if (extractedData.invoiceNumber) break;
        }
      }
    }

    // Extract date
    const datePatterns = [
      /(\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{2,4})/g,
      /(\d{1,2}\s+(?:janvier|février|mars|avril|mai|juin|juillet|août|septembre|octobre|novembre|décembre)\s+\d{4})/gi,
      /(\d{1,2}\s+(?:jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)\s+\d{4})/gi
    ];

    for (const pattern of datePatterns) {
      const matches = fullText.match(pattern);
      if (matches && matches[0]) {
        // Convert to YYYY-MM-DD format for database
        extractedData.invoiceDate = formatDateForDatabase(matches[0]);
        console.log('EE. Found invoice date:', matches[0], '-> formatted:', extractedData.invoiceDate);
        break;
      }
    }

    // Extract provider/company name (look for common energy providers)
    const providerPatterns = [
      /(?:edf|engie|total|direct energie|eni|vattenfall|planete oui|mint energie|ohm energie)/i,
      /(?:fournisseur|provider|company)[:\s]*([a-z\s]+)/i
    ];

    for (const pattern of providerPatterns) {
      const match = fullText.match(pattern);
      if (match) {
        extractedData.provider = match[0].trim();
        console.log('FF. Found provider:', extractedData.provider);
        break;
      }
    }

    // Extract amount
    const amountPatterns = [
      /(?:total|montant|amount)[:\s]*([0-9,\.]+)\s*€/i,
      /([0-9,\.]+)\s*€/g,
      /€\s*([0-9,\.]+)/g
    ];

    for (const pattern of amountPatterns) {
      const matches = fullText.match(pattern);
      if (matches) {
        // Get the largest amount (likely the total)
        const amounts = matches.map(match => {
          const numStr = match.replace(/[^0-9,\.]/g, '');
          return parseFloat(numStr.replace(',', '.'));
        }).filter(num => !isNaN(num));

        if (amounts.length > 0) {
          extractedData.amount = Math.max(...amounts).toString();
          console.log('GG. Found amount:', extractedData.amount);
          break;
        }
      }
    }

    // Extract consumption - improved patterns
    const consumptionPatterns = [
      // French patterns with various units
      /(?:consommation|conso)\s*:?\s*([0-9,\.\s]+)\s*(?:kwh|mwh|kw\/h|mw\/h)/gi,
      /(?:consumption)\s*:?\s*([0-9,\.\s]+)\s*(?:kwh|mwh|kw\/h|mw\/h)/gi,
      // Pattern for numbers followed by energy units
      /([0-9,\.\s]+)\s*(?:kwh|mwh|kw\/h|mw\/h)/gi,
      // Pattern for consumption in tables or structured data
      /(?:total|période|period)\s*:?\s*([0-9,\.\s]+)\s*(?:kwh|mwh)/gi,
      // Pattern for consumption with spaces in numbers (e.g., "1 234 kWh")
      /([0-9]+(?:\s+[0-9]+)*(?:[,\.][0-9]+)?)\s*(?:kwh|mwh)/gi
    ];

    for (const pattern of consumptionPatterns) {
      const matches = fullText.match(pattern);
      if (matches && matches.length > 0) {
        // Get the largest consumption value (likely the total)
        const consumptions = matches.map(match => {
          const numStr = match.replace(/(?:consommation|conso|consumption|total|période|period)\s*:?\s*/gi, '')
                             .replace(/\s*(?:kwh|mwh|kw\/h|mw\/h)\s*/gi, '')
                             .trim();
          return extractNumericValue(numStr);
        }).filter(num => num && !isNaN(parseFloat(num)));

        if (consumptions.length > 0) {
          // Get the largest value (most likely the total consumption)
          const maxConsumption = consumptions.reduce((max, current) => {
            const maxVal = parseFloat(max);
            const currentVal = parseFloat(current);
            return currentVal > maxVal ? current : max;
          });
          extractedData.consumption = maxConsumption;
          console.log('HH. Found consumption:', maxConsumption, 'from matches:', matches.slice(0, 3));
          break;
        }
      }
    }

    // Extract point of delivery (PDL/PRM/RAE) - improved patterns
    const pdlPatterns = [
      // French patterns for electricity (PDL)
      /(?:pdl|point de livraison|point livraison)\s*:?\s*([0-9\s]+)/gi,
      // French patterns for gas (PRM)
      /(?:prm|point de référence|point référence)\s*:?\s*([0-9\s]+)/gi,
      // RAE pattern
      /(?:rae|référence)\s*:?\s*([0-9\s]+)/gi,
      // Generic delivery point patterns
      /(?:delivery point|point)\s*:?\s*([0-9\s]+)/gi,
      // Pattern for 14-digit numbers (typical PDL length)
      /\b([0-9]{14})\b/g,
      // Pattern for 11-digit numbers (typical PRM length)
      /\b([0-9]{11})\b/g,
      // Pattern for numbers with spaces (e.g., "12 345 678 901 234")
      /\b([0-9]{2}\s+[0-9]{3}\s+[0-9]{3}\s+[0-9]{3}\s+[0-9]{3})\b/g,
      // Pattern for numbers with spaces (11 digits for gas)
      /\b([0-9]{2}\s+[0-9]{3}\s+[0-9]{3}\s+[0-9]{3})\b/g
    ];

    for (const pattern of pdlPatterns) {
      const matches = fullText.match(pattern);
      if (matches && matches.length > 0) {
        for (const match of matches) {
          // Clean the match by removing labels and extra spaces
          const cleanMatch = match.replace(/(?:pdl|prm|rae|point de livraison|point livraison|point de référence|point référence|référence|delivery point|point)\s*:?\s*/gi, '')
                                 .replace(/\s+/g, '') // Remove all spaces
                                 .trim();

          // Check if it's a valid PDL/PRM length (11 or 14 digits)
          if (cleanMatch && /^[0-9]{11,14}$/.test(cleanMatch)) {
            extractedData.pointOfDelivery = cleanMatch;
            console.log('II. Found PDL/PRM/RAE:', extractedData.pointOfDelivery, 'from pattern:', pattern);
            break;
          }
        }
        if (extractedData.pointOfDelivery) break;
      }
    }

    // Determine energy type
    if (fullText.includes('gaz') || fullText.includes('gas')) {
      extractedData.energyType = 'gas';
    }

    console.log('JJ. Final extracted data:', extractedData);

    // Return the extracted data directly (not wrapped in another object)
    // This matches the format expected by the UI
    return extractedData;

  } catch (error) {
    console.error('KK. Error extracting fields from text:', error);
    return {
      error: 'Text processing failed',
      message: 'Unable to extract invoice fields from the text. The document format may not be supported.'
    };
  }
};

/**
 * Format date string to YYYY-MM-DD format for database storage
 * @param {string} dateStr - Date string in various formats
 * @returns {string} - Formatted date string or empty string if invalid
 */
const formatDateForDatabase = (dateStr) => {
  try {
    // Handle DD/MM/YYYY format (common in French invoices)
    const ddmmyyyyMatch = dateStr.match(/(\d{1,2})[\/\-\.](\d{1,2})[\/\-\.](\d{4})/);
    if (ddmmyyyyMatch) {
      const day = ddmmyyyyMatch[1].padStart(2, '0');
      const month = ddmmyyyyMatch[2].padStart(2, '0');
      const year = ddmmyyyyMatch[3];
      return `${year}-${month}-${day}`;
    }

    // Handle YYYY/MM/DD format
    const yyyymmddMatch = dateStr.match(/(\d{4})[\/\-\.](\d{1,2})[\/\-\.](\d{1,2})/);
    if (yyyymmddMatch) {
      const year = yyyymmddMatch[1];
      const month = yyyymmddMatch[2].padStart(2, '0');
      const day = yyyymmddMatch[3].padStart(2, '0');
      return `${year}-${month}-${day}`;
    }

    // Handle text month formats
    const months = {
      'janvier': '01', 'février': '02', 'mars': '03', 'avril': '04', 'mai': '05', 'juin': '06',
      'juillet': '07', 'août': '08', 'septembre': '09', 'octobre': '10', 'novembre': '11', 'décembre': '12',
      'jan': '01', 'feb': '02', 'mar': '03', 'apr': '04', 'may': '05', 'jun': '06',
      'jul': '07', 'aug': '08', 'sep': '09', 'oct': '10', 'nov': '11', 'dec': '12'
    };

    // DD Month YYYY
    const textMonthMatch = dateStr.match(/(\d{1,2})\s+([a-z]+)\s+(\d{4})/i);
    if (textMonthMatch) {
      const day = textMonthMatch[1].padStart(2, '0');
      const monthName = textMonthMatch[2].toLowerCase();
      const year = textMonthMatch[3];
      const month = months[monthName] || months[monthName.substring(0, 3)];
      if (month) {
        return `${year}-${month}-${day}`;
      }
    }

    console.log('Could not parse date format:', dateStr);
    return '';
  } catch (error) {
    console.error('Error formatting date:', error);
    return '';
  }
};

/**
 * Extract numeric value from string (remove units and non-numeric characters)
 * @param {string} str - String containing numeric value
 * @returns {string} - Numeric value as string or empty string if invalid
 */
const extractNumericValue = (str) => {
  try {
    if (!str || typeof str !== 'string') {
      return '';
    }

    console.log('Extracting numeric value from:', str);

    // First, handle numbers with spaces (e.g., "1 234.56" or "1 234,56")
    let cleanStr = str.trim();

    // Remove common units and words
    cleanStr = cleanStr.replace(/(?:kwh|mwh|kw\/h|mw\/h|€|eur|euros?|consommation|consumption|total|montant|amount)/gi, '');

    // Handle French number format with spaces as thousand separators
    // Pattern: "1 234 567,89" or "1 234 567.89"
    const frenchNumberMatch = cleanStr.match(/([0-9]+(?:\s+[0-9]{3})*)[,\.]?([0-9]*)/);
    if (frenchNumberMatch) {
      const integerPart = frenchNumberMatch[1].replace(/\s+/g, ''); // Remove spaces
      const decimalPart = frenchNumberMatch[2] || '';

      if (integerPart) {
        const fullNumber = decimalPart ? `${integerPart}.${decimalPart}` : integerPart;
        const numericValue = parseFloat(fullNumber);

        if (!isNaN(numericValue)) {
          console.log('Extracted numeric value (French format):', numericValue, 'from:', str);
          return numericValue.toString();
        }
      }
    }

    // Fallback: remove all non-numeric characters except decimal separators
    const numericStr = cleanStr.replace(/[^0-9,\.]/g, '');

    // Convert comma to period for decimal separator
    const normalizedStr = numericStr.replace(',', '.');

    // Parse as float and return as string
    const numericValue = parseFloat(normalizedStr);

    if (isNaN(numericValue)) {
      console.log('Could not extract numeric value from:', str);
      return '';
    }

    console.log('Extracted numeric value:', numericValue, 'from:', str);
    return numericValue.toString();
  } catch (error) {
    console.error('Error extracting numeric value:', error);
    return '';
  }
};

/**
 * Calculate confidence score based on extracted fields
 * @param {Object} data - Extracted data
 * @returns {number} - Confidence score (0-100)
 */
const calculateConfidence = (data) => {
  let score = 0;
  const fields = ['invoiceNumber', 'invoiceDate', 'provider', 'amount'];

  fields.forEach(field => {
    if (data[field] && data[field].trim().length > 0) {
      score += 25;
    }
  });

  return score;
};

/**
 * Fallback function to convert PDF to image and process with Textract
 * @param {Buffer} fileBuffer - PDF file buffer
 * @returns {Promise<Object>} - Extracted data
 */
const fallbackToPdfToImage = async (fileBuffer) => {
  try {
    const imageBuffers = await convertPdfToImages(fileBuffer);
    if (imageBuffers && imageBuffers.length > 0) {
      console.log('LL. PDF converted to image, processing with synchronous API...');
      return await processImageFile(imageBuffers[0]);
    } else {
      throw new Error('PDF-to-image conversion produced no images');
    }
  } catch (conversionError) {
    console.error('MM. PDF-to-image conversion failed:', conversionError.message);

    // Return appropriate error message
    return {
      error: 'PDF processing failed',
      message: 'Unable to process this PDF file. The PDF may be password-protected, corrupted, or contain only images. Please try converting it to JPG/PNG format manually.'
    };
  }
};



/**
 * Extract text from an invoice using AWS Textract
 * @param {string} bucket - S3 bucket name
 * @param {string} key - S3 object key
 * @returns {Promise<Object>} - Extracted data
 */
const extractInvoiceData = async (bucket, key) => {
  try {
    console.log('A. Textract extraction starting for:', { bucket, key });

    // Download the file from S3 using our credentials instead of letting Textract access S3 directly
    console.log('B. Downloading file from S3 using our credentials...');
    let fileBuffer;
    try {
      const getObjectParams = {
        Bucket: bucket,
        Key: key
      };
      console.log('C. Calling s3.getObject with params:', getObjectParams);
      const s3Object = await s3.getObject(getObjectParams).promise();
      fileBuffer = s3Object.Body;
      console.log('D. File downloaded successfully, size:', fileBuffer.length, 'bytes');
    } catch (s3Error) {
      console.error('E. Failed to download file from S3:', s3Error);
      return null;
    }

    // Check file type to determine processing method
    console.log('F. About to check file type...');
    const fileExtension = path.extname(key).toLowerCase();
    const isImage = ['.jpg', '.jpeg', '.png'].includes(fileExtension);
    const isPdf = fileExtension === '.pdf';

    console.log('G. File type analysis:', { fileExtension, isImage, isPdf });

    // Validate file format for Textract compatibility
    if (!isImage && !isPdf) {
      console.log('H. Unsupported file format for text extraction:', fileExtension);
      return {
        error: 'Unsupported file format',
        message: `File format ${fileExtension} is not supported for text extraction. Please use PDF, JPG, JPEG, or PNG files.`
      };
    }

    // Additional validation for PDF files
    if (isPdf) {
      console.log('H. Validating PDF file...');
      // Check if file starts with PDF header
      const pdfHeader = fileBuffer.slice(0, 4).toString();
      if (pdfHeader !== '%PDF') {
        console.log('I. Invalid PDF file - missing PDF header');
        return {
          error: 'Invalid PDF file',
          message: 'The uploaded file appears to be corrupted or is not a valid PDF file.'
        };
      }

      // Check PDF file size (Textract has limits)
      const fileSizeMB = fileBuffer.length / (1024 * 1024);
      if (fileSizeMB > 10) {
        console.log('I. PDF file too large for Textract:', fileSizeMB, 'MB');
        return {
          error: 'File too large',
          message: 'PDF files must be smaller than 10MB for text extraction. Please compress your PDF or convert to an image format.'
        };
      }

      // Check PDF version for compatibility
      const pdfVersionMatch = fileBuffer.slice(0, 20).toString().match(/%PDF-(\d+\.\d+)/);
      if (pdfVersionMatch) {
        const pdfVersion = parseFloat(pdfVersionMatch[1]);
        console.log('I. PDF version detected:', pdfVersion);
        if (pdfVersion > 1.7) {
          console.log('I. PDF version may not be fully supported by Textract:', pdfVersion);
          return {
            error: 'Unsupported PDF version',
            message: `This PDF uses version ${pdfVersion}, which may not be fully supported by AWS Textract (supports up to PDF 1.7). Please try saving the PDF in an older format or converting to JPG/PNG.`
          };
        }
      }

      // Note: Removed basic password detection as it was giving false positives
      // PDFs with /Encrypt or /Filter are not necessarily password-protected
      // Let AWS Textract handle the actual password protection detection

      console.log('I. PDF validation passed, size:', fileSizeMB.toFixed(2), 'MB');
    }

    if (isImage) {
      // For images, use synchronous APIs with file buffer
      console.log('J. Processing as image file using synchronous API...');
      return await processImageFile(fileBuffer);
    } else {
      // For PDFs, try direct buffer processing first (simpler approach)
      console.log('Q. Processing as PDF file using direct buffer approach...');
      return await processPdfWithBuffer(fileBuffer);
    }
  } catch (error) {
    console.error('Error extracting invoice data:', error);
    console.error('Error details:', {
      message: error.message,
      code: error.code,
      statusCode: error.statusCode,
      bucket,
      key,
      region: process.env.AWS_REGION
    });

    // Check if it's an AWS credentials issue
    if (error.code === 'CredentialsError' || error.code === 'UnauthorizedOperation') {
      console.error('AWS credentials issue detected');
    }

    // Check if it's a file access issue
    if (error.code === 'NoSuchKey' || error.code === 'AccessDenied') {
      console.error('File access issue detected');
    }

    // Check if it's a Textract permissions issue
    if (error.code === 'InvalidS3ObjectException') {
      console.error('Textract cannot access S3 object - check bucket policy');
    }

    logger.error('Error extracting invoice data:', error);
    return null;
  }
};

















// Configure multer for file uploads
const upload = multer({
  storage: multerS3({
    s3: s3,
    bucket: process.env.AWS_S3_BUCKET_NAME || 'energy-app-uat-backend-files', // Use environment variable with fallback
    acl: 'private', // Make files private by default
    metadata: function (req, file, cb) {
      cb(null, {
        fieldName: file.fieldname,
        originalName: file.originalname
      });
    },
    key: function (req, file, cb) {
      // Generate a unique filename
      const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
      const filename = uniqueSuffix + path.extname(file.originalname);

      // Since req.body is not available here, we'll use a default structure
      // and fix it in the upload handler
      const key = `invoices/individual/temp-user/${filename}`;

      cb(null, key);
    }
  }),
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB file size limit per file
    files: 5 // Maximum 5 files
  },
  fileFilter: function (req, file, cb) {
    // Accept only PDF, images, and common document formats
    const allowedMimes = [
      'application/pdf',
      'image/jpeg',
      'image/png',
      'image/gif',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      // Add HTML for testing with sample files
      'text/html'
    ];

    if (allowedMimes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error(`Invalid file type: ${file.mimetype}. Only PDF, images, HTML, and Office documents are allowed.`), false);
    }
  }
});

/**
 * Process multiple images for text extraction
 * @param {Array} imageBuffers - Array of image buffers
 * @returns {Promise<Object>} - Combined extracted data from all images
 */
const processMultipleImages = async (imageBuffers) => {
  console.log(`Processing ${imageBuffers.length} images for text extraction...`);

  const allExtractedData = [];
  const allTextLines = [];

  for (let i = 0; i < imageBuffers.length; i++) {
    console.log(`Processing image ${i + 1}/${imageBuffers.length}...`);

    try {
      const imageData = await processImageFile(imageBuffers[i]);

      if (imageData && !imageData.error) {
        allExtractedData.push(imageData);

        // Also collect text lines for combined processing
        const response = await textract.detectDocumentText({
          Document: { Bytes: imageBuffers[i] }
        }).promise();

        const textLines = response.Blocks
          .filter(block => block.BlockType === 'LINE')
          .map(block => block.Text);

        allTextLines.push(...textLines);
      }
    } catch (error) {
      console.error(`Error processing image ${i + 1}:`, error.message);
    }
  }

  // Combine all text lines and extract fields from the combined text
  if (allTextLines.length > 0) {
    console.log(`Combined text from ${imageBuffers.length} images, total lines: ${allTextLines.length}`);
    return extractInvoiceFieldsFromText(allTextLines);
  }

  // If no text extracted, return the best individual result
  if (allExtractedData.length > 0) {
    return allExtractedData[0];
  }

  return {
    error: 'No text extracted',
    message: 'Unable to extract text from any of the uploaded images.'
  };
};

/**
 * Upload an invoice to S3 and save metadata to MongoDB
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const uploadInvoice = async (req, res) => {
  try {
    // Handle both single file and multiple files
    const files = req.files || (req.file ? [req.file] : []);

    if (!files || files.length === 0) {
      return res.status(400).json({ message: 'No files uploaded' });
    }

    console.log(`Processing ${files.length} file(s) for upload...`);

    // Get user information
    const cognitoId = req.body.cognitoId || (req.user && req.user.sub);
    const userType = req.body.userType || 'individual';

    if (!cognitoId) {
      return res.status(400).json({ message: 'Cognito ID is required' });
    }

    // Process files - for now, handle the first file (main file)
    // TODO: Implement proper multi-file handling
    const mainFile = files[0];
    const additionalFiles = files.slice(1);

    // Move main file from temp location to proper user folder
    let finalS3Key = mainFile.key;
    let finalLocation = mainFile.location;

    console.log('Initial file info:', {
      key: mainFile.key,
      location: mainFile.location,
      bucket: mainFile.bucket,
      cognitoId,
      userType,
      totalFiles: files.length
    });

    if (mainFile.key.includes('/temp-user/')) {
      try {
        // Generate the correct key with user information
        const filename = mainFile.key.split('/').pop(); // Get just the filename
        const correctKey = `invoices/${userType}/${cognitoId}/${filename}`;

        console.log('Moving file from temp location', {
          from: mainFile.key,
          to: correctKey,
          bucket: mainFile.bucket
        });

        // Copy the object to the correct location
        await s3.copyObject({
          Bucket: mainFile.bucket,
          CopySource: `${mainFile.bucket}/${mainFile.key}`,
          Key: correctKey,
          ACL: 'private'
        }).promise();

        // Delete the temporary file
        await s3.deleteObject({
          Bucket: mainFile.bucket,
          Key: mainFile.key
        }).promise();

        // Update file information
        finalS3Key = correctKey;
        finalLocation = `https://${mainFile.bucket}.s3.${process.env.AWS_REGION}.amazonaws.com/${correctKey}`;

        console.log('File moved successfully to correct location', {
          newKey: finalS3Key,
          newLocation: finalLocation
        });
      } catch (moveError) {
        console.error('Error moving file to correct location:', moveError);
        // Continue with temp location if move fails
      }
    }

    // Find the user in MongoDB
    let user;
    try {
      user = await User.findOne({ cognitoId });
      if (!user) {
        // Create a temporary user if not found (for testing purposes)
        user = new User({
          cognitoId,
          email: `temp-${cognitoId}@example.com`,
          firstName: 'Temporary',
          lastName: 'User',
          userType: req.body.userType || 'individual',
          status: 'Active',
          verificationStatus: 'Verified',
          profileComplete: true
        });

        await user.save();
      }
    } catch (userError) {
      return res.status(500).json({ message: 'Error finding/creating user', error: userError.message });
    }

    // Extract data from the invoice using AWS Textract
    let extractedData = null;
    try {
      console.log('1. Starting text extraction for file:', finalS3Key);
      console.log('2. S3 bucket:', mainFile.bucket);
      console.log('3. File extension:', path.extname(finalS3Key));
      console.log('4. About to call extractInvoiceData function...');

      // If multiple images, process them together
      if (files.length > 1 && files.every(f => f.mimetype.startsWith('image/'))) {
        console.log('5a. Processing multiple images together...');
        const imageBuffers = [];

        for (const file of files) {
          try {
            const s3Object = await s3.getObject({
              Bucket: file.bucket,
              Key: file.key
            }).promise();
            imageBuffers.push(s3Object.Body);
          } catch (error) {
            console.error('Error downloading file for multi-image processing:', error);
          }
        }

        if (imageBuffers.length > 0) {
          extractedData = await processMultipleImages(imageBuffers);
        }
      } else {
        // Single file processing
        extractedData = await extractInvoiceData(mainFile.bucket, finalS3Key);
      }

      console.log('5. Text extraction completed, result:', extractedData);

      // Check if extraction returned an error
      if (extractedData && extractedData.error) {
        console.log('Text extraction returned error:', extractedData.error);
        console.log('Error message:', extractedData.message);

        // For unsupported document format, return error to user immediately
        if (extractedData.error === 'Unsupported document format') {
          return res.status(400).json({
            message: 'Unsupported document format',
            error: extractedData.message || 'The uploaded file format is not supported for text extraction.',
            details: 'Please try uploading a different PDF file, or convert your document to JPG/PNG format.',
            supportedFormats: ['PDF', 'JPG', 'JPEG', 'PNG']
          });
        }

        // For other extraction errors, still save the invoice but with error information
        extractedData = {
          extractionError: extractedData.error,
          extractionMessage: extractedData.message
        };
      } else if (!extractedData) {
        console.log('No data extracted from Textract - this could be due to:');
        console.log('1. S3 bucket policy not allowing Textract access');
        console.log('2. Textract service not available in region');
        console.log('3. File format not supported');
        console.log('4. AWS credentials insufficient permissions');
        extractedData = {};
      } else {
        console.log('Successfully extracted data:', Object.keys(extractedData));
      }
    } catch (extractionError) {
      console.error('Text extraction error:', extractionError);
      console.error('Error details:', {
        message: extractionError.message,
        code: extractionError.code,
        statusCode: extractionError.statusCode
      });

      // Check if the extraction returned an error object (from our custom error handling)
      if (extractionError && extractionError.error) {
        // This is a custom error from our text extraction function
        console.log('Custom extraction error detected:', extractionError.error);

        // For unsupported document format, return error to user
        if (extractionError.error === 'Unsupported document format') {
          return res.status(400).json({
            message: 'Unsupported document format',
            error: extractionError.message || 'The uploaded file format is not supported for text extraction.',
            details: 'Please try uploading a different PDF file, or convert your document to JPG/PNG format.'
          });
        }

        // For other extraction errors, continue with empty extracted data
        extractedData = {};
      } else {
        // Provide helpful error messages for AWS errors
        if (extractionError.code === 'InvalidS3ObjectException') {
          console.error('SOLUTION: Add Textract permissions to S3 bucket policy');
          console.error('The S3 bucket needs a policy allowing Textract to access objects');
        }

        extractedData = {};
      }
    }

    // Merge extracted data with manually provided data (manual data takes precedence)
    const metadata = {
      invoiceDate: req.body.invoiceDate || (extractedData && extractedData.invoiceDate) || null,
      invoiceNumber: req.body.invoiceNumber || (extractedData && extractedData.invoiceNumber) || null,
      provider: req.body.provider || (extractedData && extractedData.provider) || null,
      energyType: req.body.energyType || (extractedData && extractedData.energyType) || null, // Now includes extraction
      pointOfDelivery: req.body.pointOfDelivery || (extractedData && extractedData.pointOfDelivery) || null,
      amount: req.body.amount || (extractedData && extractedData.amount) || null,
      currency: req.body.currency || 'EUR',
      consumption: req.body.consumption || (extractedData && extractedData.consumption) || null,
      notes: req.body.notes || null
    };

    // Create a new invoice record
    let invoice;
    try {
      invoice = new Invoice({
        userId: user._id,
        cognitoId: cognitoId,
        userType: userType,
        originalFilename: mainFile.originalname,
        s3Key: finalS3Key,
        s3Bucket: mainFile.bucket,
        fileSize: mainFile.size,
        mimeType: mainFile.mimetype,
        publicUrl: finalLocation, // S3 URL
        metadata: metadata,
        status: 'pending'
      });

      // Save the invoice to MongoDB
      await invoice.save();
    } catch (invoiceError) {
      return res.status(500).json({ message: 'Error saving invoice to database', error: invoiceError.message });
    }
    res.status(201).json({
      message: 'Invoice uploaded successfully',
      invoice: {
        id: invoice._id,
        originalFilename: invoice.originalFilename,
        s3Key: invoice.s3Key,
        publicUrl: invoice.publicUrl,
        status: invoice.status,
        createdAt: invoice.createdAt
      },
      extractedData: extractedData,
      file: {
        originalname: mainFile.originalname,
        size: mainFile.size,
        mimetype: mainFile.mimetype,
        bucket: mainFile.bucket,
        key: finalS3Key,
        location: finalLocation
      },
      totalFiles: files.length,
      additionalFiles: files.length > 1 ? files.slice(1).map(f => ({
        originalname: f.originalname,
        size: f.size,
        mimetype: f.mimetype,
        key: f.key
      })) : []
    });
  } catch (error) {
    res.status(500).json({ message: 'Failed to upload invoice', error: error.message });
  }
};

/**
 * Get all invoices for a user
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getUserInvoices = async (req, res) => {
  try {
    const cognitoId = req.params.cognitoId;

    // Find all invoices for the user
    const invoices = await Invoice.find({ cognitoId }).sort({ createdAt: -1 });

    res.status(200).json({
      message: 'Invoices retrieved successfully',
      count: invoices.length,
      invoices: invoices
    });
  } catch (error) {
    res.status(500).json({ message: 'Failed to retrieve invoices', error: error.message });
  }
};

/**
 * Get a single invoice by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getInvoiceById = async (req, res) => {
  try {
    const invoiceId = req.params.id;

    // Find the invoice
    const invoice = await Invoice.findById(invoiceId);

    if (!invoice) {
      return res.status(404).json({ message: 'Invoice not found' });
    }

    res.status(200).json({
      message: 'Invoice retrieved successfully',
      invoice: invoice
    });
  } catch (error) {
    res.status(500).json({ message: 'Failed to retrieve invoice', error: error.message });
  }
};

/**
 * Generate a pre-signed URL for downloading an invoice
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getInvoiceDownloadUrl = async (req, res) => {
  try {
    const invoiceId = req.params.id;

    // Find the invoice
    const invoice = await Invoice.findById(invoiceId);

    if (!invoice) {
      return res.status(404).json({ message: 'Invoice not found' });
    }

    // Generate a pre-signed URL (valid for 5 minutes)
    const url = s3.getSignedUrl('getObject', {
      Bucket: invoice.s3Bucket,
      Key: invoice.s3Key,
      Expires: 300 // 5 minutes
    });

    res.status(200).json({
      message: 'Download URL generated successfully',
      downloadUrl: url,
      expiresIn: '5 minutes'
    });
  } catch (error) {
    res.status(500).json({ message: 'Failed to generate download URL', error: error.message });
  }
};

/**
 * Update invoice metadata
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const updateInvoiceMetadata = async (req, res) => {
  try {
    const invoiceId = req.params.id;
    const { metadata } = req.body;

    if (!metadata) {
      return res.status(400).json({ message: 'Metadata is required' });
    }

    // Find the invoice
    const invoice = await Invoice.findById(invoiceId);

    if (!invoice) {
      return res.status(404).json({ message: 'Invoice not found' });
    }

    // Verify that the user has permission to update this invoice
    const cognitoId = req.user.sub;
    if (invoice.cognitoId !== cognitoId) {
      return res.status(403).json({ message: 'You do not have permission to update this invoice' });
    }

    // Update the metadata
    invoice.metadata = {
      ...invoice.metadata,
      ...metadata
    };

    // Update the status to 'processed' since the user has confirmed the data
    invoice.status = 'processed';

    // Save the updated invoice
    await invoice.save();

    // Return success response
    res.status(200).json({
      message: 'Invoice metadata updated successfully',
      invoice: {
        id: invoice._id,
        originalFilename: invoice.originalFilename,
        s3Key: invoice.s3Key,
        publicUrl: invoice.publicUrl,
        status: invoice.status,
        metadata: invoice.metadata,
        updatedAt: invoice.updatedAt
      }
    });
  } catch (error) {
    res.status(500).json({ message: 'Failed to update invoice metadata', error: error.message });
  }
};

/**
 * Get invoice count for a user
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getInvoiceCount = async (req, res) => {
  try {
    const cognitoId = req.params.cognitoId;

    // Find all invoices for the user
    const count = await Invoice.countDocuments({ cognitoId });

    res.status(200).json({
      success: true,
      count
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve invoice count',
      error: error.message
    });
  }
};

module.exports = {
  upload,
  uploadInvoice,
  getUserInvoices,
  getInvoiceById,
  getInvoiceDownloadUrl,
  updateInvoiceMetadata,
  getInvoiceCount
};
