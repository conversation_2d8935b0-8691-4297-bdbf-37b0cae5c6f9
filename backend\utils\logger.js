const winston = require('winston');
const { createLogger, format, transports } = winston;
const { combine, timestamp, printf, colorize, json } = format;
require('winston-daily-rotate-file');
const path = require('path');
const fs = require('fs');

// Create logs directory if it doesn't exist
const logsDir = path.join(__dirname, '../logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// Define log format
const logFormat = printf(({ level, message, timestamp, ...metadata }) => {
  let metaStr = '';
  if (Object.keys(metadata).length > 0) {
    metaStr = JSON.stringify(metadata);
  }
  return `${timestamp} [${level}]: ${message} ${metaStr}`;
});

// Define sensitive data patterns to mask
const sensitivePatterns = [
  { regex: /(password["']?\s*:\s*["']?)([^"',\s]+)(["',])/gi, replacement: '$1********$3' },
  { regex: /(Authorization["']?\s*:\s*["']?Bearer\s+)([^"',\s]+)(["',])/gi, replacement: '$1********$3' },
  { regex: /(accessKeyId["']?\s*:\s*["']?)([^"',\s]+)(["',])/gi, replacement: '$1********$3' },
  { regex: /(secretAccessKey["']?\s*:\s*["']?)([^"',\s]+)(["',])/gi, replacement: '$1********$3' },
  { regex: /(sessionToken["']?\s*:\s*["']?)([^"',\s]+)(["',])/gi, replacement: '$1********$3' },
  { regex: /(apiKey["']?\s*:\s*["']?)([^"',\s]+)(["',])/gi, replacement: '$1********$3' },
  { regex: /(token["']?\s*:\s*["']?)([^"',\s]+)(["',])/gi, replacement: '$1********$3' },
  { regex: /(jwt["']?\s*:\s*["']?)([^"',\s]+)(["',])/gi, replacement: '$1********$3' },
  { regex: /(refresh_token["']?\s*:\s*["']?)([^"',\s]+)(["',])/gi, replacement: '$1********$3' },
  { regex: /(id_token["']?\s*:\s*["']?)([^"',\s]+)(["',])/gi, replacement: '$1********$3' },
  { regex: /(access_token["']?\s*:\s*["']?)([^"',\s]+)(["',])/gi, replacement: '$1********$3' },
  { regex: /([A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,})/gi, replacement: '****@****.com' },
  { regex: /(\d{3}[-\s]?\d{3}[-\s]?\d{4})/g, replacement: '***-***-****' },
  { regex: /(\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4})/g, replacement: '****-****-****-****' },
];

// Create a format that masks sensitive data
const maskSensitiveData = format((info) => {
  if (typeof info.message === 'string') {
    sensitivePatterns.forEach(pattern => {
      info.message = info.message.replace(pattern.regex, pattern.replacement);
    });
  }
  
  // Also check metadata
  if (info.metadata) {
    const metadataStr = JSON.stringify(info.metadata);
    let maskedStr = metadataStr;
    
    sensitivePatterns.forEach(pattern => {
      maskedStr = maskedStr.replace(pattern.regex, pattern.replacement);
    });
    
    if (maskedStr !== metadataStr) {
      info.metadata = JSON.parse(maskedStr);
    }
  }
  
  return info;
});

// Define log levels
const levels = {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  debug: 4,
};

// Determine log level based on environment
const level = () => {
  const env = process.env.NODE_ENV || 'development';
  return env === 'development' ? 'debug' : 'info';
};

// Configure daily rotate file transport for production
const fileRotateTransport = new transports.DailyRotateFile({
  filename: path.join(logsDir, 'application-%DATE%.log'),
  datePattern: 'YYYY-MM-DD',
  maxSize: '20m',
  maxFiles: '14d',
  level: 'info',
  format: combine(
    timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
    maskSensitiveData(),
    json()
  ),
});

// Configure error log transport
const errorFileRotateTransport = new transports.DailyRotateFile({
  filename: path.join(logsDir, 'error-%DATE%.log'),
  datePattern: 'YYYY-MM-DD',
  maxSize: '20m',
  maxFiles: '14d',
  level: 'error',
  format: combine(
    timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
    maskSensitiveData(),
    json()
  ),
});

// Create the logger
const logger = createLogger({
  level: level(),
  levels,
  format: combine(
    timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
    maskSensitiveData(),
    json()
  ),
  transports: [
    // Console transport for development
    new transports.Console({
      format: combine(
        colorize(),
        timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
        maskSensitiveData(),
        logFormat
      ),
    }),
    fileRotateTransport,
    errorFileRotateTransport,
  ],
  exitOnError: false,
});

// Create a stream object for Morgan HTTP request logging
logger.stream = {
  write: (message) => {
    logger.http(message.trim());
  },
};

module.exports = logger;
