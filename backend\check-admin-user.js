const mongoose = require('mongoose');
const User = require('./models/User');
const path = require('path');
require('dotenv').config({
  path: path.resolve(__dirname, '.env.local')
});

async function checkAdminUser() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    // Find all users
    const users = await User.find({}, 'cognitoId email userType status firstName lastName');
    console.log('\n📊 All users in database:');
    console.log('Total users:', users.length);
    
    users.forEach((user, index) => {
      console.log(`${index + 1}. ${user.firstName} ${user.lastName} (${user.email})`);
      console.log(`   - Cognito ID: ${user.cognitoId}`);
      console.log(`   - User Type: ${user.userType}`);
      console.log(`   - Status: ${user.status}`);
      console.log('');
    });

    // Check for admin users specifically
    const adminUsers = await User.find({ userType: 'Admin' });
    console.log('\n👑 Admin users:');
    if (adminUsers.length === 0) {
      console.log('❌ No admin users found in database!');
    } else {
      adminUsers.forEach((admin, index) => {
        console.log(`${index + 1}. ${admin.firstName} ${admin.lastName} (${admin.email})`);
        console.log(`   - Cognito ID: ${admin.cognitoId}`);
        console.log(`   - Status: ${admin.status}`);
      });
    }

    // Check for users with different case variations
    const adminVariations = await User.find({ 
      userType: { $regex: /admin/i } 
    });
    console.log('\n🔍 Users with admin-like userType (case insensitive):');
    adminVariations.forEach((user) => {
      console.log(`- ${user.email}: userType = "${user.userType}"`);
    });

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await mongoose.disconnect();
    console.log('\n✅ Disconnected from MongoDB');
  }
}

checkAdminUser();
