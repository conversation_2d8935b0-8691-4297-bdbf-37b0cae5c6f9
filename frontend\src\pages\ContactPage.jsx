import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import logoImage from '../assets/logo.jpeg';
import HomeHeader from '../components/HomeHeader';
import Footer from '../components/Footer';
import { useForceScrollToTop } from '../utils/scrollToTop';
import { submitContactForm, submitAppointmentBooking } from '../services/contactApi';
import '../styles/app.css';
import '../styles/pages.css';
import '../styles/home.css';

const ContactPage = () => {
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    message: '',
  });

  const [appointmentData, setAppointmentData] = useState({
    date: '',
    time: '',
    contactMethod: 'phone',
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
  });

  const [formSubmitted, setFormSubmitted] = useState(false);
  const [appointmentSubmitted, setAppointmentSubmitted] = useState(false);
  const [formError, setFormError] = useState('');
  const [appointmentError, setAppointmentError] = useState('');
  const [formLoading, setFormLoading] = useState(false);
  const [appointmentLoading, setAppointmentLoading] = useState(false);

  // Force scroll to top when page loads
  useEffect(() => {
    useForceScrollToTop();
  }, []);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleAppointmentChange = (e) => {
    const { name, value } = e.target;
    setAppointmentData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setFormLoading(true);
    setFormError('');

    // Basic validation
    if (!formData.firstName || !formData.lastName || !formData.email || !formData.message) {
      setFormError('Please fill in all required fields');
      setFormLoading(false);
      return;
    }

    try {
      await submitContactForm(formData);

      // Success
      setFormSubmitted(true);
      setFormError('');

      // Reset form
      setFormData({
        firstName: '',
        lastName: '',
        email: '',
        phone: '',
        message: '',
      });
    } catch (error) {
      setFormError(error.message || 'An error occurred while submitting your message. Please try again.');
    } finally {
      setFormLoading(false);
    }
  };

  const handleAppointmentSubmit = async (e) => {
    e.preventDefault();
    setAppointmentLoading(true);
    setAppointmentError('');

    // Basic validation
    if (!appointmentData.date || !appointmentData.time || !appointmentData.firstName ||
        !appointmentData.lastName || !appointmentData.email) {
      setAppointmentError('Please fill in all required fields');
      setAppointmentLoading(false);
      return;
    }

    try {
      await submitAppointmentBooking(appointmentData);

      // Success
      setAppointmentSubmitted(true);
      setAppointmentError('');

      // Reset form
      setAppointmentData({
        date: '',
        time: '',
        contactMethod: 'phone',
        firstName: '',
        lastName: '',
        email: '',
        phone: '',
      });
    } catch (error) {
      setAppointmentError(error.message || 'An error occurred while booking your appointment. Please try again.');
    } finally {
      setAppointmentLoading(false);
    }
  };

  return (
    <div className="home-container">
      {/* Header */}
      <HomeHeader />

      {/* Hero Section */}
      <section className="page-hero">
        <div className="page-hero-content">
          <h1 className="page-hero-title">📞 Contact Us</h1>
          <p className="page-hero-subtitle">
            Whether you're an individual or a business, we're here to help you make informed energy decisions — with full support every step of the way.
          </p>
        </div>
      </section>

      {/* Contact Methods Section */}
      <section className="contact-methods-section">
        <div className="contact-methods-container">
          <div className="contact-method-block">
            <div className="method-header">
              <div className="method-icon">
                📨
              </div>
              <h2 className="method-title">Send us a message</h2>
            </div>
            <p className="method-description">
              Fill out the contact form and our team will get back to you as soon as possible.
            </p>
          </div>

          <div className="contact-method-block">
            <div className="method-header">
              <div className="method-icon">
                📅
              </div>
              <h2 className="method-title">Book a call with an advisor</h2>
            </div>
            <p className="method-description">
              Need direct assistance? Schedule a call with one of our energy experts at your convenience.
            </p>
          </div>
        </div>
      </section>

      {/* Contact Form Section */}
      <section className="contact-form-section">
        <div className="contact-form-container-new">
          <h2 className="section-title">Send Us a Message</h2>
          <div className="contact-form-wrapper">
            {formSubmitted ? (
              <div className="form-success">
                <div className="success-icon">
                  <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" fill="currentColor" viewBox="0 0 16 16">
                    <path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zm-3.97-3.03a.75.75 0 0 0-1.08.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-.01-1.05z"/>
                  </svg>
                </div>
                <h2>Thank You!</h2>
                <p>Your message has been sent successfully. We'll get back to you as soon as possible.</p>
                <button
                  className="btn btn-primary"
                  onClick={() => setFormSubmitted(false)}
                >
                  Send Another Message
                </button>
              </div>
            ) : (
              <form className="contact-form" onSubmit={handleSubmit}>

                {formError && (
                  <div className="form-error-message">
                    {formError}
                  </div>
                )}

                <div className="form-row">
                  <div className="form-group">
                    <label htmlFor="firstName">First name <span className="required">*</span></label>
                    <input
                      type="text"
                      id="firstName"
                      name="firstName"
                      value={formData.firstName}
                      onChange={handleChange}
                      className="form-input"
                      required
                    />
                  </div>

                  <div className="form-group">
                    <label htmlFor="lastName">Last name <span className="required">*</span></label>
                    <input
                      type="text"
                      id="lastName"
                      name="lastName"
                      value={formData.lastName}
                      onChange={handleChange}
                      className="form-input"
                      required
                    />
                  </div>
                </div>

                <div className="form-group">
                  <label htmlFor="email">Email address <span className="required">*</span></label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                    className="form-input"
                    required
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="phone">Phone number</label>
                  <input
                    type="tel"
                    id="phone"
                    name="phone"
                    value={formData.phone}
                    onChange={handleChange}
                    className="form-input"
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="message">Message <span className="required">*</span></label>
                  <textarea
                    id="message"
                    name="message"
                    value={formData.message}
                    onChange={handleChange}
                    className="form-textarea"
                    rows="5"
                    required
                  ></textarea>
                </div>

                <button type="submit" className="btn btn-primary btn-block" disabled={formLoading}>
                  {formLoading ? '⏳ Submitting...' : '✅ Submit my message'}
                </button>
              </form>
            )}
          </div>
        </div>
      </section>

      {/* Appointment Booking Section */}
      <section className="appointment-section">
        <div className="appointment-container">
          <div className="appointment-header">
            <h2 className="section-title">📅 Book a call with an advisor</h2>
            <p className="appointment-description">
              Need direct assistance? Schedule a call with one of our energy experts at your convenience.
            </p>
          </div>

          {appointmentSubmitted ? (
            <div className="form-success">
              <div className="success-icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" fill="currentColor" viewBox="0 0 16 16">
                  <path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zm-3.97-3.03a.75.75 0 0 0-1.08.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-.01-1.05z"/>
                </svg>
              </div>
              <h2>Appointment Booked!</h2>
              <p>Your appointment has been scheduled successfully. The appointment will be added to our team calendar — ensuring you receive dedicated, timely support.</p>
              <button
                className="btn btn-primary"
                onClick={() => setAppointmentSubmitted(false)}
              >
                Book Another Appointment
              </button>
            </div>
          ) : (
            <form className="appointment-form" onSubmit={handleAppointmentSubmit}>
              {appointmentError && (
                <div className="form-error-message">
                  {appointmentError}
                </div>
              )}

              <div className="form-section">
                <h3 className="form-section-title">Choose:</h3>

                <div className="form-row">
                  <div className="form-group">
                    <label htmlFor="date">Date <span className="required">*</span></label>
                    <input
                      type="date"
                      id="date"
                      name="date"
                      value={appointmentData.date}
                      onChange={handleAppointmentChange}
                      className="form-input"
                      required
                    />
                  </div>

                  <div className="form-group">
                    <label htmlFor="time">Time <span className="required">*</span></label>
                    <input
                      type="time"
                      id="time"
                      name="time"
                      value={appointmentData.time}
                      onChange={handleAppointmentChange}
                      className="form-input"
                      required
                    />
                  </div>
                </div>

                <div className="form-group">
                  <label htmlFor="contactMethod">Preferred contact method <span className="required">*</span></label>
                  <select
                    id="contactMethod"
                    name="contactMethod"
                    value={appointmentData.contactMethod}
                    onChange={handleAppointmentChange}
                    className="form-select"
                    required
                  >
                    <option value="phone">Phone call</option>
                    <option value="email">Mail</option>
                    <option value="video">Video meeting</option>
                  </select>
                </div>
              </div>

              <div className="form-section">
                <h3 className="form-section-title">Provide your contact details:</h3>

                <div className="form-row">
                  <div className="form-group">
                    <label htmlFor="appointmentFirstName">First name <span className="required">*</span></label>
                    <input
                      type="text"
                      id="appointmentFirstName"
                      name="firstName"
                      value={appointmentData.firstName}
                      onChange={handleAppointmentChange}
                      className="form-input"
                      required
                    />
                  </div>

                  <div className="form-group">
                    <label htmlFor="appointmentLastName">Last name <span className="required">*</span></label>
                    <input
                      type="text"
                      id="appointmentLastName"
                      name="lastName"
                      value={appointmentData.lastName}
                      onChange={handleAppointmentChange}
                      className="form-input"
                      required
                    />
                  </div>
                </div>

                <div className="form-group">
                  <label htmlFor="appointmentEmail">Email address <span className="required">*</span></label>
                  <input
                    type="email"
                    id="appointmentEmail"
                    name="email"
                    value={appointmentData.email}
                    onChange={handleAppointmentChange}
                    className="form-input"
                    required
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="appointmentPhone">Phone number</label>
                  <input
                    type="tel"
                    id="appointmentPhone"
                    name="phone"
                    value={appointmentData.phone}
                    onChange={handleAppointmentChange}
                    className="form-input"
                  />
                </div>
              </div>

              <button type="submit" className="btn btn-primary btn-block" disabled={appointmentLoading}>
                {appointmentLoading ? '⏳ Booking...' : 'Book Appointment'}
              </button>
            </form>
          )}

          <div className="appointment-note">
            <p>Once confirmed, the appointment will be added to our team calendar — ensuring you receive dedicated, timely support.</p>
          </div>
        </div>
      </section>

      {/* Footer */}
      <Footer />
    </div>
  );
};

export default ContactPage;
