import { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import '../styles/app.css';
import '../styles/horizontal-layout.css';
import Spinner from '../components/Spinner';
import { setItem, getItem, STORAGE_KEYS } from '../utils/localStorage';
import { Auth } from 'aws-amplify';
import { getLoadingMessage } from '../utils/loadingMessages';
import { showSuccessMessage, showErrorMessage, showInfoMessage } from '../utils/toastNotifications';
import logger from '../utils/logger';
import { useForceScrollToTopAuthenticated } from '../utils/scrollToTop';
import { API_BASE_URL } from '../config/api-config';

const UserTypeSelection = () => {
  const [selectedType, setSelectedType] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [initialLoading, setInitialLoading] = useState(true);
  const navigate = useNavigate();
  const location = useLocation();
  const { updateUserAttributes } = useAuth();

  // Get user data from location state (passed from registration page)
  const userData = location.state?.userData || {};

  // Debug logging
  console.log('DEBUG - UserTypeSelection location.state:', location.state);
  console.log('DEBUG - UserTypeSelection userData:', userData);
  console.log('DEBUG - UserTypeSelection userData.email:', userData.email);

  // Check if user already has a type selected
  useEffect(() => {
    // Force scroll to top when page loads (SUPER AGGRESSIVE for authenticated pages)
    useForceScrollToTopAuthenticated();

    const checkUserType = async () => {
      try {
        // Check localStorage for user type and profile completion
        const storedUserType = getItem(STORAGE_KEYS.USER_TYPE);
        const profileComplete = getItem(STORAGE_KEYS.PROFILE_COMPLETION) === 'true';

        if (storedUserType) {
          logger.debug('User type found in localStorage:', storedUserType);
          setSelectedType(storedUserType);

          // PRIORITY: Admin users should always redirect to dashboard immediately
          if (storedUserType.toLowerCase() === 'admin') {
            logger.info('🔑 ADMIN USER DETECTED in UserTypeSelection - Redirecting to dashboard immediately');
            navigate('/dashboard', { replace: true });
            return;
          }

          // If profile is complete, redirect to dashboard
          if (profileComplete) {
            logger.info('Profile is complete, redirecting to dashboard');
            navigate('/dashboard');
            return;
          }

          // If profile is not complete, redirect to appropriate info page
          const storedUserData = getItem(STORAGE_KEYS.USER_DATA, true); // Parse as JSON

          if (storedUserType === 'individual') {
            logger.info('Individual profile not complete, redirecting to individual info page');
            navigate('/individual-info', { state: { userData: storedUserData } });
          } else if (storedUserType === 'professional') {
            logger.info('Professional profile not complete, redirecting to professional info page');
            navigate('/professional-info', { state: { userData: storedUserData } });
          }
          return;
        }

        // If not in localStorage, try to get from Cognito
        try {
          const user = await Auth.currentAuthenticatedUser();
          const userType = user.attributes['custom:userType'];
          const profileCompletion = user.attributes['custom:profileCompletion'] === 'true';

          if (userType) {
            logger.debug('User type found in Cognito:', userType);
            setSelectedType(userType);

            // PRIORITY: Admin users should always redirect to dashboard immediately
            if (userType.toLowerCase() === 'admin') {
              logger.info('🔑 ADMIN USER DETECTED in Cognito - Redirecting to dashboard immediately');
              // Store admin data in localStorage
              setItem(STORAGE_KEYS.USER_TYPE, 'admin');
              setItem(STORAGE_KEYS.PROFILE_COMPLETION, 'true');
              navigate('/dashboard', { replace: true });
              return;
            }

            // Store in localStorage
            setItem(STORAGE_KEYS.USER_TYPE, userType);
            setItem(STORAGE_KEYS.PROFILE_COMPLETION, profileCompletion.toString());

            if (profileCompletion) {
              logger.info('Profile is complete, redirecting to dashboard');
              navigate('/dashboard');
              return;
            }

            // Profile not complete, redirect to appropriate info page
            const storedUserData = getItem(STORAGE_KEYS.USER_DATA, true);

            if (userType === 'individual') {
              navigate('/individual-info', { state: { userData: storedUserData } });
            } else if (userType === 'professional') {
              navigate('/professional-info', { state: { userData: storedUserData } });
            }
            return;
          }
        } catch (error) {
          logger.error('Error getting user type from Cognito:', error);
        }

        // If we get here, user doesn't have a type selected yet
        setInitialLoading(false);
      } catch (error) {
        logger.error('Error checking user type:', error);
        setInitialLoading(false);
      }
    };

    checkUserType();
  }, [navigate]);

  const handleTypeSelection = (type) => {
    // Only allow individual and professional types for direct registration
    if (['individual', 'professional'].includes(type)) {
      setSelectedType(type);
      setError(null); // Clear any previous errors
    } else {
      setError('This account type is not available for direct registration.');
    }
  };

  const handleContinue = async () => {
    if (!selectedType) {
      setError('Please select a user type to continue');
      return;
    }

    // Validate that only allowed user types can register directly
    if (!['individual', 'professional'].includes(selectedType)) {
      setError('Invalid user type selected. Only individual and professional accounts can register directly.');
      return;
    }

    setLoading(true);
    setError(null);



    try {
      // Prepare user data with type for passing to the next page
      const userDataWithType = {
        ...userData,
        userType: selectedType
      };

      logger.debug('Complete user data:', userDataWithType);

      // Store data in localStorage FIRST to ensure it's available for the next page
      setItem(STORAGE_KEYS.USER_TYPE, selectedType);
      setItem(STORAGE_KEYS.PROFILE_COMPLETION, 'false');
      setItem(STORAGE_KEYS.USER_DATA, userDataWithType);
      logger.info('User type, profile completion status, and user data stored in localStorage');



      // Convert selectedType to match the enum values in the User model
      let userTypeForMongoDB = selectedType;
      if (selectedType === 'individual') {
        userTypeForMongoDB = 'Individual';
      } else if (selectedType === 'professional') {
        userTypeForMongoDB = 'Professional';
      }

      // Get email and cognitoId from userData (passed from signup page)
      const email = userData.email;
      const cognitoId = userData.cognitoId || '';

      if (!email) {
        logger.error('No email found in userData');
        throw new Error('Email is required to update user type');
      }

      logger.debug('Using email from registration data:', email);
      logger.debug('Using cognitoId from registration data:', cognitoId);

      // Try to update Cognito attributes for the current authenticated user
      try {
        logger.info('Attempting to update Cognito attributes for current user');

        // Check if user is already authenticated
        const currentUser = await Auth.currentAuthenticatedUser();
        logger.info('Current user found, updating Cognito attributes');

        // Update the user type and profile completion in Cognito using the AuthContext method
        try {
          await updateUserAttributes({
            'custom:userType': selectedType,
            'custom:profileCompletion': 'false'
          });
          logger.info('User type and profile completion status updated in Cognito');
        } catch (attributeError) {
          logger.warn('Could not update custom attributes, trying with standard attribute', attributeError);
          try {
            await updateUserAttributes({
              'preferred_username': `${selectedType}_user`
            });
            logger.info('User type updated in Cognito with preferred_username');
          } catch (stdAttributeError) {
            logger.error('Failed to update any attribute in Cognito:', stdAttributeError);
          }
        }
      } catch (authError) {
        logger.warn('User not authenticated or error updating Cognito attributes:', authError);
        // Note: User should already be authenticated from the signup flow
        // If not authenticated, we'll continue with MongoDB update only
        // The Cognito attributes will be updated when they complete their profile
      }

      // Update user type in MongoDB directly using the backend API
      // This doesn't require authentication as we're using a direct API call
      logger.info('Updating user type in MongoDB directly:', { userType: userTypeForMongoDB, email });

      try {
        // Use the direct API endpoint that doesn't require authentication
        const response = await fetch(`${API_BASE_URL}/auth/update-user-type`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            userType: userTypeForMongoDB,
            email: email,
            cognitoId: cognitoId,
            status: 'Active',
            verificationStatus: 'Verified'
          })
        });

        const responseData = await response.json();
        logger.debug('MongoDB update response:', responseData);

        if (!response.ok) {
          throw new Error(responseData.message || 'Failed to update user type in MongoDB');
        }

        logger.info('User type updated in MongoDB successfully');
      } catch (mongoError) {
        logger.error('Error updating MongoDB directly:', mongoError);
        setError('Failed to update user type. Please try again.');
        setLoading(false);
        return;
      }

      // Show success message
      showSuccessMessage('PROFILE_UPDATED', `Account type set to ${selectedType}. Please complete your profile information.`);

      // Navigate to the appropriate page based on user type
      if (selectedType === 'individual') {
        logger.info('Individual type selected, redirecting to individual info page');
        navigate('/individual-info', { state: { userData: userDataWithType } });
      } else if (selectedType === 'professional') {
        logger.info('Professional type selected, redirecting to professional info page');
        navigate('/professional-info', { state: { userData: userDataWithType } });
      }
    } catch (err) {
      logger.error('User type update error:', err);
      showErrorMessage('PROFILE_SAVE_FAILED', 'Failed to update user type. Please try again.');
      setError('Failed to update user type. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="fullscreen-container">
      {initialLoading ? (
        <Spinner
          fullScreen={true}
          message={getLoadingMessage('USER_TYPE_SELECTION')}
          size="large"
          color="#3498db"
        />
      ) : (
        <div className="content-wrapper">
          <h1 className="page-title">Select Your Account Type</h1>
          <p className="page-subtitle">
            Choose the account type that best describes you. We offer personalized energy solutions for both individual consumers and business professionals.
          </p>

          <div className="horizontal-cards">
          {/* Individual Card */}
        <div
          className={`card ${selectedType === 'individual' ? 'selected' : ''}`}
          onClick={() => handleTypeSelection('individual')}
        >
          <div className="card-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 16 16">
              <path d="M8 8a3 3 0 1 0 0-6 3 3 0 0 0 0 6zm2-3a2 2 0 1 1-4 0 2 2 0 0 1 4 0zm4 8c0 1-1 1-1 1H3s-1 0-1-1 1-4 6-4 6 3 6 4zm-1-.004c-.001-.246-.154-.986-.832-1.664C11.516 10.68 10.289 10 8 10c-2.29 0-3.516.68-4.168 1.332-.678.678-.83 1.418-.832 1.664h10z"/>
            </svg>
          </div>
          <h3 className="card-title">Individual</h3>
          <p className="card-description">
            Personal account for managing your home energy bills and getting personalized recommendations.
          </p>
        </div>

        {/* Professional Card */}
        <div
          className={`card ${selectedType === 'professional' ? 'selected' : ''}`}
          onClick={() => handleTypeSelection('professional')}
        >
          <div className="card-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 16 16">
              <path d="M6.5 1A1.5 1.5 0 0 0 5 2.5V3H1.5A1.5 1.5 0 0 0 0 4.5v1.384l7.614 2.03a1.5 1.5 0 0 0 .772 0L16 5.884V4.5A1.5 1.5 0 0 0 14.5 3H11v-.5A1.5 1.5 0 0 0 9.5 1h-3zm0 1h3a.5.5 0 0 1 .5.5V3H6v-.5a.5.5 0 0 1 .5-.5z"/>
              <path d="M0 12.5A1.5 1.5 0 0 0 1.5 14h13a1.5 1.5 0 0 0 1.5-1.5V6.85L8.129 8.947a.5.5 0 0 1-.258 0L0 6.85v5.65z"/>
            </svg>
          </div>
          <h3 className="card-title">Professional</h3>
          <p className="card-description">
            Business account for managing multiple properties with advanced reporting tools.
          </p>
        </div>
      </div>

      {error && <div className="error-container">{error}</div>}

      <div className="action-container">
        <button
          className="action-button"
          onClick={handleContinue}
          disabled={loading || !selectedType}
        >
          {loading ? 'Processing...' : 'Continue'}
        </button>
      </div>

      {loading && (
        <Spinner
          fullScreen={true}
          message={getLoadingMessage('SAVING_USER_TYPE')}
          size="large"
          color="#3498db"
        />
      )}
      </div>
      )}
    </div>
  );
};

export default UserTypeSelection;
