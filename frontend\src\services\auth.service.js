import { Auth } from 'aws-amplify';
import { setItem, getItem, removeItem, STORAGE_KEYS } from '../utils/localStorage';
import { API_BASE_URL } from '../config/api-config';

// Helper function to get Auth - ensures Auth is properly initialized
const getAuth = async () => {
  try {
    // Return the imported Auth
    return Auth;
  } catch (error) {
    console.error('AWS Amplify Auth not available', error);
    throw new Error('Authentication service is not available');
  }
};

/**
 * Service for handling authentication with AWS Cognito
 */
class AuthService {
  /**
   * Sign up a new user
   * @param {string} username - The username (email)
   * @param {string} password - The password
   * @param {object} attributes - Additional user attributes
   * @returns {Promise} - The sign up result
   */
  async signUp(username, password, attributes = {}) {
    try {
      console.log('Signing up user:', username, 'with attributes:', attributes);
      const Auth = await getAuth();
      const { user } = await Auth.signUp({
        username,
        password,
        attributes
      });
      return user;
    } catch (error) {
      console.error('Error signing up:', error);
      throw this.handleError(error);
    }
  }

  /**
   * Confirm a user's registration with verification code
   * @param {string} username - The username (email)
   * @param {string} code - The verification code
   * @returns {Promise} - The confirmation result
   */
  async confirmSignUp(username, code) {
    try {
      console.log('Confirming signup for user:', username, 'with code:', code);
      const Auth = await getAuth();
      return await Auth.confirmSignUp(username, code);
    } catch (error) {
      console.error('Error confirming sign up:', error);
      throw this.handleError(error);
    }
  }

  /**
   * Resend confirmation code
   * @param {string} username - The username (email)
   * @returns {Promise} - The result
   */
  async resendConfirmationCode(username) {
    try {
      console.log('Resending confirmation code for:', username);
      const Auth = await getAuth();
      return await Auth.resendSignUp(username);
    } catch (error) {
      console.error('Error resending code:', error);
      throw this.handleError(error);
    }
  }

  /**
   * Sign in a user
   * @param {string} username - The username (email or phone)
   * @param {string} password - The password
   * @returns {Promise} - The sign in result
   */
  async signIn(username, password) {
    try {
      console.log('Signing in user:', username);
      const Auth = await getAuth();
      const user = await Auth.signIn(username, password);
      return user;
    } catch (error) {
      console.error('Error signing in:', error);
      throw this.handleError(error);
    }
  }

  /**
   * Sign out the current user
   * @returns {Promise} - The sign out result
   */
  async signOut() {
    try {
      console.log('Signing out user');
      const Auth = await getAuth();
      return await Auth.signOut();
    } catch (error) {
      console.error('Error signing out:', error);
      throw this.handleError(error);
    }
  }

  /**
   * Get the current authenticated user
   * @returns {Promise} - The current user
   */
  async getCurrentUser() {
    try {
      console.log('Getting current authenticated user');
      const Auth = await getAuth();
      return await Auth.currentAuthenticatedUser();
    } catch (error) {
      console.error('Error getting current user:', error);
      return null;
    }
  }

  /**
   * Get the current session
   * @returns {Promise} - The current session
   */
  async getCurrentSession() {
    try {
      console.log('Getting current session');
      const Auth = await getAuth();
      return await Auth.currentSession();
    } catch (error) {
      console.error('Error getting current session:', error);
      return null;
    }
  }

  /**
   * Forgot password - initiate reset
   * @param {string} username - The username (email)
   * @returns {Promise} - The result
   */
  async forgotPassword(username) {
    try {
      console.log('Initiating forgot password for:', username);
      const Auth = await getAuth();
      return await Auth.forgotPassword(username);
    } catch (error) {
      console.error('Error initiating forgot password:', error);
      throw this.handleError(error);
    }
  }

  /**
   * Forgot password - complete reset
   * @param {string} username - The username (email)
   * @param {string} code - The verification code
   * @param {string} newPassword - The new password
   * @returns {Promise} - The result
   */
  async forgotPasswordSubmit(username, code, newPassword) {
    try {
      console.log('Completing forgot password for:', username);
      const Auth = await getAuth();
      return await Auth.forgotPasswordSubmit(username, code, newPassword);
    } catch (error) {
      console.error('Error completing forgot password:', error);
      throw this.handleError(error);
    }
  }

  /**
   * Change password for authenticated user
   * @param {object} user - The current user
   * @param {string} oldPassword - The old password
   * @param {string} newPassword - The new password
   * @returns {Promise} - The result
   */
  async changePassword(user, oldPassword, newPassword) {
    try {
      console.log('Changing password for user');
      const Auth = await getAuth();
      return await Auth.changePassword(user, oldPassword, newPassword);
    } catch (error) {
      console.error('Error changing password:', error);
      throw this.handleError(error);
    }
  }

  /**
   * Update user attributes
   * @param {object} user - The current user
   * @param {object} attributes - The attributes to update
   * @returns {Promise} - The result
   */
  async updateUserAttributes(user, attributes) {
    try {
      console.log('Updating user attributes:', attributes);
      const Auth = await getAuth();
      return await Auth.updateUserAttributes(user, attributes);
    } catch (error) {
      console.error('Error updating user attributes:', error);
      throw this.handleError(error);
    }
  }

  /**
   * Verify user attribute (like email or phone)
   * @param {string} attribute - The attribute to verify
   * @param {string} code - The verification code
   * @returns {Promise} - The result
   */
  async verifyUserAttribute(attribute, code) {
    try {
      console.log('Verifying user attribute:', attribute);
      const Auth = await getAuth();
      return await Auth.verifyUserAttribute(attribute, code);
    } catch (error) {
      console.error('Error verifying attribute:', error);
      throw this.handleError(error);
    }
  }

  /**
   * Register a new user in the backend MongoDB after Cognito registration
   * @param {Object} userData - User data including cognitoId, email, etc.
   * @returns {Promise} Promise object representing the registration result
   */
  async registerUserInBackend(userData) {
    try {
      console.log('Registering user in backend MongoDB:', userData);

      // Get the token from localStorage or the current session
      let token = getItem(STORAGE_KEYS.ID_TOKEN);

      if (!token) {
        // If no token in localStorage, try to get it from the current session
        try {
          const Auth = await getAuth();
          const session = await Auth.currentSession();
          token = session.getIdToken().getJwtToken();
          setItem(STORAGE_KEYS.ID_TOKEN, token);
        } catch (sessionError) {
          console.error('Error getting current session:', sessionError);
          // Continue without token - the backend will handle unauthenticated requests
        }
      }

      const headers = {
        'Content-Type': 'application/json'
      };

      // Add Authorization header if token exists
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      console.log('Sending request to backend:', {
        url: `${API_BASE_URL}/auth/register-mongodb`,
        method: 'POST',
        headers,
        body: userData
      });

      const response = await fetch(`${API_BASE_URL}/auth/register-mongodb`, {
        method: 'POST',
        headers,
        body: JSON.stringify(userData)
      });

      console.log('Response status:', response.status);

      const responseData = await response.json();
      console.log('Response data:', responseData);

      if (!response.ok) {
        throw new Error(responseData.message || 'Failed to register user in backend');
      }

      return responseData;
    } catch (error) {
      console.error('Error registering user in backend:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Update user type in the backend MongoDB
   * @param {string} userType - The user type to update
   * @param {string} email - User's email
   * @param {string} cognitoId - User's Cognito ID
   * @returns {Promise} Promise object representing the update result
   */
  async updateUserTypeInBackend(userType, email, cognitoId) {
    try {
      console.log('Updating user type in backend MongoDB:', userType, email, cognitoId);

      // Get the token from localStorage or the current session
      let token = getItem(STORAGE_KEYS.ID_TOKEN);

      if (!token) {
        // If no token in localStorage, try to get it from the current session
        try {
          const Auth = await getAuth();
          const session = await Auth.currentSession();
          token = session.getIdToken().getJwtToken();
          setItem(STORAGE_KEYS.ID_TOKEN, token);
        } catch (sessionError) {
          console.error('Error getting current session:', sessionError);
          // Continue without token - the backend will handle unauthenticated requests
        }
      }

      const headers = {
        'Content-Type': 'application/json'
      };

      // Add Authorization header if token exists
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      console.log('Sending request to update user type:', {
        url: `${API_BASE_URL}/auth/update-user-type`,
        method: 'POST',
        headers,
        body: { userType, email, cognitoId }
      });

      const response = await fetch(`${API_BASE_URL}/auth/update-user-type`, {
        method: 'POST',
        headers,
        body: JSON.stringify({ userType, email, cognitoId })
      });

      console.log('Update user type response status:', response.status);

      const responseData = await response.json();
      console.log('Update user type response data:', responseData);

      if (!response.ok) {
        throw new Error(responseData.message || 'Failed to update user type in backend');
      }

      return responseData;
    } catch (error) {
      console.error('Error updating user type in backend:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Handle authentication errors
   * @param {Error} error - The error object
   * @returns {Error} - A formatted error
   */
  handleError(error) {
    // Format the error message for better user experience
    let message = error.message || 'An unknown error occurred';

    console.log('Handling auth error:', error);

    // Handle specific Cognito error codes
    switch (error.code) {
      case 'UserNotFoundException':
        message = 'User does not exist';
        break;
      case 'NotAuthorizedException':
        message = 'Incorrect username or password';
        break;
      case 'UserNotConfirmedException':
        message = 'User is not confirmed. Please check your email for a confirmation code.';
        break;
      case 'CodeMismatchException':
        message = 'Invalid verification code';
        break;
      case 'InvalidPasswordException':
        message = 'Password does not meet requirements';
        break;
      case 'LimitExceededException':
        message = 'Attempt limit exceeded, please try again later';
        break;
      case 'UsernameExistsException':
        message = 'This username already exists';
        break;
      default:
        // For development, log the full error
        console.warn('Unhandled auth error code:', error.code);
        break;
    }

    return new Error(message);
  }
}

export default new AuthService();
