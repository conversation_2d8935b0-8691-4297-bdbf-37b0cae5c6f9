version: '3.8'

services:
  # MongoDB service
  mongodb:
    image: mongo:4.4
    container_name: energy-mongodb
    restart: always
    ports:
      - "27017:27017"
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=admin123
      - MONGO_INITDB_DATABASE=energy_bill
    volumes:
      - mongodb_data:/data/db
    networks:
      - app-network
    command: ["--nojournal", "--wiredTigerCacheSizeGB", "0.5"]

  # Backend service
  backend:
    build:
      context: ./backend
    container_name: energy-backend
    restart: always
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - MONGODB_URI=*******************************************************************
      - PORT=3000
    depends_on:
      - mongodb
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Frontend service
  frontend:
    build:
      context: ./frontend
    container_name: energy-frontend
    restart: always
    ports:
      - "8080:80"
    environment:
      - NODE_ENV=development
    depends_on:
      backend:
        condition: service_healthy
    networks:
      - app-network

networks:
  app-network:
    driver: bridge

volumes:
  mongodb_data:
    name: energy-mongodb-data
