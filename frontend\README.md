# My Energy Bill Frontend

This is the frontend application for the My Energy Bill project.

## Running the Application

### Development Mode

To run the application in development mode:

```bash
# Using npm script (recommended)
npm run dev

# Using batch script (cleans up processes before starting)
start-app.bat

# Using PowerShell script (cleans up processes and cache)
powershell -ExecutionPolicy Bypass -File "start-app.ps1"
```

### Building for Production

To build the application for production:

```bash
# For Windows
npm run build

# For Linux/macOS
npm run build:linux
```

### Previewing Production Build

To preview the production build locally:

```bash
npm run preview
```

## Environment Variables

The application uses the following environment variables:

- `VITE_APP_API_URL` - The URL of the backend API (default: http://localhost:3000)
- `VITE_AWS_REGION` - AWS Cognito region (default: eu-west-3)
- `VITE_COGNITO_USER_POOL_ID` - AWS Cognito User Pool ID
- `VITE_COGNITO_CLIENT_ID` - AWS Cognito Client ID

## Docker

The application can be run using Docker:

### Development

```bash
docker build -f Dockerfile.dev -t energy-frontend-dev .
docker run -p 8080:8080 -v $(pwd):/app energy-frontend-dev
```

### Production

```bash
docker build -t energy-frontend .
docker run -p 80:80 energy-frontend
```

## Project Structure

The project has been cleaned up to remove redundant files. The following files are essential:

- `package.json` - Defines dependencies and scripts
- `vite.config.js` - Configuration for Vite (the build tool)
- `index.html` - Main HTML entry point
- `src/` - Source code for the application
- `public/` - Static assets
- `.env` and `.env.production` - Environment variables
- `Dockerfile` and `Dockerfile.dev` - Docker configurations
- `nginx.conf` - Nginx configuration for production
- `start-app.bat` - Batch script to run the application with process cleanup
- `start-app.ps1` - PowerShell script to run the application with process and cache cleanup
