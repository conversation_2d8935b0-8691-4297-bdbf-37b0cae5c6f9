const mongoose = require('mongoose');

const referrerProfileSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    unique: true
  },
  referralCode: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  commissionRate: {
    type: Number,
    default: 0
  },
  paymentDetails: {
    bankName: String,
    accountHolder: String,
    iban: String,
    bic: String
  },
  referralHistory: [{
    referredUser: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    status: {
      type: String,
      enum: ['Pending', 'Registered', 'Completed', 'Cancelled'],
      default: 'Pending'
    },
    dateReferred: {
      type: Date,
      default: Date.now
    },
    commissionEarned: Number,
    commissionPaid: {
      type: Boolean,
      default: false
    },
    paymentDate: Date
  }],
  totalEarnings: {
    type: Number,
    default: 0
  },
  pendingEarnings: {
    type: Number,
    default: 0
  }
}, {
  timestamps: true
});

// Create indexes
referrerProfileSchema.index({ userId: 1 });
referrerProfileSchema.index({ referralCode: 1 });

const ReferrerProfile = mongoose.model('ReferrerProfile', referrerProfileSchema);

module.exports = ReferrerProfile;
