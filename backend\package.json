{"name": "backend", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node server.js", "dev": "nodemon server.js", "check-db": "node scripts/check-db-connection.js", "seed-db": "node scripts/seed-database.js", "enhanced-seed": "node scripts/enhanced-seed.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"aws-sdk": "^2.1692.0", "axios": "^1.8.4", "body-parser": "^2.2.0", "cors": "^2.8.5", "csv-parser": "^3.2.0", "docusign-esign": "^6.5.1", "dotenv": "^16.5.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "jwk-to-pem": "^2.0.7", "mongoose": "^8.14.1", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "multer-s3": "^2.10.0", "pdf-parse": "^1.1.1", "pdf2pic": "^3.2.0", "sharp": "^0.34.2", "uuid": "^11.1.0", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"nodemon": "^3.1.10"}}