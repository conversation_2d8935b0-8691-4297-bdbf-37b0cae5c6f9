const crypto = require('crypto');
const jwt = require('jsonwebtoken');
const User = require('../models/User');
const UserActivity = require('../models/UserActivity');
const emailService = require('./emailService');
const logger = require('../utils/logger');

class PasswordResetService {
  constructor() {
    this.tokenExpiry = 60 * 60 * 1000; // 1 hour in milliseconds
    this.jwtSecret = process.env.JWT_SECRET || 'your-secret-key';
  }

  /**
   * Generate a secure password reset token
   */
  generateResetToken(userEmail, userId) {
    const payload = {
      email: userEmail,
      userId: userId,
      type: 'password_reset',
      timestamp: Date.now()
    };

    const token = jwt.sign(payload, this.jwtSecret, {
      expiresIn: '1h',
      issuer: 'energy-platform',
      audience: 'password-reset'
    });

    return token;
  }

  /**
   * Verify and decode a password reset token
   */
  verifyResetToken(token) {
    try {
      const decoded = jwt.verify(token, this.jwtSecret, {
        issuer: 'energy-platform',
        audience: 'password-reset'
      });

      // Check if token is expired (additional check)
      const tokenAge = Date.now() - decoded.timestamp;
      if (tokenAge > this.tokenExpiry) {
        throw new Error('Token has expired');
      }

      return {
        valid: true,
        email: decoded.email,
        userId: decoded.userId,
        timestamp: decoded.timestamp
      };
    } catch (error) {
      logger.error('Token verification failed:', error);
      return {
        valid: false,
        error: error.message
      };
    }
  }

  /**
   * Initiate password reset process
   */
  async initiatePasswordReset(email, initiatedBy = 'user') {
    try {
      // Find user in database
      const user = await User.findOne({ email: email.toLowerCase() });
      if (!user) {
        // For security, we don't reveal if email exists or not
        logger.info(`Password reset attempted for non-existent email: ${email}`);
        return {
          success: true,
          message: 'If an account with this email exists, a password reset link has been sent.'
        };
      }

      // Check if user is suspended
      if (user.status === 'Suspended') {
        logger.warn(`Password reset attempted for suspended user: ${email}`);
        return {
          success: false,
          message: 'Account is suspended. Please contact support.'
        };
      }

      // Generate reset token
      const resetToken = this.generateResetToken(user.email, user._id);
      
      // Store reset token info in user document (optional, for tracking)
      user.passwordResetToken = resetToken;
      user.passwordResetExpires = new Date(Date.now() + this.tokenExpiry);
      user.passwordResetRequestedAt = new Date();
      await user.save();

      // Send password reset email
      const userName = user.firstName ? `${user.firstName} ${user.lastName || ''}`.trim() : '';
      await emailService.sendPasswordResetEmail(user.email, resetToken, userName);

      // Log the activity
      await UserActivity.logActivity({
        userId: user._id,
        activityType: 'PasswordResetRequested',
        description: `Password reset requested by ${initiatedBy}`,
        details: {
          initiatedBy,
          requestedAt: new Date(),
          tokenExpires: user.passwordResetExpires
        },
        severity: 'Medium',
        isSystemGenerated: true
      });

      logger.info(`Password reset initiated for user: ${email}`, {
        userId: user._id,
        initiatedBy
      });

      return {
        success: true,
        message: 'Password reset link has been sent to your email address.'
      };

    } catch (error) {
      logger.error('Error initiating password reset:', error);
      throw new Error(`Failed to initiate password reset: ${error.message}`);
    }
  }

  /**
   * Reset password using token
   */
  async resetPassword(token, newPassword, confirmPassword) {
    try {
      // Validate passwords match
      if (newPassword !== confirmPassword) {
        return {
          success: false,
          message: 'Passwords do not match.'
        };
      }

      // Validate password strength
      const passwordValidation = this.validatePassword(newPassword);
      if (!passwordValidation.valid) {
        return {
          success: false,
          message: passwordValidation.message
        };
      }

      // Verify token
      const tokenVerification = this.verifyResetToken(token);
      if (!tokenVerification.valid) {
        return {
          success: false,
          message: 'Invalid or expired reset token.'
        };
      }

      // Find user
      const user = await User.findById(tokenVerification.userId);
      if (!user) {
        return {
          success: false,
          message: 'User not found.'
        };
      }

      // Check if token matches the one stored (if we're storing it)
      if (user.passwordResetToken !== token) {
        return {
          success: false,
          message: 'Invalid reset token.'
        };
      }

      // Check if token is expired
      if (user.passwordResetExpires && user.passwordResetExpires < new Date()) {
        return {
          success: false,
          message: 'Reset token has expired.'
        };
      }

      // Update password in Cognito
      const cognitoUpdate = await this.updateCognitoPassword(user.email, newPassword);
      if (!cognitoUpdate.success) {
        return {
          success: false,
          message: cognitoUpdate.message
        };
      }

      // Clear reset token fields
      user.passwordResetToken = undefined;
      user.passwordResetExpires = undefined;
      user.passwordResetRequestedAt = undefined;
      user.lastPasswordChange = new Date();
      await user.save();

      // Send confirmation email
      const userName = user.firstName ? `${user.firstName} ${user.lastName || ''}`.trim() : '';
      await emailService.sendPasswordResetConfirmationEmail(user.email, userName);

      // Log the activity
      await UserActivity.logActivity({
        userId: user._id,
        activityType: 'PasswordReset',
        description: 'Password successfully reset using reset token',
        details: {
          resetMethod: 'email_token',
          resetAt: new Date()
        },
        severity: 'High',
        isSystemGenerated: true
      });

      logger.info(`Password successfully reset for user: ${user.email}`, {
        userId: user._id
      });

      return {
        success: true,
        message: 'Password has been successfully reset.'
      };

    } catch (error) {
      logger.error('Error resetting password:', error);
      throw new Error(`Failed to reset password: ${error.message}`);
    }
  }

  /**
   * Update password in AWS Cognito
   */
  async updateCognitoPassword(email, newPassword) {
    try {
      const AWS = require('aws-sdk');
      const cognitoIdentityServiceProvider = new AWS.CognitoIdentityServiceProvider();
      
      const params = {
        UserPoolId: process.env.COGNITO_USER_POOL_ID,
        Username: email,
        Password: newPassword,
        Permanent: true
      };

      await cognitoIdentityServiceProvider.adminSetUserPassword(params).promise();
      
      return {
        success: true,
        message: 'Password updated in Cognito successfully'
      };

    } catch (error) {
      logger.error('Error updating Cognito password:', error);
      return {
        success: false,
        message: `Failed to update password: ${error.message}`
      };
    }
  }

  /**
   * Validate password strength
   */
  validatePassword(password) {
    if (!password || password.length < 8) {
      return {
        valid: false,
        message: 'Password must be at least 8 characters long.'
      };
    }

    if (!/(?=.*[a-z])/.test(password)) {
      return {
        valid: false,
        message: 'Password must contain at least one lowercase letter.'
      };
    }

    if (!/(?=.*[A-Z])/.test(password)) {
      return {
        valid: false,
        message: 'Password must contain at least one uppercase letter.'
      };
    }

    if (!/(?=.*\d)/.test(password)) {
      return {
        valid: false,
        message: 'Password must contain at least one number.'
      };
    }

    if (!/(?=.*[@$!%*?&])/.test(password)) {
      return {
        valid: false,
        message: 'Password must contain at least one special character (@$!%*?&).'
      };
    }

    return {
      valid: true,
      message: 'Password is valid.'
    };
  }

  /**
   * Clean up expired reset tokens
   */
  async cleanupExpiredTokens() {
    try {
      const result = await User.updateMany(
        {
          passwordResetExpires: { $lt: new Date() }
        },
        {
          $unset: {
            passwordResetToken: 1,
            passwordResetExpires: 1,
            passwordResetRequestedAt: 1
          }
        }
      );

      logger.info(`Cleaned up ${result.modifiedCount} expired password reset tokens`);
      return result.modifiedCount;

    } catch (error) {
      logger.error('Error cleaning up expired tokens:', error);
      throw error;
    }
  }
}

module.exports = new PasswordResetService();
