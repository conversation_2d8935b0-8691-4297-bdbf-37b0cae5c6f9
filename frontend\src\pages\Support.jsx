import { useState } from 'react';
import DashboardLayout from '../components/DashboardLayout';
import { showSuccessMessage, showErrorMessage } from '../utils/toastNotifications';
import '../styles/support.css';

const Support = () => {
  const [activeTab, setActiveTab] = useState('faq');
  const [searchQuery, setSearchQuery] = useState('');
  const [contactForm, setContactForm] = useState({
    subject: '',
    message: '',
    priority: 'medium'
  });

  const faqs = [
    {
      id: 1,
      question: 'How do I upload my energy bill?',
      answer: 'You can upload your energy bill by navigating to the "My Invoices" section in the sidebar menu. Click on the "Upload Invoice" button and select your bill file from your computer. We accept PDF, JPG, and PNG formats.'
    },
    {
      id: 2,
      question: 'How long does it take to receive offers?',
      answer: 'After uploading your energy bill, our system typically processes it within 24-48 hours. You will then receive personalized energy offers based on your consumption patterns and preferences.'
    },
    {
      id: 3,
      question: 'Can I change my energy provider through this platform?',
      answer: 'Yes, you can change your energy provider directly through our platform. Once you select an offer that suits your needs, you can sign the contract electronically, and we will handle the switch process for you.'
    },
    {
      id: 4,
      question: 'How are my savings calculated?',
      answer: 'Your savings are calculated by comparing your current energy rates with the rates offered by our partner providers. We take into account your consumption patterns, fixed charges, and any applicable discounts to provide an accurate estimate of your potential savings.'
    },
    {
      id: 5,
      question: 'Is my personal information secure?',
      answer: 'Yes, we take data security very seriously. All your personal information is encrypted and stored securely. We comply with GDPR regulations and never share your data with third parties without your explicit consent.'
    },
    {
      id: 6,
      question: 'What happens after I sign a contract?',
      answer: 'After signing a contract, we will notify your new energy provider, who will then initiate the switch process. You will receive confirmation emails at each step of the process. The switch typically takes 2-3 weeks to complete, during which time your energy supply will continue uninterrupted.'
    }
  ];

  const filteredFaqs = searchQuery
    ? faqs.filter(faq =>
        faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
        faq.answer.toLowerCase().includes(searchQuery.toLowerCase())
      )
    : faqs;

  const handleContactFormChange = (e) => {
    const { name, value } = e.target;
    setContactForm({
      ...contactForm,
      [name]: value
    });
  };

  const handleContactSubmit = (e) => {
    e.preventDefault();
    // In a real app, this would be an API call to submit the support ticket
    console.log('Submitting support ticket:', contactForm);

    // Show success message and reset form
    showSuccessMessage('OPERATION_COMPLETED', 'Your support ticket has been submitted. We will get back to you soon!');
    setContactForm({
      subject: '',
      message: '',
      priority: 'medium'
    });
  };

  return (
    <DashboardLayout>
      <div className="support-container">
        <h1 className="support-title">Help & Support</h1>

        <div className="support-tabs">
          <button
            className={`support-tab ${activeTab === 'faq' ? 'active' : ''}`}
            onClick={() => setActiveTab('faq')}
          >
            <i className="fas fa-question-circle"></i> FAQ
          </button>
          <button
            className={`support-tab ${activeTab === 'contact' ? 'active' : ''}`}
            onClick={() => setActiveTab('contact')}
          >
            <i className="fas fa-envelope"></i> Contact Support
          </button>
          <button
            className={`support-tab ${activeTab === 'tickets' ? 'active' : ''}`}
            onClick={() => setActiveTab('tickets')}
          >
            <i className="fas fa-ticket-alt"></i> My Tickets
          </button>
        </div>

        <div className="support-content">
          {activeTab === 'faq' && (
            <div className="faq-section">
              <div className="faq-search">
                <input
                  type="text"
                  placeholder="Search FAQ..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
                <i className="fas fa-search"></i>
              </div>

              {filteredFaqs.length === 0 ? (
                <div className="no-results">
                  <p>No results found for "{searchQuery}"</p>
                  <button onClick={() => setSearchQuery('')}>Clear Search</button>
                </div>
              ) : (
                <div className="faq-list">
                  {filteredFaqs.map(faq => (
                    <div key={faq.id} className="faq-item">
                      <div className="faq-question">
                        <h3>{faq.question}</h3>
                        <i className="fas fa-chevron-down"></i>
                      </div>
                      <div className="faq-answer">
                        <p>{faq.answer}</p>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {activeTab === 'contact' && (
            <div className="contact-section">
              <div className="contact-info">
                <div className="contact-method">
                  <i className="fas fa-phone"></i>
                  <div>
                    <h3>Phone Support</h3>
                    <p>+33 1 23 45 67 89</p>
                    <p className="contact-hours">Monday to Friday, 9:00 AM - 6:00 PM</p>
                  </div>
                </div>

                <div className="contact-method">
                  <i className="fas fa-envelope"></i>
                  <div>
                    <h3>Email Support</h3>
                    <p><EMAIL></p>
                    <p className="contact-hours">We typically respond within 24 hours</p>
                  </div>
                </div>

                <div className="contact-method">
                  <i className="fas fa-comments"></i>
                  <div>
                    <h3>Live Chat</h3>
                    <p>Available on our website</p>
                    <p className="contact-hours">Monday to Friday, 9:00 AM - 6:00 PM</p>
                  </div>
                </div>
              </div>

              <div className="contact-form-container">
                <h2>Send us a message</h2>
                <form className="contact-form" onSubmit={handleContactSubmit}>
                  <div className="form-group">
                    <label htmlFor="subject">Subject</label>
                    <input
                      type="text"
                      id="subject"
                      name="subject"
                      value={contactForm.subject}
                      onChange={handleContactFormChange}
                      required
                      placeholder="What is your inquiry about?"
                    />
                  </div>

                  <div className="form-group">
                    <label htmlFor="message">Message</label>
                    <textarea
                      id="message"
                      name="message"
                      value={contactForm.message}
                      onChange={handleContactFormChange}
                      required
                      placeholder="Please describe your issue or question in detail"
                      rows="6"
                    ></textarea>
                  </div>

                  <div className="form-group">
                    <label htmlFor="priority">Priority</label>
                    <select
                      id="priority"
                      name="priority"
                      value={contactForm.priority}
                      onChange={handleContactFormChange}
                    >
                      <option value="low">Low</option>
                      <option value="medium">Medium</option>
                      <option value="high">High</option>
                    </select>
                  </div>

                  <button type="submit" className="submit-btn">
                    Submit Ticket
                  </button>
                </form>
              </div>
            </div>
          )}

          {activeTab === 'tickets' && (
            <div className="tickets-section">
              <div className="tickets-header">
                <h2>My Support Tickets</h2>
                <button className="new-ticket-btn">
                  <i className="fas fa-plus"></i> New Ticket
                </button>
              </div>

              <div className="tickets-list">
                <div className="ticket-item">
                  <div className="ticket-status open">Open</div>
                  <div className="ticket-info">
                    <h3 className="ticket-subject">Issue with invoice upload</h3>
                    <p className="ticket-message">I'm having trouble uploading my latest energy bill. The system keeps showing an error.</p>
                    <div className="ticket-meta">
                      <span className="ticket-id">Ticket #12345</span>
                      <span className="ticket-date">Opened: May 15, 2023</span>
                    </div>
                  </div>
                  <div className="ticket-actions">
                    <button className="view-ticket-btn">View</button>
                  </div>
                </div>

                <div className="ticket-item">
                  <div className="ticket-status closed">Closed</div>
                  <div className="ticket-info">
                    <h3 className="ticket-subject">Question about my contract</h3>
                    <p className="ticket-message">I need clarification on some terms in my new energy contract.</p>
                    <div className="ticket-meta">
                      <span className="ticket-id">Ticket #12340</span>
                      <span className="ticket-date">Closed: April 28, 2023</span>
                    </div>
                  </div>
                  <div className="ticket-actions">
                    <button className="view-ticket-btn">View</button>
                  </div>
                </div>

                <div className="ticket-item">
                  <div className="ticket-status in-progress">In Progress</div>
                  <div className="ticket-info">
                    <h3 className="ticket-subject">Savings calculation discrepancy</h3>
                    <p className="ticket-message">The savings shown on my dashboard don't match what was promised in the offer.</p>
                    <div className="ticket-meta">
                      <span className="ticket-id">Ticket #12342</span>
                      <span className="ticket-date">Opened: May 10, 2023</span>
                    </div>
                  </div>
                  <div className="ticket-actions">
                    <button className="view-ticket-btn">View</button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </DashboardLayout>
  );
};

export default Support;
