const dotenv = require('dotenv');
const path = require('path');

// Load environment variables
dotenv.config({
  path: path.resolve(__dirname, '.env.uat'),
  override: false,
});

const mongoose = require('mongoose');
const SupportTicket = require('./models/SupportTicket');
const User = require('./models/User');
const logger = require('./utils/logger');

async function testSupportTicketCreation() {
  console.log('🧪 Testing Support Ticket Creation\n');

  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    // Test data
    const testEmail = '<EMAIL>';
    const testTicketData = {
      email: testEmail,
      subject: 'Account Status Issue - ACCOUNT_SUSPENDED',
      message: 'My account has been suspended and I need assistance to resolve this issue. Please help me understand why this happened and how to restore access.',
      priority: 'high',
      status: 'ACCOUNT_SUSPENDED',
      errorCode: 'ACCOUNT_SUSPENDED'
    };

    console.log('📧 Test ticket data:');
    console.log(JSON.stringify(testTicketData, null, 2));

    // Find user by email
    const user = await User.findOne({ email: testEmail });
    console.log(`👤 User found: ${user ? 'YES' : 'NO'}`);
    if (user) {
      console.log(`   Email: ${user.email}`);
      console.log(`   Status: ${user.status}`);
      console.log(`   ID: ${user._id}`);
    }

    // Create support ticket (simulate the backend endpoint logic)
    const ticketData = {
      userId: user ? user._id : null,
      subject: testTicketData.subject,
      description: testTicketData.message,
      type: 'Account',
      priority: (testTicketData.priority && testTicketData.priority.charAt(0).toUpperCase() + testTicketData.priority.slice(1)) || 'High',
      status: 'Open',
      relatedEntities: [{
        entityType: 'AccountStatus',
        entityId: null
      }],
      comments: [{
        text: `Account Status Support Request

Status: ${testTicketData.status}
Error Code: ${testTicketData.errorCode}
User Email: ${testEmail}
Priority: ${testTicketData.priority}

User Message:
${testTicketData.message}`,
        createdBy: user ? user._id : null,
        isInternal: false,
        createdAt: new Date()
      }]
    };

    console.log('\n🎫 Creating support ticket...');
    const ticket = new SupportTicket(ticketData);
    await ticket.save();

    const ticketNumber = `#${ticket._id.toString().slice(-6).toUpperCase()}`;
    console.log(`✅ Support ticket created successfully!`);
    console.log(`   Ticket ID: ${ticket._id}`);
    console.log(`   Ticket Number: ${ticketNumber}`);
    console.log(`   Subject: ${ticket.subject}`);
    console.log(`   Priority: ${ticket.priority}`);
    console.log(`   Status: ${ticket.status}`);

    // Test email notification (optional - comment out if you don't want to send actual emails)
    console.log('\n📧 Testing email notification...');
    try {
      const emailService = require('./services/emailService');
      await emailService.sendAccountStatusTicketNotification({
        ticketId: ticket._id,
        userEmail: testEmail,
        subject: ticket.subject,
        message: testTicketData.message,
        priority: ticket.priority,
        accountStatus: testTicketData.status,
        errorCode: testTicketData.errorCode
      });
      console.log('✅ Email notification sent successfully!');
    } catch (emailError) {
      console.log('❌ Email notification failed:', emailError.message);
    }

    // Check recent tickets
    console.log('\n📋 Recent support tickets:');
    const recentTickets = await SupportTicket.find({})
      .sort({ createdAt: -1 })
      .limit(3)
      .populate('userId', 'email firstName lastName');

    recentTickets.forEach((t, index) => {
      const ticketNum = `#${t._id.toString().slice(-6).toUpperCase()}`;
      const userEmail = t.userId ? t.userId.email : 'Unknown';
      console.log(`   ${index + 1}. ${ticketNum} - ${t.subject} (${userEmail}) - ${t.status}`);
    });

    console.log('\n🎉 Support ticket system is working correctly!');
    console.log('\n📝 Manual Testing Steps:');
    console.log('1. Go to AccountStatus page (login with suspended user)');
    console.log('2. Click "Contact Support" button');
    console.log('3. Try all three options:');
    console.log('   - Email: Should open email client');
    console.log('   - Phone: Should copy number to clipboard');
    console.log('   - Support Ticket: Should create ticket and show success message');

  } catch (error) {
    console.error('💥 Test failed:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n✨ Disconnected from MongoDB');
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  testSupportTicketCreation()
    .then(() => {
      console.log('\n✨ Test completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Test failed:', error);
      process.exit(1);
    });
}

module.exports = { testSupportTicketCreation };
