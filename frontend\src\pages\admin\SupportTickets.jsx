import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import DashboardLayout from '../../components/DashboardLayout';
import Spinner from '../../components/Spinner';
import api from '../../services/api';
import logger from '../../utils/logger';
import './SupportTickets.css';

const SupportTickets = () => {
  const navigate = useNavigate();
  const [tickets, setTickets] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedTicket, setSelectedTicket] = useState(null);
  const [showTicketModal, setShowTicketModal] = useState(false);
  const [filters, setFilters] = useState({
    status: 'all',
    priority: 'all',
    type: 'all',
    search: ''
  });
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalCount: 0,
    limit: 10
  });
  const [statistics, setStatistics] = useState({
    statusBreakdown: {},
    priorityBreakdown: {},
    typeBreakdown: {}
  });

  useEffect(() => {
    fetchSupportTickets();
  }, [filters, pagination.currentPage]);

  const fetchSupportTickets = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: pagination.currentPage,
        limit: pagination.limit,
        ...filters
      });

      const response = await api.get(`/api/admin/support-tickets?${params}`);
      
      if (response.data.success) {
        setTickets(response.data.data.tickets);
        setPagination(response.data.data.pagination);
        setStatistics(response.data.data.statistics);
        setError(null);
      }
    } catch (error) {
      logger.error('Error fetching support tickets:', error);
      setError('Failed to load support tickets');
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
    setPagination(prev => ({
      ...prev,
      currentPage: 1
    }));
  };

  const handleTicketClick = async (ticketId) => {
    try {
      const response = await api.get(`/api/admin/support-tickets/${ticketId}`);
      if (response.data.success) {
        setSelectedTicket(response.data.data);
        setShowTicketModal(true);
      }
    } catch (error) {
      logger.error('Error fetching ticket details:', error);
    }
  };

  const handleUpdateTicket = async (ticketId, updates) => {
    try {
      const response = await api.patch(`/api/admin/support-tickets/${ticketId}`, updates);
      if (response.data.success) {
        setSelectedTicket(response.data.data);
        fetchSupportTickets(); // Refresh the list
        return true;
      }
    } catch (error) {
      logger.error('Error updating ticket:', error);
      return false;
    }
  };

  const handleAddComment = async (ticketId, comment) => {
    try {
      const response = await api.post(`/api/admin/support-tickets/${ticketId}/comments`, comment);
      if (response.data.success) {
        setSelectedTicket(response.data.data);
        return true;
      }
    } catch (error) {
      logger.error('Error adding comment:', error);
      return false;
    }
  };

  const getStatusBadgeClass = (status) => {
    const statusClasses = {
      'Open': 'status-open',
      'InProgress': 'status-in-progress',
      'Resolved': 'status-resolved',
      'Closed': 'status-closed',
      'Reopened': 'status-reopened'
    };
    return statusClasses[status] || 'status-default';
  };

  const getPriorityBadgeClass = (priority) => {
    const priorityClasses = {
      'Low': 'priority-low',
      'Medium': 'priority-medium',
      'High': 'priority-high',
      'Critical': 'priority-critical'
    };
    return priorityClasses[priority] || 'priority-default';
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading && tickets.length === 0) {
    return (
      <DashboardLayout>
        <div className="support-tickets-loading">
          <Spinner size="large" message="Loading support tickets..." />
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="support-tickets-container">
        {/* Header */}
        <div className="page-header">
          <div className="header-content">
            <h1>Support Ticket Management</h1>
            <p>Manage and resolve customer support tickets</p>
          </div>
        </div>

        {/* Statistics Cards */}
        <div className="tickets-stats-grid">
          <div className="stat-card total">
            <div className="stat-icon">
              <i className="fas fa-ticket-alt"></i>
            </div>
            <div className="stat-content">
              <h3>{pagination.totalCount}</h3>
              <p>Total Tickets</p>
            </div>
          </div>

          <div className="stat-card open">
            <div className="stat-icon">
              <i className="fas fa-folder-open"></i>
            </div>
            <div className="stat-content">
              <h3>{statistics.statusBreakdown.Open || 0}</h3>
              <p>Open Tickets</p>
            </div>
          </div>

          <div className="stat-card in-progress">
            <div className="stat-icon">
              <i className="fas fa-clock"></i>
            </div>
            <div className="stat-content">
              <h3>{statistics.statusBreakdown.InProgress || 0}</h3>
              <p>In Progress</p>
            </div>
          </div>

          <div className="stat-card critical">
            <div className="stat-icon">
              <i className="fas fa-exclamation-triangle"></i>
            </div>
            <div className="stat-content">
              <h3>{statistics.priorityBreakdown.Critical || 0}</h3>
              <p>Critical Priority</p>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="tickets-filters">
          <div className="filter-group">
            <label>Status:</label>
            <select 
              value={filters.status} 
              onChange={(e) => handleFilterChange('status', e.target.value)}
            >
              <option value="all">All Status</option>
              <option value="Open">Open</option>
              <option value="InProgress">In Progress</option>
              <option value="Resolved">Resolved</option>
              <option value="Closed">Closed</option>
              <option value="Reopened">Reopened</option>
            </select>
          </div>

          <div className="filter-group">
            <label>Priority:</label>
            <select 
              value={filters.priority} 
              onChange={(e) => handleFilterChange('priority', e.target.value)}
            >
              <option value="all">All Priorities</option>
              <option value="Low">Low</option>
              <option value="Medium">Medium</option>
              <option value="High">High</option>
              <option value="Critical">Critical</option>
            </select>
          </div>

          <div className="filter-group">
            <label>Type:</label>
            <select 
              value={filters.type} 
              onChange={(e) => handleFilterChange('type', e.target.value)}
            >
              <option value="all">All Types</option>
              <option value="Technical">Technical</option>
              <option value="Billing">Billing</option>
              <option value="Account">Account</option>
              <option value="Offer">Offer</option>
              <option value="Contract">Contract</option>
              <option value="Other">Other</option>
            </select>
          </div>

          <div className="filter-group search-group">
            <label>Search:</label>
            <input
              type="text"
              placeholder="Search tickets..."
              value={filters.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
            />
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="error-message">
            <i className="fas fa-exclamation-circle"></i>
            {error}
          </div>
        )}

        {/* Tickets Table */}
        <div className="tickets-table-container">
          <table className="tickets-table">
            <thead>
              <tr>
                <th>Ticket ID</th>
                <th>Subject</th>
                <th>User</th>
                <th>Type</th>
                <th>Priority</th>
                <th>Status</th>
                <th>Created</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {tickets.map(ticket => (
                <tr key={ticket._id} onClick={() => handleTicketClick(ticket._id)}>
                  <td className="ticket-id">
                    #{ticket._id.slice(-6).toUpperCase()}
                  </td>
                  <td className="ticket-subject">
                    <div className="subject-text">{ticket.subject}</div>
                    <div className="subject-preview">
                      {ticket.description.substring(0, 80)}...
                    </div>
                  </td>
                  <td className="ticket-user">
                    <div className="user-info">
                      <div className="user-email">{ticket.userEmail}</div>
                      {ticket.userId && (
                        <div className="user-name">
                          {ticket.userId.firstName} {ticket.userId.lastName}
                        </div>
                      )}
                    </div>
                  </td>
                  <td className="ticket-type">
                    <span className={`type-badge type-${ticket.type.toLowerCase()}`}>
                      {ticket.type}
                    </span>
                  </td>
                  <td className="ticket-priority">
                    <span className={`priority-badge ${getPriorityBadgeClass(ticket.priority)}`}>
                      {ticket.priority}
                    </span>
                  </td>
                  <td className="ticket-status">
                    <span className={`status-badge ${getStatusBadgeClass(ticket.status)}`}>
                      {ticket.status}
                    </span>
                  </td>
                  <td className="ticket-created">
                    {formatDate(ticket.createdAt)}
                  </td>
                  <td className="ticket-actions">
                    <button 
                      className="action-btn view-btn"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleTicketClick(ticket._id);
                      }}
                    >
                      <i className="fas fa-eye"></i>
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>

          {tickets.length === 0 && !loading && (
            <div className="no-tickets">
              <i className="fas fa-ticket-alt"></i>
              <p>No support tickets found</p>
            </div>
          )}
        </div>

        {/* Pagination */}
        {pagination.totalPages > 1 && (
          <div className="pagination">
            <button 
              className="pagination-btn"
              disabled={pagination.currentPage === 1}
              onClick={() => setPagination(prev => ({ ...prev, currentPage: prev.currentPage - 1 }))}
            >
              <i className="fas fa-chevron-left"></i>
              Previous
            </button>
            
            <span className="pagination-info">
              Page {pagination.currentPage} of {pagination.totalPages}
            </span>
            
            <button 
              className="pagination-btn"
              disabled={pagination.currentPage === pagination.totalPages}
              onClick={() => setPagination(prev => ({ ...prev, currentPage: prev.currentPage + 1 }))}
            >
              Next
              <i className="fas fa-chevron-right"></i>
            </button>
          </div>
        )}
      </div>

      {/* Ticket Detail Modal */}
      {showTicketModal && selectedTicket && (
        <TicketDetailModal
          ticket={selectedTicket}
          onClose={() => {
            setShowTicketModal(false);
            setSelectedTicket(null);
          }}
          onUpdate={handleUpdateTicket}
          onAddComment={handleAddComment}
        />
      )}
    </DashboardLayout>
  );
};

// Ticket Detail Modal Component (will be created separately)
const TicketDetailModal = ({ ticket, onClose, onUpdate, onAddComment }) => {
  // This will be implemented in the next part
  return (
    <div className="modal-overlay" onClick={onClose}>
      <div className="ticket-modal" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h2>Ticket #{ticket._id.slice(-6).toUpperCase()}</h2>
          <button className="close-btn" onClick={onClose}>
            <i className="fas fa-times"></i>
          </button>
        </div>
        <div className="modal-content">
          <p>Ticket details will be implemented here...</p>
        </div>
      </div>
    </div>
  );
};

export default SupportTickets;
