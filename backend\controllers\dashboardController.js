const Invoice = require('../models/Invoice');
const Offer = require('../models/Offer');
const Contract = require('../models/Contract');
const Appointment = require('../models/Appointment');
const User = require('../models/User');

/**
 * Get dashboard statistics for a user
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getDashboardStats = async (req, res) => {
  try {
    const { cognitoId } = req.params;

    if (!cognitoId) {
      return res.status(400).json({
        success: false,
        message: 'Cognito ID is required'
      });
    }

    console.log('Fetching dashboard stats for user:', cognitoId);

    // Find the user first to get the userId for appointments
    const user = await User.findOne({ cognitoId });
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Get counts for each category
    const [invoiceCount, offerCount, contractCount, appointmentCount] = await Promise.all([
      Invoice.countDocuments({ cognitoId }),
      Offer.countDocuments({ cognitoId }),
      Contract.countDocuments({ cognitoId }),
      Appointment.countDocuments({ userId: user._id })
    ]);

    // Get recent items for each category (optional - for future use)
    const [recentInvoices, recentOffers, recentContracts, recentAppointments] = await Promise.all([
      Invoice.find({ cognitoId })
        .sort({ createdAt: -1 })
        .limit(5)
        .select('_id fileName energyType status createdAt'),
      Offer.find({ cognitoId })
        .sort({ createdAt: -1 })
        .limit(5)
        .select('_id offerType status createdAt'),
      Contract.find({ cognitoId })
        .sort({ createdAt: -1 })
        .limit(5)
        .select('_id contractType status createdAt'),
      Appointment.find({ userId: user._id })
        .sort({ scheduledTime: 1 })
        .limit(5)
        .select('_id type status scheduledTime location')
    ]);

    const dashboardStats = {
      invoices: {
        count: invoiceCount,
        recentInvoices: recentInvoices
      },
      offers: {
        count: offerCount,
        recentOffers: recentOffers
      },
      contracts: {
        count: contractCount,
        recentContracts: recentContracts
      },
      appointments: {
        count: appointmentCount,
        recentAppointments: recentAppointments
      }
    };

    console.log('Dashboard stats fetched successfully:', {
      cognitoId,
      invoices: invoiceCount,
      offers: offerCount,
      contracts: contractCount,
      appointments: appointmentCount
    });

    res.status(200).json({
      success: true,
      data: dashboardStats
    });

  } catch (error) {
    console.error('Error fetching dashboard stats:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch dashboard statistics',
      error: error.message
    });
  }
};

/**
 * Get invoice count for a user
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getInvoiceCount = async (req, res) => {
  try {
    const { cognitoId } = req.params;

    if (!cognitoId) {
      return res.status(400).json({
        success: false,
        message: 'Cognito ID is required'
      });
    }

    const count = await Invoice.countDocuments({ cognitoId });

    console.log('Invoice count fetched:', { cognitoId, count });

    res.status(200).json({
      success: true,
      count: count
    });

  } catch (error) {
    console.error('Error fetching invoice count:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch invoice count',
      error: error.message
    });
  }
};

/**
 * Get offer count for a user
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getOfferCount = async (req, res) => {
  try {
    const { cognitoId } = req.params;

    if (!cognitoId) {
      return res.status(400).json({
        success: false,
        message: 'Cognito ID is required'
      });
    }

    const count = await Offer.countDocuments({ cognitoId });

    console.log('Offer count fetched:', { cognitoId, count });

    res.status(200).json({
      success: true,
      count: count
    });

  } catch (error) {
    console.error('Error fetching offer count:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch offer count',
      error: error.message
    });
  }
};

/**
 * Get contract count for a user
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getContractCount = async (req, res) => {
  try {
    const { cognitoId } = req.params;

    if (!cognitoId) {
      return res.status(400).json({
        success: false,
        message: 'Cognito ID is required'
      });
    }

    const count = await Contract.countDocuments({ cognitoId });

    console.log('Contract count fetched:', { cognitoId, count });

    res.status(200).json({
      success: true,
      count: count
    });

  } catch (error) {
    console.error('Error fetching contract count:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch contract count',
      error: error.message
    });
  }
};

/**
 * Get appointment count for a user
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getAppointmentCount = async (req, res) => {
  try {
    const { cognitoId } = req.params;

    if (!cognitoId) {
      return res.status(400).json({
        success: false,
        message: 'Cognito ID is required'
      });
    }

    // Find the user first to get the userId
    const user = await User.findOne({ cognitoId });
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    const count = await Appointment.countDocuments({ userId: user._id });

    console.log('Appointment count fetched:', { cognitoId, userId: user._id, count });

    res.status(200).json({
      success: true,
      count: count
    });

  } catch (error) {
    console.error('Error fetching appointment count:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch appointment count',
      error: error.message
    });
  }
};

module.exports = {
  getDashboardStats,
  getInvoiceCount,
  getOfferCount,
  getContractCount,
  getAppointmentCount
};
