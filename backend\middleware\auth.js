// AWS Cognito Authentication Middleware
const { User } = require('../models');
const jwt = require('jsonwebtoken');
const jwkToPem = require('jwk-to-pem');
const axios = require('axios');
const { cognitoIdentityServiceProvider, userPoolConfig } = require('../config/cognito');
const logger = require('../utils/logger');

// Cache for JWKs
let jwksCache = null;
let jwksCacheTime = null;
const JWKS_CACHE_DURATION = 60 * 60 * 1000; // 1 hour in milliseconds

// Fetch JWKs from Cognito
const fetchJWKs = async () => {
  try {
    // Check if we have a valid cache
    const now = Date.now();
    if (jwksCache && jwksCacheTime && (now - jwksCacheTime < JWKS_CACHE_DURATION)) {
      return jwksCache;
    }

    // Fetch JWKs from Cognito
    const region = process.env.AWS_REGION;
    const userPoolId = process.env.COGNITO_USER_POOL_ID;
    const url = `https://cognito-idp.${region}.amazonaws.com/${userPoolId}/.well-known/jwks.json`;

    const response = await axios.get(url);
    jwksCache = response.data.keys;
    jwksCacheTime = now;

    return jwksCache;
  } catch (error) {
    logger.error('Error fetching JWKs:', error);
    throw error;
  }
};

// Verify JWT token
const verifyToken = async (req, res, next) => {
  try {
    // Get token from header
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      logger.warn('No valid authorization header found');
      return res.status(401).json({
        success: false,
        message: 'No token provided'
      });
    }

    logger.debug('Authorization header found, extracting token');

    const token = authHeader.split(' ')[1];
    logger.debug('Token extracted, length:', token ? token.length : 'null');
    // Parse the token
    const tokenSections = token.split('.');
    if (tokenSections.length !== 3) {
      logger.debug('Token length mismatch');
      return res.status(401).json({
        success: false,
        message: 'Invalid token format'
      });
    }
 logger.debug('Token length matching');
    // Get the JWT header
    const headerJSON = Buffer.from(tokenSections[0], 'base64').toString('utf8');
    const header = JSON.parse(headerJSON);
    logger.debug('JWT header parsed:', header);

    // Get the kid (Key ID) from the token header
    const kid = header.kid;
    logger.debug('Token kid (Key ID):', kid);

    // Fetch the JWKs
    logger.debug('Fetching JWKs...');
    const keys = await fetchJWKs();
    logger.debug('JWKs fetched, count:', keys.length);

    // Find the signing key
    const signingKey = keys.find(key => key.kid === kid);
    logger.debug('Signing key found:', signingKey ? 'Yes' : 'No');
    if (!signingKey) {
      logger.error('No signing key found for kid:', kid);
      return res.status(401).json({
        success: false,
        message: 'Invalid token: Unknown key'
      });
    }

    // Convert JWK to PEM
    logger.debug('Converting JWK to PEM...');
    const pem = jwkToPem(signingKey);
    logger.debug('PEM conversion successful');

    // Verify the token
    logger.debug('Starting JWT verification...');
    jwt.verify(token, pem, { algorithms: ['RS256'] }, async (err, decodedToken) => {
      if (err) {
        logger.error('JWT verification failed:', err.message);
        return res.status(401).json({
          success: false,
          message: 'Invalid token: ' + err.message
        });
      }

      logger.debug('JWT verification successful');

      // Check token use (access or id)
      if (decodedToken.token_use !== 'access' && decodedToken.token_use !== 'id') {
        return res.status(401).json({
          success: false,
          message: 'Invalid token use'
        });
      }

      // Find or create user in our database
      try {
        logger.debug('Auth middleware: Looking for user with cognitoId:', decodedToken.sub);
        let user = await User.findOne({ cognitoId: decodedToken.sub });
        logger.debug('Auth middleware: User found by cognitoId:', !!user);

        if (!user && decodedToken.email) {
          // Try to find by email as fallback
          logger.debug('Auth middleware: Looking for user with email:', decodedToken.email);
          user = await User.findOne({ email: decodedToken.email });
          logger.debug('Auth middleware: User found by email:', !!user);

          if (user) {
            // Update the cognitoId if found by email
            logger.info('Auth middleware: Updating cognitoId for user found by email');
            user.cognitoId = decodedToken.sub;
            await user.save();
          }
        }

        if (user) {
          logger.debug('Auth middleware: User details:', {
            id: user._id,
            email: user.email,
            userType: user.userType,
            status: user.status,
            cognitoId: user.cognitoId
          });
        } else {
          logger.warn('Auth middleware: No user found in database for token', {
            cognitoId: decodedToken.sub,
            email: decodedToken.email
          });
        }

        // Check if user is suspended
        if (user && user.status === 'Suspended') {
          logger.warn(`Suspended user attempted to access protected route: ${user.email}`);

          // Log the suspended access attempt
          const UserActivity = require('../models/UserActivity');
          await UserActivity.logActivity({
            userId: user._id,
            activityType: 'SuspendedAccessAttempt',
            description: 'Suspended user attempted to access protected resource',
            details: {
              email: user.email,
              route: req.originalUrl,
              method: req.method,
              attemptedAt: new Date(),
              userAgent: req.headers['user-agent'],
              ipAddress: req.ip || req.connection.remoteAddress
            },
            severity: 'High',
            isSystemGenerated: true
          });

          return res.status(403).json({
            success: false,
            message: 'Account is suspended. Please contact support for assistance.',
            error: 'ACCOUNT_SUSPENDED',
            supportEmail: '<EMAIL>'
          });
        }

        // Set user info in request
        req.user = {
          sub: decodedToken.sub,
          email: decodedToken.email,
          username: decodedToken.username || decodedToken.email,
          cognitoUser: decodedToken,
          dbUser: user
        };

        logger.debug('Auth middleware: Set req.user:', {
          sub: req.user.sub,
          email: req.user.email,
          hasDbUser: !!req.user.dbUser,
          userStatus: user?.status
        });

        return next();
      } catch (dbError) {
        logger.error('Database error in auth middleware:', dbError);
        return res.status(500).json({
          success: false,
          message: 'Database error during authentication'
        });
      }
    });
  } catch (error) {
    logger.error('Error in auth middleware:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error during authentication'
    });
  }
};

// Simple middleware for routes that need authentication
const authMiddleware = (req, res, next) => {
  logger.debug('Auth middleware called for:', req.method, req.originalUrl);
  logger.debug('Authorization header:', req.headers.authorization ? 'Present' : 'Missing');

  // Special handling for file uploads
  if (req.originalUrl.includes('/upload') && req.method === 'POST') {
    logger.info('Processing file upload request');

    // For debugging purposes, log the request body keys
    if (req.body) {
      logger.debug('Request body keys:', Object.keys(req.body));
    }

    // For debugging purposes, log the request files
    if (req.files) {
      logger.debug('Request files:', Object.keys(req.files));
    } else if (req.file) {
      logger.debug('Request file present:', req.file.originalname);
    } else {
      logger.debug('No files in request');
    }
  }

  verifyToken(req, res, next);
};

module.exports = {
  verifyToken,
  fetchJWKs,
  authMiddleware
};
