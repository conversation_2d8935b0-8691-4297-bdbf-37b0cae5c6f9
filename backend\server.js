const express = require('express');
const cors = require('cors');
const path = require('path');
const bodyParser = require('body-parser');
const dotenv = require('dotenv');
const connectDB = require('./config/database');

// Import logger and request logger middleware
const logger = require('./utils/logger');
const requestLogger = require('./middleware/requestLogger');

// Load environment variables
const cliEnv = process.argv[2];
process.env.NODE_ENV = cliEnv || process.env.NODE_ENV || 'local';

const envFile = `.env.${process.env.NODE_ENV}`;

dotenv.config({
  path: path.resolve(__dirname, envFile),
  override: false, // Don't override ECS-injected vars
});

console.log(`Loaded environment: ${envFile}`);
console.log('Environment variables:', process.env);
console.log('MongoDB URI:', process.env.MONGODB_URI);

// Connect to MongoDB

console.log('MongoDB connection status:', {
  enabled: process.env.ENABLE_MONGODB === 'true',
  connectionString: process.env.MONGODB_URI ? 'Set' : 'Not set'
});

if (process.env.ENABLE_MONGODB === 'true') {
  connectDB().then(() => {
    console.log('MongoDB connection established successfully');
  }).catch(err => {
    console.error('MongoDB connection failed:', err);
    console.log('Continuing with mock data for development');
  });
} else {
  console.log('MongoDB connection disabled - using mock data');
}

// Import routes
const authRoutes = require('./routes/auth');
const apiRoutes = require('./routes/api');
const energyRequestRoutes = require('./routes/energyRequests');
const userRoutes = require('./routes/users');
const healthRoutes = require('./routes/health');
const invoiceRoutes = require('./routes/invoices');
const dashboardRoutes = require('./routes/dashboard');
const logsRoutes = require('./routes/logs');
const offerRoutes = require('./routes/offers');
const contractRoutes = require('./routes/contracts');
const supplierRoutes = require('./routes/supplier');
const brokerRoutes = require('./routes/broker');
const adminRoutes = require('./routes/admin');
const invitationRoutes = require('./routes/invitations');

// Initialize express app
const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// Add request logging middleware (temporarily disabled for debugging)
// app.use(requestLogger);

// Routes
app.use('/auth', authRoutes);
app.use('/api', apiRoutes);
app.use('/api/requests', energyRequestRoutes);
app.use('/api/users', userRoutes);
app.use('/api/invoices', invoiceRoutes);
app.use('/api/dashboard', dashboardRoutes);
app.use('/api/logs', logsRoutes);
app.use('/api/offers', offerRoutes);
app.use('/api/contracts', contractRoutes);
app.use('/api/supplier', supplierRoutes);
app.use('/api/broker', brokerRoutes);
app.use('/api/admin', adminRoutes);
app.use('/api/invitations', invitationRoutes);
app.use('/api', healthRoutes);

// Root route
app.get('/', (req, res) => {
  res.send('Backend API is running');
});

// All file upload functionality is handled by the invoice controller and routes
// No need for duplicate S3 upload logic in server.js

// Start server
app.listen(PORT, () => {
  console.log(`✅ Server running at http://localhost:${PORT}`);
  console.log('📋 Available endpoints:');
  console.log('  POST /auth/register - User registration');
  console.log('  POST /auth/login - User login');
  console.log('  PUT  /api/users/update-type - Update user type');
  console.log('  GET  /api/dashboard/stats - Dashboard statistics');
  console.log('  POST /api/invoices/upload - Upload invoice (S3 + MongoDB + Textract)');
  console.log('  GET  /api/offers/user - Get user offers');
  console.log('  POST /api/offers/:id/accept - Accept offer');
  console.log('  GET  /api/contracts/user - Get user contracts');
  console.log('  GET  /api/supplier/dashboard/stats - Get supplier dashboard stats');
  console.log('  GET  /api/broker/dashboard/stats - Get broker dashboard stats');
  console.log('  GET  /api/supplier/offers/active - Get supplier active offers');
  console.log('  GET  /api/broker/clients/active - Get broker active clients');
  console.log('  GET  /api/health - Health check');
});
