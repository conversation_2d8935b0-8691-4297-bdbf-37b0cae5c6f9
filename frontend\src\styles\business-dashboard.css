.business-dashboard-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.business-dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.business-dashboard-header h1 {
  font-size: 24px;
  font-weight: 600;
  color: #000;
  margin: 0;
}

.business-dashboard-content {
  display: grid;
  grid-template-columns: 1fr;
  gap: 20px;
}

/* Business Overview Card */
.business-overview-card {
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid #000;
  overflow: hidden;
}

.business-overview-header {
  padding: 15px 20px;
  border-bottom: 1px solid #eaeaea;
}

.business-overview-header h2 {
  font-size: 18px;
  font-weight: 600;
  color: #000;
  margin: 0;
}

.business-overview-content {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.business-info {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
}

.business-info-item {
  display: flex;
  flex-direction: column;
}

.business-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15px;
  margin-top: 10px;
}

.business-stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 8px;
  border: 1px solid #eaeaea;
}

.info-label,
.stat-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 5px;
}

.info-value {
  font-size: 16px;
  font-weight: 500;
  color: #000;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  color: #000;
}

.stat-value.highlight {
  color: #000;
}

/* Business Cards */
.business-card {
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid #000;
  overflow: hidden;
}

.business-card-header {
  padding: 15px 20px;
  border-bottom: 1px solid #eaeaea;
}

.business-card-header h2 {
  font-size: 18px;
  font-weight: 600;
  color: #000;
  margin: 0;
}

.business-card-content {
  padding: 20px;
}

/* Energy Consumption */
.energy-consumption {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15px;
  margin-bottom: 20px;
}

.consumption-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 8px;
  border: 1px solid #eaeaea;
}

.consumption-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 5px;
}

.consumption-value {
  font-size: 18px;
  font-weight: 600;
  color: #000;
}

.consumption-change {
  font-size: 18px;
  font-weight: 600;
}

.consumption-change.positive {
  color: #28a745;
}

.consumption-change.negative {
  color: #dc3545;
}

.consumption-chart,
.cost-chart {
  height: 200px;
  background-color: #f9f9f9;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #eaeaea;
}

.chart-placeholder {
  color: #666;
  font-style: italic;
}

/* Cost Breakdown */
.cost-breakdown {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15px;
  margin-bottom: 20px;
}

.cost-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 8px;
  border: 1px solid #eaeaea;
}

.cost-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 5px;
}

.cost-value {
  font-size: 18px;
  font-weight: 600;
  color: #000;
}

/* Recent Activity */
.activity-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.activity-item {
  display: flex;
  align-items: center;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 8px;
  border: 1px solid #eaeaea;
}

.activity-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #000;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  flex-shrink: 0;
}

.activity-details {
  flex: 1;
}

.activity-title {
  font-size: 16px;
  font-weight: 500;
  color: #000;
  margin-bottom: 5px;
}

.activity-date {
  font-size: 14px;
  color: #666;
}

.activity-status {
  margin-left: 15px;
}

.status-badge {
  padding: 5px 10px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
}

.status-completed {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.status-pending {
  background-color: #fff8e1;
  color: #f57f17;
}

.status-paid {
  background-color: #e3f2fd;
  color: #1565c0;
}

/* Responsive styles */
@media (max-width: 768px) {
  .business-info,
  .business-stats,
  .energy-consumption,
  .cost-breakdown {
    grid-template-columns: 1fr;
  }
  
  .activity-item {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .activity-icon {
    margin-bottom: 10px;
  }
  
  .activity-status {
    margin-left: 0;
    margin-top: 10px;
    align-self: flex-start;
  }
}
