const mongoose = require('mongoose');
const User = require('../models/User');
require('dotenv').config();

const checkAdminUser = async () => {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('Connected to MongoDB');

    // Check if admin user exists by email
    const adminUserByEmail = await User.findOne({ email: '<EMAIL>' });
    
    if (adminUserByEmail) {
      console.log('Admin user found by email:');
      console.log('ID:', adminUserByEmail._id);
      console.log('Email:', adminUserByEmail.email);
      console.log('User Type:', adminUserByEmail.userType);
      console.log('Status:', adminUserByEmail.status);
      console.log('Verification Status:', adminUserByEmail.verificationStatus);
      console.log('Profile Complete:', adminUserByEmail.profileComplete);
      console.log('Cognito ID:', adminUserByEmail.cognitoId);
      console.log('Created At:', adminUserByEmail.createdAt);
      console.log('Updated At:', adminUserByEmail.updatedAt);
    } else {
      console.log('Admin user not found by email');
    }

    // Check if admin user exists by cognitoId
    const adminUserByCognito = await User.findOne({ cognitoId: 'c18930be-f051-703f-ce81-f884516da30a' });
    
    if (adminUserByCognito) {
      console.log('\nAdmin user found by Cognito ID:');
      console.log('ID:', adminUserByCognito._id);
      console.log('Email:', adminUserByCognito.email);
      console.log('User Type:', adminUserByCognito.userType);
      console.log('Status:', adminUserByCognito.status);
      console.log('Verification Status:', adminUserByCognito.verificationStatus);
      console.log('Profile Complete:', adminUserByCognito.profileComplete);
      console.log('Cognito ID:', adminUserByCognito.cognitoId);
    } else {
      console.log('\nAdmin user not found by Cognito ID');
    }

    // Check all admin users
    const allAdminUsers = await User.find({ userType: 'Admin' });
    console.log('\nAll Admin users found:', allAdminUsers.length);
    allAdminUsers.forEach((user, index) => {
      console.log(`Admin ${index + 1}:`, {
        id: user._id,
        email: user.email,
        cognitoId: user.cognitoId,
        status: user.status,
        verificationStatus: user.verificationStatus,
        profileComplete: user.profileComplete
      });
    });

    // Disconnect
    await mongoose.disconnect();
    console.log('\nDisconnected from MongoDB');
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
};

checkAdminUser();
