const mongoose = require('mongoose');

const analyticsSchema = new mongoose.Schema({
  type: {
    type: String,
    enum: [
      'UserRegistration', 
      'RequestCreation', 
      'OfferGeneration', 
      'ContractSigning',
      'UserActivity',
      'SupplierPerformance',
      'BrokerPerformance',
      'ReferralPerformance',
      'SystemPerformance'
    ],
    required: true
  },
  period: {
    type: String,
    enum: ['Daily', 'Weekly', 'Monthly', 'Quarterly', 'Yearly', 'Custom'],
    required: true
  },
  startDate: {
    type: Date,
    required: true
  },
  endDate: {
    type: Date,
    required: true
  },
  metrics: {
    type: mongoose.Schema.Types.Mixed,
    required: true
  },
  segments: {
    type: mongoose.Schema.Types.Mixed
  },
  generatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  isAutoGenerated: {
    type: Boolean,
    default: true
  },
  reportUrl: {
    type: String
  },
  status: {
    type: String,
    enum: ['Pending', 'Completed', 'Failed'],
    default: 'Completed'
  }
}, {
  timestamps: true
});

// Create indexes
analyticsSchema.index({ type: 1 });
analyticsSchema.index({ period: 1 });
analyticsSchema.index({ startDate: 1, endDate: 1 });

const Analytics = mongoose.model('Analytics', analyticsSchema);

module.exports = Analytics;
