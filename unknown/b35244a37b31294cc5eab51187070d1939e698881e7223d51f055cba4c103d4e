const mongoose = require('mongoose');

const supplierProfileSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    unique: true
  },
  companyName: {
    type: String,
    required: true,
    trim: true
  },
  companyAddress: {
    street: {
      type: String,
      trim: true
    },
    city: {
      type: String,
      trim: true
    },
    postalCode: {
      type: String,
      trim: true
    },
    country: {
      type: String,
      default: 'France',
      trim: true
    }
  },
  licenseNumber: {
    type: String,
    trim: true
  },
  energyTypesProvided: [{
    type: String,
    enum: ['Electricity', 'Gas', 'Both']
  }],
  serviceAreas: [{
    type: String,
    trim: true
  }],
  contractTypes: [{
    type: String,
    enum: ['Fixed Rate', 'Variable Rate', 'Indexed', 'Green Energy']
  }],
  verificationDocuments: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Document'
  }],
  verificationStatus: {
    type: String,
    enum: ['Pending', 'Verified', 'Rejected'],
    default: 'Pending'
  },
  verificationNotes: {
    type: String
  },
  offerTemplates: [{
    name: String,
    energyType: {
      type: String,
      enum: ['Electricity', 'Gas', 'Both']
    },
    rateType: {
      type: String,
      enum: ['Fixed', 'Variable', 'Indexed', 'Green']
    },
    baseRate: Number,
    durationMonths: Number,
    additionalBenefits: [String],
    isActive: {
      type: Boolean,
      default: true
    }
  }]
}, {
  timestamps: true
});

// Create indexes
supplierProfileSchema.index({ userId: 1 });
supplierProfileSchema.index({ verificationStatus: 1 });
supplierProfileSchema.index({ companyName: 1 });
supplierProfileSchema.index({ 'energyTypesProvided': 1 });

const SupplierProfile = mongoose.model('SupplierProfile', supplierProfileSchema);

module.exports = SupplierProfile;
