const AWS = require('aws-sdk');
const dotenv = require('dotenv');
const path = require('path');

// Load environment variables
const cliEnv = process.argv[2];
process.env.NODE_ENV = cliEnv || process.env.NODE_ENV || 'local';

const envFile = `.env.${process.env.NODE_ENV}`;

dotenv.config({
  path: path.resolve(__dirname, `../${envFile}`),
  override: false,
});

console.log(`Loaded environment: ${envFile}`);

// Configure AWS
AWS.config.update({
  region: process.env.AWS_REGION
});

// Create Cognito Identity Service Provider
const cognitoIdentityServiceProvider = new AWS.CognitoIdentityServiceProvider();

// Cognito User Pool configuration
const userPoolConfig = {
  UserPoolId: process.env.COGNITO_USER_POOL_ID,
  ClientId: process.env.COGNITO_CLIENT_ID
};

module.exports = {
  cognitoIdentityServiceProvider,
  userPoolConfig
};
