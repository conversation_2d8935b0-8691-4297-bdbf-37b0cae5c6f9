const mongoose = require('mongoose');

const tariffRowSchema = new mongoose.Schema({
  region: {
    type: String,
    trim: true
  },
  rateType: {
    type: String,
    enum: ['Fixed', 'Variable', 'Indexed', 'Green', 'Peak/Off-Peak', 'Tiered'],
    required: true
  },
  baseRate: {
    type: Number,
    min: 0
  },
  standingCharge: {
    type: Number,
    min: 0
  },
  peakRate: {
    type: Number,
    min: 0
  },
  offPeakRate: {
    type: Number,
    min: 0
  },
  unitRate: {
    type: Number,
    min: 0
  },
  tier1Rate: {
    type: Number,
    min: 0
  },
  tier2Rate: {
    type: Number,
    min: 0
  },
  tier3Rate: {
    type: Number,
    min: 0
  },
  tier1Threshold: {
    type: Number,
    min: 0
  },
  tier2Threshold: {
    type: Number,
    min: 0
  },
  greenEnergyPercentage: {
    type: Number,
    min: 0,
    max: 100
  },
  contractDuration: {
    type: Number,
    min: 1,
    max: 60 // months
  },
  additionalFees: {
    type: Number,
    default: 0
  },
  discounts: {
    type: Number,
    default: 0
  },
  conditions: {
    type: String,
    trim: true
  },
  notes: {
    type: String,
    trim: true
  }
}, { _id: false });

const tariffGridSchema = new mongoose.Schema({
  supplierId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  energyType: {
    type: String,
    enum: ['Electricity', 'Gas', 'Both'],
    required: true
  },
  validFrom: {
    type: Date,
    required: true
  },
  validUntil: {
    type: Date,
    required: true
  },
  description: {
    type: String,
    trim: true
  },
  tariffData: [tariffRowSchema],
  uploadedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  fileName: {
    type: String,
    required: true
  },
  fileSize: {
    type: Number,
    required: true
  },
  status: {
    type: String,
    enum: ['Active', 'Inactive', 'Expired'],
    default: 'Active'
  },
  statusNotes: {
    type: String,
    trim: true
  },
  statusUpdatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  statusUpdatedAt: {
    type: Date
  },
  metadata: {
    totalRecords: {
      type: Number,
      default: 0
    },
    regions: [{
      type: String,
      trim: true
    }],
    rateTypes: [{
      type: String,
      enum: ['Fixed', 'Variable', 'Indexed', 'Green', 'Peak/Off-Peak', 'Tiered']
    }],
    priceRange: {
      min: Number,
      max: Number,
      average: Number
    }
  },
  apiIntegration: {
    isApiEnabled: {
      type: Boolean,
      default: false
    },
    apiEndpoint: {
      type: String,
      trim: true
    },
    apiKey: {
      type: String,
      trim: true
    },
    lastApiSync: {
      type: Date
    },
    apiSyncStatus: {
      type: String,
      enum: ['Success', 'Failed', 'Pending', 'Not_Configured'],
      default: 'Not_Configured'
    },
    apiSyncErrors: [{
      timestamp: Date,
      error: String,
      details: mongoose.Schema.Types.Mixed
    }]
  },
  usage: {
    timesUsed: {
      type: Number,
      default: 0
    },
    lastUsed: {
      type: Date
    },
    quotesGenerated: {
      type: Number,
      default: 0
    },
    contractsSigned: {
      type: Number,
      default: 0
    }
  }
}, {
  timestamps: true
});

// Pre-save middleware to calculate metadata
tariffGridSchema.pre('save', function(next) {
  if (this.isModified('tariffData')) {
    this.metadata.totalRecords = this.tariffData.length;
    
    // Extract unique regions
    this.metadata.regions = [...new Set(this.tariffData
      .map(row => row.region)
      .filter(region => region))];
    
    // Extract unique rate types
    this.metadata.rateTypes = [...new Set(this.tariffData
      .map(row => row.rateType)
      .filter(rateType => rateType))];
    
    // Calculate price range
    const rates = this.tariffData
      .map(row => row.baseRate || row.unitRate || 0)
      .filter(rate => rate > 0);
    
    if (rates.length > 0) {
      this.metadata.priceRange = {
        min: Math.min(...rates),
        max: Math.max(...rates),
        average: rates.reduce((sum, rate) => sum + rate, 0) / rates.length
      };
    }
  }
  next();
});

// Instance methods
tariffGridSchema.methods.isValid = function() {
  const now = new Date();
  return this.status === 'Active' && 
         this.validFrom <= now && 
         this.validUntil >= now;
};

tariffGridSchema.methods.getTariffForRegion = function(region) {
  return this.tariffData.filter(row => 
    !region || row.region === region || !row.region
  );
};

tariffGridSchema.methods.incrementUsage = function() {
  this.usage.timesUsed += 1;
  this.usage.lastUsed = new Date();
  return this.save();
};

// Static methods
tariffGridSchema.statics.getActiveTariffs = function(supplierId, energyType) {
  const now = new Date();
  return this.find({
    supplierId,
    energyType,
    status: 'Active',
    validFrom: { $lte: now },
    validUntil: { $gte: now }
  }).populate('supplierId', 'firstName lastName email');
};

tariffGridSchema.statics.getExpiredTariffs = function() {
  const now = new Date();
  return this.find({
    status: 'Active',
    validUntil: { $lt: now }
  });
};

// Create indexes
tariffGridSchema.index({ supplierId: 1, energyType: 1 });
tariffGridSchema.index({ validFrom: 1, validUntil: 1 });
tariffGridSchema.index({ status: 1 });
tariffGridSchema.index({ 'tariffData.region': 1 });
tariffGridSchema.index({ 'tariffData.rateType': 1 });

const TariffGrid = mongoose.model('TariffGrid', tariffGridSchema);

module.exports = TariffGrid;
