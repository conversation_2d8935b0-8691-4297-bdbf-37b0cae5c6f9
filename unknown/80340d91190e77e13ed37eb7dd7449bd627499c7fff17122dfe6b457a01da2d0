const mongoose = require('mongoose');

const documentSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  type: {
    type: String,
    enum: [
      'EnergyBill', 
      'IdentityProof', 
      'AddressProof', 
      'BusinessRegistration', 
      'Contract', 
      'LicenseDocument', 
      'Other'
    ],
    required: true
  },
  fileUrl: {
    type: String,
    required: true
  },
  fileName: {
    type: String,
    required: true
  },
  fileSize: {
    type: Number
  },
  mimeType: {
    type: String
  },
  status: {
    type: String,
    enum: ['Pending', 'Verified', 'Rejected'],
    default: 'Pending'
  },
  verificationNotes: {
    type: String
  },
  metadata: {
    type: mongoose.Schema.Types.Mixed
  },
  uploadDate: {
    type: Date,
    default: Date.now
  },
  verifiedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  verificationDate: {
    type: Date
  }
}, {
  timestamps: true
});

// Create indexes
documentSchema.index({ userId: 1 });
documentSchema.index({ type: 1 });
documentSchema.index({ status: 1 });

const Document = mongoose.model('Document', documentSchema);

module.exports = Document;
