const mongoose = require('mongoose');

const contractSchema = new mongoose.Schema({
  offerId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Offer',
    required: true
  },
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  supplierId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  brokerId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  referrerId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  contractDetails: {
    contractNumber: String,
    energyType: {
      type: String,
      enum: ['Electricity', 'Gas', 'Both'],
      required: true
    },
    rateType: {
      type: String,
      enum: ['Fixed', 'Variable', 'Indexed', 'Green'],
      required: true
    },
    rate: Number,
    standingCharge: Number,
    estimatedAnnualCost: Number,
    currency: {
      type: String,
      default: 'EUR'
    },
    specialTerms: String
  },
  startDate: {
    type: Date,
    required: true
  },
  endDate: {
    type: Date,
    required: true
  },
  status: {
    type: String,
    enum: ['Pending', 'Active', 'Completed', 'Cancelled', 'Renewed'],
    default: 'Pending'
  },
  documentUrl: {
    type: String
  },
  docusignEnvelopeId: {
    type: String
  },
  docusignStatus: {
    type: String,
    enum: ['created', 'sent', 'delivered', 'signed', 'completed', 'declined', 'voided'],
    default: 'created'
  },
  signatureStatus: {
    userSigned: {
      type: Boolean,
      default: false
    },
    userSignedDate: Date,
    supplierSigned: {
      type: Boolean,
      default: false
    },
    supplierSignedDate: Date,
    completed: {
      type: Boolean,
      default: false
    },
    completedDate: Date
  },
  paymentDetails: {
    initialPayment: Number,
    paymentFrequency: {
      type: String,
      enum: ['Monthly', 'Quarterly', 'Annually'],
      default: 'Monthly'
    },
    paymentMethod: {
      type: String,
      enum: ['Direct Debit', 'Credit Card', 'Bank Transfer', 'Other'],
      default: 'Direct Debit'
    },
    paymentStatus: {
      type: String,
      enum: ['Pending', 'Active', 'Failed', 'Cancelled'],
      default: 'Pending'
    }
  },
  monthlyValue: {
    type: Number,
    default: 0
  },
  commissions: {
    brokerCommission: Number,
    referrerCommission: Number,
    commissionPaid: {
      type: Boolean,
      default: false
    },
    commissionPaidDate: Date
  }
}, {
  timestamps: true
});

// Create indexes
contractSchema.index({ userId: 1 });
contractSchema.index({ supplierId: 1 });
contractSchema.index({ brokerId: 1 });
contractSchema.index({ referrerId: 1 });
contractSchema.index({ offerId: 1 });
contractSchema.index({ status: 1 });
contractSchema.index({ startDate: 1 });
contractSchema.index({ endDate: 1 });

const Contract = mongoose.model('Contract', contractSchema);

module.exports = Contract;
