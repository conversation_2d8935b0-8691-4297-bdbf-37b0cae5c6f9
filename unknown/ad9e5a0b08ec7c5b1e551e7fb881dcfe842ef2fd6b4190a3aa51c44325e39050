const express = require('express');
const router = express.Router();
const QuoteRequest = require('../../models/QuoteRequest');
const EnergyRequest = require('../../models/EnergyRequest');
const User = require('../../models/User');
const UserActivity = require('../../models/UserActivity');
const logger = require('../../utils/logger');
const { authMiddleware } = require('../../middleware/auth');

// Apply auth middleware to all admin routes
router.use(authMiddleware);

// Middleware to check if user is admin
const requireAdmin = async (req, res, next) => {
  try {
    const userEmail = req.user?.email;
    if (!userEmail) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    const user = await User.findOne({ email: userEmail });
    if (!user || user.userType !== 'Admin') {
      return res.status(403).json({
        success: false,
        message: 'Admin access required'
      });
    }

    req.adminUser = user;
    next();
  } catch (error) {
    logger.error('Admin middleware error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// Get all quote requests with filtering and pagination
router.get('/', requireAdmin, async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      status,
      priority,
      energyType,
      search
    } = req.query;

    const filter = {};
    
    if (status && status !== 'all') {
      filter.status = status;
    }
    
    if (priority && priority !== 'all') {
      filter.priority = priority;
    }
    
    if (energyType && energyType !== 'all') {
      filter.energyType = energyType;
    }
    
    if (search) {
      // Search in user details
      const users = await User.find({
        $or: [
          { email: { $regex: search, $options: 'i' } },
          { firstName: { $regex: search, $options: 'i' } },
          { lastName: { $regex: search, $options: 'i' } }
        ]
      }).select('_id');
      
      filter.userId = { $in: users.map(u => u._id) };
    }

    const result = await QuoteRequest.getFilteredRequests(filter, {
      page,
      limit,
      status,
      priority,
      energyType
    });

    res.json({
      success: true,
      data: result
    });

  } catch (error) {
    logger.error('Error fetching quote requests:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch quote requests'
    });
  }
});

// Get specific quote request details
router.get('/:requestId', requireAdmin, async (req, res) => {
  try {
    const { requestId } = req.params;

    const quoteRequest = await QuoteRequest.findById(requestId)
      .populate('userId', 'firstName lastName email userType phone')
      .populate('brokerId', 'firstName lastName email')
      .populate('quotes.supplierId', 'firstName lastName email')
      .populate('adminActions.adminId', 'firstName lastName email');

    if (!quoteRequest) {
      return res.status(404).json({
        success: false,
        message: 'Quote request not found'
      });
    }

    // Get related energy request
    const energyRequest = await EnergyRequest.findById(quoteRequest.requestId);

    res.json({
      success: true,
      data: {
        quoteRequest,
        energyRequest
      }
    });

  } catch (error) {
    logger.error('Error fetching quote request details:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch quote request details'
    });
  }
});

// Update quote request status
router.patch('/:requestId/status', requireAdmin, async (req, res) => {
  try {
    const { requestId } = req.params;
    const { status, adminNotes } = req.body;

    const validStatuses = ['Pending', 'Processing', 'Quotes_Available', 'Sent_to_Client', 'Approved', 'Rejected', 'Expired'];
    if (!validStatuses.includes(status)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid status value'
      });
    }

    const quoteRequest = await QuoteRequest.findById(requestId);
    if (!quoteRequest) {
      return res.status(404).json({
        success: false,
        message: 'Quote request not found'
      });
    }

    const oldStatus = quoteRequest.status;
    quoteRequest.status = status;
    
    if (adminNotes) {
      quoteRequest.adminNotes = adminNotes;
    }

    // Add admin action
    await quoteRequest.addAdminAction(
      req.adminUser._id,
      'Status_Changed',
      `Status changed from ${oldStatus} to ${status}`,
      { oldStatus, newStatus: status, adminNotes }
    );

    // Log user activity
    await UserActivity.logActivity({
      userId: quoteRequest.userId,
      activityType: 'RequestStatusUpdate',
      description: `Quote request status updated to ${status} by admin`,
      details: {
        requestId: quoteRequest._id,
        oldStatus,
        newStatus: status,
        adminId: req.adminUser._id
      },
      severity: 'Normal',
      isSystemGenerated: true
    });

    await quoteRequest.save();

    logger.info(`Admin ${req.adminUser.email} updated quote request ${requestId} status to ${status}`);

    res.json({
      success: true,
      message: 'Quote request status updated successfully',
      data: quoteRequest
    });

  } catch (error) {
    logger.error('Error updating quote request status:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update quote request status'
    });
  }
});

// Add or modify quote in a request
router.post('/:requestId/quotes', requireAdmin, async (req, res) => {
  try {
    const { requestId } = req.params;
    const quoteData = req.body;

    const quoteRequest = await QuoteRequest.findById(requestId);
    if (!quoteRequest) {
      return res.status(404).json({
        success: false,
        message: 'Quote request not found'
      });
    }

    // Validate supplier exists
    const supplier = await User.findById(quoteData.supplierId);
    if (!supplier || supplier.userType !== 'Supplier') {
      return res.status(400).json({
        success: false,
        message: 'Invalid supplier'
      });
    }

    // Add the new quote
    quoteRequest.quotes.push({
      ...quoteData,
      createdAt: new Date()
    });

    // Update comparison data
    quoteRequest.comparisonData.totalQuotes = quoteRequest.quotes.length;
    
    // Calculate best savings
    const activeSavings = quoteRequest.quotes
      .filter(q => q.status === 'Active' && q.estimatedSavings)
      .map(q => q.estimatedSavings);
    
    if (activeSavings.length > 0) {
      const bestSavings = activeSavings.reduce((best, current) => 
        current.amount > best.amount ? current : best
      );
      quoteRequest.comparisonData.bestSavings = bestSavings;
    }

    // Add admin action
    await quoteRequest.addAdminAction(
      req.adminUser._id,
      'Quote_Added',
      `New quote added from supplier ${supplier.firstName} ${supplier.lastName}`,
      { supplierId: supplier._id, quoteData }
    );

    await quoteRequest.save();

    logger.info(`Admin ${req.adminUser.email} added quote to request ${requestId}`);

    res.json({
      success: true,
      message: 'Quote added successfully',
      data: quoteRequest
    });

  } catch (error) {
    logger.error('Error adding quote:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to add quote'
    });
  }
});

// Remove quote from request
router.delete('/:requestId/quotes/:quoteIndex', requireAdmin, async (req, res) => {
  try {
    const { requestId, quoteIndex } = req.params;
    const { reason } = req.body;

    const quoteRequest = await QuoteRequest.findById(requestId);
    if (!quoteRequest) {
      return res.status(404).json({
        success: false,
        message: 'Quote request not found'
      });
    }

    const quoteIndexNum = parseInt(quoteIndex);
    if (quoteIndexNum < 0 || quoteIndexNum >= quoteRequest.quotes.length) {
      return res.status(400).json({
        success: false,
        message: 'Invalid quote index'
      });
    }

    const removedQuote = quoteRequest.quotes[quoteIndexNum];
    quoteRequest.quotes[quoteIndexNum].status = 'Removed';
    quoteRequest.quotes[quoteIndexNum].adminNotes = reason || 'Removed by admin';

    // Add admin action
    await quoteRequest.addAdminAction(
      req.adminUser._id,
      'Quote_Removed',
      `Quote removed: ${reason || 'No reason provided'}`,
      { quoteIndex: quoteIndexNum, removedQuote, reason }
    );

    await quoteRequest.save();

    logger.info(`Admin ${req.adminUser.email} removed quote from request ${requestId}`);

    res.json({
      success: true,
      message: 'Quote removed successfully',
      data: quoteRequest
    });

  } catch (error) {
    logger.error('Error removing quote:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to remove quote'
    });
  }
});

// Get quote statistics
router.get('/stats/overview', requireAdmin, async (req, res) => {
  try {
    const [
      totalRequests,
      pendingRequests,
      processingRequests,
      quotesAvailable,
      approvedRequests,
      expiredRequests
    ] = await Promise.all([
      QuoteRequest.countDocuments(),
      QuoteRequest.countDocuments({ status: 'Pending' }),
      QuoteRequest.countDocuments({ status: 'Processing' }),
      QuoteRequest.countDocuments({ status: 'Quotes_Available' }),
      QuoteRequest.countDocuments({ status: 'Approved' }),
      QuoteRequest.countDocuments({ status: 'Expired' })
    ]);

    // Get recent requests
    const recentRequests = await QuoteRequest.find()
      .populate('userId', 'firstName lastName email')
      .sort({ createdAt: -1 })
      .limit(5)
      .select('userId energyType status priority createdAt quotes');

    const stats = {
      totalRequests,
      pendingRequests,
      processingRequests,
      quotesAvailable,
      approvedRequests,
      expiredRequests,
      recentRequests
    };

    res.json({
      success: true,
      data: stats
    });

  } catch (error) {
    logger.error('Error fetching quote statistics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch quote statistics'
    });
  }
});

module.exports = router;
