const dotenv = require('dotenv');
const path = require('path');

// Load environment variables
dotenv.config({
  path: path.resolve(__dirname, '.env.uat'),
  override: false,
});

const express = require('express');
const mongoose = require('mongoose');
const User = require('./models/User');
const UserActivity = require('./models/UserActivity');
const logger = require('./utils/logger');

async function testStatusCheckEndpoint() {
  console.log('🧪 Testing Status Check Endpoint Logic\n');

  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    // Test data
    const testEmail = '<EMAIL>';
    const testCognitoId = 'test-cognito-id';

    console.log(`📧 Testing with email: ${testEmail}`);

    // Get user from database
    const user = await User.findOne({ email: testEmail });

    if (!user) {
      console.log('❌ User not found in database');
      return;
    }

    console.log(`👤 User found: ${user.email}`);
    console.log(`🔍 Current status: ${user.status}`);

    // Test the status check logic
    const userStatus = user.status;
    const isRestricted = ['Suspended', 'Inactive', 'Pending'].includes(userStatus);

    console.log(`🚫 Is restricted: ${isRestricted ? 'YES' : 'NO'}`);

    if (isRestricted) {
      console.log('\n✅ Status check would BLOCK the user');
      
      // Test activity logging
      try {
        await UserActivity.logActivity({
          userId: user._id,
          activityType: `${user.status}LoginAttempt`,
          description: `User with status '${user.status}' passed Cognito auth but blocked by status check`,
          details: {
            email: testEmail,
            status: user.status,
            cognitoId: testCognitoId,
            attemptedAt: new Date(),
            checkType: 'post-cognito-status-check'
          },
          severity: 'High',
          isSystemGenerated: true
        });
        console.log('✅ Activity logged successfully');
      } catch (activityError) {
        console.log('❌ Activity logging failed:', activityError.message);
      }

      // Test response format
      let message, errorCode;
      switch (user.status) {
        case 'Suspended':
          message = 'Account is suspended. Please contact support for assistance.';
          errorCode = 'ACCOUNT_SUSPENDED';
          break;
        case 'Inactive':
          message = 'Account is inactive. Please contact support to reactivate your account.';
          errorCode = 'ACCOUNT_INACTIVE';
          break;
        case 'Pending':
          message = 'Account is pending approval. Please wait for admin approval or contact support.';
          errorCode = 'ACCOUNT_PENDING';
          break;
      }

      console.log('\n📋 Response would be:');
      console.log(`   Status: 403`);
      console.log(`   Error Code: ${errorCode}`);
      console.log(`   Message: ${message}`);
      console.log(`   User Status: ${user.status}`);

    } else {
      console.log('\n✅ Status check would ALLOW the user');
      console.log('📋 Response would be:');
      console.log(`   Status: 200`);
      console.log(`   Message: User status check passed`);
      console.log(`   User Status: ${user.status}`);
    }

    console.log('\n🔧 Frontend Integration:');
    console.log('1. User authenticates with Cognito ✅');
    console.log('2. Frontend calls /auth/check-user-status ✅');
    console.log('3. Backend checks database status ✅');
    console.log('4. If restricted, returns 403 with error code ✅');
    console.log('5. Frontend handles 403 and redirects to AccountStatus page ✅');

    console.log('\n🧪 Manual Testing Steps:');
    console.log('1. Start the backend server');
    console.log('2. Try to login with suspended user credentials');
    console.log('3. Should be blocked after Cognito auth but before dashboard');
    console.log('4. Should redirect to AccountStatus error page');

  } catch (error) {
    console.error('💥 Test failed:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n✨ Disconnected from MongoDB');
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  testStatusCheckEndpoint()
    .then(() => {
      console.log('\n✨ Test completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Test failed:', error);
      process.exit(1);
    });
}

module.exports = { testStatusCheckEndpoint };
