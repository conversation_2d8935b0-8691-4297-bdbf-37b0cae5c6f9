#!/bin/bash
# Script to build and run the application in development mode

echo "Building and running the application in development mode..."

# Build and start the containers using the development configuration
docker-compose -f docker-compose-dev.yml up --build -d

# Check if the containers are running
if [ $? -eq 0 ]; then
  echo "Development environment is now running!"
  echo "Frontend: http://localhost:8080"
  echo "Backend: http://localhost:3000"
  echo "MongoDB: mongodb://localhost:27017"
else
  echo "Failed to start the development environment."
  exit 1
fi
