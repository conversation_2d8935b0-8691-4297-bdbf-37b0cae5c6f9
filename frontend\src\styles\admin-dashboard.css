/* Admin Dashboard - Black & White Theme */
.admin-dashboard {
  padding: 32px;
  width: 100%;
  min-height: calc(100vh - 80px);
  background: #ffffff;
  position: relative;
}

.admin-dashboard-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  background: #ffffff;
}

/* Header */
.admin-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40px;
  background: #ffffff;
  padding: 32px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
  position: relative;
  z-index: 1;
}

.admin-header-content h1 {
  font-size: 32px;
  font-weight: 800;
  margin-bottom: 8px;
  color: #000000;
}

.admin-header-content p {
  font-size: 16px;
  color: #333333;
  margin: 0;
  font-weight: 500;
}

.admin-header-actions {
  display: flex;
  gap: 16px;
}

.btn-admin-action {
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  border: 1px solid #e0e0e0;
  background: #ffffff;
  color: #333333;
}

.btn-admin-action.primary {
  background: #000000;
  color: #ffffff;
  border: 1px solid #000000;
}

.btn-admin-action:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  background: #f5f5f5;
}

.btn-admin-action.primary:hover {
  background: #333333;
}

/* Stats Grid */
.admin-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 40px;
  position: relative;
  z-index: 1;
}

.stat-card {
  background: #ffffff;
  border-radius: 8px;
  padding: 28px;
  display: flex;
  align-items: center;
  gap: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  border-color: #000000;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: #ffffff;
  flex-shrink: 0;
  background: #000000;
}

.stat-card:hover .stat-icon {
  background: #333333;
}

.stat-content {
  flex: 1;
}

.stat-content h3 {
  font-size: 28px;
  font-weight: 800;
  margin: 0 0 4px 0;
  color: #000000;
}

.stat-content p {
  font-size: 14px;
  color: #333333;
  margin: 0;
  font-weight: 600;
}

.stat-action {
  color: #666666;
  font-size: 16px;
  transition: all 0.3s ease;
}

.stat-card:hover .stat-action {
  color: #000000;
  transform: translateX(4px);
}

/* Content Grid */
.admin-content-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 32px;
  margin-bottom: 40px;
  position: relative;
  z-index: 1;
}

.admin-section {
  background: #ffffff;
  border-radius: 8px;
  padding: 28px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e0e0e0;
}

.section-header h2 {
  font-size: 20px;
  font-weight: 700;
  color: #000000;
  margin: 0;
}

.btn-view-all {
  background: #ffffff;
  border: 1px solid #e0e0e0;
  color: #333333;
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 13px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-view-all:hover {
  background: #f5f5f5;
  color: #000000;
  border-color: #000000;
}

/* Activities */
.activities-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  border-radius: 8px;
  background: #f5f5f5;
  border-left: 4px solid transparent;
  transition: all 0.3s ease;
}

.activity-item:hover {
  background: #e0e0e0;
  transform: translateX(4px);
}

.activity-item.info {
  border-left-color: #000000;
}

.activity-item.success {
  border-left-color: #000000;
}

.activity-item.warning {
  border-left-color: #666666;
}

.activity-item.error {
  border-left-color: #333333;
}

.activity-icon {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  flex-shrink: 0;
  background: #000000;
  color: #ffffff;
}

.activity-content p {
  font-size: 14px;
  color: #000000;
  margin: 0 0 4px 0;
  font-weight: 500;
}

.activity-time {
  font-size: 12px;
  color: #666666;
  font-weight: 500;
}

/* System Status */
.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 600;
  color: #000000;
}

.status-indicator i {
  font-size: 8px;
}

.system-stats {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.system-stat {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #e0e0e0;
}

.system-stat:last-child {
  border-bottom: none;
}

.stat-label {
  font-size: 14px;
  color: #333333;
  font-weight: 500;
}

.stat-value {
  font-size: 14px;
  color: #000000;
  font-weight: 700;
}

.stat-value.warning {
  color: #666666;
}

/* Quick Actions */
.admin-quick-actions {
  position: relative;
  z-index: 1;
}

.admin-quick-actions h2 {
  font-size: 24px;
  font-weight: 700;
  color: #000000;
  margin-bottom: 24px;
}

.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.quick-action-btn {
  background: #ffffff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 24px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.quick-action-btn:hover {
  transform: translateY(-4px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  background: #f5f5f5;
  border-color: #000000;
}

.quick-action-btn i {
  font-size: 24px;
  color: #333333;
  transition: all 0.3s ease;
}

.quick-action-btn:hover i {
  color: #000000;
  transform: scale(1.1);
}

.quick-action-btn span {
  font-size: 14px;
  font-weight: 600;
  color: #333333;
}

.quick-action-btn:hover span {
  color: #000000;
}

/* Responsive */
@media (max-width: 768px) {
  .admin-dashboard {
    padding: 20px 16px;
  }

  .admin-header {
    flex-direction: column;
    gap: 20px;
    text-align: center;
    padding: 24px 20px;
  }

  .admin-header-actions {
    width: 100%;
    justify-content: center;
  }

  .admin-stats-grid {
    grid-template-columns: 1fr;
  }

  .admin-content-grid {
    grid-template-columns: 1fr;
    gap: 24px;
  }

  .quick-actions-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .admin-header-content h1 {
    font-size: 24px;
  }

  .stat-card {
    padding: 20px;
  }

  .admin-section {
    padding: 20px;
  }

  .quick-actions-grid {
    grid-template-columns: 1fr;
  }
}
