import React from 'react';

const IndividualInfoDisplay = ({ individualInfo, onEdit }) => {
  if (!individualInfo) return null;

  // Handle different formats of data (API response vs localStorage)
  let info;

  if (typeof individualInfo === 'string') {
    // Parse the stored JSON if it's a string from localStorage
    info = JSON.parse(individualInfo);
  } else if (individualInfo.data) {
    // Handle API response format
    const userData = individualInfo.data;
    const profileData = userData.profile || {};

    // Combine user data with profile data, giving priority to profile data
    info = {
      ...userData,
      ...profileData,
      // Ensure we preserve nested objects
      address: profileData.address || userData.address || {},
      energyIdentifiers: profileData.energyIdentifiers || userData.energyIdentifiers || {},
      consumptionDetails: profileData.consumptionDetails || userData.consumptionDetails || {}
    };

    console.log('Processed individual info:', info);
  } else {
    // Direct object
    info = individualInfo;
  }

  return (
    <div className="info-display-card enhanced">
      <div className="info-display-header">
        <h2 className="info-display-title">Individual Account Information</h2>
        <button
          className="edit-button"
          onClick={onEdit}
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
            <path d="M12.146.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1 0 .708l-10 10a.5.5 0 0 1-.168.11l-5 2a.5.5 0 0 1-.65-.65l2-5a.5.5 0 0 1 .11-.168l10-10zM11.207 2.5 13.5 4.793 14.793 3.5 12.5 1.207 11.207 2.5zm1.586 3L10.5 3.207 4 9.707V10h.5a.5.5 0 0 1 .5.5v.5h.5a.5.5 0 0 1 .5.5v.5h.293l6.5-6.5zm-9.761 5.175-.106.106-1.528 3.821 3.821-1.528.106-.106A.5.5 0 0 1 5 12.5V12h-.5a.5.5 0 0 1-.5-.5V11h-.5a.5.5 0 0 1-.468-.325z"/>
          </svg>
          Edit
        </button>
      </div>

      <div className="info-display-content">
        <div className="info-display-column">
          <div className="info-display-section">
            <h3 className="info-display-subtitle">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                <path d="M8 8a3 3 0 1 0 0-6 3 3 0 0 0 0 6zm2-3a2 2 0 1 1-4 0 2 2 0 0 1 4 0zm4 8c0 1-1 1-1 1H3s-1 0-1-1 1-4 6-4 6 3 6 4zm-1-.004c-.001-.246-.154-.986-.832-1.664C11.516 10.68 10.289 10 8 10c-2.29 0-3.516.68-4.168 1.332-.678.678-.83 1.418-.832 1.664h10z"/>
              </svg>
              Personal Information
            </h3>

            <div className="info-display-item">
              <div className="info-display-label">Name</div>
              <div className="info-display-value highlight">{info.firstName} {info.lastName}</div>
            </div>

            <div className="info-display-item">
              <div className="info-display-label">Email</div>
              <div className="info-display-value">{info.email}</div>
            </div>

            {(info.phoneNumber || info.phone) && (
              <div className="info-display-item">
                <div className="info-display-label">Phone Number</div>
                <div className="info-display-value">{info.phoneNumber || info.phone}</div>
              </div>
            )}
          </div>
        </div>

        <div className="info-display-column">
          <div className="info-display-section">
            <h3 className="info-display-subtitle">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                <path fillRule="evenodd" d="M8 0c-.69 0-1.843.265-2.928.56-1.11.3-2.229.655-2.887.87a1.54 1.54 0 0 0-1.044 1.262c-.596 4.477.787 7.795 2.465 9.99a11.777 11.777 0 0 0 2.517 2.453c.386.273.744.482 1.048.625.28.132.581.24.829.24s.548-.108.829-.24a7.159 7.159 0 0 0 1.048-.625 11.775 11.775 0 0 0 2.517-2.453c1.678-2.195 3.061-5.513 2.465-9.99a1.541 1.541 0 0 0-1.044-1.263 62.467 62.467 0 0 0-2.887-.87C9.843.266 8.69 0 8 0zm0 5a1.5 1.5 0 0 1 .5 2.915l.385 1.99a.5.5 0 0 1-.491.595h-.788a.5.5 0 0 1-.49-.595l.384-1.99A1.5 1.5 0 0 1 8 5z"/>
              </svg>
              Additional Information
            </h3>

            <div className="info-display-item">
              <div className="info-display-label">Address</div>
              <div className="info-display-value">
                {/* Handle both direct properties and nested address object */}
                {info.streetAddress && <div>{info.streetAddress}</div>}
                {info.address?.street && <div>{info.address.street}</div>}

                {info.city && info.postalCode && (
                  <div>{info.city}, {info.postalCode}</div>
                )}
                {info.address?.city && info.address?.postalCode && (
                  <div>{info.address.city}, {info.address.postalCode}</div>
                )}

                {info.country && <div>{info.country}</div>}
                {info.address?.country && <div>{info.address.country}</div>}
              </div>
            </div>

            <div className="info-display-item">
              <div className="info-display-label">Meter Number</div>
              <div className="info-display-value highlight">
                {/* Handle both direct property and nested energyIdentifiers */}
                {info.meterNumber ||
                 info.energyIdentifiers?.pdl ||
                 info.energyIdentifiers?.prm ||
                 info.energyIdentifiers?.rae ||
                 'Not provided'}
              </div>
              <div className="info-display-hint">PDL/PRM/RAE</div>
            </div>
          </div>
        </div>
      </div>

      <div className="info-display-footer">
        <div className="info-display-badge">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
            <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
            <path d="M10.97 4.97a.235.235 0 0 0-.02.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05z"/>
          </svg>
          Account verified
        </div>
      </div>
    </div>
  );
};

export default IndividualInfoDisplay;
