import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import DashboardLayout from '../../components/DashboardLayout';
import Spinner from '../../components/Spinner';
import { showSuccessMessage, showErrorMessage } from '../../utils/toastNotifications';
import logger from '../../utils/logger';
import { loadCSS, unloadCSS, ADMIN_CSS, CSS_IDS } from '../../utils/cssLoader';
import api from '../../services/api';

const ManageUsers = () => {
  const [loading, setLoading] = useState(true);
  const [users, setUsers] = useState([]);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalUsers: 0,
    hasNextPage: false,
    hasPrevPage: false
  });
  const [filters, setFilters] = useState({
    userType: 'all',
    status: 'all',
    verificationStatus: 'all',
    search: ''
  });
  const [selectedUsers, setSelectedUsers] = useState([]);
  const [selectedUser, setSelectedUser] = useState(null);
  const [showUserDetails, setShowUserDetails] = useState(false);
  const [userDetails, setUserDetails] = useState(null);
  const [userActivities, setUserActivities] = useState([]);
  const [showPasswordReset, setShowPasswordReset] = useState(false);
  const navigate = useNavigate();

  // Dynamically load CSS only for this component
  useEffect(() => {
    loadCSS(ADMIN_CSS.MANAGEMENT, CSS_IDS.ADMIN_MANAGEMENT);

    // Cleanup function to remove CSS when component unmounts
    return () => {
      unloadCSS(CSS_IDS.ADMIN_MANAGEMENT);
    };
  }, []);

  useEffect(() => {
    fetchUsers();
  }, [filters, pagination.currentPage]);

  const fetchUsers = async () => {
    try {
      setLoading(true);
      logger.info('Fetching users with filters:', filters);

      const queryParams = new URLSearchParams({
        page: pagination.currentPage,
        limit: 10,
        ...filters
      });

      const response = await api.get(`/api/admin/users?${queryParams}`);
      setUsers(response.data.data.users);
      setPagination(response.data.data.pagination);

      logger.info('Users fetched successfully');
    } catch (error) {
      logger.error('Error fetching users:', error);
      showErrorMessage('USERS_LOAD_FAILED', 'Failed to load users');
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = (filterName, value) => {
    setFilters(prev => ({
      ...prev,
      [filterName]: value
    }));
    setPagination(prev => ({ ...prev, currentPage: 1 }));
  };

  const handleStatusUpdate = async (userId, newStatus) => {
    try {
      await api.patch(`/api/admin/users/${userId}/status`, { status: newStatus });
      showSuccessMessage('USER_STATUS_UPDATED', 'User status updated successfully');
      fetchUsers(); // Refresh the list
    } catch (error) {
      logger.error('Error updating user status:', error);
      showErrorMessage('STATUS_UPDATE_FAILED', 'Failed to update user status');
    }
  };

  const handlePageChange = (newPage) => {
    setPagination(prev => ({ ...prev, currentPage: newPage }));
  };

  const handleViewUserDetails = async (user) => {
    try {
      setSelectedUser(user);
      setShowUserDetails(true);

      // Fetch detailed user information
      const response = await api.get(`/api/admin/users/${user._id}/details`);
      setUserDetails(response.data.data);

    } catch (error) {
      logger.error('Error fetching user details:', error);
      showErrorMessage('USER_DETAILS_FAILED', 'Failed to load user details');
    }
  };

  const handleViewUserActivities = async (userId) => {
    try {
      const response = await api.get(`/api/admin/users/${userId}/activities?limit=50`);
      setUserActivities(response.data.data.activities);

    } catch (error) {
      logger.error('Error fetching user activities:', error);
      showErrorMessage('USER_ACTIVITIES_FAILED', 'Failed to load user activities');
    }
  };

  const handlePasswordReset = async (userId) => {
    try {
      const response = await api.post(`/api/admin/users/${userId}/reset-password`);

      if (response.data.success) {
        showSuccessMessage('PASSWORD_RESET_SUCCESS', 'Password reset email sent successfully. User will receive a secure reset link.');
        logger.info('Password reset email sent for user ID:', userId);
      } else {
        showErrorMessage('PASSWORD_RESET_FAILED', response.data.message || 'Failed to send password reset email');
      }

      setShowPasswordReset(false);
      setSelectedUser(null);

    } catch (error) {
      logger.error('Error sending password reset email:', error);
      const errorMessage = error.response?.data?.message || 'Failed to send password reset email. Please try again.';
      showErrorMessage('PASSWORD_RESET_FAILED', errorMessage);
      setShowPasswordReset(false);
      setSelectedUser(null);
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getStatusBadgeClass = (status) => {
    switch (status?.toLowerCase()) {
      case 'active': return 'status-badge active';
      case 'inactive': return 'status-badge inactive';
      case 'pending': return 'status-badge pending';
      case 'suspended': return 'status-badge suspended';
      default: return 'status-badge';
    }
  };

  const getUserTypeIcon = (userType) => {
    switch (userType?.toLowerCase()) {
      case 'individual': return 'fas fa-user';
      case 'professional': return 'fas fa-user-tie';
      case 'broker': return 'fas fa-handshake';
      case 'supplier': return 'fas fa-industry';
      case 'admin': return 'fas fa-user-shield';
      default: return 'fas fa-user';
    }
  };

  if (loading) {
    return <Spinner fullScreen={true} message="Loading users..." size="large" />;
  }

  return (
    <DashboardLayout>
      <div className="admin-management-container">
        {/* Header */}
        <div className="management-header">
          <div className="header-content">
            <button 
              className="back-button"
              onClick={() => navigate('/dashboard')}
            >
              <i className="fas fa-arrow-left"></i>
              Back to Dashboard
            </button>
            <h1>Manage Users</h1>
            <p>View and manage all users in the system</p>
          </div>
          <div className="header-stats">
            <div className="stat-item">
              <span className="stat-value">{pagination.totalUsers}</span>
              <span className="stat-label">Total Users</span>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="management-filters">
          <div className="filter-group">
            <label>User Type:</label>
            <select 
              value={filters.userType} 
              onChange={(e) => handleFilterChange('userType', e.target.value)}
            >
              <option value="all">All Types</option>
              <option value="Individual">Individual</option>
              <option value="Professional">Professional</option>
              <option value="Broker">Broker</option>
              <option value="Supplier">Supplier</option>
              <option value="Admin">Admin</option>
            </select>
          </div>

          <div className="filter-group">
            <label>Status:</label>
            <select 
              value={filters.status} 
              onChange={(e) => handleFilterChange('status', e.target.value)}
            >
              <option value="all">All Status</option>
              <option value="Active">Active</option>
              <option value="Inactive">Inactive</option>
              <option value="Pending">Pending</option>
              <option value="Suspended">Suspended</option>
            </select>
          </div>

          <div className="filter-group">
            <label>Verification:</label>
            <select 
              value={filters.verificationStatus} 
              onChange={(e) => handleFilterChange('verificationStatus', e.target.value)}
            >
              <option value="all">All Verification</option>
              <option value="Verified">Verified</option>
              <option value="Pending">Pending</option>
              <option value="Rejected">Rejected</option>
            </select>
          </div>

          <div className="filter-group search-group">
            <label>Search:</label>
            <input
              type="text"
              placeholder="Search by email or name..."
              value={filters.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
            />
          </div>
        </div>

        {/* Users Table */}
        <div className="management-table-container">
          <table className="management-table">
            <thead>
              <tr>
                <th>User</th>
                <th>Type</th>
                <th>Status</th>
                <th>Verification</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {users.map(user => (
                <tr key={user._id}>
                  <td>
                    <div className="user-info">
                      <div className="user-icon">
                        <i className={getUserTypeIcon(user.userType)}></i>
                      </div>
                      <div className="user-details">
                        <div className="user-name">
                          {user.firstName} {user.lastName}
                        </div>
                        <div className="user-email">{user.email}</div>
                      </div>
                    </div>
                  </td>
                  <td>
                    <span className={`type-badge ${user.userType?.toLowerCase()}`}>
                      {user.userType}
                    </span>
                  </td>
                  <td>
                    <span className={getStatusBadgeClass(user.status)}>
                      {user.status || 'Unknown'}
                    </span>
                  </td>
                  <td>
                    <span className={getStatusBadgeClass(user.verificationStatus)}>
                      {user.verificationStatus || 'Unknown'}
                    </span>
                  </td>
                  <td>
                    <div className="action-buttons">
                      <button
                        className="action-btn view"
                        onClick={() => handleViewUserDetails(user)}
                        title="View Details"
                      >
                        <i className="fas fa-eye"></i>
                      </button>
                      <button
                        className="action-btn edit"
                        onClick={() => {
                          setSelectedUser(user);
                          setShowPasswordReset(true);
                        }}
                        title="Reset Password"
                      >
                        <i className="fas fa-key"></i>
                      </button>
                      <select
                        value={user.status || 'Active'}
                        onChange={(e) => handleStatusUpdate(user._id, e.target.value)}
                        className="status-select"
                      >
                        <option value="Active">Active</option>
                        <option value="Inactive">Inactive</option>
                        <option value="Pending">Pending</option>
                        <option value="Suspended">Suspended</option>
                      </select>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        <div className="management-pagination">
          <button 
            disabled={!pagination.hasPrevPage}
            onClick={() => handlePageChange(pagination.currentPage - 1)}
            className="pagination-btn"
          >
            <i className="fas fa-chevron-left"></i>
            Previous
          </button>
          
          <span className="pagination-info">
            Page {pagination.currentPage} of {pagination.totalPages}
          </span>
          
          <button 
            disabled={!pagination.hasNextPage}
            onClick={() => handlePageChange(pagination.currentPage + 1)}
            className="pagination-btn"
          >
            Next
            <i className="fas fa-chevron-right"></i>
          </button>
        </div>

        {/* User Details Modal */}
        {showUserDetails && selectedUser && (
          <div className="modal-overlay user-details-overlay" onClick={() => setShowUserDetails(false)}>
            <div className="modal-content large" onClick={(e) => e.stopPropagation()}>
              <div className="modal-header">
                <h3>User Details - {selectedUser.firstName} {selectedUser.lastName}</h3>
                <button
                  className="modal-close"
                  onClick={() => setShowUserDetails(false)}
                >
                  <i className="fas fa-times"></i>
                </button>
              </div>
              <div className="modal-body">
                {userDetails ? (
                  <div className="user-details-content">
                    {/* User Profile Header */}
                    <div className="user-profile-header">
                      <div className="profile-avatar">
                        <div className="user-icon large">
                          <i className={getUserTypeIcon(userDetails.user.userType)}></i>
                        </div>
                        <div className="profile-info">
                          <h2>{userDetails.user.firstName} {userDetails.user.lastName}</h2>
                          <p className="profile-email">{userDetails.user.email}</p>
                          <div className="profile-badges">
                            <span className={`type-badge ${userDetails.user.userType?.toLowerCase()}`}>
                              {userDetails.user.userType}
                            </span>
                            <span className={getStatusBadgeClass(userDetails.user.status)}>
                              {userDetails.user.status}
                            </span>
                            <span className={getStatusBadgeClass(userDetails.user.verificationStatus)}>
                              {userDetails.user.verificationStatus}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="details-section">
                      <h4>Contact Information</h4>
                      <div className="details-grid">
                        <div className="detail-item">
                          <label>Email Address:</label>
                          <span>{userDetails.user.email}</span>
                        </div>
                        <div className="detail-item">
                          <label>Phone Number:</label>
                          <span>{userDetails.user.phone || 'Not provided'}</span>
                        </div>
                        <div className="detail-item">
                          <label>Address:</label>
                          <span>{userDetails.user.address || 'Not provided'}</span>
                        </div>
                      </div>
                    </div>

                    <div className="details-section">
                      <h4>Account Information</h4>
                      <div className="details-grid">
                        <div className="detail-item">
                          <label>Account Created:</label>
                          <span>{formatDate(userDetails.user.createdAt)}</span>
                        </div>
                        <div className="detail-item">
                          <label>Last Login:</label>
                          <span>{userDetails.user.lastLogin ? formatDate(userDetails.user.lastLogin) : 'Never logged in'}</span>
                        </div>
                        <div className="detail-item">
                          <label>Account Status:</label>
                          <span className={getStatusBadgeClass(userDetails.user.status)}>
                            {userDetails.user.status}
                          </span>
                        </div>
                        <div className="detail-item">
                          <label>Email Verified:</label>
                          <span className={getStatusBadgeClass(userDetails.user.verificationStatus)}>
                            {userDetails.user.verificationStatus}
                          </span>
                        </div>
                      </div>
                    </div>

                    <div className="details-section">
                      <h4>Statistics</h4>
                      <div className="stats-grid">
                        <div className="stat-card mini">
                          <span className="stat-value">{userDetails.stats.contractsCount}</span>
                          <span className="stat-label">Contracts</span>
                        </div>
                        <div className="stat-card mini">
                          <span className="stat-value">{userDetails.stats.offersCount}</span>
                          <span className="stat-label">Offers</span>
                        </div>
                        <div className="stat-card mini">
                          <span className="stat-value">{userDetails.stats.activitiesCount}</span>
                          <span className="stat-label">Activities</span>
                        </div>
                      </div>
                    </div>

                    <div className="details-section">
                      <div className="section-header">
                        <h4>Recent Activities</h4>
                        <button
                          className="btn-view-all"
                          onClick={() => handleViewUserActivities(selectedUser._id)}
                        >
                          View All
                        </button>
                      </div>
                      <div className="activities-list">
                        {userDetails.recentActivities.map(activity => (
                          <div key={activity._id} className={`activity-item ${activity.severity?.toLowerCase()}`}>
                            <div className="activity-content">
                              <span className="activity-type">{activity.activityType}</span>
                              <p>{activity.description}</p>
                              <span className="activity-time">{formatDate(activity.createdAt)}</span>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="loading-state">
                    <Spinner size="medium" message="Loading user details..." />
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Password Reset Modal */}
        {showPasswordReset && selectedUser && (
          <div className="modal-overlay password-reset-overlay" onClick={() => setShowPasswordReset(false)}>
            <div className="password-reset-modal" onClick={(e) => e.stopPropagation()}>
              <div className="modal-header">
                <h2>
                  <i className="fas fa-key"></i>
                  Send Password Reset Email
                </h2>
                <button
                  className="close-btn"
                  onClick={() => setShowPasswordReset(false)}
                >
                  <i className="fas fa-times"></i>
                </button>
              </div>

              <div className="modal-content">
                <div className="password-reset-content">
                  <div className="reset-header">
                    <div className="reset-icon">
                      <i className="fas fa-envelope-circle-check"></i>
                    </div>
                    <h3>Send Secure Reset Link</h3>
                    <p className="reset-description">
                      Send a password reset email to <strong>{selectedUser.firstName} {selectedUser.lastName}</strong> at <strong>{selectedUser.email}</strong>
                    </p>
                  </div>

                  <div className="reset-features">
                    <div className="feature-item">
                      <div className="feature-icon">
                        <i className="fas fa-shield-alt"></i>
                      </div>
                      <div className="feature-content">
                        <h4>Secure Process</h4>
                        <p>Token-based reset with encryption</p>
                      </div>
                    </div>

                    <div className="feature-item">
                      <div className="feature-icon">
                        <i className="fas fa-clock"></i>
                      </div>
                      <div className="feature-content">
                        <h4>Time Limited</h4>
                        <p>Reset link expires in 1 hour</p>
                      </div>
                    </div>

                    <div className="feature-item">
                      <div className="feature-icon">
                        <i className="fas fa-user-check"></i>
                      </div>
                      <div className="feature-content">
                        <h4>User Control</h4>
                        <p>User creates their own secure password</p>
                      </div>
                    </div>

                    <div className="feature-item">
                      <div className="feature-icon">
                        <i className="fas fa-sync"></i>
                      </div>
                      <div className="feature-content">
                        <h4>Full Sync</h4>
                        <p>Updates both database and Cognito</p>
                      </div>
                    </div>
                  </div>

                  {selectedUser.status === 'Suspended' && (
                    <div className="warning-notice">
                      <div className="warning-icon">
                        <i className="fas fa-exclamation-triangle"></i>
                      </div>
                      <div className="warning-content">
                        <h4>Account Suspended</h4>
                        <p>This user account is currently suspended. They will not be able to complete the password reset process until their account is reactivated.</p>
                      </div>
                    </div>
                  )}

                  <div className="modal-actions">
                    <button
                      className="btn-secondary"
                      onClick={() => setShowPasswordReset(false)}
                    >
                      <i className="fas fa-times"></i>
                      Cancel
                    </button>
                    <button
                      className="btn-primary"
                      onClick={() => handlePasswordReset(selectedUser._id)}
                      disabled={selectedUser.status === 'Suspended'}
                    >
                      <i className="fas fa-paper-plane"></i>
                      Send Reset Email
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </DashboardLayout>
  );
};

export default ManageUsers;
