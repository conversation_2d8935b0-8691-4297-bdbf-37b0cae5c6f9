.contract-signing-container {
  padding: 10px 20px 20px;
  width: 100%;
  margin: 0;
  max-width: 1200px;
  margin: 0 auto;
}

.contract-header {
  margin-bottom: 30px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
}

.contract-header h1 {
  font-size: 28px;
  font-weight: 700;
  margin: 0;
  color: #000;
  display: flex;
  align-items: center;
  gap: 12px;
}

.contract-header h1 i {
  color: #0070f3;
}

.contract-status .status-badge {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.status-badge.pending {
  background-color: #fff7e6;
  color: #fa8c16;
}

.status-badge.active {
  background-color: #f6ffed;
  color: #52c41a;
}

.status-badge.completed {
  background-color: #e6f7ff;
  color: #0070f3;
}

.status-badge.cancelled {
  background-color: #fff2f0;
  color: #ff4d4f;
}

.contract-content {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.contract-card,
.signing-status-card {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.card-header {
  padding: 20px 25px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  color: #000;
}

.contract-number {
  font-size: 14px;
  color: #666;
  background-color: #f5f5f5;
  padding: 4px 8px;
  border-radius: 4px;
  font-family: monospace;
}

.card-body {
  padding: 25px;
}

.contract-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.info-item label {
  font-size: 13px;
  color: #666;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.info-item span {
  font-size: 16px;
  color: #000;
  font-weight: 600;
}

.signing-completed,
.signing-pending {
  text-align: center;
  padding: 20px;
}

.success-icon,
.pending-icon {
  font-size: 48px;
  margin-bottom: 20px;
}

.success-icon i {
  color: #52c41a;
}

.pending-icon i {
  color: #fa8c16;
}

.signing-completed h4,
.signing-pending h4 {
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 10px 0;
  color: #000;
}

.signing-completed p,
.signing-pending p {
  font-size: 14px;
  color: #666;
  margin: 0 0 25px 0;
  line-height: 1.5;
}

.action-buttons {
  display: flex;
  gap: 15px;
  justify-content: center;
  flex-wrap: wrap;
}

.contract-signing-container .btn-primary,
.contract-signing-container .btn-secondary {
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 8px;
  border: none;
  text-decoration: none;
}

.contract-signing-container .btn-primary {
  background: linear-gradient(to right, #000, #333);
  color: #fff;
}

.contract-signing-container .btn-primary:hover {
  opacity: 0.9;
  transform: translateY(-1px);
}

.contract-signing-container .btn-secondary {
  background-color: transparent;
  border: 1px solid #ddd;
  color: #666;
}

.contract-signing-container .btn-secondary:hover {
  background-color: #f5f5f5;
  border-color: #ccc;
}

.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;
  padding: 40px 20px;
}

.error-icon {
  font-size: 64px;
  color: #ff4d4f;
  margin-bottom: 20px;
}

.error-container h2 {
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 15px 0;
  color: #000;
}

.error-container p {
  font-size: 16px;
  color: #666;
  margin: 0 0 25px 0;
  max-width: 400px;
}

.mock-notice {
  background: linear-gradient(135deg, #e6f7ff, #f0f8ff);
  border: 1px solid #91d5ff;
  border-radius: 12px;
  padding: 20px;
  display: flex;
  gap: 15px;
  align-items: flex-start;
}

.notice-icon {
  font-size: 24px;
  color: #0070f3;
  flex-shrink: 0;
}

.notice-content h4 {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: #0070f3;
}

.notice-content p {
  font-size: 14px;
  color: #0050b3;
  margin: 0;
  line-height: 1.5;
}

/* Responsive styles */
@media (max-width: 768px) {
  .contract-signing-container {
    padding: 5px 15px 15px;
  }

  .contract-header h1 {
    font-size: 24px;
  }

  .header-content {
    flex-direction: column;
    align-items: flex-start;
  }

  .contract-info-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .action-buttons {
    flex-direction: column;
    align-items: stretch;
  }

  .btn-primary,
  .btn-secondary {
    justify-content: center;
  }

  .mock-notice {
    flex-direction: column;
    text-align: center;
  }
}
