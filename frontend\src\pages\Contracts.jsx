import { useState, useEffect } from 'react';
import DashboardLayout from '../components/DashboardLayout';
import Spinner from '../components/Spinner';
import '../styles/contracts.css';

const Contracts = () => {
  const [loading, setLoading] = useState(true);
  const [contracts, setContracts] = useState([]);
  const [activeTab, setActiveTab] = useState('active');

  useEffect(() => {
    // Simulate loading contracts
    const fetchContracts = async () => {
      try {
        // In a real app, this would be an API call
        // For now, we'll use mock data
        setTimeout(() => {
          const mockContracts = [
            {
              id: 1,
              contractNumber: 'CNT-2023-001',
              provider: 'EDF Energy',
              energyType: 'Electricity',
              rateType: 'Fixed',
              rate: 0.145,
              standingCharge: 25.5,
              estimatedAnnualCost: 950,
              currency: 'EUR',
              startDate: '2023-01-15',
              endDate: '2025-01-14',
              status: 'Active',
              documentUrl: '#',
              specialTerms: 'Price guaranteed for full term'
            },
            {
              id: 2,
              contractNumber: 'CNT-2022-089',
              provider: 'Engie',
              energyType: 'Gas',
              rateType: 'Fixed',
              rate: 0.065,
              standingCharge: 18.5,
              estimatedAnnualCost: 720,
              currency: 'EUR',
              startDate: '2022-05-10',
              endDate: '2023-05-09',
              status: 'Completed',
              documentUrl: '#',
              specialTerms: 'No exit fees'
            },
            {
              id: 3,
              contractNumber: 'CNT-2023-145',
              provider: 'Total Energies',
              energyType: 'Both',
              rateType: 'Variable',
              rate: 0.138,
              standingCharge: 22.8,
              estimatedAnnualCost: 880,
              currency: 'EUR',
              startDate: '2023-06-01',
              endDate: '2024-05-31',
              status: 'Active',
              documentUrl: '#',
              specialTerms: 'Carbon neutral energy'
            }
          ];

          setContracts(mockContracts);
          setLoading(false);
        }, 1500);
      } catch (error) {
        console.error('Error fetching contracts:', error);
        setLoading(false);
      }
    };

    fetchContracts();
  }, []);

  // Filter contracts based on active tab
  const filteredContracts = contracts.filter(contract => {
    if (activeTab === 'active') return contract.status === 'Active';
    if (activeTab === 'completed') return contract.status === 'Completed';
    if (activeTab === 'pending') return contract.status === 'Pending';
    return true; // 'all' tab
  });

  // Format date to readable format
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-GB', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    });
  };

  // Calculate days remaining for active contracts
  const getDaysRemaining = (endDate) => {
    const end = new Date(endDate);
    const today = new Date();
    const diffTime = end - today;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays > 0 ? diffDays : 0;
  };

  return (
    <DashboardLayout>
      <div className="contracts-container">
        <div className="contracts-header">
          <h1>My Energy Contracts</h1>
          <p>Manage your energy contracts and track their status.</p>
        </div>

        {loading ? (
          <div className="contracts-loading">
            <Spinner size="medium" message="Loading your contracts..." />
          </div>
        ) : (
          <>
            <div className="contracts-tabs">
              <button
                className={`tab-button ${activeTab === 'active' ? 'active' : ''}`}
                onClick={() => setActiveTab('active')}
              >
                Active
              </button>
              <button
                className={`tab-button ${activeTab === 'completed' ? 'active' : ''}`}
                onClick={() => setActiveTab('completed')}
              >
                Completed
              </button>
              <button
                className={`tab-button ${activeTab === 'pending' ? 'active' : ''}`}
                onClick={() => setActiveTab('pending')}
              >
                Pending
              </button>
              <button
                className={`tab-button ${activeTab === 'all' ? 'active' : ''}`}
                onClick={() => setActiveTab('all')}
              >
                All Contracts
              </button>
            </div>

            {filteredContracts.length > 0 ? (
              <div className="contracts-list">
                {filteredContracts.map(contract => (
                  <div key={contract.id} className="contract-card">
                    <div className="contract-header">
                      <div className="contract-provider">
                        <span className="provider-name">{contract.provider}</span>
                        <span className={`contract-status ${contract.status.toLowerCase()}`}>
                          {contract.status}
                        </span>
                      </div>
                      <h2 className="contract-number">{contract.contractNumber}</h2>
                    </div>

                    <div className="contract-content">
                      <div className="contract-details">
                        <div className="detail-column">
                          <div className="detail-item">
                            <span className="detail-label">Energy Type:</span>
                            <span className="detail-value">{contract.energyType}</span>
                          </div>
                          <div className="detail-item">
                            <span className="detail-label">Rate Type:</span>
                            <span className="detail-value">{contract.rateType}</span>
                          </div>
                          <div className="detail-item">
                            <span className="detail-label">Rate:</span>
                            <span className="detail-value">{contract.rate} €/kWh</span>
                          </div>
                          <div className="detail-item">
                            <span className="detail-label">Standing Charge:</span>
                            <span className="detail-value">{contract.standingCharge} €/month</span>
                          </div>
                        </div>

                        <div className="detail-column">
                          <div className="detail-item">
                            <span className="detail-label">Start Date:</span>
                            <span className="detail-value">{formatDate(contract.startDate)}</span>
                          </div>
                          <div className="detail-item">
                            <span className="detail-label">End Date:</span>
                            <span className="detail-value">{formatDate(contract.endDate)}</span>
                          </div>
                          <div className="detail-item">
                            <span className="detail-label">Annual Cost:</span>
                            <span className="detail-value">{contract.estimatedAnnualCost} {contract.currency}</span>
                          </div>
                          {contract.status === 'Active' && (
                            <div className="detail-item">
                              <span className="detail-label">Days Remaining:</span>
                              <span className="detail-value">{getDaysRemaining(contract.endDate)} days</span>
                            </div>
                          )}
                        </div>
                      </div>

                      {contract.specialTerms && (
                        <div className="special-terms">
                          <span className="terms-label">Special Terms:</span>
                          <span className="terms-value">{contract.specialTerms}</span>
                        </div>
                      )}
                    </div>

                    <div className="contract-footer">
                      <button className="btn-view-document">
                        <i className="fas fa-file-pdf"></i> View Contract
                      </button>

                      {contract.status === 'Active' && (
                        <button className="btn-renew">
                          <i className="fas fa-sync-alt"></i> Renew Contract
                        </button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="no-contracts">
                <i className="fas fa-file-contract"></i>
                <h3>No {activeTab !== 'all' ? activeTab : ''} contracts found</h3>
                <p>
                  {activeTab === 'active' && "You don't have any active energy contracts."}
                  {activeTab === 'completed' && "You don't have any completed energy contracts."}
                  {activeTab === 'pending' && "You don't have any pending energy contracts."}
                  {activeTab === 'all' && "You don't have any energy contracts yet."}
                </p>
                <button className="btn-browse-offers">Browse Energy Offers</button>
              </div>
            )}
          </>
        )}
      </div>
    </DashboardLayout>
  );
};

export default Contracts;
