const dotenv = require('dotenv');
const path = require('path');

// Load environment variables
dotenv.config({
  path: path.resolve(__dirname, '.env.uat'),
  override: false,
});

const mongoose = require('mongoose');
const User = require('./models/User');
const logger = require('./utils/logger');

async function testLoginBlocking() {
  console.log('🧪 Testing Login Blocking for Suspended Users\n');

  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    // Find a suspended user
    const suspendedUser = await User.findOne({ status: 'Suspended' });
    
    if (!suspendedUser) {
      console.log('❌ No suspended user found. Please suspend a user first.');
      console.log('💡 Go to Admin Dashboard → Manage Users → Change status to "Suspended"');
      return;
    }

    console.log(`📧 Found suspended user: ${suspendedUser.email}`);
    console.log(`🔍 Status: ${suspendedUser.status}`);
    console.log(`🆔 Cognito ID: ${suspendedUser.cognitoId || 'Not set'}`);
    console.log(`👤 Name: ${suspendedUser.firstName} ${suspendedUser.lastName}`);

    // Test the login logic
    console.log('\n🧪 Testing login blocking logic...');
    
    // Simulate the login check
    const userStatus = suspendedUser.status;
    const isBlocked = ['Suspended', 'Inactive', 'Pending'].includes(userStatus);
    
    console.log(`📊 User status: ${userStatus}`);
    console.log(`🚫 Should be blocked: ${isBlocked ? 'YES' : 'NO'}`);
    
    if (isBlocked) {
      let message, errorCode;
      switch (userStatus) {
        case 'Suspended':
          message = 'Account is suspended. Please contact support for assistance.';
          errorCode = 'ACCOUNT_SUSPENDED';
          break;
        case 'Inactive':
          message = 'Account is inactive. Please contact support to reactivate your account.';
          errorCode = 'ACCOUNT_INACTIVE';
          break;
        case 'Pending':
          message = 'Account is pending approval. Please wait for admin approval or contact support.';
          errorCode = 'ACCOUNT_PENDING';
          break;
      }
      
      console.log(`✅ Login should be blocked with:`);
      console.log(`   Error Code: ${errorCode}`);
      console.log(`   Message: ${message}`);
    }

    // Check recent login attempts
    console.log('\n🔍 Checking recent login attempts...');
    const UserActivity = require('./models/UserActivity');
    
    try {
      const recentActivities = await UserActivity.find({
        userId: suspendedUser._id,
        activityType: { $in: ['SuspendedLoginAttempt', 'Login', 'SuspendedAccessAttempt'] }
      })
      .sort({ createdAt: -1 })
      .limit(5);

      if (recentActivities.length === 0) {
        console.log('📝 No recent login attempts found');
      } else {
        console.log('📝 Recent activities:');
        recentActivities.forEach(activity => {
          console.log(`   - ${activity.activityType}: ${activity.description} (${activity.createdAt})`);
        });
      }
    } catch (activityError) {
      console.log('⚠️ Could not check activities:', activityError.message);
    }

    console.log('\n🔧 Manual Testing Steps:');
    console.log('1. Try to login with the suspended user credentials:');
    console.log(`   Email: ${suspendedUser.email}`);
    console.log('2. Login should be blocked with error message');
    console.log('3. Check browser network tab for 403 response');
    console.log('4. Should redirect to AccountStatus error page');

    console.log('\n💡 If login is NOT blocked, check:');
    console.log('1. Frontend is calling the correct login endpoint');
    console.log('2. Backend login route is checking database status');
    console.log('3. Error response is properly formatted');
    console.log('4. Frontend is handling the error response');

  } catch (error) {
    console.error('💥 Test failed:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n✨ Disconnected from MongoDB');
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  testLoginBlocking()
    .then(() => {
      console.log('\n✨ Test completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Test failed:', error);
      process.exit(1);
    });
}

module.exports = { testLoginBlocking };
