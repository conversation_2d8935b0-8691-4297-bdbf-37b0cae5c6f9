const mongoose = require('mongoose');

/**
 * Invoice schema for storing invoice information
 */
const invoiceSchema = new mongoose.Schema({
  // Reference to the user who uploaded the invoice
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  
  // Cognito ID of the user
  cognitoId: {
    type: String,
    required: true
  },
  
  // User type (individual, professional, etc.)
  userType: {
    type: String,
    enum: ['individual', 'professional', 'broker', 'supplier', 'referrer', 'admin'],
    required: true
  },
  
  // Original filename
  originalFilename: {
    type: String,
    required: true
  },
  
  // S3 key (path in the bucket)
  s3Key: {
    type: String,
    required: true
  },
  
  // S3 bucket name
  s3Bucket: {
    type: String,
    required: true
  },
  
  // File size in bytes
  fileSize: {
    type: Number,
    required: true
  },
  
  // MIME type
  mimeType: {
    type: String,
    required: true
  },
  
  // Public URL (if available)
  publicUrl: {
    type: String
  },
  
  // Invoice metadata
  metadata: {
    // Invoice date
    invoiceDate: {
      type: Date
    },
    
    // Invoice number
    invoiceNumber: {
      type: String
    },
    
    // Energy provider
    provider: {
      type: String
    },
    
    // Energy type (electricity, gas, etc.)
    energyType: {
      type: String,
      enum: ['electricity', 'gas', 'both', 'other']
    },
    
    // PDL/PRM/RAE number (point of delivery)
    pointOfDelivery: {
      type: String
    },
    
    // Amount
    amount: {
      type: Number
    },
    
    // Currency
    currency: {
      type: String,
      default: 'EUR'
    },
    
    // Consumption in kWh
    consumption: {
      type: Number
    },
    
    // Additional notes
    notes: {
      type: String
    }
  },
  
  // Processing status
  status: {
    type: String,
    enum: ['pending', 'processing', 'processed', 'failed'],
    default: 'pending'
  },
  
  // Processing error (if any)
  processingError: {
    type: String
  },
  
  // Timestamps
  createdAt: {
    type: Date,
    default: Date.now
  },
  
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Update the updatedAt timestamp before saving
invoiceSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Create the model
const Invoice = mongoose.model('Invoice', invoiceSchema);

module.exports = Invoice;
