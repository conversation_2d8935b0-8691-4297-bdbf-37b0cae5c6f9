/**
 * Utility functions for working with localStorage
 */

// Keys used in localStorage - MINIMAL SET
const STORAGE_KEYS = {
  // Auth tokens - these are required for authentication
  ID_TOKEN: 'idToken',
  ACCESS_TOKEN: 'accessToken',
  REFRESH_TOKEN: 'refreshToken',
  IS_AUTHENTICATED: 'isAuthenticated',

  // Essential user data from Cognito
  COGNITO_ID: 'cognitoId',
  USER_TYPE: 'userType',
  PROFILE_COMPLETION: 'profileCompletion',
  USER_DATA: 'userData',
  VERIFIED: 'verified'
};

/**
 * Set a value in localStorage
 * @param {string} key - The key to set
 * @param {*} value - The value to set
 */
const setItem = (key, value) => {
  try {
    if (typeof value === 'object') {
      localStorage.setItem(key, JSON.stringify(value));
    } else {
      localStorage.setItem(key, value);
    }
    return true;
  } catch (error) {
    console.error(`Error setting localStorage key ${key}:`, error);
    return false;
  }
};

/**
 * Get a value from localStorage
 * @param {string} key - The key to get
 * @param {boolean} parseJson - Whether to parse the value as JSON
 * @returns {*} The value from localStorage
 */
const getItem = (key, parseJson = false) => {
  try {
    const value = localStorage.getItem(key);

    if (parseJson && value) {
      try {
        return JSON.parse(value);
      } catch (parseError) {
        console.error(`Error parsing JSON for localStorage key ${key}:`, parseError);
        return null;
      }
    }

    return value;
  } catch (error) {
    console.error(`Error getting localStorage key ${key}:`, error);
    return null;
  }
};

/**
 * Remove a value from localStorage
 * @param {string} key - The key to remove
 */
const removeItem = (key) => {
  try {
    // Special handling for the literal string 'undefined'
    if (key === 'undefined') {
      try {
        localStorage.removeItem('undefined');
      } catch (e) {
        // Silently fail
      }
      return;
    }

    // Handle AWS Amplify special keys
    if (isAmplifyKey(key)) {
      try {
        localStorage.removeItem(key);
      } catch (e) {
        // Silently fail
      }
      return;
    }

    // Check if the key exists and is valid before trying to remove it
    if (key && localStorage.getItem(key) !== null) {
      localStorage.removeItem(key);
    }
  } catch (error) {
    // Silently fail
  }
};

/**
 * Clear all user-related data from localStorage
 */
const clearUserData = () => {
  try {
    // Remove essential user data using our safe removeItem function
    removeItem(STORAGE_KEYS.COGNITO_ID);
    removeItem(STORAGE_KEYS.USER_TYPE);
    removeItem(STORAGE_KEYS.PROFILE_COMPLETION);
    removeItem(STORAGE_KEYS.USER_DATA);
    removeItem(STORAGE_KEYS.VERIFIED);

    // Remove any legacy keys that might still exist
    const legacyKeys = [
      'individualInfo',
      'professionalInfo',
      'individualInfoCompleted',
      'professionalInfoCompleted',
      'undefined',
      'userType',
      'profileCompletion',
      'userData',
      'cognitoId',
      '__uploadInProgress',
      '__uploadInProgress__',
      'CognitoIdentityServiceProvider',
      'amplify-signin-with-hostedUI',
      'amplify-redirected-from-hosted-ui',
      'amplify-authenticator-authState'
    ];

    // Remove each legacy key safely
    legacyKeys.forEach(key => {
      try {
        if (key) removeItem(key);
      } catch (e) {
        // Silently fail
      }
    });

    // Also check for any AWS Amplify upload keys that might be in localStorage
    try {
      // Get all keys in localStorage
      const allKeys = Object.keys(localStorage);

      // Filter for AWS Amplify upload keys
      const uploadKeys = allKeys.filter(key =>
        key === '__uploadInProgress' ||
        key === '__uploadInProgress__' ||
        key.includes('upload')
      );

      // Remove each upload key
      if (uploadKeys.length > 0) {
        uploadKeys.forEach(key => {
          try {
            removeItem(key);
          } catch (e) {
            // Silently fail
          }
        });
      }
    } catch (error) {
      // Silently fail
    }
  } catch (error) {
    // Silently fail
  }
};

/**
 * Clear all auth-related data from localStorage
 */
const clearAuthData = () => {
  try {
    // Remove auth tokens using our safe removeItem function
    removeItem(STORAGE_KEYS.ID_TOKEN);
    removeItem(STORAGE_KEYS.ACCESS_TOKEN);
    removeItem(STORAGE_KEYS.REFRESH_TOKEN);
    removeItem(STORAGE_KEYS.IS_AUTHENTICATED);
  } catch (error) {
    // Silently fail
  }
};

/**
 * Clear all data from localStorage
 */
const clearAll = () => {
  try {
    // First use our specific clear functions
    clearUserData();
    clearAuthData();

    // Then check for any additional keys that might not be covered
    const additionalKeys = [
      'individualInfo',
      'professionalInfo',
      'individualInfoCompleted',
      'professionalInfoCompleted',
      'undefined',
      '__uploadInProgress',
      '__uploadInProgress__',
      'CognitoIdentityServiceProvider',
      'amplify-signin-with-hostedUI',
      'amplify-redirected-from-hosted-ui',
      'amplify-authenticator-authState',
      'tempEmail',
      'rememberMe',
      'lastLogin',
      'sessionId',
      'redirectUrl'
    ];

    // Safely remove each additional key
    additionalKeys.forEach(key => {
      try {
        removeItem(key);
      } catch (e) {
        // Silently fail
      }
    });

    // Also check for any AWS Amplify keys that might be in localStorage
    try {
      // Get all keys in localStorage
      const allKeys = Object.keys(localStorage);

      // Filter for AWS Amplify keys
      const amplifyKeys = allKeys.filter(key => isAmplifyKey(key));

      // Remove each AWS Amplify key
      if (amplifyKeys.length > 0) {
        amplifyKeys.forEach(key => {
          try {
            removeItem(key);
          } catch (e) {
            // Silently fail
          }
        });
      }
    } catch (error) {
      // Silently fail
    }
  } catch (error) {
    // Silently fail
  }
};

// No longer using localStorage for profile completion status - relying on Cognito directly

// List of known AWS Amplify localStorage keys
const AMPLIFY_KEYS = [
  '__uploadInProgress',
  '__uploadInProgress__',
  'CognitoIdentityServiceProvider',
  'amplify-signin-with-hostedUI',
  'amplify-redirected-from-hosted-ui',
  'amplify-authenticator-authState'
];

/**
 * Check if a key is an AWS Amplify related key
 * @param {string} key - The key to check
 * @returns {boolean} - True if the key is related to AWS Amplify
 */
const isAmplifyKey = (key) => {
  if (!key) return false;

  // Check exact matches
  if (AMPLIFY_KEYS.includes(key)) return true;

  // Check if key starts with any known Amplify prefixes
  return key.startsWith('CognitoIdentityServiceProvider.') ||
         key.startsWith('amplify-') ||
         key.startsWith('aws.') ||
         key.startsWith('AWSCognito');
};

// Storage monitoring removed - not needed in production

/**
 * Clean up problematic localStorage keys on page load
 */
const cleanupProblematicKeys = () => {
  try {
    removeItem('undefined');
    removeItem('__uploadInProgress');
    removeItem('__uploadInProgress__');
    removeItem('tempPassword');

    // Check for any keys with 'upload' in the name
    const allKeys = Object.keys(localStorage);
    const uploadKeys = allKeys.filter(key => key.includes('upload'));
    uploadKeys.forEach(key => removeItem(key));
  } catch (error) {
    // Silently fail
  }
};

// Run cleanup on page load
cleanupProblematicKeys();

// Export the utility functions and constants
export {
  STORAGE_KEYS,
  setItem,
  getItem,
  removeItem,
  clearUserData,
  clearAuthData,
  clearAll
};
