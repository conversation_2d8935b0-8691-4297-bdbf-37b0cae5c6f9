const express = require('express');
const router = express.Router();
const Invitation = require('../models/Invitation');
const User = require('../models/User');
const UserActivity = require('../models/UserActivity');
const logger = require('../utils/logger');
const AWS = require('aws-sdk');

// Configure AWS Cognito
const cognitoIdentityServiceProvider = new AWS.CognitoIdentityServiceProvider({
  region: process.env.AWS_REGION || 'eu-west-3'
});

// Validate invitation token (public route)
router.get('/validate/:token', async (req, res) => {
  try {
    const { token } = req.params;

    if (!token) {
      return res.status(400).json({
        success: false,
        message: 'Invitation token is required'
      });
    }

    const invitation = await Invitation.findByToken(token);

    if (!invitation) {
      return res.status(404).json({
        success: false,
        message: 'Invalid or expired invitation token'
      });
    }

    if (!invitation.isValid()) {
      return res.status(400).json({
        success: false,
        message: 'This invitation has expired or is no longer valid'
      });
    }

    // Check if user already exists with this email
    const existingUser = await User.findOne({ email: invitation.email });
    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: 'A user with this email already exists'
      });
    }

    // Return invitation details for the registration form
    res.json({
      success: true,
      data: {
        email: invitation.email,
        userType: invitation.userType,
        inviteeDetails: invitation.inviteeDetails,
        supplierSpecific: invitation.supplierSpecific,
        invitedBy: invitation.invitedBy,
        expiresAt: invitation.expiresAt
      }
    });

  } catch (error) {
    logger.error('Error validating invitation token:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to validate invitation token'
    });
  }
});

// Accept invitation and create user account (public route)
router.post('/accept/:token', async (req, res) => {
  try {
    const { token } = req.params;
    const {
      firstName,
      lastName,
      phone,
      password,
      cognitoId,
      additionalInfo
    } = req.body;

    if (!token) {
      return res.status(400).json({
        success: false,
        message: 'Invitation token is required'
      });
    }

    const invitation = await Invitation.findByToken(token);

    if (!invitation) {
      return res.status(404).json({
        success: false,
        message: 'Invalid or expired invitation token'
      });
    }

    if (!invitation.isValid()) {
      return res.status(400).json({
        success: false,
        message: 'This invitation has expired or is no longer valid'
      });
    }

    // Check if user already exists in MongoDB
    const existingUser = await User.findOne({ email: invitation.email });
    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: 'A user with this email already exists'
      });
    }

    // Validate required fields
    if (!firstName || !lastName || !password) {
      return res.status(400).json({
        success: false,
        message: 'First name, last name, and password are required'
      });
    }

    // First, create user in Cognito
    let cognitoUserId = null;
    try {
      // Prepare user attributes for Cognito
      const userAttributes = [
        { Name: 'email', Value: invitation.email },
        { Name: 'email_verified', Value: 'true' }, // Mark as verified since they came from invitation
        { Name: 'given_name', Value: firstName },
        { Name: 'family_name', Value: lastName },
        { Name: 'custom:userType', Value: invitation.userType },
        { Name: 'custom:profileComplete', Value: 'false' }
      ];

      if (phone) {
        userAttributes.push({ Name: 'phone_number', Value: phone });
      }

      // Create user in Cognito using admin API (no email verification needed)
      const createUserParams = {
        UserPoolId: process.env.COGNITO_USER_POOL_ID,
        Username: invitation.email,
        UserAttributes: userAttributes,
        TemporaryPassword: password,
        MessageAction: 'SUPPRESS' // Don't send welcome email
      };

      const cognitoResult = await cognitoIdentityServiceProvider.adminCreateUser(createUserParams).promise();
      cognitoUserId = cognitoResult.User.Attributes.find(attr => attr.Name === 'sub').Value;

      logger.info(`Created Cognito user for invitation acceptance: ${cognitoUserId}`);

      // Set permanent password
      const setPasswordParams = {
        UserPoolId: process.env.COGNITO_USER_POOL_ID,
        Username: invitation.email,
        Password: password,
        Permanent: true
      };

      await cognitoIdentityServiceProvider.adminSetUserPassword(setPasswordParams).promise();
      logger.info('Set permanent password for invited user');

    } catch (cognitoError) {
      logger.error('Error creating user in Cognito:', cognitoError);

      // Handle specific Cognito errors
      if (cognitoError.code === 'UsernameExistsException') {
        return res.status(400).json({
          success: false,
          message: 'A user with this email already exists in the system'
        });
      }

      return res.status(500).json({
        success: false,
        message: 'Failed to create user account. Please try again.'
      });
    }

    // Create new user account in MongoDB
    const userData = {
      email: invitation.email,
      firstName,
      lastName,
      phone,
      userType: invitation.userType,
      cognitoId: cognitoUserId,
      status: 'Pending', // Pending admin approval
      verificationStatus: 'Verified', // Already verified through invitation
      invitationId: invitation._id,
      profileComplete: false
    };

    // Add invitation-specific details
    if (invitation.inviteeDetails.companyName) {
      userData.companyName = invitation.inviteeDetails.companyName;
    }

    const newUser = new User(userData);
    await newUser.save();

    // Mark invitation as accepted
    await invitation.accept(newUser._id);

    // Log activity for the new user
    await UserActivity.logActivity({
      userId: newUser._id,
      activityType: 'InvitationAccepted',
      description: `Accepted ${invitation.userType} invitation and created account`,
      details: {
        invitationId: invitation._id,
        invitedBy: invitation.invitedBy,
        userType: invitation.userType
      },
      severity: 'Normal',
      isSystemGenerated: true
    });

    // Log activity for the admin who sent the invitation
    await UserActivity.logActivity({
      userId: invitation.invitedBy,
      activityType: 'InvitationAccepted',
      description: `${invitation.userType} invitation accepted by ${firstName} ${lastName}`,
      details: {
        invitationId: invitation._id,
        newUserId: newUser._id,
        email: invitation.email,
        acceptedBy: `${firstName} ${lastName}`
      },
      severity: 'Normal',
      isSystemGenerated: true
    });

    logger.info(`Invitation accepted: ${invitation.userType} user ${newUser.email} created from invitation ${invitation._id}`);

    // Send admin notification email about new broker registration
    try {
      const emailService = require('../services/emailService');
      await emailService.sendAdminNotificationEmail({
        type: 'broker_registration',
        subject: `New ${invitation.userType} Registration - ${firstName} ${lastName}`,
        brokerName: `${firstName} ${lastName}`,
        brokerEmail: invitation.email,
        registrationDate: new Date().toLocaleDateString(),
        adminDashboardUrl: `${process.env.FRONTEND_URL || 'http://localhost:8080'}/admin/brokers`
      });
      logger.info('Admin notification email sent for broker registration');
    } catch (emailError) {
      logger.error('Failed to send admin notification email:', emailError);
      // Don't fail the registration if email fails
    }

    // Create in-app notification for admin
    try {
      const Notification = require('../models/Notification');

      // Find all admin users
      const adminUsers = await User.find({ userType: 'Admin' });

      // Create notification for each admin
      for (const admin of adminUsers) {
        await Notification.create({
          userId: admin._id,
          type: 'Other',
          title: `New ${invitation.userType} Registration`,
          message: `${firstName} ${lastName} has completed registration and is awaiting approval.`,
          actionUrl: '/admin/brokers',
          relatedEntity: {
            entityType: 'User',
            entityId: newUser._id
          }
        });
      }
      logger.info('In-app notifications created for admin users');
    } catch (notificationError) {
      logger.error('Failed to create admin notifications:', notificationError);
      // Don't fail the registration if notification fails
    }

    res.json({
      success: true,
      message: 'Account created successfully! Your login credentials are ready and you can sign in once approved by our admin team.',
      data: {
        userId: newUser._id,
        email: newUser.email,
        userType: newUser.userType,
        status: newUser.status,
        cognitoId: cognitoUserId,
        requiresApproval: true,
        canLogin: false // Will be true after admin approval
      }
    });

  } catch (error) {
    logger.error('Error accepting invitation:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to accept invitation'
    });
  }
});

// Get invitation details by token (for pre-filling registration form)
router.get('/details/:token', async (req, res) => {
  try {
    const { token } = req.params;

    const invitation = await Invitation.findByToken(token);

    if (!invitation) {
      return res.status(404).json({
        success: false,
        message: 'Invalid invitation token'
      });
    }

    if (!invitation.isValid()) {
      return res.status(400).json({
        success: false,
        message: 'This invitation has expired'
      });
    }

    // Return safe invitation details for form pre-filling
    res.json({
      success: true,
      data: {
        email: invitation.email,
        userType: invitation.userType,
        firstName: invitation.inviteeDetails.firstName,
        lastName: invitation.inviteeDetails.lastName,
        phone: invitation.inviteeDetails.phone,
        companyName: invitation.inviteeDetails.companyName,
        expiresAt: invitation.expiresAt,
        daysRemaining: Math.ceil((invitation.expiresAt - new Date()) / (1000 * 60 * 60 * 24))
      }
    });

  } catch (error) {
    logger.error('Error fetching invitation details:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch invitation details'
    });
  }
});

// Check if email has pending invitation (public route for validation)
router.get('/check-email/:email', async (req, res) => {
  try {
    const { email } = req.params;

    const pendingInvitation = await Invitation.findOne({
      email: email.toLowerCase(),
      status: 'Pending'
    });

    if (pendingInvitation && pendingInvitation.isValid()) {
      return res.json({
        success: true,
        data: {
          hasPendingInvitation: true,
          userType: pendingInvitation.userType,
          expiresAt: pendingInvitation.expiresAt
        }
      });
    }

    res.json({
      success: true,
      data: {
        hasPendingInvitation: false
      }
    });

  } catch (error) {
    logger.error('Error checking email for invitation:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to check email'
    });
  }
});

module.exports = router;
