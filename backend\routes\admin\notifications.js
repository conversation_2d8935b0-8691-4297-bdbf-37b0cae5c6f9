const express = require('express');
const router = express.Router();
const Notification = require('../../models/Notification');
const SystemAlert = require('../../models/SystemAlert');
const User = require('../../models/User');
const Contract = require('../../models/Contract');
const UserActivity = require('../../models/UserActivity');
const logger = require('../../utils/logger');
const { authMiddleware } = require('../../middleware/auth');

// Apply auth middleware to all admin routes
router.use(authMiddleware);

// Middleware to check if user is admin
const requireAdmin = async (req, res, next) => {
  try {
    const userEmail = req.user?.email;
    if (!userEmail) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    const user = await User.findOne({ email: userEmail });
    if (!user || user.userType !== 'Admin') {
      return res.status(403).json({
        success: false,
        message: 'Admin access required'
      });
    }

    req.adminUser = user;
    next();
  } catch (error) {
    logger.error('Admin middleware error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// Get all system alerts and notification settings
router.get('/settings', requireAdmin, async (req, res) => {
  try {
    const systemAlerts = await SystemAlert.find()
      .populate('createdBy', 'firstName lastName email')
      .sort({ createdAt: -1 });

    // Get notification statistics
    const [
      totalNotifications,
      unreadNotifications,
      emailNotifications,
      smsNotifications,
      recentNotifications
    ] = await Promise.all([
      Notification.countDocuments(),
      Notification.countDocuments({ read: false }),
      Notification.countDocuments({ 'deliveryChannels.email': true }),
      Notification.countDocuments({ 'deliveryChannels.sms': true }),
      Notification.find()
        .populate('userId', 'firstName lastName email')
        .sort({ createdAt: -1 })
        .limit(10)
    ]);

    res.json({
      success: true,
      data: {
        systemAlerts,
        statistics: {
          totalNotifications,
          unreadNotifications,
          emailNotifications,
          smsNotifications
        },
        recentNotifications
      }
    });

  } catch (error) {
    logger.error('Error fetching notification settings:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch notification settings'
    });
  }
});

// Create system alert
router.post('/alerts', requireAdmin, async (req, res) => {
  try {
    const {
      title,
      message,
      alertType,
      priority,
      targetAudience,
      conditions,
      frequency,
      isActive,
      expiresAt
    } = req.body;

    const systemAlert = new SystemAlert({
      title,
      message,
      alertType,
      priority,
      targetAudience,
      conditions,
      frequency,
      isActive: isActive !== undefined ? isActive : true,
      expiresAt: expiresAt ? new Date(expiresAt) : undefined,
      createdBy: req.adminUser._id
    });

    await systemAlert.save();

    // Log activity
    await UserActivity.logActivity({
      userId: req.adminUser._id,
      activityType: 'SystemAlertCreated',
      description: `Created system alert: ${title}`,
      details: {
        alertId: systemAlert._id,
        alertType,
        priority,
        targetAudience
      },
      severity: 'Normal',
      isSystemGenerated: true
    });

    logger.info(`Admin ${req.adminUser.email} created system alert: ${title}`);

    res.json({
      success: true,
      message: 'System alert created successfully',
      data: systemAlert
    });

  } catch (error) {
    logger.error('Error creating system alert:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create system alert'
    });
  }
});

// Update system alert
router.put('/alerts/:alertId', requireAdmin, async (req, res) => {
  try {
    const { alertId } = req.params;
    const updateData = req.body;

    const systemAlert = await SystemAlert.findByIdAndUpdate(
      alertId,
      {
        ...updateData,
        lastModifiedBy: req.adminUser._id,
        lastModifiedAt: new Date()
      },
      { new: true }
    );

    if (!systemAlert) {
      return res.status(404).json({
        success: false,
        message: 'System alert not found'
      });
    }

    logger.info(`Admin ${req.adminUser.email} updated system alert: ${systemAlert.title}`);

    res.json({
      success: true,
      message: 'System alert updated successfully',
      data: systemAlert
    });

  } catch (error) {
    logger.error('Error updating system alert:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update system alert'
    });
  }
});

// Delete system alert
router.delete('/alerts/:alertId', requireAdmin, async (req, res) => {
  try {
    const { alertId } = req.params;

    const systemAlert = await SystemAlert.findByIdAndDelete(alertId);

    if (!systemAlert) {
      return res.status(404).json({
        success: false,
        message: 'System alert not found'
      });
    }

    logger.info(`Admin ${req.adminUser.email} deleted system alert: ${systemAlert.title}`);

    res.json({
      success: true,
      message: 'System alert deleted successfully'
    });

  } catch (error) {
    logger.error('Error deleting system alert:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete system alert'
    });
  }
});

// Send manual notification
router.post('/send', requireAdmin, async (req, res) => {
  try {
    const {
      title,
      message,
      recipients,
      notificationType,
      deliveryChannels,
      priority,
      expiresAt
    } = req.body;

    // Validate recipients
    let targetUsers = [];
    if (recipients.type === 'all') {
      targetUsers = await User.find({ status: 'Active' });
    } else if (recipients.type === 'userType') {
      targetUsers = await User.find({ 
        userType: { $in: recipients.userTypes },
        status: 'Active'
      });
    } else if (recipients.type === 'specific') {
      targetUsers = await User.find({ 
        _id: { $in: recipients.userIds },
        status: 'Active'
      });
    }

    if (targetUsers.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'No valid recipients found'
      });
    }

    // Create notifications for each user
    const notifications = targetUsers.map(user => ({
      userId: user._id,
      type: notificationType || 'SystemAlert',
      title,
      message,
      priority: priority || 'Normal',
      deliveryChannels: {
        inApp: true,
        email: deliveryChannels?.email || false,
        sms: deliveryChannels?.sms || false,
        push: deliveryChannels?.push || false
      },
      expiresAt: expiresAt ? new Date(expiresAt) : undefined
    }));

    await Notification.insertMany(notifications);

    // Log activity
    await UserActivity.logActivity({
      userId: req.adminUser._id,
      activityType: 'Other',
      description: `Sent manual notification to ${targetUsers.length} users: ${title}`,
      details: {
        recipientCount: targetUsers.length,
        notificationType,
        deliveryChannels,
        priority
      },
      severity: 'Normal',
      isSystemGenerated: true
    });

    logger.info(`Admin ${req.adminUser.email} sent manual notification to ${targetUsers.length} users`);

    res.json({
      success: true,
      message: `Notification sent to ${targetUsers.length} users successfully`,
      data: {
        recipientCount: targetUsers.length,
        notificationIds: notifications.map(n => n._id)
      }
    });

  } catch (error) {
    logger.error('Error sending manual notification:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to send notification'
    });
  }
});

// Get notification analytics
router.get('/analytics', requireAdmin, async (req, res) => {
  try {
    const { period = '30days' } = req.query;

    let startDate;
    switch (period) {
      case '7days':
        startDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '30days':
        startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
        break;
      case '90days':
        startDate = new Date(Date.now() - 90 * 24 * 60 * 60 * 1000);
        break;
      default:
        startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    }

    const [
      notificationsByType,
      notificationsByPriority,
      deliveryStats,
      readStats,
      dailyTrend
    ] = await Promise.all([
      Notification.aggregate([
        { $match: { createdAt: { $gte: startDate } } },
        { $group: { _id: '$type', count: { $sum: 1 } } }
      ]),
      Notification.aggregate([
        { $match: { createdAt: { $gte: startDate } } },
        { $group: { _id: '$priority', count: { $sum: 1 } } }
      ]),
      Notification.aggregate([
        { $match: { createdAt: { $gte: startDate } } },
        {
          $group: {
            _id: null,
            totalInApp: { $sum: { $cond: ['$deliveryChannels.inApp', 1, 0] } },
            totalEmail: { $sum: { $cond: ['$deliveryChannels.email', 1, 0] } },
            totalSMS: { $sum: { $cond: ['$deliveryChannels.sms', 1, 0] } },
            totalPush: { $sum: { $cond: ['$deliveryChannels.push', 1, 0] } }
          }
        }
      ]),
      Notification.aggregate([
        { $match: { createdAt: { $gte: startDate } } },
        {
          $group: {
            _id: null,
            totalNotifications: { $sum: 1 },
            readNotifications: { $sum: { $cond: ['$read', 1, 0] } }
          }
        }
      ]),
      Notification.aggregate([
        { $match: { createdAt: { $gte: startDate } } },
        {
          $group: {
            _id: {
              year: { $year: '$createdAt' },
              month: { $month: '$createdAt' },
              day: { $dayOfMonth: '$createdAt' }
            },
            count: { $sum: 1 }
          }
        },
        { $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 } }
      ])
    ]);

    const readRate = readStats[0] ? 
      ((readStats[0].readNotifications / readStats[0].totalNotifications) * 100).toFixed(1) : 0;

    res.json({
      success: true,
      data: {
        period,
        notificationsByType,
        notificationsByPriority,
        deliveryStats: deliveryStats[0] || {},
        readRate: parseFloat(readRate),
        dailyTrend
      }
    });

  } catch (error) {
    logger.error('Error fetching notification analytics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch notification analytics'
    });
  }
});

// Trigger contract expiration reminders
router.post('/contract-reminders', requireAdmin, async (req, res) => {
  try {
    const { daysBeforeExpiration = 30 } = req.body;

    const expirationDate = new Date();
    expirationDate.setDate(expirationDate.getDate() + daysBeforeExpiration);

    const expiringContracts = await Contract.find({
      endDate: { $lte: expirationDate },
      status: 'Active'
    }).populate('userId', 'firstName lastName email');

    const notifications = expiringContracts.map(contract => ({
      userId: contract.userId._id,
      type: 'ContractExpiration',
      title: 'Contract Expiration Reminder',
      message: `Your energy contract will expire on ${contract.endDate.toLocaleDateString()}. Please review your options.`,
      priority: 'High',
      deliveryChannels: {
        inApp: true,
        email: true,
        sms: false,
        push: true
      },
      relatedEntity: {
        entityType: 'Contract',
        entityId: contract._id
      }
    }));

    if (notifications.length > 0) {
      await Notification.insertMany(notifications);
    }

    logger.info(`Admin ${req.adminUser.email} triggered ${notifications.length} contract expiration reminders`);

    res.json({
      success: true,
      message: `Contract expiration reminders sent to ${notifications.length} users`,
      data: {
        remindersSent: notifications.length,
        contractsExpiring: expiringContracts.length
      }
    });

  } catch (error) {
    logger.error('Error triggering contract reminders:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to trigger contract reminders'
    });
  }
});

module.exports = router;
