# Manual Conversion Instructions

If you encounter issues with the automated conversion script, you can manually convert the HTML files to PDF and PNG using these methods:

## Method 1: Using a Web Browser

1. Open each HTML file in a web browser (Chrome, Firefox, Edge, etc.)
2. Use the browser's print function (Ctrl+P or Cmd+P)
3. Select "Save as PDF" as the destination
4. Save the file to the `pdf` folder with the same base name

## Method 2: Using Online Conversion Tools

You can use online HTML to PDF conversion tools:

1. [HTML2PDF.com](https://html2pdf.com/)
2. [PDFShift](https://pdfshift.io/)
3. [WeasyPrint Online](https://weasyprint.org/)

## Method 3: Taking Screenshots

For PNG versions:

1. Open each HTML file in a browser
2. Use a screenshot tool to capture the entire page
3. Save the screenshot to the `png` folder with the same base name

## Expected Files After Conversion

After conversion, you should have the following files:

- `pdf/edf-sample-invoice.pdf`
- `pdf/engie-sample-invoice.pdf`
- `pdf/totalenergies-sample-invoice.pdf`
- `png/edf-sample-invoice.png`
- `png/engie-sample-invoice.png`
- `png/totalenergies-sample-invoice.png`
