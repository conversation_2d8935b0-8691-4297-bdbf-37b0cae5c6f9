.clients-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.clients-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.clients-header h1 {
  font-size: 24px;
  font-weight: 600;
  color: #000;
  margin: 0;
}

.add-client-btn {
  background: linear-gradient(to right, #000, #333);
  color: #fff;
  border: none;
  border-radius: 6px;
  padding: 10px 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: all 0.2s ease;
}

.add-client-btn i {
  margin-right: 8px;
}

.add-client-btn:hover {
  opacity: 0.9;
  transform: translateY(-2px);
}

.clients-filters {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 15px;
}

.search-container {
  position: relative;
  flex: 1;
  min-width: 200px;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #666;
}

.search-input {
  width: 100%;
  padding: 10px 10px 10px 35px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.search-input:focus {
  border-color: #000;
  outline: none;
}

.filter-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.filter-btn {
  padding: 8px 15px;
  border: 1px solid #ddd;
  border-radius: 6px;
  background-color: #fff;
  color: #333;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.filter-btn.active {
  background-color: #000;
  color: #fff;
  border-color: #000;
}

.filter-btn:hover:not(.active) {
  background-color: #f5f5f5;
}

.clients-table-container {
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  border: 1px solid #000;
}

.clients-table {
  width: 100%;
  border-collapse: collapse;
}

.clients-table th,
.clients-table td {
  padding: 15px;
  text-align: left;
  border-bottom: 1px solid #eaeaea;
}

.clients-table th {
  background-color: #f9f9f9;
  font-weight: 600;
  color: #333;
  font-size: 14px;
}

.clients-table tr:last-child td {
  border-bottom: none;
}

.clients-table tbody tr {
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.clients-table tbody tr:hover {
  background-color: #f9f9f9;
}

.client-name {
  font-weight: 500;
  color: #000;
}

.client-type {
  display: inline-block;
  padding: 5px 10px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
}

.client-type.individual {
  background-color: #e3f2fd;
  color: #1565c0;
}

.client-type.business {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.client-status {
  display: inline-block;
  padding: 5px 10px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
}

.status-active {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.status-inactive {
  background-color: #f5f5f5;
  color: #757575;
}

.status-pending {
  background-color: #fff8e1;
  color: #f57f17;
}

.client-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #fff;
}

.view-btn {
  background-color: #000;
}

.edit-btn {
  background-color: #333;
}

.message-btn {
  background-color: #555;
}

.action-btn:hover {
  opacity: 0.9;
  transform: translateY(-2px);
}

.no-clients {
  text-align: center;
  padding: 30px;
  color: #666;
  font-style: italic;
}

/* Responsive styles */
@media (max-width: 992px) {
  .hide-tablet {
    display: none;
  }
}

@media (max-width: 768px) {
  .clients-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
  
  .clients-filters {
    flex-direction: column;
    align-items: stretch;
  }
  
  .hide-mobile {
    display: none;
  }
}
