import { useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { getItem, STORAGE_KEYS } from '../utils/localStorage';
import logger from '../utils/logger';

/**
 * AdminRouteGuard - Ensures admin users are always redirected to dashboard
 * This component should be used to wrap any route that admin users shouldn't access
 */
const AdminRouteGuard = ({ children }) => {
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    const checkAdminUser = () => {
      try {
        // Check if user is admin
        const userType = getItem(STORAGE_KEYS.USER_TYPE);
        
        if (userType && userType.toLowerCase() === 'admin') {
          logger.info(`🔑 AdminRouteGuard: Admin user detected on ${location.pathname} - Redirecting to dashboard`);
          
          // Admin users should only be on dashboard
          if (location.pathname !== '/dashboard') {
            navigate('/dashboard', { replace: true });
            return;
          }
        }
      } catch (error) {
        logger.error('AdminRouteGuard: Error checking admin user:', error);
      }
    };

    checkAdminUser();
  }, [location.pathname, navigate]);

  return children;
};

export default AdminRouteGuard;
