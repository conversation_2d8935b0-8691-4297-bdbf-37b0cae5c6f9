import React, { useState, useEffect } from 'react';
import PersonalInfoStep from './wizard-steps/PersonalInfoStep';
import AddressMeterInfoStep from './wizard-steps/AddressMeterInfoStep';
import ConfirmationStep from './wizard-steps/ConfirmationStep';
import StepperProgress from './StepperProgress';
import { useForceScrollToTopAuthenticated } from '../utils/scrollToTop';
import '../styles/stepper.css';

const IndividualInfoWizard = ({ onSubmit, onCancel, userData }) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // Initialize form data with default values
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phoneNumber: '',
    streetAddress: '',
    city: '',
    postalCode: '',
    country: 'France',
    meterNumber: '',
    authorizeDataAccess: false
  });

  // Update form data when userData changes
  useEffect(() => {
    if (userData) {
      console.log('Updating form data with userData:', userData);
      setFormData(prevData => ({
        ...prevData,
        firstName: userData.firstName || prevData.firstName,
        lastName: userData.lastName || prevData.lastName,
        email: userData.email || prevData.email,
        phoneNumber: userData.phoneNumber || prevData.phoneNumber,
        streetAddress: userData.streetAddress || prevData.streetAddress,
        city: userData.city || prevData.city,
        postalCode: userData.postalCode || prevData.postalCode,
        country: userData.country || 'France',
        meterNumber: userData.meterNumber || prevData.meterNumber,
        authorizeDataAccess: userData.authorizeDataAccess || prevData.authorizeDataAccess
      }));
    }
  }, [userData]);

  // Prevent page refresh/navigation when there are unsaved changes
  useEffect(() => {
    const handleBeforeUnload = (event) => {
      if (hasUnsavedChanges) {
        event.preventDefault();
        event.returnValue = 'You have unsaved changes. Are you sure you want to leave?';
        return event.returnValue;
      }
    };

    const handlePopState = (event) => {
      if (hasUnsavedChanges) {
        const confirmLeave = window.confirm('You have unsaved changes. Are you sure you want to leave?');
        if (!confirmLeave) {
          event.preventDefault();
          window.history.pushState(null, '', window.location.pathname);
        }
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    window.addEventListener('popstate', handlePopState);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      window.removeEventListener('popstate', handlePopState);
    };
  }, [hasUnsavedChanges]);

  // Force scroll to top whenever step changes
  useEffect(() => {
    console.log('🔄 IndividualInfoWizard: Step changed to:', currentStep);
    useForceScrollToTopAuthenticated();
  }, [currentStep]);

  const totalSteps = 3;
  const stepLabels = ['Personal Info', 'Address & Meter', 'Confirmation'];

  const handleNextStep = () => {
    console.log('🔄 IndividualInfoWizard: Moving to next step');
    setCurrentStep(prev => Math.min(prev + 1, totalSteps));

    // Force scroll to top when moving to next step
    setTimeout(() => {
      useForceScrollToTopAuthenticated();
    }, 50); // Small delay to ensure state update completes
  };

  const handlePrevStep = () => {
    console.log('🔄 IndividualInfoWizard: Moving to previous step');
    setCurrentStep(prev => Math.max(prev - 1, 1));

    // Force scroll to top when moving to previous step
    setTimeout(() => {
      useForceScrollToTopAuthenticated();
    }, 50); // Small delay to ensure state update completes
  };

  const handleChange = (field, value) => {
    console.log('Form field changed:', field, value);
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Mark as having unsaved changes
    setHasUnsavedChanges(true);
  };

  const handleSubmit = () => {
    // Clear unsaved changes flag since we're submitting
    setHasUnsavedChanges(false);
    onSubmit(formData);
  };

  return (
    <div className="stepper-container">
      <div className="stepper-content">
        <StepperProgress
          currentStep={currentStep}
          steps={stepLabels}
        />

        {/* Unsaved changes indicator */}
        {hasUnsavedChanges && (
          <div style={{
            padding: '0.5rem 1rem',
            background: '#fff3cd',
            border: '1px solid #ffeaa7',
            borderRadius: '4px',
            marginBottom: '1rem',
            fontSize: '0.875rem',
            color: '#856404',
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem'
          }}>
            <span style={{ fontSize: '1rem' }}>⚠️</span>
            You have unsaved changes. Please complete the form or your progress will be lost.
          </div>
        )}

        <div className="stepper-form">
        {currentStep === 1 && (
          <PersonalInfoStep
            formData={formData}
            onChange={handleChange}
            onNext={handleNextStep}
            onCancel={onCancel}
          />
        )}

        {currentStep === 2 && (
          <AddressMeterInfoStep
            formData={formData}
            onChange={handleChange}
            onNext={handleNextStep}
            onPrev={handlePrevStep}
          />
        )}

        {currentStep === 3 && (
          <ConfirmationStep
            formData={formData}
            onChange={handleChange}
            onSubmit={handleSubmit}
            onPrev={handlePrevStep}
            onCancel={onCancel}
          />
        )}
        </div>
      </div>
    </div>
  );
};

export default IndividualInfoWizard;
