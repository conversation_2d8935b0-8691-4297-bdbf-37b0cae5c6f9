import api from './api';
import logger from '../utils/logger';

class SupplierService {
  /**
   * Get supplier dashboard statistics
   */
  async getDashboardStats() {
    try {
      logger.info('Fetching supplier dashboard stats');

      // Get cognitoId from localStorage
      const cognitoId = localStorage.getItem('cognitoId');
      if (!cognitoId) {
        throw new Error('No cognito ID found');
      }

      const response = await api.get(`/api/supplier/dashboard/stats/${cognitoId}`);
      logger.info('Supplier dashboard stats fetched successfully');
      return response.data;
    } catch (error) {
      logger.error('Error fetching supplier dashboard stats:', error);

      // Return mock data for development
      return {
        success: true,
        data: {
          totalOffers: 0,
          activeContracts: 0,
          totalRevenue: 0,
          newCustomers: 0,
          totalAppointments: 0,
          recentOffers: [],
          recentContracts: [],
          recentAppointments: []
        }
      };
    }
  }

  /**
   * Get active offers for supplier
   */
  async getActiveOffers() {
    try {
      logger.info('Fetching supplier active offers');
      const response = await api.get('/api/supplier/offers/active');
      logger.info('Supplier active offers fetched successfully');
      return response.data;
    } catch (error) {
      logger.error('Error fetching supplier active offers:', error);

      // Return mock data for development
      return {
        success: true,
        data: [
          {
            id: '1',
            name: 'Green Energy Fixed Rate',
            energyType: 'Electricity',
            rateType: 'Fixed',
            baseRate: 0.145,
            views: 234,
            applications: 12,
            status: 'Active'
          },
          {
            id: '2',
            name: 'Business Gas Plan',
            energyType: 'Gas',
            rateType: 'Variable',
            baseRate: 0.065,
            views: 156,
            applications: 8,
            status: 'Active'
          },
          {
            id: '3',
            name: 'Dual Fuel Package',
            energyType: 'Both',
            rateType: 'Fixed',
            baseRate: 0.135,
            views: 189,
            applications: 15,
            status: 'Active'
          }
        ]
      };
    }
  }

  /**
   * Get recent contracts for supplier
   */
  async getRecentContracts() {
    try {
      logger.info('Fetching supplier recent contracts');
      const response = await api.get('/api/supplier/contracts/recent');
      logger.info('Supplier recent contracts fetched successfully');
      return response.data;
    } catch (error) {
      logger.error('Error fetching supplier recent contracts:', error);

      // Return mock data for development
      return {
        success: true,
        data: [
          {
            id: '1',
            customerName: 'Acme Corporation',
            contractNumber: 'CNT-2024-001',
            startDate: '2024-01-15',
            status: 'Active',
            monthlyValue: 850
          },
          {
            id: '2',
            customerName: 'Tech Solutions Ltd',
            contractNumber: 'CNT-2024-002',
            startDate: '2024-01-20',
            status: 'Pending',
            monthlyValue: 1200
          },
          {
            id: '3',
            customerName: 'Green Manufacturing',
            contractNumber: 'CNT-2024-003',
            startDate: '2024-01-25',
            status: 'Active',
            monthlyValue: 2100
          }
        ]
      };
    }
  }

  /**
   * Create a new energy offer
   */
  async createOffer(offerData) {
    try {
      logger.info('Creating new supplier offer:', offerData);
      const response = await api.post('/api/supplier/offers', offerData);
      logger.info('Supplier offer created successfully');
      return response.data;
    } catch (error) {
      logger.error('Error creating supplier offer:', error);
      throw error;
    }
  }

  /**
   * Update an existing offer
   */
  async updateOffer(offerId, offerData) {
    try {
      logger.info('Updating supplier offer:', offerId, offerData);
      const response = await api.put(`/api/supplier/offers/${offerId}`, offerData);
      logger.info('Supplier offer updated successfully');
      return response.data;
    } catch (error) {
      logger.error('Error updating supplier offer:', error);
      throw error;
    }
  }

  /**
   * Delete an offer
   */
  async deleteOffer(offerId) {
    try {
      logger.info('Deleting supplier offer:', offerId);
      const response = await api.delete(`/api/supplier/offers/${offerId}`);
      logger.info('Supplier offer deleted successfully');
      return response.data;
    } catch (error) {
      logger.error('Error deleting supplier offer:', error);
      throw error;
    }
  }

  /**
   * Get all offers for supplier
   */
  async getAllOffers() {
    try {
      logger.info('Fetching all supplier offers');
      const response = await api.get('/api/supplier/offers');
      logger.info('All supplier offers fetched successfully');
      return response.data;
    } catch (error) {
      logger.error('Error fetching all supplier offers:', error);
      throw error;
    }
  }

  /**
   * Get offer applications
   */
  async getOfferApplications(offerId) {
    try {
      logger.info('Fetching offer applications for:', offerId);
      const response = await api.get(`/api/supplier/offers/${offerId}/applications`);
      logger.info('Offer applications fetched successfully');
      return response.data;
    } catch (error) {
      logger.error('Error fetching offer applications:', error);
      throw error;
    }
  }

  /**
   * Approve or reject an offer application
   */
  async processApplication(applicationId, action, notes = '') {
    try {
      logger.info('Processing offer application:', applicationId, action);
      const response = await api.post(`/api/supplier/applications/${applicationId}/process`, {
        action,
        notes
      });
      logger.info('Offer application processed successfully');
      return response.data;
    } catch (error) {
      logger.error('Error processing offer application:', error);
      throw error;
    }
  }

  /**
   * Get supplier analytics data
   */
  async getAnalytics(period = '30d') {
    try {
      logger.info('Fetching supplier analytics for period:', period);
      const response = await api.get(`/api/supplier/analytics?period=${period}`);
      logger.info('Supplier analytics fetched successfully');
      return response.data;
    } catch (error) {
      logger.error('Error fetching supplier analytics:', error);

      // Return mock data for development
      return {
        success: true,
        data: {
          revenue: {
            current: 25750,
            previous: 23200,
            growth: 11.0
          },
          customers: {
            total: 45,
            new: 8,
            retention: 94.2
          },
          offers: {
            views: 1250,
            applications: 89,
            conversions: 23
          }
        }
      };
    }
  }

  /**
   * Get supplier profile
   */
  async getProfile() {
    try {
      logger.info('Fetching supplier profile');
      const response = await api.get('/api/supplier/profile');
      logger.info('Supplier profile fetched successfully');
      return response.data;
    } catch (error) {
      logger.error('Error fetching supplier profile:', error);
      throw error;
    }
  }

  /**
   * Update supplier profile
   */
  async updateProfile(profileData) {
    try {
      logger.info('Updating supplier profile:', profileData);
      const response = await api.put('/api/supplier/profile', profileData);
      logger.info('Supplier profile updated successfully');
      return response.data;
    } catch (error) {
      logger.error('Error updating supplier profile:', error);
      throw error;
    }
  }
}

export default new SupplierService();
