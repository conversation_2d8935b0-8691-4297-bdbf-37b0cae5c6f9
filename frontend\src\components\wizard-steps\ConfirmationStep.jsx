import React from 'react';

const ConfirmationStep = ({ formData, onChange, onSubmit, onPrev, onCancel }) => {
  const handleChange = (e) => {
    const { name, checked } = e.target;
    onChange(name, checked);
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (formData.authorizeDataAccess) {
      console.log('Authorization checkbox checked, submitting form');

      // Call the onSubmit function
      onSubmit();

      // No fallback redirect needed - the parent component handles this
    } else {
      console.warn('Authorization checkbox not checked, cannot submit form');
    }
  };

  return (
    <div>
      <div className="stepper-header">
        <h3 className="page-title">Confirmation</h3>
        <p className="page-subtitle">
          Please review your information below and confirm that all details are correct
          before submitting your application.
        </p>
      </div>
      <form onSubmit={handleSubmit}>
        <div className="confirmation-summary">
          <h4>Review Your Information</h4>

          <div className="summary-section">
            <h5>Personal Information</h5>
            <div className="summary-row">
              <span className="summary-label">Name:</span>
              <span className="summary-value">{formData.firstName} {formData.lastName}</span>
            </div>
            <div className="summary-row">
              <span className="summary-label">Email:</span>
              <span className="summary-value">{formData.email}</span>
            </div>
            <div className="summary-row">
              <span className="summary-label">Phone:</span>
              <span className="summary-value">{formData.phoneNumber || 'Not provided'}</span>
            </div>
          </div>

          <div className="summary-section">
            <h5>Address</h5>
            <div className="summary-row">
              <span className="summary-label">Street Address:</span>
              <span className="summary-value">{formData.streetAddress}</span>
            </div>
            <div className="summary-row">
              <span className="summary-label">City:</span>
              <span className="summary-value">{formData.city}</span>
            </div>
            <div className="summary-row">
              <span className="summary-label">Postal Code:</span>
              <span className="summary-value">{formData.postalCode}</span>
            </div>
            <div className="summary-row">
              <span className="summary-label">Country:</span>
              <span className="summary-value">{formData.country}</span>
            </div>
          </div>

          <div className="summary-section">
            <h5>Meter Information</h5>
            <div className="summary-row">
              <span className="summary-label">PDL/PRM/RAE Number:</span>
              <span className="summary-value">{formData.meterNumber}</span>
            </div>
          </div>
        </div>

        <div className="form-group">
          <div className="checkbox-item authorization-checkbox">
            <input
              type="checkbox"
              id="authorizeDataAccess"
              name="authorizeDataAccess"
              checked={formData.authorizeDataAccess}
              onChange={handleChange}
            />
            <label htmlFor="authorizeDataAccess">
              I authorize My Energy Bill and its partners to access my consumption data for the past 12 months in order to provide offers <span className="required">*</span>
            </label>
          </div>
          {!formData.authorizeDataAccess && (
            <div className="error-message">Please authorize data access to continue</div>
          )}
        </div>

        <div className="stepper-buttons">
          <button type="button" className="stepper-button stepper-button-prev" onClick={onPrev}>
            Back
          </button>
          <button
            type="submit"
            className="stepper-button stepper-button-next"
            disabled={!formData.authorizeDataAccess}
          >
            Submit
          </button>
        </div>
      </form>
    </div>
  );
};

export default ConfirmationStep;
