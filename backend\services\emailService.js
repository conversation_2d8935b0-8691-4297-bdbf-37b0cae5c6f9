const AWS = require('aws-sdk');
const logger = require('../utils/logger');
const dotenv = require('dotenv');
const path = require('path');

// Load environment variables if not already loaded
if (!process.env.FROM_EMAIL) {
  const cliEnv = process.argv[2];
  const nodeEnv = cliEnv || process.env.NODE_ENV || 'local';
  const envFile = `.env.${nodeEnv}`;

  dotenv.config({
    path: path.resolve(__dirname, `../${envFile}`),
    override: false,
  });

  logger.info(`EmailService: Loaded environment from ${envFile}`);
}

// Configure AWS SES
const ses = new AWS.SES({
  region: process.env.AWS_REGION || 'eu-west-1',
  accessKeyId: process.env.AWS_ACCESS_KEY_ID,
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
});

class EmailService {
  constructor() {
    this.fromEmail = process.env.FROM_EMAIL || '<EMAIL>';
    this.companyName = process.env.COMPANY_NAME || 'Energy Platform';
    this.frontendUrl = process.env.FRONTEND_URL || 'http://localhost:5173';
  }

  async sendInvitationEmail(invitation) {
    try {
      const { userType, email, inviteeDetails, invitationToken } = invitation;
      
      const registrationUrl = `${this.frontendUrl}/register/invited?token=${invitationToken}&type=${userType.toLowerCase()}`;
      
      const emailTemplate = this.getInvitationEmailTemplate(userType, inviteeDetails, registrationUrl);
      
      const params = {
        Source: this.fromEmail,
        Destination: {
          ToAddresses: [email]
        },
        Message: {
          Subject: {
            Data: emailTemplate.subject,
            Charset: 'UTF-8'
          },
          Body: {
            Html: {
              Data: emailTemplate.htmlBody,
              Charset: 'UTF-8'
            },
            Text: {
              Data: emailTemplate.textBody,
              Charset: 'UTF-8'
            }
          }
        },
        Tags: [
          {
            Name: 'EmailType',
            Value: 'Invitation'
          },
          {
            Name: 'UserType',
            Value: userType
          }
        ]
      };

      const result = await ses.sendEmail(params).promise();
      
      logger.info(`Invitation email sent successfully to ${email}`, {
        messageId: result.MessageId,
        userType,
        invitationToken
      });

      return {
        success: true,
        messageId: result.MessageId
      };

    } catch (error) {
      logger.error('Failed to send invitation email:', error);
      throw new Error(`Failed to send invitation email: ${error.message}`);
    }
  }

  async sendReminderEmail(invitation) {
    try {
      const { userType, email, inviteeDetails, invitationToken, remindersSent } = invitation;
      
      const registrationUrl = `${this.frontendUrl}/register/invited?token=${invitationToken}&type=${userType.toLowerCase()}`;
      
      const emailTemplate = this.getReminderEmailTemplate(userType, inviteeDetails, registrationUrl, remindersSent);
      
      const params = {
        Source: this.fromEmail,
        Destination: {
          ToAddresses: [email]
        },
        Message: {
          Subject: {
            Data: emailTemplate.subject,
            Charset: 'UTF-8'
          },
          Body: {
            Html: {
              Data: emailTemplate.htmlBody,
              Charset: 'UTF-8'
            },
            Text: {
              Data: emailTemplate.textBody,
              Charset: 'UTF-8'
            }
          }
        },
        Tags: [
          {
            Name: 'EmailType',
            Value: 'Reminder'
          },
          {
            Name: 'UserType',
            Value: userType
          }
        ]
      };

      const result = await ses.sendEmail(params).promise();
      
      logger.info(`Reminder email sent successfully to ${email}`, {
        messageId: result.MessageId,
        userType,
        reminderNumber: remindersSent + 1
      });

      return {
        success: true,
        messageId: result.MessageId
      };

    } catch (error) {
      logger.error('Failed to send reminder email:', error);
      throw new Error(`Failed to send reminder email: ${error.message}`);
    }
  }

  getInvitationEmailTemplate(userType, inviteeDetails, registrationUrl) {
    const name = inviteeDetails.firstName ? 
      `${inviteeDetails.firstName} ${inviteeDetails.lastName || ''}`.trim() : 
      'there';
    
    const userTypeDisplay = userType === 'Broker' ? 'Energy Broker' : 'Energy Supplier';
    
    const subject = `Invitation to Join ${this.companyName} as ${userTypeDisplay}`;
    
    const htmlBody = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${subject}</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f4f4f4; }
        .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; border-radius: 10px; overflow: hidden; box-shadow: 0 0 20px rgba(0,0,0,0.1); }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; }
        .header h1 { margin: 0; font-size: 28px; font-weight: 300; }
        .content { padding: 40px 30px; }
        .greeting { font-size: 18px; margin-bottom: 20px; color: #2c3e50; }
        .message { margin-bottom: 30px; font-size: 16px; line-height: 1.8; }
        .cta-button { display: inline-block; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 15px 30px; text-decoration: none; border-radius: 50px; font-weight: 600; font-size: 16px; margin: 20px 0; transition: transform 0.2s; }
        .cta-button:hover { transform: translateY(-2px); }
        .details { background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .details h3 { margin-top: 0; color: #495057; }
        .footer { background-color: #f8f9fa; padding: 20px 30px; text-align: center; font-size: 14px; color: #6c757d; }
        .expiry { background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0; }
        .expiry strong { color: #856404; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>${this.companyName}</h1>
            <p>Energy Management Platform</p>
        </div>
        
        <div class="content">
            <div class="greeting">Hello ${name},</div>
            
            <div class="message">
                <p>You have been invited to join <strong>${this.companyName}</strong> as a <strong>${userTypeDisplay}</strong>!</p>
                
                <p>Our platform connects energy professionals with clients seeking the best energy solutions. As a ${userTypeDisplay.toLowerCase()}, you'll have access to:</p>
                
                ${userType === 'Broker' ? `
                <ul>
                    <li>🎯 <strong>Client Management</strong> - Manage your assigned clients and their energy needs</li>
                    <li>📊 <strong>Quote Comparison Tools</strong> - Generate and send competitive energy quotes</li>
                    <li>💰 <strong>Commission Tracking</strong> - Monitor your earnings and commission payments</li>
                    <li>📈 <strong>Performance Analytics</strong> - Track your success metrics and growth</li>
                    <li>🤝 <strong>Supplier Network</strong> - Access to our verified supplier partners</li>
                </ul>
                ` : `
                <ul>
                    <li>🏢 <strong>Offer Management</strong> - Publish and manage your energy offers</li>
                    <li>📋 <strong>Tariff Grid Upload</strong> - Easy CSV import for your pricing structures</li>
                    <li>🔗 <strong>API Integration</strong> - Connect your systems for real-time pricing</li>
                    <li>📊 <strong>Market Analytics</strong> - Insights into market trends and competition</li>
                    <li>🎯 <strong>Lead Generation</strong> - Access to qualified energy customers</li>
                </ul>
                `}
            </div>
            
            <div style="text-align: center;">
                <a href="${registrationUrl}" class="cta-button">Complete Your Registration</a>
            </div>
            
            <div class="expiry">
                <strong>⏰ Important:</strong> This invitation expires in 7 days. Please complete your registration before the link expires.
            </div>
            
            <div class="details">
                <h3>What happens next?</h3>
                <ol>
                    <li><strong>Click the registration button</strong> above to access the secure registration form</li>
                    <li><strong>Complete your profile</strong> with your professional information</li>
                    <li><strong>Wait for approval</strong> - Our admin team will review and approve your account</li>
                    <li><strong>Start using the platform</strong> once your account is activated</li>
                </ol>
            </div>
            
            <div class="message">
                <p>If you have any questions about this invitation or need assistance with the registration process, please don't hesitate to contact our support team.</p>
                
                <p>We look forward to welcoming you to our platform!</p>
                
                <p>Best regards,<br>
                <strong>The ${this.companyName} Team</strong></p>
            </div>
        </div>
        
        <div class="footer">
            <p>This invitation was sent to ${inviteeDetails.email || email}. If you received this email in error, please ignore it.</p>
            <p>&copy; ${new Date().getFullYear()} ${this.companyName}. All rights reserved.</p>
        </div>
    </div>
</body>
</html>`;

    const textBody = `
Hello ${name},

You have been invited to join ${this.companyName} as a ${userTypeDisplay}!

Our platform connects energy professionals with clients seeking the best energy solutions.

To complete your registration, please visit:
${registrationUrl}

This invitation expires in 7 days.

What happens next?
1. Click the registration link above
2. Complete your profile with your professional information  
3. Wait for approval from our admin team
4. Start using the platform once activated

If you have any questions, please contact our support team.

Best regards,
The ${this.companyName} Team

---
This invitation was sent to ${inviteeDetails.email || email}.
© ${new Date().getFullYear()} ${this.companyName}. All rights reserved.
`;

    return {
      subject,
      htmlBody,
      textBody
    };
  }

  getReminderEmailTemplate(userType, inviteeDetails, registrationUrl, reminderNumber) {
    const name = inviteeDetails.firstName ? 
      `${inviteeDetails.firstName} ${inviteeDetails.lastName || ''}`.trim() : 
      'there';
    
    const userTypeDisplay = userType === 'Broker' ? 'Energy Broker' : 'Energy Supplier';
    
    const subject = `Reminder: Complete Your ${this.companyName} Registration`;
    
    const htmlBody = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${subject}</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f4f4f4; }
        .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; border-radius: 10px; overflow: hidden; box-shadow: 0 0 20px rgba(0,0,0,0.1); }
        .header { background: linear-gradient(135deg, #f39c12 0%, #e74c3c 100%); color: white; padding: 30px; text-align: center; }
        .header h1 { margin: 0; font-size: 28px; font-weight: 300; }
        .content { padding: 40px 30px; }
        .greeting { font-size: 18px; margin-bottom: 20px; color: #2c3e50; }
        .message { margin-bottom: 30px; font-size: 16px; line-height: 1.8; }
        .cta-button { display: inline-block; background: linear-gradient(135deg, #f39c12 0%, #e74c3c 100%); color: white; padding: 15px 30px; text-decoration: none; border-radius: 50px; font-weight: 600; font-size: 16px; margin: 20px 0; transition: transform 0.2s; }
        .cta-button:hover { transform: translateY(-2px); }
        .footer { background-color: #f8f9fa; padding: 20px 30px; text-align: center; font-size: 14px; color: #6c757d; }
        .urgent { background-color: #fff3cd; border-left: 4px solid #f39c12; padding: 15px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>⏰ Registration Reminder</h1>
            <p>${this.companyName}</p>
        </div>
        
        <div class="content">
            <div class="greeting">Hello ${name},</div>
            
            <div class="message">
                <p>This is a friendly reminder that you have a pending invitation to join <strong>${this.companyName}</strong> as a <strong>${userTypeDisplay}</strong>.</p>
                
                <p>We noticed you haven't completed your registration yet, and we don't want you to miss this opportunity!</p>
            </div>
            
            <div class="urgent">
                <strong>⚠️ Your invitation will expire soon!</strong><br>
                Please complete your registration as soon as possible to secure your access to our platform.
            </div>
            
            <div style="text-align: center;">
                <a href="${registrationUrl}" class="cta-button">Complete Registration Now</a>
            </div>
            
            <div class="message">
                <p>If you're experiencing any issues with the registration process or have questions about the platform, please don't hesitate to reach out to our support team.</p>
                
                <p>We're excited to have you join our growing network of energy professionals!</p>
                
                <p>Best regards,<br>
                <strong>The ${this.companyName} Team</strong></p>
            </div>
        </div>
        
        <div class="footer">
            <p>This is reminder #${reminderNumber + 1} for the invitation sent to ${inviteeDetails.email || email}.</p>
            <p>&copy; ${new Date().getFullYear()} ${this.companyName}. All rights reserved.</p>
        </div>
    </div>
</body>
</html>`;

    const textBody = `
Hello ${name},

This is a friendly reminder that you have a pending invitation to join ${this.companyName} as a ${userTypeDisplay}.

Your invitation will expire soon! Please complete your registration as soon as possible.

Registration link: ${registrationUrl}

If you have any questions, please contact our support team.

Best regards,
The ${this.companyName} Team

---
This is reminder #${reminderNumber + 1} for ${inviteeDetails.email || email}.
© ${new Date().getFullYear()} ${this.companyName}. All rights reserved.
`;

    return {
      subject,
      htmlBody,
      textBody
    };
  }

  getPasswordResetEmailTemplate(userName, resetUrl, userEmail) {
    const displayName = userName || 'User';
    const subject = `Reset Your ${this.companyName} Password`;

    const htmlBody = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Password Reset - ${this.companyName}</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif; margin: 0; padding: 0; background-color: #f8f9fa; }
        .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; }
        .header { background: linear-gradient(135deg, #000000 0%, #**********%); color: #ffffff; padding: 40px 30px; text-align: center; }
        .header h1 { margin: 0; font-size: 28px; font-weight: 700; }
        .content { padding: 40px 30px; }
        .content h2 { color: #111827; font-size: 24px; margin-bottom: 20px; }
        .content p { color: #374151; font-size: 16px; line-height: 1.6; margin-bottom: 20px; }
        .reset-button { display: inline-block; background: linear-gradient(135deg, #000000 0%, #**********%); color: #ffffff; text-decoration: none; padding: 16px 32px; border-radius: 8px; font-weight: 600; font-size: 16px; margin: 20px 0; }
        .reset-button:hover { opacity: 0.9; }
        .security-notice { background: #fef3c7; border-left: 4px solid #f59e0b; padding: 20px; margin: 30px 0; border-radius: 4px; }
        .security-notice h3 { color: #92400e; margin: 0 0 10px 0; font-size: 18px; }
        .security-notice p { color: #92400e; margin: 0; font-size: 14px; }
        .footer { background: #f8f9fa; padding: 30px; text-align: center; border-top: 1px solid #e5e7eb; }
        .footer p { color: #6b7280; font-size: 14px; margin: 5px 0; }
        .footer a { color: #111827; text-decoration: none; }
        .expiry-info { background: #f3f4f6; padding: 15px; border-radius: 6px; margin: 20px 0; }
        .expiry-info p { margin: 0; color: #374151; font-size: 14px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>${this.companyName}</h1>
            <p style="margin: 10px 0 0 0; opacity: 0.9;">Password Reset Request</p>
        </div>

        <div class="content">
            <h2>Hello ${displayName},</h2>

            <p>We received a request to reset your password for your ${this.companyName} account. If you made this request, click the button below to reset your password:</p>

            <div style="text-align: center; margin: 30px 0;">
                <a href="${resetUrl}" class="reset-button">Reset My Password</a>
            </div>

            <div class="expiry-info">
                <p><strong>⏰ This link expires in 1 hour</strong> for your security.</p>
            </div>

            <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
            <p style="word-break: break-all; background: #f3f4f6; padding: 10px; border-radius: 4px; font-family: monospace; font-size: 14px;">${resetUrl}</p>

            <div class="security-notice">
                <h3>🔒 Security Notice</h3>
                <p>If you didn't request this password reset, please ignore this email. Your password will remain unchanged. For security concerns, contact our support team immediately.</p>
            </div>

            <p>Best regards,<br>The ${this.companyName} Team</p>
        </div>

        <div class="footer">
            <p>This email was sent to <strong>${userEmail}</strong></p>
            <p>© ${new Date().getFullYear()} ${this.companyName}. All rights reserved.</p>
            <p>Need help? Contact us at <a href="mailto:<EMAIL>"><EMAIL></a></p>
        </div>
    </div>
</body>
</html>`;

    const textBody = `
Password Reset Request - ${this.companyName}

Hello ${displayName},

We received a request to reset your password for your ${this.companyName} account.

To reset your password, please visit the following link:
${resetUrl}

⏰ This link expires in 1 hour for your security.

🔒 Security Notice:
If you didn't request this password reset, please ignore this email. Your password will remain unchanged.

If you have any security concerns, please contact our support team immediately.

Best regards,
The ${this.companyName} Team

---
This email was sent to ${userEmail}
© ${new Date().getFullYear()} ${this.companyName}. All rights reserved.
Need help? Contact <NAME_EMAIL>
`;

    return {
      subject,
      htmlBody,
      textBody
    };
  }

  getPasswordResetConfirmationTemplate(userName, userEmail) {
    const displayName = userName || 'User';
    const subject = `Password Successfully Reset - ${this.companyName}`;

    const htmlBody = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Password Reset Confirmation - ${this.companyName}</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif; margin: 0; padding: 0; background-color: #f8f9fa; }
        .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; }
        .header { background: linear-gradient(135deg, #10b981 0%, #**********%); color: #ffffff; padding: 40px 30px; text-align: center; }
        .header h1 { margin: 0; font-size: 28px; font-weight: 700; }
        .content { padding: 40px 30px; }
        .content h2 { color: #111827; font-size: 24px; margin-bottom: 20px; }
        .content p { color: #374151; font-size: 16px; line-height: 1.6; margin-bottom: 20px; }
        .success-icon { font-size: 48px; text-align: center; margin: 20px 0; }
        .login-button { display: inline-block; background: linear-gradient(135deg, #000000 0%, #**********%); color: #ffffff; text-decoration: none; padding: 16px 32px; border-radius: 8px; font-weight: 600; font-size: 16px; margin: 20px 0; }
        .security-tips { background: #f0f9ff; border-left: 4px solid #0ea5e9; padding: 20px; margin: 30px 0; border-radius: 4px; }
        .security-tips h3 { color: #0c4a6e; margin: 0 0 15px 0; font-size: 18px; }
        .security-tips ul { color: #0c4a6e; margin: 0; padding-left: 20px; }
        .security-tips li { margin-bottom: 8px; font-size: 14px; }
        .footer { background: #f8f9fa; padding: 30px; text-align: center; border-top: 1px solid #e5e7eb; }
        .footer p { color: #6b7280; font-size: 14px; margin: 5px 0; }
        .footer a { color: #111827; text-decoration: none; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>${this.companyName}</h1>
            <p style="margin: 10px 0 0 0; opacity: 0.9;">Password Reset Successful</p>
        </div>

        <div class="content">
            <div class="success-icon">✅</div>

            <h2>Password Reset Complete!</h2>

            <p>Hello ${displayName},</p>

            <p>Your password has been successfully reset. You can now log in to your ${this.companyName} account using your new password.</p>

            <div style="text-align: center; margin: 30px 0;">
                <a href="${this.frontendUrl}/login" class="login-button">Log In Now</a>
            </div>

            <div class="security-tips">
                <h3>🔐 Security Tips</h3>
                <ul>
                    <li>Use a strong, unique password that you don't use elsewhere</li>
                    <li>Consider using a password manager to generate and store secure passwords</li>
                    <li>Enable two-factor authentication if available</li>
                    <li>Never share your password with anyone</li>
                    <li>Log out of shared or public computers</li>
                </ul>
            </div>

            <p>If you didn't reset your password, please contact our support team immediately at <a href="mailto:<EMAIL>"><EMAIL></a>.</p>

            <p>Best regards,<br>The ${this.companyName} Team</p>
        </div>

        <div class="footer">
            <p>This email was sent to <strong>${userEmail}</strong></p>
            <p>© ${new Date().getFullYear()} ${this.companyName}. All rights reserved.</p>
            <p>Need help? Contact us at <a href="mailto:<EMAIL>"><EMAIL></a></p>
        </div>
    </div>
</body>
</html>`;

    const textBody = `
Password Reset Successful - ${this.companyName}

✅ Password Reset Complete!

Hello ${displayName},

Your password has been successfully reset. You can now log in to your ${this.companyName} account using your new password.

Log in at: ${this.frontendUrl}/login

🔐 Security Tips:
- Use a strong, unique password that you don't use elsewhere
- Consider using a password manager to generate and store secure passwords
- Enable two-factor authentication if available
- Never share your password with anyone
- Log out of shared or public computers

If you didn't reset your password, please contact our support team <NAME_EMAIL>.

Best regards,
The ${this.companyName} Team

---
This email was sent to ${userEmail}
© ${new Date().getFullYear()} ${this.companyName}. All rights reserved.
Need help? Contact <NAME_EMAIL>
`;

    return {
      subject,
      htmlBody,
      textBody
    };
  }

  getUserSuspendedTemplate(userName, userEmail, adminName) {
    const displayName = userName || 'User';
    const adminDisplay = adminName || 'Administrator';
    const subject = `Account Suspended - ${this.companyName}`;

    const htmlBody = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Account Suspended</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f4f4f4; }
        .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px 20px; text-align: center; border-radius: 8px 8px 0 0; margin: -20px -20px 30px -20px; }
        .header h1 { margin: 0; font-size: 28px; font-weight: 300; }
        .content { padding: 0 10px; }
        .greeting { font-size: 18px; margin-bottom: 20px; color: #2c3e50; }
        .message { background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 6px; padding: 20px; margin: 20px 0; }
        .warning { background-color: #f8d7da; border: 1px solid #f5c6cb; border-radius: 6px; padding: 20px; margin: 20px 0; color: #721c24; }
        .warning strong { color: #721c24; }
        .contact-info { background-color: #e8f4fd; border: 1px solid #bee5eb; border-radius: 6px; padding: 20px; margin: 20px 0; }
        .footer { margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; font-size: 12px; color: #666; text-align: center; }
        .icon { font-size: 24px; margin-right: 10px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚫 Account Suspended</h1>
            <p>${this.companyName}</p>
        </div>

        <div class="content">
            <div class="greeting">Hello ${displayName},</div>

            <div class="warning">
                <strong>⚠️ Your account has been suspended</strong><br>
                Your ${this.companyName} account has been suspended by our administrative team.
            </div>

            <div class="message">
                <p><strong>What this means:</strong></p>
                <ul>
                    <li>You will not be able to access your account</li>
                    <li>All platform services are temporarily unavailable to you</li>
                    <li>Any pending transactions or requests may be affected</li>
                </ul>

                <p><strong>Action taken by:</strong> ${adminDisplay}</p>
                <p><strong>Date:</strong> ${new Date().toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                })}</p>
            </div>

            <div class="contact-info">
                <p><strong>📞 Need Help?</strong></p>
                <p>If you believe this suspension was made in error or if you have questions about your account status, please contact our support team immediately.</p>
                <p>We're here to help resolve any issues and get your account back in good standing.</p>
            </div>
        </div>

        <div class="footer">
            This email was sent to ${userEmail}<br>
            © ${new Date().getFullYear()} ${this.companyName}. All rights reserved.<br>
            Need help? Contact <NAME_EMAIL>
        </div>
    </div>
</body>
</html>`;

    const textBody = `
Account Suspended - ${this.companyName}

Hello ${displayName},

⚠️ Your account has been suspended

Your ${this.companyName} account has been suspended by our administrative team.

What this means:
- You will not be able to access your account
- All platform services are temporarily unavailable to you
- Any pending transactions or requests may be affected

Action taken by: ${adminDisplay}
Date: ${new Date().toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
})}

📞 Need Help?
If you believe this suspension was made in error or if you have questions about your account status, please contact our support team immediately.

We're here to help resolve any issues and get your account back in good standing.

---
This email was sent to ${userEmail}
© ${new Date().getFullYear()} ${this.companyName}. All rights reserved.
Need help? Contact <NAME_EMAIL>
`;

    return {
      subject,
      htmlBody,
      textBody
    };
  }

  getUserInactiveTemplate(userName, userEmail, adminName) {
    const displayName = userName || 'User';
    const adminDisplay = adminName || 'Administrator';
    const subject = `Account Status Changed - ${this.companyName}`;

    const htmlBody = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Account Status Changed</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f4f4f4; }
        .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px 20px; text-align: center; border-radius: 8px 8px 0 0; margin: -20px -20px 30px -20px; }
        .header h1 { margin: 0; font-size: 28px; font-weight: 300; }
        .content { padding: 0 10px; }
        .greeting { font-size: 18px; margin-bottom: 20px; color: #2c3e50; }
        .message { background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 6px; padding: 20px; margin: 20px 0; }
        .info { background-color: #d1ecf1; border: 1px solid #bee5eb; border-radius: 6px; padding: 20px; margin: 20px 0; color: #0c5460; }
        .contact-info { background-color: #e8f4fd; border: 1px solid #bee5eb; border-radius: 6px; padding: 20px; margin: 20px 0; }
        .footer { margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; font-size: 12px; color: #666; text-align: center; }
        .icon { font-size: 24px; margin-right: 10px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📋 Account Status Update</h1>
            <p>${this.companyName}</p>
        </div>

        <div class="content">
            <div class="greeting">Hello ${displayName},</div>

            <div class="info">
                <strong>ℹ️ Your account status has been changed to Inactive</strong><br>
                Your ${this.companyName} account status has been updated by our administrative team.
            </div>

            <div class="message">
                <p><strong>What this means:</strong></p>
                <ul>
                    <li>Your account access may be limited</li>
                    <li>Some platform features may not be available</li>
                    <li>You may need to contact support to reactivate your account</li>
                </ul>

                <p><strong>Status changed by:</strong> ${adminDisplay}</p>
                <p><strong>Date:</strong> ${new Date().toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                })}</p>
            </div>

            <div class="contact-info">
                <p><strong>📞 Questions?</strong></p>
                <p>If you have questions about this status change or need to reactivate your account, please contact our support team.</p>
                <p>We're here to help you get back to using our platform.</p>
            </div>
        </div>

        <div class="footer">
            This email was sent to ${userEmail}<br>
            © ${new Date().getFullYear()} ${this.companyName}. All rights reserved.<br>
            Need help? Contact <NAME_EMAIL>
        </div>
    </div>
</body>
</html>`;

    const textBody = `
Account Status Update - ${this.companyName}

Hello ${displayName},

ℹ️ Your account status has been changed to Inactive

Your ${this.companyName} account status has been updated by our administrative team.

What this means:
- Your account access may be limited
- Some platform features may not be available
- You may need to contact support to reactivate your account

Status changed by: ${adminDisplay}
Date: ${new Date().toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
})}

📞 Questions?
If you have questions about this status change or need to reactivate your account, please contact our support team.

We're here to help you get back to using our platform.

---
This email was sent to ${userEmail}
© ${new Date().getFullYear()} ${this.companyName}. All rights reserved.
Need help? Contact <NAME_EMAIL>
`;

    return {
      subject,
      htmlBody,
      textBody
    };
  }

  getAdminPasswordResetNotificationTemplate(userName, userEmail, adminName) {
    const displayName = userName || 'User';
    const adminDisplay = adminName || 'Administrator';
    const subject = `Password Reset Initiated - ${this.companyName}`;

    const htmlBody = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Password Reset Initiated</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f4f4f4; }
        .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px 20px; text-align: center; border-radius: 8px 8px 0 0; margin: -20px -20px 30px -20px; }
        .header h1 { margin: 0; font-size: 28px; font-weight: 300; }
        .content { padding: 0 10px; }
        .greeting { font-size: 18px; margin-bottom: 20px; color: #2c3e50; }
        .message { background-color: #e8f4fd; border: 1px solid #bee5eb; border-radius: 6px; padding: 20px; margin: 20px 0; }
        .security-notice { background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 6px; padding: 20px; margin: 20px 0; }
        .contact-info { background-color: #f8f9fa; border: 1px solid #dee2e6; border-radius: 6px; padding: 20px; margin: 20px 0; }
        .footer { margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; font-size: 12px; color: #666; text-align: center; }
        .icon { font-size: 24px; margin-right: 10px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔐 Password Reset Initiated</h1>
            <p>${this.companyName}</p>
        </div>

        <div class="content">
            <div class="greeting">Hello ${displayName},</div>

            <div class="message">
                <p><strong>📧 A password reset has been initiated for your account</strong></p>
                <p>An administrator has initiated a password reset for your ${this.companyName} account. You should receive a separate email with instructions to reset your password shortly.</p>

                <p><strong>Initiated by:</strong> ${adminDisplay}</p>
                <p><strong>Date:</strong> ${new Date().toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                })}</p>
            </div>

            <div class="security-notice">
                <p><strong>🔒 Security Notice:</strong></p>
                <ul>
                    <li>You will receive a separate email with a secure reset link</li>
                    <li>The reset link will expire in 1 hour for your security</li>
                    <li>If you don't receive the reset email, check your spam folder</li>
                    <li>If you didn't request this reset, please contact support immediately</li>
                </ul>
            </div>

            <div class="contact-info">
                <p><strong>📞 Need Help?</strong></p>
                <p>If you have any questions about this password reset or need assistance, please contact our support team.</p>
                <p>We're here to help ensure your account remains secure.</p>
            </div>
        </div>

        <div class="footer">
            This email was sent to ${userEmail}<br>
            © ${new Date().getFullYear()} ${this.companyName}. All rights reserved.<br>
            Need help? Contact <NAME_EMAIL>
        </div>
    </div>
</body>
</html>`;

    const textBody = `
Password Reset Initiated - ${this.companyName}

Hello ${displayName},

📧 A password reset has been initiated for your account

An administrator has initiated a password reset for your ${this.companyName} account. You should receive a separate email with instructions to reset your password shortly.

Initiated by: ${adminDisplay}
Date: ${new Date().toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
})}

🔒 Security Notice:
- You will receive a separate email with a secure reset link
- The reset link will expire in 1 hour for your security
- If you don't receive the reset email, check your spam folder
- If you didn't request this reset, please contact support immediately

📞 Need Help?
If you have any questions about this password reset or need assistance, please contact our support team.

We're here to help ensure your account remains secure.

---
This email was sent to ${userEmail}
© ${new Date().getFullYear()} ${this.companyName}. All rights reserved.
Need help? Contact <NAME_EMAIL>
`;

    return {
      subject,
      htmlBody,
      textBody
    };
  }

  async verifyEmailAddress(email) {
    try {
      const params = {
        EmailAddress: email
      };

      await ses.verifyEmailIdentity(params).promise();
      logger.info(`Email verification initiated for ${email}`);
      return true;
    } catch (error) {
      logger.error(`Failed to verify email ${email}:`, error);
      return false;
    }
  }

  async sendPasswordResetEmail(userEmail, resetToken, userName = '') {
    try {
      const resetUrl = `${this.frontendUrl}/reset-password?token=${resetToken}&email=${encodeURIComponent(userEmail)}`;

      const emailTemplate = this.getPasswordResetEmailTemplate(userName, resetUrl, userEmail);

      const params = {
        Source: this.fromEmail,
        Destination: {
          ToAddresses: [userEmail]
        },
        Message: {
          Subject: {
            Data: emailTemplate.subject,
            Charset: 'UTF-8'
          },
          Body: {
            Html: {
              Data: emailTemplate.htmlBody,
              Charset: 'UTF-8'
            },
            Text: {
              Data: emailTemplate.textBody,
              Charset: 'UTF-8'
            }
          }
        },
        Tags: [
          {
            Name: 'EmailType',
            Value: 'PasswordReset'
          }
        ]
      };

      const result = await ses.sendEmail(params).promise();

      logger.info(`Password reset email sent successfully to ${userEmail}`, {
        messageId: result.MessageId,
        resetToken
      });

      return {
        success: true,
        messageId: result.MessageId
      };

    } catch (error) {
      logger.error('Failed to send password reset email:', error);
      throw new Error(`Failed to send password reset email: ${error.message}`);
    }
  }

  async sendPasswordResetConfirmationEmail(userEmail, userName = '') {
    try {
      const emailTemplate = this.getPasswordResetConfirmationTemplate(userName, userEmail);

      const params = {
        Source: this.fromEmail,
        Destination: {
          ToAddresses: [userEmail]
        },
        Message: {
          Subject: {
            Data: emailTemplate.subject,
            Charset: 'UTF-8'
          },
          Body: {
            Html: {
              Data: emailTemplate.htmlBody,
              Charset: 'UTF-8'
            },
            Text: {
              Data: emailTemplate.textBody,
              Charset: 'UTF-8'
            }
          }
        },
        Tags: [
          {
            Name: 'EmailType',
            Value: 'PasswordResetConfirmation'
          }
        ]
      };

      const result = await ses.sendEmail(params).promise();

      logger.info(`Password reset confirmation email sent successfully to ${userEmail}`, {
        messageId: result.MessageId
      });

      return {
        success: true,
        messageId: result.MessageId
      };

    } catch (error) {
      logger.error('Failed to send password reset confirmation email:', error);
      throw new Error(`Failed to send password reset confirmation email: ${error.message}`);
    }
  }

  async getSendingQuota() {
    try {
      const quota = await ses.getSendQuota().promise();
      return quota;
    } catch (error) {
      logger.error('Failed to get SES sending quota:', error);
      throw error;
    }
  }

  // Admin Action Email Notifications

  async sendUserSuspendedEmail(userEmail, userName = '', adminName = '') {
    try {
      logger.info(`🔍 DEBUG: sendUserSuspendedEmail called with:`, {
        userEmail,
        userName,
        adminName,
        fromEmail: this.fromEmail,
        companyName: this.companyName
      });

      const emailTemplate = this.getUserSuspendedTemplate(userName, userEmail, adminName);
      logger.info(`🔍 DEBUG: Email template generated successfully`);

      const params = {
        Source: this.fromEmail,
        Destination: {
          ToAddresses: [userEmail]
        },
        Message: {
          Subject: {
            Data: emailTemplate.subject,
            Charset: 'UTF-8'
          },
          Body: {
            Html: {
              Data: emailTemplate.htmlBody,
              Charset: 'UTF-8'
            },
            Text: {
              Data: emailTemplate.textBody,
              Charset: 'UTF-8'
            }
          }
        },
        Tags: [
          {
            Name: 'EmailType',
            Value: 'UserSuspended'
          }
        ]
      };

      logger.info(`🔍 DEBUG: About to send email via SES to ${userEmail}`);
      const result = await ses.sendEmail(params).promise();
      logger.info(`🔍 DEBUG: SES sendEmail completed successfully`);

      logger.info(`User suspended notification email sent successfully to ${userEmail}`, {
        messageId: result.MessageId
      });

      return {
        success: true,
        messageId: result.MessageId
      };

    } catch (error) {
      logger.error('Failed to send user suspended notification email:', {
        error: error.message,
        code: error.code,
        statusCode: error.statusCode,
        stack: error.stack,
        userEmail,
        userName,
        adminName
      });
      throw new Error(`Failed to send user suspended notification email: ${error.message}`);
    }
  }

  async sendUserInactiveEmail(userEmail, userName = '', adminName = '') {
    try {
      logger.info(`🔍 DEBUG: sendUserInactiveEmail called with:`, {
        userEmail,
        userName,
        adminName,
        fromEmail: this.fromEmail,
        companyName: this.companyName
      });

      const emailTemplate = this.getUserInactiveTemplate(userName, userEmail, adminName);
      logger.info(`🔍 DEBUG: Email template generated successfully`);

      const params = {
        Source: this.fromEmail,
        Destination: {
          ToAddresses: [userEmail]
        },
        Message: {
          Subject: {
            Data: emailTemplate.subject,
            Charset: 'UTF-8'
          },
          Body: {
            Html: {
              Data: emailTemplate.htmlBody,
              Charset: 'UTF-8'
            },
            Text: {
              Data: emailTemplate.textBody,
              Charset: 'UTF-8'
            }
          }
        },
        Tags: [
          {
            Name: 'EmailType',
            Value: 'UserInactive'
          }
        ]
      };

      logger.info(`🔍 DEBUG: About to send email via SES to ${userEmail}`);
      const result = await ses.sendEmail(params).promise();
      logger.info(`🔍 DEBUG: SES sendEmail completed successfully`);

      logger.info(`User inactive notification email sent successfully to ${userEmail}`, {
        messageId: result.MessageId
      });

      return {
        success: true,
        messageId: result.MessageId
      };

    } catch (error) {
      logger.error('Failed to send user inactive notification email:', {
        error: error.message,
        code: error.code,
        statusCode: error.statusCode,
        stack: error.stack,
        userEmail,
        userName,
        adminName
      });
      throw new Error(`Failed to send user inactive notification email: ${error.message}`);
    }
  }

  async sendAdminPasswordResetNotificationEmail(userEmail, userName = '', adminName = '') {
    try {
      const emailTemplate = this.getAdminPasswordResetNotificationTemplate(userName, userEmail, adminName);

      const params = {
        Source: this.fromEmail,
        Destination: {
          ToAddresses: [userEmail]
        },
        Message: {
          Subject: {
            Data: emailTemplate.subject,
            Charset: 'UTF-8'
          },
          Body: {
            Html: {
              Data: emailTemplate.htmlBody,
              Charset: 'UTF-8'
            },
            Text: {
              Data: emailTemplate.textBody,
              Charset: 'UTF-8'
            }
          }
        },
        Tags: [
          {
            Name: 'EmailType',
            Value: 'AdminPasswordResetNotification'
          }
        ]
      };

      const result = await ses.sendEmail(params).promise();

      logger.info(`Admin password reset notification email sent successfully to ${userEmail}`, {
        messageId: result.MessageId
      });

      return {
        success: true,
        messageId: result.MessageId
      };

    } catch (error) {
      logger.error('Failed to send admin password reset notification email:', error);
      throw new Error(`Failed to send admin password reset notification email: ${error.message}`);
    }
  }
}

module.exports = new EmailService();
