const AWS = require('aws-sdk');
const logger = require('../utils/logger');
const dotenv = require('dotenv');
const path = require('path');

// Load environment variables if not already loaded
if (!process.env.FROM_EMAIL) {
  const cliEnv = process.argv[2];
  const nodeEnv = cliEnv || process.env.NODE_ENV || 'local';
  const envFile = `.env.${nodeEnv}`;

  dotenv.config({
    path: path.resolve(__dirname, `../${envFile}`),
    override: false,
  });

  logger.info(`EmailService: Loaded environment from ${envFile}`);
}

// Configure AWS SES
const ses = new AWS.SES({
  region: process.env.AWS_REGION || 'eu-west-3',
  accessKeyId: process.env.AWS_ACCESS_KEY_ID,
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
});

class EmailService {
  constructor() {
    this.fromEmail = process.env.FROM_EMAIL || '<EMAIL>';
    this.companyName = process.env.COMPANY_NAME || 'Energy Platform';
    this.frontendUrl = process.env.FRONTEND_URL || 'http://localhost:8080';
  }

  async sendInvitationEmail(invitation) {
    try {
      const { userType, email, inviteeDetails, invitationToken } = invitation;
      
      const registrationUrl = `${this.frontendUrl}/register/invited?token=${invitationToken}&type=${userType.toLowerCase()}`;
      
      const emailTemplate = this.getInvitationEmailTemplate(userType, inviteeDetails, registrationUrl);
      
      const params = {
        Source: this.fromEmail,
        Destination: {
          ToAddresses: [email]
        },
        Message: {
          Subject: {
            Data: emailTemplate.subject,
            Charset: 'UTF-8'
          },
          Body: {
            Html: {
              Data: emailTemplate.htmlBody,
              Charset: 'UTF-8'
            },
            Text: {
              Data: emailTemplate.textBody,
              Charset: 'UTF-8'
            }
          }
        },
        Tags: [
          {
            Name: 'EmailType',
            Value: 'Invitation'
          },
          {
            Name: 'UserType',
            Value: userType
          }
        ]
      };

      const result = await ses.sendEmail(params).promise();
      
      logger.info(`Invitation email sent successfully to ${email}`, {
        messageId: result.MessageId,
        userType,
        invitationToken
      });

      return {
        success: true,
        messageId: result.MessageId
      };

    } catch (error) {
      logger.error('Failed to send invitation email:', error);
      throw new Error(`Failed to send invitation email: ${error.message}`);
    }
  }

  async sendReminderEmail(invitation) {
    try {
      const { userType, email, inviteeDetails, invitationToken, remindersSent } = invitation;

      const registrationUrl = `${this.frontendUrl}/register/invited?token=${invitationToken}&type=${userType.toLowerCase()}`;

      const emailTemplate = this.getReminderEmailTemplate(userType, inviteeDetails, registrationUrl, remindersSent);

      const params = {
        Source: this.fromEmail,
        Destination: {
          ToAddresses: [email]
        },
        Message: {
          Subject: {
            Data: emailTemplate.subject,
            Charset: 'UTF-8'
          },
          Body: {
            Html: {
              Data: emailTemplate.htmlBody,
              Charset: 'UTF-8'
            },
            Text: {
              Data: emailTemplate.textBody,
              Charset: 'UTF-8'
            }
          }
        },
        Tags: [
          {
            Name: 'EmailType',
            Value: 'Reminder'
          },
          {
            Name: 'UserType',
            Value: userType
          }
        ]
      };

      const result = await ses.sendEmail(params).promise();

      logger.info(`Reminder email sent successfully to ${email}`, {
        messageId: result.MessageId,
        userType,
        reminderNumber: remindersSent + 1
      });

      return {
        success: true,
        messageId: result.MessageId
      };

    } catch (error) {
      logger.error('Failed to send reminder email:', error);
      throw new Error(`Failed to send reminder email: ${error.message}`);
    }
  }

  // Verification Status Email Methods
  async sendProfileSubmittedEmail(user) {
    try {
      const emailTemplate = this.getProfileSubmittedEmailTemplate(user);

      const params = {
        Source: this.fromEmail,
        Destination: {
          ToAddresses: [user.email]
        },
        Message: {
          Subject: {
            Data: emailTemplate.subject,
            Charset: 'UTF-8'
          },
          Body: {
            Html: {
              Data: emailTemplate.htmlBody,
              Charset: 'UTF-8'
            },
            Text: {
              Data: emailTemplate.textBody,
              Charset: 'UTF-8'
            }
          }
        },
        Tags: [
          {
            Name: 'EmailType',
            Value: 'ProfileSubmitted'
          },
          {
            Name: 'UserType',
            Value: user.userType
          }
        ]
      };

      const result = await ses.sendEmail(params).promise();

      logger.info(`Profile submitted email sent successfully to ${user.email}`, {
        messageId: result.MessageId,
        userType: user.userType
      });

      return {
        success: true,
        messageId: result.MessageId
      };

    } catch (error) {
      logger.error('Failed to send profile submitted email:', error);
      throw new Error(`Failed to send profile submitted email: ${error.message}`);
    }
  }

  async sendProfileApprovedEmail(user) {
    try {
      const emailTemplate = this.getProfileApprovedEmailTemplate(user);

      const params = {
        Source: this.fromEmail,
        Destination: {
          ToAddresses: [user.email]
        },
        Message: {
          Subject: {
            Data: emailTemplate.subject,
            Charset: 'UTF-8'
          },
          Body: {
            Html: {
              Data: emailTemplate.htmlBody,
              Charset: 'UTF-8'
            },
            Text: {
              Data: emailTemplate.textBody,
              Charset: 'UTF-8'
            }
          }
        },
        Tags: [
          {
            Name: 'EmailType',
            Value: 'ProfileApproved'
          },
          {
            Name: 'UserType',
            Value: user.userType
          }
        ]
      };

      const result = await ses.sendEmail(params).promise();

      logger.info(`Profile approved email sent successfully to ${user.email}`, {
        messageId: result.MessageId,
        userType: user.userType
      });

      return {
        success: true,
        messageId: result.MessageId
      };

    } catch (error) {
      logger.error('Failed to send profile approved email:', error);
      throw new Error(`Failed to send profile approved email: ${error.message}`);
    }
  }

  async sendChangesRequestedEmail(user, changesRequested, adminNotes) {
    try {
      const emailTemplate = this.getChangesRequestedEmailTemplate(user, changesRequested, adminNotes);

      const params = {
        Source: this.fromEmail,
        Destination: {
          ToAddresses: [user.email]
        },
        Message: {
          Subject: {
            Data: emailTemplate.subject,
            Charset: 'UTF-8'
          },
          Body: {
            Html: {
              Data: emailTemplate.htmlBody,
              Charset: 'UTF-8'
            },
            Text: {
              Data: emailTemplate.textBody,
              Charset: 'UTF-8'
            }
          }
        },
        Tags: [
          {
            Name: 'EmailType',
            Value: 'ChangesRequested'
          },
          {
            Name: 'UserType',
            Value: user.userType
          }
        ]
      };

      const result = await ses.sendEmail(params).promise();

      logger.info(`Changes requested email sent successfully to ${user.email}`, {
        messageId: result.MessageId,
        userType: user.userType
      });

      return {
        success: true,
        messageId: result.MessageId
      };

    } catch (error) {
      logger.error('Failed to send changes requested email:', error);
      throw new Error(`Failed to send changes requested email: ${error.message}`);
    }
  }

  getInvitationEmailTemplate(userType, inviteeDetails, registrationUrl) {
    const name = inviteeDetails.firstName ? 
      `${inviteeDetails.firstName} ${inviteeDetails.lastName || ''}`.trim() : 
      'there';
    
    const userTypeDisplay = userType === 'Broker' ? 'Energy Broker' : 'Energy Supplier';
    
    const subject = `Invitation to Join ${this.companyName} as ${userTypeDisplay}`;
    
    const htmlBody = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${subject}</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f4f4f4; }
        .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; border-radius: 10px; overflow: hidden; box-shadow: 0 0 20px rgba(0,0,0,0.1); }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; }
        .header h1 { margin: 0; font-size: 28px; font-weight: 300; }
        .content { padding: 40px 30px; }
        .greeting { font-size: 18px; margin-bottom: 20px; color: #2c3e50; }
        .message { margin-bottom: 30px; font-size: 16px; line-height: 1.8; }
        .cta-button { display: inline-block; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 15px 30px; text-decoration: none; border-radius: 50px; font-weight: 600; font-size: 16px; margin: 20px 0; transition: transform 0.2s; }
        .cta-button:hover { transform: translateY(-2px); }
        .details { background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .details h3 { margin-top: 0; color: #495057; }
        .footer { background-color: #f8f9fa; padding: 20px 30px; text-align: center; font-size: 14px; color: #6c757d; }
        .expiry { background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0; }
        .expiry strong { color: #856404; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>${this.companyName}</h1>
            <p>Energy Management Platform</p>
        </div>
        
        <div class="content">
            <div class="greeting">Hello ${name},</div>
            
            <div class="message">
                <p>You have been invited to join <strong>${this.companyName}</strong> as a <strong>${userTypeDisplay}</strong>!</p>
                
                <p>Our platform connects energy professionals with clients seeking the best energy solutions. As a ${userTypeDisplay.toLowerCase()}, you'll have access to:</p>
                
                ${userType === 'Broker' ? `
                <ul>
                    <li>🎯 <strong>Client Management</strong> - Manage your assigned clients and their energy needs</li>
                    <li>📊 <strong>Quote Comparison Tools</strong> - Generate and send competitive energy quotes</li>
                    <li>💰 <strong>Commission Tracking</strong> - Monitor your earnings and commission payments</li>
                    <li>📈 <strong>Performance Analytics</strong> - Track your success metrics and growth</li>
                    <li>🤝 <strong>Supplier Network</strong> - Access to our verified supplier partners</li>
                </ul>
                ` : `
                <ul>
                    <li>🏢 <strong>Offer Management</strong> - Publish and manage your energy offers</li>
                    <li>📋 <strong>Tariff Grid Upload</strong> - Easy CSV import for your pricing structures</li>
                    <li>🔗 <strong>API Integration</strong> - Connect your systems for real-time pricing</li>
                    <li>📊 <strong>Market Analytics</strong> - Insights into market trends and competition</li>
                    <li>🎯 <strong>Lead Generation</strong> - Access to qualified energy customers</li>
                </ul>
                `}
            </div>
            
            <div style="text-align: center;">
                <a href="${registrationUrl}" class="cta-button">Complete Your Registration</a>
            </div>
            
            <div class="expiry">
                <strong>⏰ Important:</strong> This invitation expires in 7 days. Please complete your registration before the link expires.
            </div>
            
            <div class="details">
                <h3>What happens next?</h3>
                <ol>
                    <li><strong>Click the registration button</strong> above to access the secure registration form</li>
                    <li><strong>Complete your profile</strong> with your professional information</li>
                    <li><strong>Wait for approval</strong> - Our admin team will review and approve your account</li>
                    <li><strong>Start using the platform</strong> once your account is activated</li>
                </ol>
            </div>
            
            <div class="message">
                <p>If you have any questions about this invitation or need assistance with the registration process, please don't hesitate to contact our support team.</p>
                
                <p>We look forward to welcoming you to our platform!</p>
                
                <p>Best regards,<br>
                <strong>The ${this.companyName} Team</strong></p>
            </div>
        </div>
        
        <div class="footer">
            <p>This invitation was sent to ${inviteeDetails.email || email}. If you received this email in error, please ignore it.</p>
            <p>&copy; ${new Date().getFullYear()} ${this.companyName}. All rights reserved.</p>
        </div>
    </div>
</body>
</html>`;

    const textBody = `
Hello ${name},

You have been invited to join ${this.companyName} as a ${userTypeDisplay}!

Our platform connects energy professionals with clients seeking the best energy solutions.

To complete your registration, please visit:
${registrationUrl}

This invitation expires in 7 days.

What happens next?
1. Click the registration link above
2. Complete your profile with your professional information  
3. Wait for approval from our admin team
4. Start using the platform once activated

If you have any questions, please contact our support team.

Best regards,
The ${this.companyName} Team

---
This invitation was sent to ${inviteeDetails.email || email}.
© ${new Date().getFullYear()} ${this.companyName}. All rights reserved.
`;

    return {
      subject,
      htmlBody,
      textBody
    };
  }

  getReminderEmailTemplate(userType, inviteeDetails, registrationUrl, reminderNumber) {
    const name = inviteeDetails.firstName ? 
      `${inviteeDetails.firstName} ${inviteeDetails.lastName || ''}`.trim() : 
      'there';
    
    const userTypeDisplay = userType === 'Broker' ? 'Energy Broker' : 'Energy Supplier';
    
    const subject = `Reminder: Complete Your ${this.companyName} Registration`;
    
    const htmlBody = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${subject}</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f4f4f4; }
        .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; border-radius: 10px; overflow: hidden; box-shadow: 0 0 20px rgba(0,0,0,0.1); }
        .header { background: linear-gradient(135deg, #f39c12 0%, #e74c3c 100%); color: white; padding: 30px; text-align: center; }
        .header h1 { margin: 0; font-size: 28px; font-weight: 300; }
        .content { padding: 40px 30px; }
        .greeting { font-size: 18px; margin-bottom: 20px; color: #2c3e50; }
        .message { margin-bottom: 30px; font-size: 16px; line-height: 1.8; }
        .cta-button { display: inline-block; background: linear-gradient(135deg, #f39c12 0%, #e74c3c 100%); color: white; padding: 15px 30px; text-decoration: none; border-radius: 50px; font-weight: 600; font-size: 16px; margin: 20px 0; transition: transform 0.2s; }
        .cta-button:hover { transform: translateY(-2px); }
        .footer { background-color: #f8f9fa; padding: 20px 30px; text-align: center; font-size: 14px; color: #6c757d; }
        .urgent { background-color: #fff3cd; border-left: 4px solid #f39c12; padding: 15px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>⏰ Registration Reminder</h1>
            <p>${this.companyName}</p>
        </div>
        
        <div class="content">
            <div class="greeting">Hello ${name},</div>
            
            <div class="message">
                <p>This is a friendly reminder that you have a pending invitation to join <strong>${this.companyName}</strong> as a <strong>${userTypeDisplay}</strong>.</p>
                
                <p>We noticed you haven't completed your registration yet, and we don't want you to miss this opportunity!</p>
            </div>
            
            <div class="urgent">
                <strong>⚠️ Your invitation will expire soon!</strong><br>
                Please complete your registration as soon as possible to secure your access to our platform.
            </div>
            
            <div style="text-align: center;">
                <a href="${registrationUrl}" class="cta-button">Complete Registration Now</a>
            </div>
            
            <div class="message">
                <p>If you're experiencing any issues with the registration process or have questions about the platform, please don't hesitate to reach out to our support team.</p>
                
                <p>We're excited to have you join our growing network of energy professionals!</p>
                
                <p>Best regards,<br>
                <strong>The ${this.companyName} Team</strong></p>
            </div>
        </div>
        
        <div class="footer">
            <p>This is reminder #${reminderNumber + 1} for the invitation sent to ${inviteeDetails.email || email}.</p>
            <p>&copy; ${new Date().getFullYear()} ${this.companyName}. All rights reserved.</p>
        </div>
    </div>
</body>
</html>`;

    const textBody = `
Hello ${name},

This is a friendly reminder that you have a pending invitation to join ${this.companyName} as a ${userTypeDisplay}.

Your invitation will expire soon! Please complete your registration as soon as possible.

Registration link: ${registrationUrl}

If you have any questions, please contact our support team.

Best regards,
The ${this.companyName} Team

---
This is reminder #${reminderNumber + 1} for ${inviteeDetails.email || email}.
© ${new Date().getFullYear()} ${this.companyName}. All rights reserved.
`;

    return {
      subject,
      htmlBody,
      textBody
    };
  }

  getPasswordResetEmailTemplate(userName, resetUrl, userEmail) {
    const displayName = userName || 'User';
    const subject = `Reset Your ${this.companyName} Password`;

    const htmlBody = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Password Reset - ${this.companyName}</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif; margin: 0; padding: 0; background-color: #f8f9fa; }
        .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; }
        .header { background: linear-gradient(135deg, #000000 0%, #**********%); color: #ffffff; padding: 40px 30px; text-align: center; }
        .header h1 { margin: 0; font-size: 28px; font-weight: 700; }
        .content { padding: 40px 30px; }
        .content h2 { color: #111827; font-size: 24px; margin-bottom: 20px; }
        .content p { color: #374151; font-size: 16px; line-height: 1.6; margin-bottom: 20px; }
        .reset-button { display: inline-block; background: linear-gradient(135deg, #000000 0%, #**********%); color: #ffffff; text-decoration: none; padding: 16px 32px; border-radius: 8px; font-weight: 600; font-size: 16px; margin: 20px 0; }
        .reset-button:hover { opacity: 0.9; }
        .security-notice { background: #fef3c7; border-left: 4px solid #f59e0b; padding: 20px; margin: 30px 0; border-radius: 4px; }
        .security-notice h3 { color: #92400e; margin: 0 0 10px 0; font-size: 18px; }
        .security-notice p { color: #92400e; margin: 0; font-size: 14px; }
        .footer { background: #f8f9fa; padding: 30px; text-align: center; border-top: 1px solid #e5e7eb; }
        .footer p { color: #6b7280; font-size: 14px; margin: 5px 0; }
        .footer a { color: #111827; text-decoration: none; }
        .expiry-info { background: #f3f4f6; padding: 15px; border-radius: 6px; margin: 20px 0; }
        .expiry-info p { margin: 0; color: #374151; font-size: 14px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>${this.companyName}</h1>
            <p style="margin: 10px 0 0 0; opacity: 0.9;">Password Reset Request</p>
        </div>

        <div class="content">
            <h2>Hello ${displayName},</h2>

            <p>We received a request to reset your password for your ${this.companyName} account. If you made this request, click the button below to reset your password:</p>

            <div style="text-align: center; margin: 30px 0;">
                <a href="${resetUrl}" class="reset-button">Reset My Password</a>
            </div>

            <div class="expiry-info">
                <p><strong>⏰ This link expires in 1 hour</strong> for your security.</p>
            </div>

            <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
            <p style="word-break: break-all; background: #f3f4f6; padding: 10px; border-radius: 4px; font-family: monospace; font-size: 14px;">${resetUrl}</p>

            <div class="security-notice">
                <h3>🔒 Security Notice</h3>
                <p>If you didn't request this password reset, please ignore this email. Your password will remain unchanged. For security concerns, contact our support team immediately.</p>
            </div>

            <p>Best regards,<br>The ${this.companyName} Team</p>
        </div>

        <div class="footer">
            <p>This email was sent to <strong>${userEmail}</strong></p>
            <p>© ${new Date().getFullYear()} ${this.companyName}. All rights reserved.</p>
            <p>Need help? Contact us at <a href="mailto:<EMAIL>"><EMAIL></a></p>
        </div>
    </div>
</body>
</html>`;

    const textBody = `
Password Reset Request - ${this.companyName}

Hello ${displayName},

We received a request to reset your password for your ${this.companyName} account.

To reset your password, please visit the following link:
${resetUrl}

⏰ This link expires in 1 hour for your security.

🔒 Security Notice:
If you didn't request this password reset, please ignore this email. Your password will remain unchanged.

If you have any security concerns, please contact our support team immediately.

Best regards,
The ${this.companyName} Team

---
This email was sent to ${userEmail}
© ${new Date().getFullYear()} ${this.companyName}. All rights reserved.
Need help? Contact <NAME_EMAIL>
`;

    return {
      subject,
      htmlBody,
      textBody
    };
  }

  getPasswordResetConfirmationTemplate(userName, userEmail) {
    const displayName = userName || 'User';
    const subject = `Password Successfully Reset - ${this.companyName}`;

    const htmlBody = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Password Reset Confirmation - ${this.companyName}</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif; margin: 0; padding: 0; background-color: #f8f9fa; }
        .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; }
        .header { background: linear-gradient(135deg, #10b981 0%, #**********%); color: #ffffff; padding: 40px 30px; text-align: center; }
        .header h1 { margin: 0; font-size: 28px; font-weight: 700; }
        .content { padding: 40px 30px; }
        .content h2 { color: #111827; font-size: 24px; margin-bottom: 20px; }
        .content p { color: #374151; font-size: 16px; line-height: 1.6; margin-bottom: 20px; }
        .success-icon { font-size: 48px; text-align: center; margin: 20px 0; }
        .login-button { display: inline-block; background: linear-gradient(135deg, #000000 0%, #**********%); color: #ffffff; text-decoration: none; padding: 16px 32px; border-radius: 8px; font-weight: 600; font-size: 16px; margin: 20px 0; }
        .security-tips { background: #f0f9ff; border-left: 4px solid #0ea5e9; padding: 20px; margin: 30px 0; border-radius: 4px; }
        .security-tips h3 { color: #0c4a6e; margin: 0 0 15px 0; font-size: 18px; }
        .security-tips ul { color: #0c4a6e; margin: 0; padding-left: 20px; }
        .security-tips li { margin-bottom: 8px; font-size: 14px; }
        .footer { background: #f8f9fa; padding: 30px; text-align: center; border-top: 1px solid #e5e7eb; }
        .footer p { color: #6b7280; font-size: 14px; margin: 5px 0; }
        .footer a { color: #111827; text-decoration: none; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>${this.companyName}</h1>
            <p style="margin: 10px 0 0 0; opacity: 0.9;">Password Reset Successful</p>
        </div>

        <div class="content">
            <div class="success-icon">✅</div>

            <h2>Password Reset Complete!</h2>

            <p>Hello ${displayName},</p>

            <p>Your password has been successfully reset. You can now log in to your ${this.companyName} account using your new password.</p>

            <div style="text-align: center; margin: 30px 0;">
                <a href="${this.frontendUrl}/login" class="login-button">Log In Now</a>
            </div>

            <div class="security-tips">
                <h3>🔐 Security Tips</h3>
                <ul>
                    <li>Use a strong, unique password that you don't use elsewhere</li>
                    <li>Consider using a password manager to generate and store secure passwords</li>
                    <li>Enable two-factor authentication if available</li>
                    <li>Never share your password with anyone</li>
                    <li>Log out of shared or public computers</li>
                </ul>
            </div>

            <p>If you didn't reset your password, please contact our support team immediately at <a href="mailto:<EMAIL>"><EMAIL></a>.</p>

            <p>Best regards,<br>The ${this.companyName} Team</p>
        </div>

        <div class="footer">
            <p>This email was sent to <strong>${userEmail}</strong></p>
            <p>© ${new Date().getFullYear()} ${this.companyName}. All rights reserved.</p>
            <p>Need help? Contact us at <a href="mailto:<EMAIL>"><EMAIL></a></p>
        </div>
    </div>
</body>
</html>`;

    const textBody = `
Password Reset Successful - ${this.companyName}

✅ Password Reset Complete!

Hello ${displayName},

Your password has been successfully reset. You can now log in to your ${this.companyName} account using your new password.

Log in at: ${this.frontendUrl}/login

🔐 Security Tips:
- Use a strong, unique password that you don't use elsewhere
- Consider using a password manager to generate and store secure passwords
- Enable two-factor authentication if available
- Never share your password with anyone
- Log out of shared or public computers

If you didn't reset your password, please contact our support team <NAME_EMAIL>.

Best regards,
The ${this.companyName} Team

---
This email was sent to ${userEmail}
© ${new Date().getFullYear()} ${this.companyName}. All rights reserved.
Need help? Contact <NAME_EMAIL>
`;

    return {
      subject,
      htmlBody,
      textBody
    };
  }

  getUserSuspendedTemplate(userName, userEmail, adminName) {
    const displayName = userName || 'User';
    const adminDisplay = adminName || 'Administrator';
    const subject = `Account Suspended - ${this.companyName}`;

    const htmlBody = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Account Suspended</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f4f4f4; }
        .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px 20px; text-align: center; border-radius: 8px 8px 0 0; margin: -20px -20px 30px -20px; }
        .header h1 { margin: 0; font-size: 28px; font-weight: 300; }
        .content { padding: 0 10px; }
        .greeting { font-size: 18px; margin-bottom: 20px; color: #2c3e50; }
        .message { background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 6px; padding: 20px; margin: 20px 0; }
        .warning { background-color: #f8d7da; border: 1px solid #f5c6cb; border-radius: 6px; padding: 20px; margin: 20px 0; color: #721c24; }
        .warning strong { color: #721c24; }
        .contact-info { background-color: #e8f4fd; border: 1px solid #bee5eb; border-radius: 6px; padding: 20px; margin: 20px 0; }
        .footer { margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; font-size: 12px; color: #666; text-align: center; }
        .icon { font-size: 24px; margin-right: 10px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚫 Account Suspended</h1>
            <p>${this.companyName}</p>
        </div>

        <div class="content">
            <div class="greeting">Hello ${displayName},</div>

            <div class="warning">
                <strong>⚠️ Your account has been suspended</strong><br>
                Your ${this.companyName} account has been suspended by our administrative team.
            </div>

            <div class="message">
                <p><strong>What this means:</strong></p>
                <ul>
                    <li>You will not be able to access your account</li>
                    <li>All platform services are temporarily unavailable to you</li>
                    <li>Any pending transactions or requests may be affected</li>
                </ul>

                <p><strong>Action taken by:</strong> ${adminDisplay}</p>
                <p><strong>Date:</strong> ${new Date().toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                })}</p>
            </div>

            <div class="contact-info">
                <p><strong>📞 Need Help?</strong></p>
                <p>If you believe this suspension was made in error or if you have questions about your account status, please contact our support team immediately.</p>
                <p>We're here to help resolve any issues and get your account back in good standing.</p>
            </div>
        </div>

        <div class="footer">
            This email was sent to ${userEmail}<br>
            © ${new Date().getFullYear()} ${this.companyName}. All rights reserved.<br>
            Need help? Contact <NAME_EMAIL>
        </div>
    </div>
</body>
</html>`;

    const textBody = `
Account Suspended - ${this.companyName}

Hello ${displayName},

⚠️ Your account has been suspended

Your ${this.companyName} account has been suspended by our administrative team.

What this means:
- You will not be able to access your account
- All platform services are temporarily unavailable to you
- Any pending transactions or requests may be affected

Action taken by: ${adminDisplay}
Date: ${new Date().toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
})}

📞 Need Help?
If you believe this suspension was made in error or if you have questions about your account status, please contact our support team immediately.

We're here to help resolve any issues and get your account back in good standing.

---
This email was sent to ${userEmail}
© ${new Date().getFullYear()} ${this.companyName}. All rights reserved.
Need help? Contact <NAME_EMAIL>
`;

    return {
      subject,
      htmlBody,
      textBody
    };
  }

  getUserInactiveTemplate(userName, userEmail, adminName) {
    const displayName = userName || 'User';
    const adminDisplay = adminName || 'Administrator';
    const subject = `Account Status Changed - ${this.companyName}`;

    const htmlBody = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Account Status Changed</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f4f4f4; }
        .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px 20px; text-align: center; border-radius: 8px 8px 0 0; margin: -20px -20px 30px -20px; }
        .header h1 { margin: 0; font-size: 28px; font-weight: 300; }
        .content { padding: 0 10px; }
        .greeting { font-size: 18px; margin-bottom: 20px; color: #2c3e50; }
        .message { background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 6px; padding: 20px; margin: 20px 0; }
        .info { background-color: #d1ecf1; border: 1px solid #bee5eb; border-radius: 6px; padding: 20px; margin: 20px 0; color: #0c5460; }
        .contact-info { background-color: #e8f4fd; border: 1px solid #bee5eb; border-radius: 6px; padding: 20px; margin: 20px 0; }
        .footer { margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; font-size: 12px; color: #666; text-align: center; }
        .icon { font-size: 24px; margin-right: 10px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📋 Account Status Update</h1>
            <p>${this.companyName}</p>
        </div>

        <div class="content">
            <div class="greeting">Hello ${displayName},</div>

            <div class="info">
                <strong>ℹ️ Your account status has been changed to Inactive</strong><br>
                Your ${this.companyName} account status has been updated by our administrative team.
            </div>

            <div class="message">
                <p><strong>What this means:</strong></p>
                <ul>
                    <li>Your account access may be limited</li>
                    <li>Some platform features may not be available</li>
                    <li>You may need to contact support to reactivate your account</li>
                </ul>

                <p><strong>Status changed by:</strong> ${adminDisplay}</p>
                <p><strong>Date:</strong> ${new Date().toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                })}</p>
            </div>

            <div class="contact-info">
                <p><strong>📞 Questions?</strong></p>
                <p>If you have questions about this status change or need to reactivate your account, please contact our support team.</p>
                <p>We're here to help you get back to using our platform.</p>
            </div>
        </div>

        <div class="footer">
            This email was sent to ${userEmail}<br>
            © ${new Date().getFullYear()} ${this.companyName}. All rights reserved.<br>
            Need help? Contact <NAME_EMAIL>
        </div>
    </div>
</body>
</html>`;

    const textBody = `
Account Status Update - ${this.companyName}

Hello ${displayName},

ℹ️ Your account status has been changed to Inactive

Your ${this.companyName} account status has been updated by our administrative team.

What this means:
- Your account access may be limited
- Some platform features may not be available
- You may need to contact support to reactivate your account

Status changed by: ${adminDisplay}
Date: ${new Date().toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
})}

📞 Questions?
If you have questions about this status change or need to reactivate your account, please contact our support team.

We're here to help you get back to using our platform.

---
This email was sent to ${userEmail}
© ${new Date().getFullYear()} ${this.companyName}. All rights reserved.
Need help? Contact <NAME_EMAIL>
`;

    return {
      subject,
      htmlBody,
      textBody
    };
  }

  getAdminPasswordResetNotificationTemplate(userName, userEmail, adminName) {
    const displayName = userName || 'User';
    const adminDisplay = adminName || 'Administrator';
    const subject = `Password Reset Initiated - ${this.companyName}`;

    const htmlBody = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Password Reset Initiated</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f4f4f4; }
        .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px 20px; text-align: center; border-radius: 8px 8px 0 0; margin: -20px -20px 30px -20px; }
        .header h1 { margin: 0; font-size: 28px; font-weight: 300; }
        .content { padding: 0 10px; }
        .greeting { font-size: 18px; margin-bottom: 20px; color: #2c3e50; }
        .message { background-color: #e8f4fd; border: 1px solid #bee5eb; border-radius: 6px; padding: 20px; margin: 20px 0; }
        .security-notice { background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 6px; padding: 20px; margin: 20px 0; }
        .contact-info { background-color: #f8f9fa; border: 1px solid #dee2e6; border-radius: 6px; padding: 20px; margin: 20px 0; }
        .footer { margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; font-size: 12px; color: #666; text-align: center; }
        .icon { font-size: 24px; margin-right: 10px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔐 Password Reset Initiated</h1>
            <p>${this.companyName}</p>
        </div>

        <div class="content">
            <div class="greeting">Hello ${displayName},</div>

            <div class="message">
                <p><strong>📧 A password reset has been initiated for your account</strong></p>
                <p>An administrator has initiated a password reset for your ${this.companyName} account. You should receive a separate email with instructions to reset your password shortly.</p>

                <p><strong>Initiated by:</strong> ${adminDisplay}</p>
                <p><strong>Date:</strong> ${new Date().toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                })}</p>
            </div>

            <div class="security-notice">
                <p><strong>🔒 Security Notice:</strong></p>
                <ul>
                    <li>You will receive a separate email with a secure reset link</li>
                    <li>The reset link will expire in 1 hour for your security</li>
                    <li>If you don't receive the reset email, check your spam folder</li>
                    <li>If you didn't request this reset, please contact support immediately</li>
                </ul>
            </div>

            <div class="contact-info">
                <p><strong>📞 Need Help?</strong></p>
                <p>If you have any questions about this password reset or need assistance, please contact our support team.</p>
                <p>We're here to help ensure your account remains secure.</p>
            </div>
        </div>

        <div class="footer">
            This email was sent to ${userEmail}<br>
            © ${new Date().getFullYear()} ${this.companyName}. All rights reserved.<br>
            Need help? Contact <NAME_EMAIL>
        </div>
    </div>
</body>
</html>`;

    const textBody = `
Password Reset Initiated - ${this.companyName}

Hello ${displayName},

📧 A password reset has been initiated for your account

An administrator has initiated a password reset for your ${this.companyName} account. You should receive a separate email with instructions to reset your password shortly.

Initiated by: ${adminDisplay}
Date: ${new Date().toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
})}

🔒 Security Notice:
- You will receive a separate email with a secure reset link
- The reset link will expire in 1 hour for your security
- If you don't receive the reset email, check your spam folder
- If you didn't request this reset, please contact support immediately

📞 Need Help?
If you have any questions about this password reset or need assistance, please contact our support team.

We're here to help ensure your account remains secure.

---
This email was sent to ${userEmail}
© ${new Date().getFullYear()} ${this.companyName}. All rights reserved.
Need help? Contact <NAME_EMAIL>
`;

    return {
      subject,
      htmlBody,
      textBody
    };
  }

  async verifyEmailAddress(email) {
    try {
      const params = {
        EmailAddress: email
      };

      await ses.verifyEmailIdentity(params).promise();
      logger.info(`Email verification initiated for ${email}`);
      return true;
    } catch (error) {
      logger.error(`Failed to verify email ${email}:`, error);
      return false;
    }
  }

  async sendPasswordResetEmail(userEmail, resetToken, userName = '') {
    try {
      const resetUrl = `${this.frontendUrl}/reset-password?token=${resetToken}&email=${encodeURIComponent(userEmail)}`;

      const emailTemplate = this.getPasswordResetEmailTemplate(userName, resetUrl, userEmail);

      const params = {
        Source: this.fromEmail,
        Destination: {
          ToAddresses: [userEmail]
        },
        Message: {
          Subject: {
            Data: emailTemplate.subject,
            Charset: 'UTF-8'
          },
          Body: {
            Html: {
              Data: emailTemplate.htmlBody,
              Charset: 'UTF-8'
            },
            Text: {
              Data: emailTemplate.textBody,
              Charset: 'UTF-8'
            }
          }
        },
        Tags: [
          {
            Name: 'EmailType',
            Value: 'PasswordReset'
          }
        ]
      };

      const result = await ses.sendEmail(params).promise();

      logger.info(`Password reset email sent successfully to ${userEmail}`, {
        messageId: result.MessageId,
        resetToken
      });

      return {
        success: true,
        messageId: result.MessageId
      };

    } catch (error) {
      logger.error('Failed to send password reset email:', error);
      throw new Error(`Failed to send password reset email: ${error.message}`);
    }
  }

  async sendPasswordResetConfirmationEmail(userEmail, userName = '') {
    try {
      const emailTemplate = this.getPasswordResetConfirmationTemplate(userName, userEmail);

      const params = {
        Source: this.fromEmail,
        Destination: {
          ToAddresses: [userEmail]
        },
        Message: {
          Subject: {
            Data: emailTemplate.subject,
            Charset: 'UTF-8'
          },
          Body: {
            Html: {
              Data: emailTemplate.htmlBody,
              Charset: 'UTF-8'
            },
            Text: {
              Data: emailTemplate.textBody,
              Charset: 'UTF-8'
            }
          }
        },
        Tags: [
          {
            Name: 'EmailType',
            Value: 'PasswordResetConfirmation'
          }
        ]
      };

      const result = await ses.sendEmail(params).promise();

      logger.info(`Password reset confirmation email sent successfully to ${userEmail}`, {
        messageId: result.MessageId
      });

      return {
        success: true,
        messageId: result.MessageId
      };

    } catch (error) {
      logger.error('Failed to send password reset confirmation email:', error);
      throw new Error(`Failed to send password reset confirmation email: ${error.message}`);
    }
  }

  async getSendingQuota() {
    try {
      const quota = await ses.getSendQuota().promise();
      return quota;
    } catch (error) {
      logger.error('Failed to get SES sending quota:', error);
      throw error;
    }
  }

  // Admin Action Email Notifications

  async sendUserSuspendedEmail(userEmail, userName = '', adminName = '') {
    try {
      logger.info(`🔍 DEBUG: sendUserSuspendedEmail called with:`, {
        userEmail,
        userName,
        adminName,
        fromEmail: this.fromEmail,
        companyName: this.companyName
      });

      const emailTemplate = this.getUserSuspendedTemplate(userName, userEmail, adminName);
      logger.info(`🔍 DEBUG: Email template generated successfully`);

      const params = {
        Source: this.fromEmail,
        Destination: {
          ToAddresses: [userEmail]
        },
        Message: {
          Subject: {
            Data: emailTemplate.subject,
            Charset: 'UTF-8'
          },
          Body: {
            Html: {
              Data: emailTemplate.htmlBody,
              Charset: 'UTF-8'
            },
            Text: {
              Data: emailTemplate.textBody,
              Charset: 'UTF-8'
            }
          }
        },
        Tags: [
          {
            Name: 'EmailType',
            Value: 'UserSuspended'
          }
        ]
      };

      logger.info(`🔍 DEBUG: About to send email via SES to ${userEmail}`);
      const result = await ses.sendEmail(params).promise();
      logger.info(`🔍 DEBUG: SES sendEmail completed successfully`);

      logger.info(`User suspended notification email sent successfully to ${userEmail}`, {
        messageId: result.MessageId
      });

      return {
        success: true,
        messageId: result.MessageId
      };

    } catch (error) {
      logger.error('Failed to send user suspended notification email:', {
        error: error.message,
        code: error.code,
        statusCode: error.statusCode,
        stack: error.stack,
        userEmail,
        userName,
        adminName
      });
      throw new Error(`Failed to send user suspended notification email: ${error.message}`);
    }
  }

  async sendUserInactiveEmail(userEmail, userName = '', adminName = '') {
    try {
      logger.info(`🔍 DEBUG: sendUserInactiveEmail called with:`, {
        userEmail,
        userName,
        adminName,
        fromEmail: this.fromEmail,
        companyName: this.companyName
      });

      const emailTemplate = this.getUserInactiveTemplate(userName, userEmail, adminName);
      logger.info(`🔍 DEBUG: Email template generated successfully`);

      const params = {
        Source: this.fromEmail,
        Destination: {
          ToAddresses: [userEmail]
        },
        Message: {
          Subject: {
            Data: emailTemplate.subject,
            Charset: 'UTF-8'
          },
          Body: {
            Html: {
              Data: emailTemplate.htmlBody,
              Charset: 'UTF-8'
            },
            Text: {
              Data: emailTemplate.textBody,
              Charset: 'UTF-8'
            }
          }
        },
        Tags: [
          {
            Name: 'EmailType',
            Value: 'UserInactive'
          }
        ]
      };

      logger.info(`🔍 DEBUG: About to send email via SES to ${userEmail}`);
      const result = await ses.sendEmail(params).promise();
      logger.info(`🔍 DEBUG: SES sendEmail completed successfully`);

      logger.info(`User inactive notification email sent successfully to ${userEmail}`, {
        messageId: result.MessageId
      });

      return {
        success: true,
        messageId: result.MessageId
      };

    } catch (error) {
      logger.error('Failed to send user inactive notification email:', {
        error: error.message,
        code: error.code,
        statusCode: error.statusCode,
        stack: error.stack,
        userEmail,
        userName,
        adminName
      });
      throw new Error(`Failed to send user inactive notification email: ${error.message}`);
    }
  }

  async sendAdminPasswordResetNotificationEmail(userEmail, userName = '', adminName = '') {
    try {
      const emailTemplate = this.getAdminPasswordResetNotificationTemplate(userName, userEmail, adminName);

      const params = {
        Source: this.fromEmail,
        Destination: {
          ToAddresses: [userEmail]
        },
        Message: {
          Subject: {
            Data: emailTemplate.subject,
            Charset: 'UTF-8'
          },
          Body: {
            Html: {
              Data: emailTemplate.htmlBody,
              Charset: 'UTF-8'
            },
            Text: {
              Data: emailTemplate.textBody,
              Charset: 'UTF-8'
            }
          }
        },
        Tags: [
          {
            Name: 'EmailType',
            Value: 'AdminPasswordResetNotification'
          }
        ]
      };

      const result = await ses.sendEmail(params).promise();

      logger.info(`Admin password reset notification email sent successfully to ${userEmail}`, {
        messageId: result.MessageId
      });

      return {
        success: true,
        messageId: result.MessageId
      };

    } catch (error) {
      logger.error('Failed to send admin password reset notification email:', error);
      throw new Error(`Failed to send admin password reset notification email: ${error.message}`);
    }
  }

  // Send account status support ticket notification to support team
  async sendAccountStatusTicketNotification(ticketInfo) {
    try {
      const { ticketId, userEmail, subject, message, priority, accountStatus, errorCode } = ticketInfo;

      const supportEmail = this.fromEmail; // Use verified email address
      const ticketNumber = `#${ticketId.toString().slice(-6).toUpperCase()}`;

      const emailSubject = `🚨 Account Status Support Ticket ${ticketNumber} - ${priority} Priority`;

      const htmlContent = `
      <!DOCTYPE html>
      <html>
      <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Account Status Support Ticket</title>
          <style>
              body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
              .container { max-width: 600px; margin: 0 auto; padding: 20px; }
              .header { background: #ff3b30; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
              .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 8px 8px; }
              .ticket-info { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #ff3b30; }
              .priority-high { border-left-color: #ff3b30; }
              .priority-medium { border-left-color: #ff9500; }
              .priority-low { border-left-color: #007aff; }
              .status-badge { display: inline-block; padding: 4px 12px; border-radius: 20px; font-size: 12px; font-weight: bold; text-transform: uppercase; }
              .status-suspended { background: #ff3b30; color: white; }
              .status-inactive { background: #ff9500; color: white; }
              .status-pending { background: #007aff; color: white; }
              .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
          </style>
      </head>
      <body>
          <div class="container">
              <div class="header">
                  <h1>🚨 Account Status Support Ticket</h1>
                  <p>Ticket ${ticketNumber} - ${priority} Priority</p>
              </div>

              <div class="content">
                  <div class="ticket-info priority-${priority.toLowerCase()}">
                      <h2>Ticket Details</h2>
                      <p><strong>Ticket ID:</strong> ${ticketNumber}</p>
                      <p><strong>User Email:</strong> ${userEmail}</p>
                      <p><strong>Account Status:</strong> <span class="status-badge status-${accountStatus?.toLowerCase() || 'unknown'}">${accountStatus || 'Unknown'}</span></p>
                      <p><strong>Error Code:</strong> ${errorCode || 'Not provided'}</p>
                      <p><strong>Priority:</strong> ${priority}</p>
                      <p><strong>Subject:</strong> ${subject}</p>
                  </div>

                  <div class="ticket-info">
                      <h3>User Message:</h3>
                      <p style="white-space: pre-wrap; background: #f5f5f5; padding: 15px; border-radius: 4px;">${message}</p>
                  </div>

                  <div class="ticket-info">
                      <h3>Recommended Actions:</h3>
                      <ul>
                          <li>Review user account status and recent admin actions</li>
                          <li>Check if status change was intentional or in error</li>
                          <li>Contact user within 2 hours for high priority tickets</li>
                          <li>Update ticket status in admin dashboard</li>
                          <li>Document resolution steps for future reference</li>
                      </ul>
                  </div>
              </div>

              <div class="footer">
                  <p>This is an automated notification from the Energy Platform Support System.</p>
                  <p>Please respond to this ticket promptly to maintain customer satisfaction.</p>
              </div>
          </div>
      </body>
      </html>`;

      const params = {
        Source: this.fromEmail,
        Destination: {
          ToAddresses: [supportEmail]
        },
        Message: {
          Subject: {
            Data: emailSubject,
            Charset: 'UTF-8'
          },
          Body: {
            Html: {
              Data: htmlContent,
              Charset: 'UTF-8'
            }
          }
        }
      };

      const result = await ses.sendEmail(params).promise();
      logger.info(`Account status ticket notification sent successfully. MessageId: ${result.MessageId}`);

      return {
        success: true,
        messageId: result.MessageId,
        ticketNumber: ticketNumber
      };

    } catch (error) {
      logger.error('Failed to send account status ticket notification:', error);
      throw new Error(`Failed to send account status ticket notification: ${error.message}`);
    }
  }

  // Verification Email Templates
  getProfileSubmittedEmailTemplate(user) {
    const name = user.firstName ? `${user.firstName} ${user.lastName || ''}`.trim() : 'there';
    const userTypeDisplay = user.userType === 'Broker' ? 'Energy Broker' : 'Energy Supplier';

    const subject = `Profile Submitted - ${this.companyName} ${userTypeDisplay} Application`;

    const htmlBody = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${subject}</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f4f4f4; }
        .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; border-radius: 10px; overflow: hidden; box-shadow: 0 0 20px rgba(0,0,0,0.1); }
        .header { background: linear-gradient(135deg, #10b981 0%, #**********%); color: white; padding: 30px; text-align: center; }
        .header h1 { margin: 0; font-size: 28px; font-weight: 300; }
        .content { padding: 40px 30px; }
        .greeting { font-size: 18px; margin-bottom: 20px; color: #2c3e50; }
        .message { margin-bottom: 30px; font-size: 16px; line-height: 1.8; }
        .status-badge { display: inline-block; background: #10b981; color: white; padding: 8px 16px; border-radius: 20px; font-size: 14px; font-weight: 600; margin: 10px 0; }
        .timeline { background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .timeline h3 { margin-top: 0; color: #495057; }
        .timeline-item { display: flex; align-items: center; margin: 10px 0; }
        .timeline-icon { width: 24px; height: 24px; border-radius: 50%; margin-right: 12px; display: flex; align-items: center; justify-content: center; font-size: 12px; }
        .completed { background-color: #10b981; color: white; }
        .current { background-color: #3b82f6; color: white; }
        .pending { background-color: #e5e7eb; color: #6b7280; }
        .footer { background-color: #f8f9fa; padding: 20px 30px; text-align: center; font-size: 14px; color: #6c757d; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>${this.companyName}</h1>
            <p>Application Status Update</p>
        </div>

        <div class="content">
            <div class="greeting">Hello ${name},</div>

            <div class="message">
                <p>Thank you for submitting your <strong>${userTypeDisplay}</strong> profile! We have received your application and it is now in our review queue.</p>

                <div style="text-align: center;">
                    <span class="status-badge">✓ Profile Submitted</span>
                </div>

                <p>Your application includes all the necessary information for our admin team to review. Here's what happens next:</p>
            </div>

            <div class="timeline">
                <h3>Review Process Timeline</h3>
                <div class="timeline-item">
                    <div class="timeline-icon completed">✓</div>
                    <span><strong>Profile Submitted</strong> - Completed</span>
                </div>
                <div class="timeline-item">
                    <div class="timeline-icon current">2</div>
                    <span><strong>Admin Review</strong> - In Progress (2-3 business days)</span>
                </div>
                <div class="timeline-item">
                    <div class="timeline-icon pending">3</div>
                    <span><strong>Decision & Notification</strong> - Pending</span>
                </div>
                <div class="timeline-item">
                    <div class="timeline-icon pending">4</div>
                    <span><strong>Account Activation</strong> - Pending</span>
                </div>
            </div>

            <div class="message">
                <p><strong>What to expect:</strong></p>
                <ul>
                    <li>📋 Our team will review your profile and documentation</li>
                    <li>📧 You'll receive an email notification once the review is complete</li>
                    <li>⏱️ Typical review time is 2-3 business days</li>
                    <li>❓ We may contact you if additional information is needed</li>
                </ul>

                <p>In the meantime, you can:</p>
                <ul>
                    <li>🔍 Explore our platform resources and documentation</li>
                    <li>📞 Contact our support team if you have any questions</li>
                    <li>📱 Check your dashboard for status updates</li>
                </ul>

                <p>We appreciate your interest in joining our platform and look forward to welcoming you to our community of energy professionals!</p>

                <p>Best regards,<br>
                <strong>The ${this.companyName} Team</strong></p>
            </div>
        </div>

        <div class="footer">
            <p>This email was sent to ${user.email}. If you have questions, please contact our support team.</p>
            <p>© ${new Date().getFullYear()} ${this.companyName}. All rights reserved.</p>
        </div>
    </div>
</body>
</html>`;

    const textBody = `
Hello ${name},

Thank you for submitting your ${userTypeDisplay} profile! We have received your application and it is now in our review queue.

REVIEW PROCESS TIMELINE:
✓ Profile Submitted - Completed
→ Admin Review - In Progress (2-3 business days)
→ Decision & Notification - Pending
→ Account Activation - Pending

WHAT TO EXPECT:
- Our team will review your profile and documentation
- You'll receive an email notification once the review is complete
- Typical review time is 2-3 business days
- We may contact you if additional information is needed

IN THE MEANTIME:
- Explore our platform resources and documentation
- Contact our support team if you have any questions
- Check your dashboard for status updates

We appreciate your interest in joining our platform and look forward to welcoming you to our community of energy professionals!

Best regards,
The ${this.companyName} Team

This email was sent to ${user.email}. If you have questions, please contact our support team.
© ${new Date().getFullYear()} ${this.companyName}. All rights reserved.
`;

    return { subject, htmlBody, textBody };
  }

  getProfileApprovedEmailTemplate(user) {
    const name = user.firstName ? `${user.firstName} ${user.lastName || ''}`.trim() : 'there';
    const userTypeDisplay = user.userType === 'Broker' ? 'Energy Broker' : 'Energy Supplier';
    const dashboardUrl = `${this.frontendUrl}/dashboard`;

    const subject = `Welcome to ${this.companyName} - Your ${userTypeDisplay} Account is Approved!`;

    const htmlBody = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${subject}</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f4f4f4; }
        .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; border-radius: 10px; overflow: hidden; box-shadow: 0 0 20px rgba(0,0,0,0.1); }
        .header { background: linear-gradient(135deg, #10b981 0%, #**********%); color: white; padding: 30px; text-align: center; }
        .header h1 { margin: 0; font-size: 28px; font-weight: 300; }
        .content { padding: 40px 30px; }
        .greeting { font-size: 18px; margin-bottom: 20px; color: #2c3e50; }
        .message { margin-bottom: 30px; font-size: 16px; line-height: 1.8; }
        .cta-button { display: inline-block; background: linear-gradient(135deg, #10b981 0%, #**********%); color: white; padding: 15px 30px; text-decoration: none; border-radius: 50px; font-weight: 600; font-size: 16px; margin: 20px 0; transition: transform 0.2s; }
        .cta-button:hover { transform: translateY(-2px); }
        .success-badge { display: inline-block; background: #10b981; color: white; padding: 8px 16px; border-radius: 20px; font-size: 14px; font-weight: 600; margin: 10px 0; }
        .features { background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .features h3 { margin-top: 0; color: #495057; }
        .footer { background-color: #f8f9fa; padding: 20px 30px; text-align: center; font-size: 14px; color: #6c757d; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎉 Congratulations!</h1>
            <p>Your ${userTypeDisplay} account has been approved</p>
        </div>

        <div class="content">
            <div class="greeting">Hello ${name},</div>

            <div class="message">
                <p>Excellent news! Your <strong>${userTypeDisplay}</strong> application has been approved and your account is now active.</p>

                <div style="text-align: center;">
                    <span class="success-badge">✓ Account Approved</span>
                </div>

                <p>You now have full access to all platform features and can start using ${this.companyName} to grow your energy business.</p>
            </div>

            <div style="text-align: center;">
                <a href="${dashboardUrl}" class="cta-button">Access Your Dashboard</a>
            </div>

            <div class="features">
                <h3>What you can do now:</h3>
                ${user.userType === 'Broker' ? `
                <ul>
                    <li>🎯 <strong>Manage Clients</strong> - Start adding and managing your client portfolio</li>
                    <li>📊 <strong>Generate Quotes</strong> - Create competitive energy quotes for your clients</li>
                    <li>💰 <strong>Track Commissions</strong> - Monitor your earnings and commission payments</li>
                    <li>📈 <strong>View Analytics</strong> - Access performance metrics and growth insights</li>
                    <li>🤝 <strong>Connect with Suppliers</strong> - Access our verified supplier network</li>
                </ul>
                ` : `
                <ul>
                    <li>🏢 <strong>Publish Offers</strong> - Create and manage your energy offers</li>
                    <li>📋 <strong>Upload Tariffs</strong> - Import your pricing structures via CSV</li>
                    <li>🔗 <strong>API Integration</strong> - Connect your systems for real-time pricing</li>
                    <li>📊 <strong>Market Insights</strong> - Access market analytics and trends</li>
                    <li>🎯 <strong>Generate Leads</strong> - Connect with qualified energy customers</li>
                </ul>
                `}
            </div>

            <div class="message">
                <p><strong>Getting Started Tips:</strong></p>
                <ul>
                    <li>📚 Review our platform documentation and best practices</li>
                    <li>🔧 Complete your profile settings and preferences</li>
                    <li>📞 Contact our support team if you need assistance</li>
                    <li>🚀 Start exploring the platform features</li>
                </ul>

                <p>Our support team is here to help you succeed. Don't hesitate to reach out if you have any questions or need guidance getting started.</p>

                <p>Welcome to the ${this.companyName} community!</p>

                <p>Best regards,<br>
                <strong>The ${this.companyName} Team</strong></p>
            </div>
        </div>

        <div class="footer">
            <p>This email was sent to ${user.email}. You can now log in to your account anytime.</p>
            <p>© ${new Date().getFullYear()} ${this.companyName}. All rights reserved.</p>
        </div>
    </div>
</body>
</html>`;

    const textBody = `
Congratulations ${name}!

Your ${userTypeDisplay} application has been approved and your account is now active.

✓ ACCOUNT APPROVED

You now have full access to all platform features and can start using ${this.companyName} to grow your energy business.

ACCESS YOUR DASHBOARD: ${dashboardUrl}

WHAT YOU CAN DO NOW:
${user.userType === 'Broker' ? `
- Manage Clients - Start adding and managing your client portfolio
- Generate Quotes - Create competitive energy quotes for your clients
- Track Commissions - Monitor your earnings and commission payments
- View Analytics - Access performance metrics and growth insights
- Connect with Suppliers - Access our verified supplier network
` : `
- Publish Offers - Create and manage your energy offers
- Upload Tariffs - Import your pricing structures via CSV
- API Integration - Connect your systems for real-time pricing
- Market Insights - Access market analytics and trends
- Generate Leads - Connect with qualified energy customers
`}

GETTING STARTED TIPS:
- Review our platform documentation and best practices
- Complete your profile settings and preferences
- Contact our support team if you need assistance
- Start exploring the platform features

Our support team is here to help you succeed. Don't hesitate to reach out if you have any questions or need guidance getting started.

Welcome to the ${this.companyName} community!

Best regards,
The ${this.companyName} Team

This email was sent to ${user.email}. You can now log in to your account anytime.
© ${new Date().getFullYear()} ${this.companyName}. All rights reserved.
`;

    return { subject, htmlBody, textBody };
  }

  getChangesRequestedEmailTemplate(user, changesRequested, adminNotes) {
    const name = user.firstName ? `${user.firstName} ${user.lastName || ''}`.trim() : 'there';
    const userTypeDisplay = user.userType === 'Broker' ? 'Energy Broker' : 'Energy Supplier';
    const dashboardUrl = `${this.frontendUrl}/dashboard`;

    const subject = `Action Required - ${this.companyName} ${userTypeDisplay} Application`;

    const changesList = changesRequested.map(change =>
      `<li><strong>${change.field}:</strong> ${change.reason}</li>`
    ).join('');

    const changesListText = changesRequested.map(change =>
      `- ${change.field}: ${change.reason}`
    ).join('\n');

    const htmlBody = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${subject}</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f4f4f4; }
        .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; border-radius: 10px; overflow: hidden; box-shadow: 0 0 20px rgba(0,0,0,0.1); }
        .header { background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); color: white; padding: 30px; text-align: center; }
        .header h1 { margin: 0; font-size: 28px; font-weight: 300; }
        .content { padding: 40px 30px; }
        .greeting { font-size: 18px; margin-bottom: 20px; color: #2c3e50; }
        .message { margin-bottom: 30px; font-size: 16px; line-height: 1.8; }
        .cta-button { display: inline-block; background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); color: white; padding: 15px 30px; text-decoration: none; border-radius: 50px; font-weight: 600; font-size: 16px; margin: 20px 0; transition: transform 0.2s; }
        .cta-button:hover { transform: translateY(-2px); }
        .action-badge { display: inline-block; background: #f59e0b; color: white; padding: 8px 16px; border-radius: 20px; font-size: 14px; font-weight: 600; margin: 10px 0; }
        .changes-box { background-color: #fef3c7; border: 1px solid #f59e0b; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .changes-box h3 { margin-top: 0; color: #92400e; }
        .changes-box ul { margin: 10px 0; padding-left: 20px; }
        .changes-box li { margin-bottom: 8px; color: #92400e; }
        .admin-notes { background-color: #f0f9ff; border: 1px solid #0ea5e9; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .admin-notes h3 { margin-top: 0; color: #0c4a6e; }
        .admin-notes p { margin: 0; color: #0c4a6e; }
        .footer { background-color: #f8f9fa; padding: 20px 30px; text-align: center; font-size: 14px; color: #6c757d; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📝 Action Required</h1>
            <p>Updates needed for your ${userTypeDisplay} application</p>
        </div>

        <div class="content">
            <div class="greeting">Hello ${name},</div>

            <div class="message">
                <p>Thank you for your patience while we reviewed your <strong>${userTypeDisplay}</strong> application. Our admin team has completed the initial review and would like you to make some updates to your profile.</p>

                <div style="text-align: center;">
                    <span class="action-badge">📝 Updates Required</span>
                </div>

                <p>Don't worry - this is a normal part of the review process and helps ensure we have all the information needed to approve your account.</p>
            </div>

            <div class="changes-box">
                <h3>📋 Requested Changes:</h3>
                <ul>
                    ${changesList}
                </ul>
            </div>

            ${adminNotes ? `
            <div class="admin-notes">
                <h3>💬 Additional Notes from Admin:</h3>
                <p>${adminNotes}</p>
            </div>
            ` : ''}

            <div style="text-align: center;">
                <a href="${dashboardUrl}" class="cta-button">Update Your Profile</a>
            </div>

            <div class="message">
                <p><strong>Next Steps:</strong></p>
                <ol>
                    <li>📝 <strong>Click the button above</strong> to access your dashboard</li>
                    <li>✏️ <strong>Update your profile</strong> based on the feedback provided</li>
                    <li>💾 <strong>Save your changes</strong> to resubmit for review</li>
                    <li>⏱️ <strong>Wait for approval</strong> - we'll review your updates promptly</li>
                </ol>

                <p><strong>Need Help?</strong></p>
                <ul>
                    <li>📞 Contact our support team if you have questions about the requested changes</li>
                    <li>📚 Review our documentation for guidance on profile completion</li>
                    <li>💬 Use the in-app support chat for quick assistance</li>
                </ul>

                <p>We appreciate your cooperation and look forward to welcoming you to our platform once these updates are complete!</p>

                <p>Best regards,<br>
                <strong>The ${this.companyName} Team</strong></p>
            </div>
        </div>

        <div class="footer">
            <p>This email was sent to ${user.email}. Please update your profile to continue the approval process.</p>
            <p>© ${new Date().getFullYear()} ${this.companyName}. All rights reserved.</p>
        </div>
    </div>
</body>
</html>`;

    const textBody = `
Hello ${name},

Thank you for your patience while we reviewed your ${userTypeDisplay} application. Our admin team has completed the initial review and would like you to make some updates to your profile.

📝 UPDATES REQUIRED

Don't worry - this is a normal part of the review process and helps ensure we have all the information needed to approve your account.

REQUESTED CHANGES:
${changesListText}

${adminNotes ? `
ADDITIONAL NOTES FROM ADMIN:
${adminNotes}
` : ''}

UPDATE YOUR PROFILE: ${dashboardUrl}

NEXT STEPS:
1. Click the link above to access your dashboard
2. Update your profile based on the feedback provided
3. Save your changes to resubmit for review
4. Wait for approval - we'll review your updates promptly

NEED HELP?
- Contact our support team if you have questions about the requested changes
- Review our documentation for guidance on profile completion
- Use the in-app support chat for quick assistance

We appreciate your cooperation and look forward to welcoming you to our platform once these updates are complete!

Best regards,
The ${this.companyName} Team

This email was sent to ${user.email}. Please update your profile to continue the approval process.
© ${new Date().getFullYear()} ${this.companyName}. All rights reserved.
`;

    return { subject, htmlBody, textBody };
  }
}

module.exports = new EmailService();
