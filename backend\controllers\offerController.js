// const Offer = require('../models/Offer');
// const Contract = require('../models/Contract');
// const User = require('../models/User');
const docusignService = require('../services/docusignService');
// const logger = require('../utils/logger');

/**
 * Get all offers for a user
 */
const getUserOffers = async (req, res) => {
  try {
    console.log('Getting user offers from database');

    // Import required models
    const Offer = require('../models/Offer');
    const User = require('../models/User');
    const EnergyRequest = require('../models/EnergyRequest');
    const SupplierProfile = require('../models/SupplierProfile');

    // Get all active offers that are approved and not expired
    const offers = await Offer.find({
      status: 'Active',
      reviewStatus: 'Approved',
      validUntil: { $gt: new Date() }
    })
    .populate('supplierId', 'firstName lastName email')
    .populate('requestId', 'requestType consumptionDetails')
    .sort({ createdAt: -1 })
    .limit(20);

    console.log(`Found ${offers.length} active offers`);

    // Transform offers to match frontend expectations
    const transformedOffers = await Promise.all(offers.map(async (offer) => {
      // Get supplier company name
      let providerName = 'Unknown Provider';
      try {
        const supplierProfile = await SupplierProfile.findOne({ userId: offer.supplierId._id });
        if (supplierProfile && supplierProfile.companyName) {
          providerName = supplierProfile.companyName;
        } else if (offer.supplierId.firstName && offer.supplierId.lastName) {
          providerName = `${offer.supplierId.firstName} ${offer.supplierId.lastName}`;
        }
      } catch (error) {
        console.warn('Error getting supplier profile:', error);
      }

      return {
        id: offer._id.toString(),
        provider: providerName,
        name: offer.offerDetails?.name || 'Energy Offer',
        description: offer.offerDetails?.description || '',
        energyType: offer.energyType,
        rateType: offer.rateType,
        duration: offer.duration,
        price: {
          baseRate: offer.price?.baseRate || 0,
          standingCharge: offer.price?.standingCharge || 0,
          totalEstimatedAnnual: offer.price?.totalEstimatedAnnual || 0,
          currency: offer.price?.currency || 'EUR'
        },
        estimatedSavings: {
          amount: offer.estimatedSavings?.amount || 0,
          percentage: offer.estimatedSavings?.percentage || 0
        },
        highlights: offer.offerDetails?.highlights || [],
        additionalBenefits: offer.additionalBenefits || [],
        validUntil: offer.validUntil,
        status: offer.status,
        views: offer.views || 0,
        applications: offer.applications || 0,
        createdAt: offer.createdAt
      };
    }));

    console.log(`Transformed ${transformedOffers.length} offers for frontend`);

    res.json({
      success: true,
      data: transformedOffers
    });

  } catch (error) {
    console.error('Error fetching user offers:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch offers',
      error: error.message
    });
  }
};

/**
 * Get a specific offer by ID
 */
const getOfferById = async (req, res) => {
  try {
    const { id } = req.params;

    // Import required models
    const Offer = require('../models/Offer');
    const SupplierProfile = require('../models/SupplierProfile');

    // Find the offer by ID
    const offer = await Offer.findById(id)
      .populate('supplierId', 'firstName lastName email')
      .populate('requestId', 'requestType consumptionDetails');

    if (!offer) {
      return res.status(404).json({
        success: false,
        message: 'Offer not found'
      });
    }

    // Get supplier company name
    let providerName = 'Unknown Provider';
    try {
      const supplierProfile = await SupplierProfile.findOne({ userId: offer.supplierId._id });
      if (supplierProfile && supplierProfile.companyName) {
        providerName = supplierProfile.companyName;
      } else if (offer.supplierId.firstName && offer.supplierId.lastName) {
        providerName = `${offer.supplierId.firstName} ${offer.supplierId.lastName}`;
      }
    } catch (error) {
      console.warn('Error getting supplier profile:', error);
    }

    // Transform offer to match frontend expectations
    const transformedOffer = {
      id: offer._id.toString(),
      provider: providerName,
      name: offer.offerDetails?.name || 'Energy Offer',
      description: offer.offerDetails?.description || '',
      energyType: offer.energyType,
      rateType: offer.rateType,
      duration: offer.duration,
      price: {
        baseRate: offer.price?.baseRate || 0,
        standingCharge: offer.price?.standingCharge || 0,
        totalEstimatedAnnual: offer.price?.totalEstimatedAnnual || 0,
        currency: offer.price?.currency || 'EUR'
      },
      estimatedSavings: {
        amount: offer.estimatedSavings?.amount || 0,
        percentage: offer.estimatedSavings?.percentage || 0
      },
      highlights: offer.offerDetails?.highlights || [],
      additionalBenefits: offer.additionalBenefits || [],
      validUntil: offer.validUntil,
      status: offer.status,
      views: offer.views || 0,
      applications: offer.applications || 0,
      termsUrl: offer.termsUrl || '',
      createdAt: offer.createdAt
    };

    res.json({
      success: true,
      data: transformedOffer
    });

  } catch (error) {
    console.error('Error fetching offer:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch offer',
      error: error.message
    });
  }
};

/**
 * Accept an offer and initiate contract signing
 */
const acceptOffer = async (req, res) => {
  try {
    const { id } = req.params;

    // For demo purposes, use mock user data
    const mockUser = {
      _id: 'demo-user-id',
      email: '<EMAIL>',
      firstName: 'Demo',
      lastName: 'User'
    };

    console.log(`Demo user accepting offer ${id}`);

    // Get offer details (mock for now)
    const offer = {
      id,
      provider: 'EDF Energy',
      name: 'Fixed Rate Electricity Plan',
      energyType: 'Electricity',
      rateType: 'Fixed',
      duration: 24,
      price: {
        baseRate: 0.145,
        standingCharge: 25.5,
        totalEstimatedAnnual: 950
      }
    };

    // Create contract record
    const contractNumber = `CNT-${Date.now()}`;
    const startDate = new Date();
    const endDate = new Date();
    endDate.setMonth(endDate.getMonth() + offer.duration);

    // For demo purposes, create mock contract data without database
    const mockContractId = `contract-${Date.now()}`;
    const mockContract = {
      _id: mockContractId,
      offerId: id,
      userId: mockUser._id,
      supplierId: mockUser._id,
      contractDetails: {
        contractNumber,
        energyType: offer.energyType,
        rateType: offer.rateType,
        rate: offer.price.baseRate,
        standingCharge: offer.price.standingCharge,
        estimatedAnnualCost: offer.price.totalEstimatedAnnual,
        currency: 'EUR'
      },
      startDate,
      endDate,
      status: 'Pending'
    };

    console.log(`Mock contract created with ID: ${mockContractId}`);

    // Create DocuSign envelope for contract signing
    try {
      const signingResult = await docusignService.createSigningEnvelope({
        contractId: mockContractId,
        userEmail: mockUser.email,
        userName: `${mockUser.firstName} ${mockUser.lastName}`,
        contractDetails: {
          name: offer.name,
          provider: offer.provider,
          energyType: offer.energyType,
          rateType: offer.rateType,
          duration: offer.duration,
          price: offer.price,
          contractNumber: contractNumber
        }
      });

      console.log('DocuSign envelope created:', signingResult.envelopeId);

      res.json({
        success: true,
        message: 'Offer accepted successfully',
        data: {
          contractId: mockContractId,
          contractNumber,
          signingUrl: signingResult.url,
          envelopeId: signingResult.envelopeId,
          status: 'pending_signature'
        }
      });
    } catch (docusignError) {
      console.error('DocuSign error, falling back to mock:', docusignError);

      // Fallback to mock signing URL if DocuSign fails
      const mockEnvelopeId = `mock-envelope-${Date.now()}`;
      const mockSigningUrl = `http://localhost:8080/contract-signed?contractId=${mockContractId}&envelopeId=${mockEnvelopeId}&mock=true`;

      res.json({
        success: true,
        message: 'Offer accepted successfully',
        data: {
          contractId: mockContractId,
          contractNumber,
          signingUrl: mockSigningUrl,
          envelopeId: mockEnvelopeId,
          status: 'pending_signature'
        }
      });
    }

  } catch (error) {
    console.error('Error accepting offer:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to accept offer',
      error: error.message
    });
  }
};

/**
 * Get offer acceptance status
 */
const getOfferStatus = async (req, res) => {
  try {
    const { id } = req.params;

    console.log(`Getting offer status for offer ${id} - using mock data for demo`);

    // For demo purposes, return that offer is available
    res.json({
      success: true,
      data: {
        status: 'available',
        message: 'Offer is available for acceptance'
      }
    });

  } catch (error) {
    console.error('Error getting offer status:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get offer status',
      error: error.message
    });
  }
};

module.exports = {
  getUserOffers,
  getOfferById,
  acceptOffer,
  getOfferStatus
};
