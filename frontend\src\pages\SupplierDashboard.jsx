import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { showErrorMessage, showSuccessMessage } from '../utils/toastNotifications';
import DashboardLayout from '../components/DashboardLayout';
import Spinner from '../components/Spinner';
import supplierService from '../services/supplier.service';
import logger from '../utils/logger';
import '../styles/supplier-dashboard.css';

const SupplierDashboard = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [dashboardData, setDashboardData] = useState(null);
  const [activeOffers, setActiveOffers] = useState([]);
  const [recentContracts, setRecentContracts] = useState([]);
  const [stats, setStats] = useState({
    totalOffers: 0,
    activeContracts: 0,
    totalRevenue: 0,
    newCustomers: 0
  });

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      logger.info('Fetching supplier dashboard data');

      // Fetch dashboard stats
      const statsResponse = await supplierService.getDashboardStats();
      if (statsResponse.success) {
        setStats(statsResponse.data);
      }

      // Fetch active offers
      const offersResponse = await supplierService.getActiveOffers();
      if (offersResponse.success) {
        setActiveOffers(offersResponse.data);
      }

      // Fetch recent contracts
      const contractsResponse = await supplierService.getRecentContracts();
      if (contractsResponse.success) {
        setRecentContracts(contractsResponse.data);
      }

      logger.info('Supplier dashboard data loaded successfully');
    } catch (error) {
      logger.error('Error fetching supplier dashboard data:', error);
      showErrorMessage('DATA_LOAD_FAILED');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateOffer = () => {
    navigate('/create-offer');
  };

  const handleViewAllOffers = () => {
    navigate('/my-offers');
  };

  const handleViewAllContracts = () => {
    navigate('/supplier-contracts');
  };

  const handleViewAnalytics = () => {
    navigate('/analytics');
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="supplier-dashboard-container">
          <div className="loading-container">
            <Spinner size="large" message="Loading supplier dashboard..." />
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="supplier-dashboard-container">
        {/* Stats Cards */}
        <div className="supplier-stats-container">
          <div className="supplier-stats-grid">
            <div className="supplier-stat-card">
              <div className="supplier-stat-card-content">
                <div className="supplier-stat-card-icon">
                  <i className="fas fa-tags"></i>
                </div>
                <div className="supplier-stat-card-title">Active Offers</div>
                <div className="supplier-stat-card-description">Current energy offers available</div>
              </div>
              <div className="supplier-stat-card-value">{stats.totalOffers}</div>
            </div>

            <div className="supplier-stat-card">
              <div className="supplier-stat-card-content">
                <div className="supplier-stat-card-icon">
                  <i className="fas fa-file-contract"></i>
                </div>
                <div className="supplier-stat-card-title">Active Contracts</div>
                <div className="supplier-stat-card-description">Current active customer contracts</div>
              </div>
              <div className="supplier-stat-card-value">{stats.activeContracts}</div>
            </div>

            <div className="supplier-stat-card">
              <div className="supplier-stat-card-content">
                <div className="supplier-stat-card-icon">
                  <i className="fas fa-euro-sign"></i>
                </div>
                <div className="supplier-stat-card-title">Monthly Revenue</div>
                <div className="supplier-stat-card-description">Revenue generated this month</div>
              </div>
              <div className="supplier-stat-card-value">€{stats.totalRevenue.toLocaleString()}</div>
            </div>

            <div className="supplier-stat-card">
              <div className="supplier-stat-card-content">
                <div className="supplier-stat-card-icon">
                  <i className="fas fa-user-plus"></i>
                </div>
                <div className="supplier-stat-card-title">New Customers</div>
                <div className="supplier-stat-card-description">New customers this month</div>
              </div>
              <div className="supplier-stat-card-value">{stats.newCustomers}</div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="dashboard-content-grid">
          {/* Active Offers */}
          <div className="dashboard-card">
            <div className="card-header">
              <h3>
                <i className="fas fa-tags"></i>
                Active Offers
              </h3>
              <button className="btn-link" onClick={handleViewAllOffers}>
                View All
              </button>
            </div>
            <div className="card-content">
              {activeOffers.length > 0 ? (
                <div className="offers-list">
                  {activeOffers.slice(0, 5).map((offer) => (
                    <div key={offer._id} className="offer-item">
                      <div className="offer-info">
                        <h4>{offer.offerDetails?.name || 'Energy Offer'}</h4>
                        <p>{offer.energyType} • {offer.rateType}</p>
                        <span className="offer-rate">€{offer.price?.baseRate || 0}/kWh</span>
                      </div>
                      <div className="offer-stats">
                        <span className="views">{offer.views || 0} views</span>
                        <span className="applications">{offer.applications || 0} applications</span>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="empty-state">
                  <i className="fas fa-tags"></i>
                  <p>No active offers</p>
                  <button className="btn-primary" onClick={handleCreateOffer}>
                    Create Your First Offer
                  </button>
                </div>
              )}
            </div>
          </div>

          {/* Recent Contracts */}
          <div className="dashboard-card">
            <div className="card-header">
              <h3>
                <i className="fas fa-file-contract"></i>
                Recent Contracts
              </h3>
              <button className="btn-link" onClick={handleViewAllContracts}>
                View All
              </button>
            </div>
            <div className="card-content">
              {recentContracts.length > 0 ? (
                <div className="contracts-list">
                  {recentContracts.slice(0, 5).map((contract) => (
                    <div key={contract._id} className="contract-item">
                      <div className="contract-info">
                        <h4>{contract.userId?.firstName} {contract.userId?.lastName}</h4>
                        <p>Contract #{contract.contractDetails?.contractNumber || 'N/A'}</p>
                        <span className="contract-date">
                          {new Date(contract.startDate).toLocaleDateString()}
                        </span>
                      </div>
                      <div className="contract-status">
                        <span className={`status-badge ${contract.status.toLowerCase()}`}>
                          {contract.status}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="empty-state">
                  <i className="fas fa-file-contract"></i>
                  <p>No recent contracts</p>
                </div>
              )}
            </div>
          </div>

          {/* Quick Actions */}
          <div className="dashboard-card">
            <div className="card-header">
              <h3>
                <i className="fas fa-bolt"></i>
                Quick Actions
              </h3>
            </div>
            <div className="card-content">
              <div className="quick-actions">
                <button className="action-btn" onClick={handleCreateOffer}>
                  <i className="fas fa-plus"></i>
                  <span>Create Offer</span>
                </button>
                <button className="action-btn" onClick={handleViewAnalytics}>
                  <i className="fas fa-chart-line"></i>
                  <span>View Analytics</span>
                </button>
                <button className="action-btn" onClick={() => navigate('/clients')}>
                  <i className="fas fa-users"></i>
                  <span>Manage Clients</span>
                </button>
                <button className="action-btn" onClick={() => navigate('/profile')}>
                  <i className="fas fa-cog"></i>
                  <span>Settings</span>
                </button>
              </div>
            </div>
          </div>

          {/* Performance Overview */}
          <div className="dashboard-card">
            <div className="card-header">
              <h3>
                <i className="fas fa-chart-bar"></i>
                Performance Overview
              </h3>
            </div>
            <div className="card-content">
              <div className="performance-metrics">
                <div className="metric">
                  <span className="metric-label">Conversion Rate</span>
                  <span className="metric-value">{stats?.performanceMetrics?.conversionRate || 0}%</span>
                </div>
                <div className="metric">
                  <span className="metric-label">Avg. Contract Value</span>
                  <span className="metric-value">€{stats?.performanceMetrics?.avgContractValue || 0}</span>
                </div>
                <div className="metric">
                  <span className="metric-label">Customer Satisfaction</span>
                  <span className="metric-value">{stats?.performanceMetrics?.customerSatisfaction || 0}/5</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default SupplierDashboard;
