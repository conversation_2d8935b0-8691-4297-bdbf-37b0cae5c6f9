/**
 * Utility functions for PDL/PRM/RAE meter number validation
 */

/**
 * Validates PDL/PRM/RAE meter number
 * @param {string} meterNumber - The meter number to validate
 * @returns {object} - Validation result with isValid and errorMessage
 */
export const validateMeterNumber = (meterNumber) => {
  if (!meterNumber || !meterNumber.trim()) {
    return {
      isValid: false,
      errorMessage: 'PDL/PRM/RAE number is required'
    };
  }

  // Remove any spaces and validate
  const cleanNumber = meterNumber.replace(/\s/g, '');
  
  // Check if it's exactly 14 digits
  if (!/^\d{14}$/.test(cleanNumber)) {
    return {
      isValid: false,
      errorMessage: 'PDL/PRM/RAE number must be exactly 14 digits'
    };
  }

  return {
    isValid: true,
    errorMessage: null
  };
};

/**
 * Formats meter number input to only allow digits and limit to 14 characters
 * @param {string} value - The input value
 * @returns {string} - Formatted value (digits only, max 14 characters)
 */
export const formatMeterNumberInput = (value) => {
  // Remove all non-digit characters
  const digitsOnly = value.replace(/\D/g, '');
  
  // Limit to 14 digits
  return digitsOnly.slice(0, 14);
};

/**
 * Handles meter number input change with validation
 * @param {Event} event - The input change event
 * @param {Function} setFormData - Function to update form data
 * @param {Function} setErrors - Function to update errors (optional)
 * @returns {void}
 */
export const handleMeterNumberChange = (event, setFormData, setErrors = null) => {
  const { name, value } = event.target;
  const formattedValue = formatMeterNumberInput(value);
  
  // Update form data
  setFormData(prevData => ({
    ...prevData,
    [name]: formattedValue
  }));

  // Clear error if value is being corrected and setErrors is provided
  if (setErrors && formattedValue.length > 0) {
    setErrors(prevErrors => ({
      ...prevErrors,
      [name]: null
    }));
  }
};

/**
 * Common props for meter number input fields
 */
export const getMeterNumberInputProps = () => ({
  type: 'text',
  placeholder: 'Enter your 14-digit meter number',
  pattern: '[0-9]{14}',
  maxLength: '14',
  inputMode: 'numeric',
  autoComplete: 'off'
});

/**
 * Common hint text for meter number fields
 */
export const METER_NUMBER_HINT = 'Your meter identification number (exactly 14 digits)';

/**
 * Common label for meter number fields
 */
export const METER_NUMBER_LABEL = 'PDL/PRM/RAE Number';
