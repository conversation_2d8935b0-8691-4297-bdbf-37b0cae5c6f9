const mongoose = require('mongoose');
const dotenv = require('dotenv');
const logger = require('../utils/logger');
const path = require('path');

// Load environment variables
const cliEnv = process.argv[2];
process.env.NODE_ENV = cliEnv || process.env.NODE_ENV || 'local';

const envFile = `.env.${process.env.NODE_ENV}`;

dotenv.config({
  path: path.resolve(__dirname, `../${envFile}`),
  override: false,
});

console.log(`Loaded environment: ${envFile}`);

// MongoDB connection string
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/energy_bill_db';
console.log('database MongoDB URI:', MONGODB_URI);

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(MONGODB_URI);
    logger.info('MongoDB connected successfully');
  } catch (error) {
    logger.error('MongoDB connection error:', error);
    process.exit(1); // Exit process with failure
  }
};

module.exports = connectDB;
