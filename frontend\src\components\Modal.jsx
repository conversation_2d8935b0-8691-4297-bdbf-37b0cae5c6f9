import React, { useEffect } from 'react';
import '../styles/modal-system.css';

const Modal = ({
  isOpen,
  onClose,
  title,
  subtitle,
  children,
  footer,
  size = 'medium',
  showCloseButton = true,
  closeOnOverlayClick = true,
  className = ''
}) => {
  // Handle escape key
  useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  const handleOverlayClick = (e) => {
    if (e.target === e.currentTarget && closeOnOverlayClick) {
      onClose();
    }
  };

  const sizeClasses = {
    small: 'max-w-md',
    medium: 'max-w-2xl',
    large: 'max-w-4xl',
    full: 'max-w-6xl'
  };

  return (
    <div className="modal-overlay" onClick={handleOverlayClick}>
      <div className={`modal-container ${sizeClasses[size]} ${className}`}>
        {/* Header */}
        <div className="modal-header">
          <div>
            <h3>{title}</h3>
            {subtitle && <p>{subtitle}</p>}
          </div>
          {showCloseButton && (
            <button className="modal-close" onClick={onClose} aria-label="Close modal">
              ×
            </button>
          )}
        </div>

        {/* Body */}
        <div className="modal-body">
          {children}
        </div>

        {/* Footer */}
        {footer && footer}
      </div>
    </div>
  );
};

// Step Indicator Component
export const StepIndicator = ({ steps, currentStep }) => {
  return (
    <div className="step-indicator">
      {steps.map((step, index) => {
        const stepNumber = index + 1;
        const isActive = stepNumber === currentStep;
        const isCompleted = stepNumber < currentStep;
        const isPending = stepNumber > currentStep;

        return (
          <React.Fragment key={step.id || index}>
            <div className={`step-item ${isActive ? 'active' : ''} ${isCompleted ? 'completed' : ''} ${isPending ? 'pending' : ''}`}>
              <div className="step-number">
                {isCompleted ? '✓' : stepNumber}
              </div>
              <div className="step-label">{step.label}</div>
            </div>
            {index < steps.length - 1 && <div className="step-connector" />}
          </React.Fragment>
        );
      })}
    </div>
  );
};

// Form Section Component
export const FormSection = ({ title, icon, children }) => {
  return (
    <div className="form-section">
      {title && (
        <h4 className="section-title">
          {icon && <i className={icon}></i>}
          {title}
        </h4>
      )}
      {children}
    </div>
  );
};

// Form Group Component
export const FormGroup = ({ label, required, error, children, className = '' }) => {
  return (
    <div className={`form-group ${className}`}>
      {label && (
        <label>
          {label}
          {required && <span style={{ color: '#ef4444', marginLeft: '4px' }}>*</span>}
        </label>
      )}
      {children}
      {error && (
        <div style={{ color: '#ef4444', fontSize: '12px', marginTop: '4px' }}>
          {error}
        </div>
      )}
    </div>
  );
};

// File Upload Component
export const FileUpload = ({ onFileSelect, accept, multiple = false, children }) => {
  const handleFileChange = (e) => {
    const files = Array.from(e.target.files);
    onFileSelect(multiple ? files : files[0]);
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    const files = Array.from(e.dataTransfer.files);
    onFileSelect(multiple ? files : files[0]);
    e.currentTarget.classList.remove('dragover');
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    e.stopPropagation();
    e.currentTarget.classList.add('dragover');
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    e.stopPropagation();
    e.currentTarget.classList.remove('dragover');
  };

  return (
    <div
      className="file-upload-area"
      onDrop={handleDrop}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onClick={() => document.getElementById('file-input').click()}
    >
      <input
        id="file-input"
        type="file"
        accept={accept}
        multiple={multiple}
        onChange={handleFileChange}
        style={{ display: 'none' }}
      />
      {children || (
        <>
          <div className="file-upload-icon">📁</div>
          <div className="file-upload-text">
            <strong>Click to upload</strong> or drag and drop
            <br />
            <small>Supported formats: {accept}</small>
          </div>
        </>
      )}
    </div>
  );
};

// Button Component
export const Button = ({
  children,
  variant = 'primary',
  size = 'medium',
  loading = false,
  disabled = false,
  onClick,
  type = 'button',
  className = '',
  ...props
}) => {
  const baseClass = 'btn';
  const variantClass = `btn-${variant}`;
  const loadingClass = loading ? 'btn-loading' : '';
  
  return (
    <button
      type={type}
      className={`${baseClass} ${variantClass} ${loadingClass} ${className}`}
      onClick={onClick}
      disabled={disabled || loading}
      {...props}
    >
      {children}
    </button>
  );
};

// Modal Footer Helper
export const ModalFooter = ({ children, leftContent, rightContent }) => {
  if (children) {
    return <div className="modal-footer">{children}</div>;
  }

  return (
    <div className="modal-footer">
      <div>{leftContent}</div>
      <div style={{ display: 'flex', gap: '12px' }}>{rightContent}</div>
    </div>
  );
};

export default Modal;
