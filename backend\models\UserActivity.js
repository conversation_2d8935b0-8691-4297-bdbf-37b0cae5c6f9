const mongoose = require('mongoose');

const userActivitySchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  activityType: {
    type: String,
    enum: [
      'Login',
      'Logout',
      'ProfileUpdate',
      'DocumentUpload',
      'RequestCreated',
      'OfferReceived',
      'ContractSigned',
      'AppointmentScheduled',
      'PaymentMade',
      'PasswordReset',
      'PasswordResetRequested',
      'EmailVerified',
      'PhoneVerified',
      'StatusChanged',
      'SuspendedLoginAttempt',
      'InactiveLoginAttempt',
      'PendingLoginAttempt',
      'SuspendedAccessAttempt',
      'Other'
    ],
    required: true
  },
  description: {
    type: String,
    required: true,
    trim: true
  },
  details: {
    type: mongoose.Schema.Types.Mixed
  },
  ipAddress: {
    type: String,
    trim: true
  },
  userAgent: {
    type: String,
    trim: true
  },
  location: {
    country: String,
    city: String,
    region: String
  },
  relatedEntity: {
    entityType: {
      type: String,
      enum: ['EnergyRequest', 'Offer', 'Contract', 'Appointment', 'Document', 'User']
    },
    entityId: {
      type: mongoose.Schema.Types.ObjectId
    }
  },
  severity: {
    type: String,
    enum: ['Low', 'Normal', 'High', 'Critical'],
    default: 'Normal'
  },
  isSystemGenerated: {
    type: Boolean,
    default: false
  }
}, {
  timestamps: true
});

// Create indexes for efficient querying
userActivitySchema.index({ userId: 1, createdAt: -1 });
userActivitySchema.index({ activityType: 1 });
userActivitySchema.index({ createdAt: -1 });
userActivitySchema.index({ severity: 1 });

// Static method to log activity
userActivitySchema.statics.logActivity = async function(activityData) {
  try {
    const activity = new this(activityData);
    await activity.save();
    return activity;
  } catch (error) {
    console.error('Error logging user activity:', error);
    throw error;
  }
};

// Static method to get user activities with pagination
userActivitySchema.statics.getUserActivities = async function(userId, options = {}) {
  const {
    page = 1,
    limit = 20,
    activityType,
    startDate,
    endDate,
    severity
  } = options;

  const filter = { userId };
  
  if (activityType) {
    filter.activityType = activityType;
  }
  
  if (severity) {
    filter.severity = severity;
  }
  
  if (startDate || endDate) {
    filter.createdAt = {};
    if (startDate) filter.createdAt.$gte = new Date(startDate);
    if (endDate) filter.createdAt.$lte = new Date(endDate);
  }

  const skip = (parseInt(page) - 1) * parseInt(limit);

  const [activities, totalCount] = await Promise.all([
    this.find(filter)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit))
      .populate('userId', 'firstName lastName email userType'),
    this.countDocuments(filter)
  ]);

  return {
    activities,
    pagination: {
      currentPage: parseInt(page),
      totalPages: Math.ceil(totalCount / parseInt(limit)),
      totalCount,
      hasNextPage: parseInt(page) < Math.ceil(totalCount / parseInt(limit)),
      hasPrevPage: parseInt(page) > 1
    }
  };
};

const UserActivity = mongoose.model('UserActivity', userActivitySchema);

module.exports = UserActivity;
