import axios from 'axios';
import logger from '../utils/logger';
import { API_BASE_URL } from '../config/api-config';

class ContractService {
  constructor() {
    this.api = axios.create({
      baseURL: `${API_BASE_URL}/api/contracts`,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Add request interceptor to include auth token
    this.api.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        logger.error('Request interceptor error:', error);
        return Promise.reject(error);
      }
    );

    // Add response interceptor for error handling
    this.api.interceptors.response.use(
      (response) => response,
      (error) => {
        logger.error('API Error:', error.response?.data || error.message);

        if (error.response?.status === 401) {
          // Token expired or invalid
          localStorage.removeItem('token');
          window.location.href = '/login';
        }

        return Promise.reject(error);
      }
    );
  }

  /**
   * Get all contracts for the current user
   */
  async getUserContracts() {
    try {
      logger.info('Fetching user contracts');
      const response = await this.api.get('/user');
      logger.info('Contracts fetched successfully:', response.data.data?.length || 0);
      return response.data;
    } catch (error) {
      logger.error('Error fetching user contracts:', error);
      throw this.handleError(error);
    }
  }

  /**
   * Get a specific contract by ID
   */
  async getContractById(contractId) {
    try {
      logger.info('Fetching contract by ID:', contractId);
      const response = await this.api.get(`/${contractId}`);
      logger.info('Contract fetched successfully');
      return response.data;
    } catch (error) {
      logger.error('Error fetching contract:', error);
      throw this.handleError(error);
    }
  }

  /**
   * Get contract signing status
   */
  async getSigningStatus(contractId) {
    try {
      logger.info('Getting contract signing status:', contractId);
      const response = await this.api.get(`/${contractId}/signing-status`);
      return response.data;
    } catch (error) {
      logger.error('Error getting signing status:', error);
      throw this.handleError(error);
    }
  }

  /**
   * Update contract signature status
   */
  async updateSignatureStatus(contractId, statusData) {
    try {
      logger.info('Updating contract signature status:', contractId);
      const response = await this.api.post(`/${contractId}/signature-status`, statusData);
      logger.info('Signature status updated successfully');
      return response.data;
    } catch (error) {
      logger.error('Error updating signature status:', error);
      throw this.handleError(error);
    }
  }

  /**
   * Download signed contract
   */
  async downloadContract(contractId) {
    try {
      logger.info('Downloading contract:', contractId);
      const response = await this.api.get(`/${contractId}/download`, {
        responseType: 'blob'
      });

      // Create download link
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `contract-${contractId}.pdf`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);

      logger.info('Contract downloaded successfully');
      return true;
    } catch (error) {
      logger.error('Error downloading contract:', error);
      throw this.handleError(error);
    }
  }

  /**
   * Resend signing invitation
   */
  async resendSigning(contractId) {
    try {
      logger.info('Resending signing invitation:', contractId);
      const response = await this.api.post(`/${contractId}/resend-signing`);
      logger.info('Signing invitation sent successfully');
      return response.data;
    } catch (error) {
      logger.error('Error resending signing invitation:', error);
      throw this.handleError(error);
    }
  }

  /**
   * Handle API errors
   */
  handleError(error) {
    if (error.response) {
      // Server responded with error status
      const message = error.response.data?.message || 'An error occurred';
      return new Error(message);
    } else if (error.request) {
      // Request was made but no response received
      return new Error('Network error - please check your connection');
    } else {
      // Something else happened
      return new Error(error.message || 'An unexpected error occurred');
    }
  }
}

// Export singleton instance
export default new ContractService();
