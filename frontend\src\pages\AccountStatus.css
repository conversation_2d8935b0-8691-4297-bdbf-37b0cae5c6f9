.account-status-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', sans-serif;
}

.account-status-card {
  max-width: 600px;
  width: 100%;
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: 2px solid;
  overflow: hidden;
  animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.status-header {
  text-align: center;
  padding: 40px 30px 20px;
  background: rgba(255, 255, 255, 0.9);
}

.status-icon {
  margin-bottom: 16px;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

.status-title {
  font-size: 28px;
  font-weight: 600;
  margin: 0;
  line-height: 1.2;
}

.status-content {
  padding: 20px 30px;
}

.status-description {
  font-size: 16px;
  color: #4a5568;
  margin-bottom: 20px;
  line-height: 1.6;
}

.status-message {
  background: #f7fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 24px;
  font-size: 14px;
  color: #2d3748;
}

.status-section {
  margin-bottom: 24px;
}

.status-section h3 {
  font-size: 18px;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 12px;
  margin-top: 0;
}

.status-section ul {
  margin: 0;
  padding-left: 20px;
  color: #4a5568;
}

.status-section li {
  margin-bottom: 8px;
  line-height: 1.5;
}

.support-info {
  background: #edf2f7;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 24px;
}

.support-info h3 {
  font-size: 16px;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 8px;
  margin-top: 0;
}

.support-info p {
  margin: 0;
  color: #4a5568;
  line-height: 1.6;
}

.status-actions {
  padding: 20px 30px 30px;
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.btn-primary {
  background: #007aff;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  flex: 1;
  min-width: 160px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 122, 255, 0.3);
}

.btn-primary:active {
  transform: translateY(0);
}

.btn-secondary {
  background: #f7fafc;
  color: #4a5568;
  border: 2px solid #e2e8f0;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  flex: 1;
  min-width: 160px;
}

.btn-secondary:hover {
  background: #edf2f7;
  border-color: #cbd5e0;
  transform: translateY(-1px);
}

.btn-secondary:active {
  transform: translateY(0);
}

.loading {
  text-align: center;
  color: white;
  font-size: 18px;
  font-weight: 500;
}

/* Contact Support Modal - Professional Design */
.contact-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.75);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
  animation: modalOverlayFadeIn 0.3s ease-out;
}

@keyframes modalOverlayFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.contact-modal {
  background: white;
  border-radius: 20px;
  max-width: 500px;
  width: 100%;
  max-height: 85vh;
  position: relative;
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(255, 255, 255, 0.1);
  animation: modalSlideIn 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Modal Header */
.contact-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 32px;
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  border-bottom: 1px solid #e2e8f0;
  flex-shrink: 0;
}

.contact-modal-header h2 {
  margin: 0;
  font-size: 22px;
  font-weight: 600;
  color: #2d3748;
  display: flex;
  align-items: center;
  gap: 12px;
}

.close-modal {
  background: #f7fafc;
  border: 2px solid #e2e8f0;
  font-size: 18px;
  color: #718096;
  cursor: pointer;
  padding: 0;
  border-radius: 12px;
  transition: all 0.2s ease;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
}

.close-modal:hover {
  background: #edf2f7;
  border-color: #cbd5e0;
  color: #4a5568;
  transform: scale(1.05);
}

/* Modal Content */
.modal-content-wrapper {
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}





/* Support Ticket Form */
.support-ticket-form {
  padding: 32px;
  background: #ffffff;
  flex: 1;
  overflow-y: auto;
}

/* Form Introduction */
.form-intro {
  text-align: center;
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid #e2e8f0;
}

.form-intro-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.form-intro h3 {
  margin: 0 0 12px 0;
  font-size: 24px;
  font-weight: 600;
  color: #2d3748;
}

.form-intro p {
  margin: 0;
  font-size: 16px;
  color: #4a5568;
  line-height: 1.5;
}

.form-group {
  margin-bottom: 24px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #2d3748;
  font-size: 15px;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 14px 16px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 15px;
  font-family: inherit;
  background: #ffffff;
  color: #2d3748;
  transition: all 0.2s ease;
  box-sizing: border-box;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  outline: none;
  border-color: #007aff;
  box-shadow: 0 0 0 4px rgba(0, 122, 255, 0.15);
  background: #fafbff;
}

.form-group input::placeholder,
.form-group textarea::placeholder {
  color: #a0aec0;
  font-style: italic;
}

.form-group textarea {
  resize: vertical;
  min-height: 120px;
  line-height: 1.5;
}

.form-group select {
  cursor: pointer;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 12px center;
  background-repeat: no-repeat;
  background-size: 16px;
  padding-right: 40px;
  appearance: none;
}

/* Submission Status */
.submission-status {
  margin: 24px 0;
  padding: 20px;
  border-radius: 12px;
  border: 2px solid;
  animation: slideIn 0.3s ease-out;
}

.submission-status.success {
  background: linear-gradient(135deg, #f0fff4 0%, #e6fffa 100%);
  border-color: #38a169;
  color: #2d3748;
}

.submission-status.error {
  background: linear-gradient(135deg, #fff5f5 0%, #fed7d7 100%);
  border-color: #e53e3e;
  color: #2d3748;
}

.success-message, .error-message {
  display: flex;
  align-items: flex-start;
  gap: 16px;
}

.success-icon, .error-icon {
  font-size: 24px;
  flex-shrink: 0;
  margin-top: 2px;
}

.success-text, .error-text {
  flex: 1;
}

.success-text strong, .error-text strong {
  display: block;
  font-size: 16px;
  margin-bottom: 8px;
}

.success-text p, .error-text p {
  margin: 4px 0;
  font-size: 14px;
  line-height: 1.4;
}

/* Loading Spinner */
.loading-spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #ffffff;
  animation: spin 1s ease-in-out infinite;
  margin-right: 8px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Form Actions */
.ticket-form-actions {
  display: flex;
  gap: 16px;
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #e2e8f0;
}

.ticket-form-actions .btn-primary {
  flex: 2;
  background: linear-gradient(135deg, #007aff 0%, #0056cc 100%);
  padding: 16px 24px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 122, 255, 0.3);
  transition: all 0.2s ease;
  color: white;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.ticket-form-actions .btn-primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 122, 255, 0.4);
}

.ticket-form-actions .btn-primary:disabled {
  background: linear-gradient(135deg, #a0aec0 0%, #718096 100%);
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  opacity: 0.7;
}

.ticket-form-actions .btn-secondary {
  flex: 1;
  background: #f7fafc;
  color: #4a5568;
  border: 2px solid #e2e8f0;
  padding: 16px 24px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 12px;
  transition: all 0.2s ease;
}

.ticket-form-actions .btn-secondary:hover:not(:disabled) {
  background: #edf2f7;
  border-color: #cbd5e0;
  transform: translateY(-1px);
}

.ticket-form-actions .btn-secondary:disabled {
  background: #f7fafc;
  color: #a0aec0;
  border-color: #e2e8f0;
  cursor: not-allowed;
  opacity: 0.6;
}

/* Responsive Design */
@media (max-width: 768px) {
  .account-status-container {
    padding: 16px;
  }

  .account-status-card {
    border-radius: 12px;
  }

  .status-header {
    padding: 30px 20px 16px;
  }

  .status-title {
    font-size: 24px;
  }

  .status-content {
    padding: 16px 20px;
  }

  .status-actions {
    padding: 16px 20px 24px;
    flex-direction: column;
  }

  .btn-primary,
  .btn-secondary {
    min-width: auto;
    width: 100%;
  }

  .contact-modal {
    margin: 10px;
    max-height: 95vh;
  }



  .contact-modal-header {
    padding: 20px;
  }

  .support-ticket-form {
    padding: 20px;
  }

  .ticket-form-actions {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .status-header {
    padding: 24px 16px 12px;
  }

  .status-title {
    font-size: 20px;
  }

  .status-content {
    padding: 12px 16px;
  }

  .status-actions {
    padding: 12px 16px 20px;
  }

  .status-section h3 {
    font-size: 16px;
  }

  .btn-primary,
  .btn-secondary {
    padding: 14px 20px;
    font-size: 15px;
  }
}
