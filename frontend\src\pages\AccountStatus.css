.account-status-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', sans-serif;
}

.account-status-card {
  max-width: 600px;
  width: 100%;
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: 2px solid;
  overflow: hidden;
  animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.status-header {
  text-align: center;
  padding: 40px 30px 20px;
  background: rgba(255, 255, 255, 0.9);
}

.status-icon {
  margin-bottom: 16px;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

.status-title {
  font-size: 28px;
  font-weight: 600;
  margin: 0;
  line-height: 1.2;
}

.status-content {
  padding: 20px 30px;
}

.status-description {
  font-size: 16px;
  color: #4a5568;
  margin-bottom: 20px;
  line-height: 1.6;
}

.status-message {
  background: #f7fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 24px;
  font-size: 14px;
  color: #2d3748;
}

.status-section {
  margin-bottom: 24px;
}

.status-section h3 {
  font-size: 18px;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 12px;
  margin-top: 0;
}

.status-section ul {
  margin: 0;
  padding-left: 20px;
  color: #4a5568;
}

.status-section li {
  margin-bottom: 8px;
  line-height: 1.5;
}

.support-info {
  background: #edf2f7;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 24px;
}

.support-info h3 {
  font-size: 16px;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 8px;
  margin-top: 0;
}

.support-info p {
  margin: 0;
  color: #4a5568;
  line-height: 1.6;
}

.status-actions {
  padding: 20px 30px 30px;
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.btn-primary {
  background: #007aff;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  flex: 1;
  min-width: 160px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 122, 255, 0.3);
}

.btn-primary:active {
  transform: translateY(0);
}

.btn-secondary {
  background: #f7fafc;
  color: #4a5568;
  border: 2px solid #e2e8f0;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  flex: 1;
  min-width: 160px;
}

.btn-secondary:hover {
  background: #edf2f7;
  border-color: #cbd5e0;
  transform: translateY(-1px);
}

.btn-secondary:active {
  transform: translateY(0);
}

.loading {
  text-align: center;
  color: white;
  font-size: 18px;
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
  .account-status-container {
    padding: 16px;
  }

  .account-status-card {
    border-radius: 12px;
  }

  .status-header {
    padding: 30px 20px 16px;
  }

  .status-title {
    font-size: 24px;
  }

  .status-content {
    padding: 16px 20px;
  }

  .status-actions {
    padding: 16px 20px 24px;
    flex-direction: column;
  }

  .btn-primary,
  .btn-secondary {
    min-width: auto;
    width: 100%;
  }
}

@media (max-width: 480px) {
  .status-header {
    padding: 24px 16px 12px;
  }

  .status-title {
    font-size: 20px;
  }

  .status-content {
    padding: 12px 16px;
  }

  .status-actions {
    padding: 12px 16px 20px;
  }

  .status-section h3 {
    font-size: 16px;
  }

  .btn-primary,
  .btn-secondary {
    padding: 14px 20px;
    font-size: 15px;
  }
}
