.account-status-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', sans-serif;
}

.account-status-card {
  max-width: 600px;
  width: 100%;
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: 2px solid;
  overflow: hidden;
  animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.status-header {
  text-align: center;
  padding: 40px 30px 20px;
  background: rgba(255, 255, 255, 0.9);
}

.status-icon {
  margin-bottom: 16px;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

.status-title {
  font-size: 28px;
  font-weight: 600;
  margin: 0;
  line-height: 1.2;
}

.status-content {
  padding: 20px 30px;
}

.status-description {
  font-size: 16px;
  color: #4a5568;
  margin-bottom: 20px;
  line-height: 1.6;
}

.status-message {
  background: #f7fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 24px;
  font-size: 14px;
  color: #2d3748;
}

.status-section {
  margin-bottom: 24px;
}

.status-section h3 {
  font-size: 18px;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 12px;
  margin-top: 0;
}

.status-section ul {
  margin: 0;
  padding-left: 20px;
  color: #4a5568;
}

.status-section li {
  margin-bottom: 8px;
  line-height: 1.5;
}

.support-info {
  background: #edf2f7;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 24px;
}

.support-info h3 {
  font-size: 16px;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 8px;
  margin-top: 0;
}

.support-info p {
  margin: 0;
  color: #4a5568;
  line-height: 1.6;
}

.status-actions {
  padding: 20px 30px 30px;
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.btn-primary {
  background: #007aff;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  flex: 1;
  min-width: 160px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 122, 255, 0.3);
}

.btn-primary:active {
  transform: translateY(0);
}

.btn-secondary {
  background: #f7fafc;
  color: #4a5568;
  border: 2px solid #e2e8f0;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  flex: 1;
  min-width: 160px;
}

.btn-secondary:hover {
  background: #edf2f7;
  border-color: #cbd5e0;
  transform: translateY(-1px);
}

.btn-secondary:active {
  transform: translateY(0);
}

.loading {
  text-align: center;
  color: white;
  font-size: 18px;
  font-weight: 500;
}

/* Contact Support Modal */
.contact-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.contact-modal {
  background: white;
  border-radius: 16px;
  max-width: 800px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.contact-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 30px;
  border-bottom: 1px solid #e2e8f0;
}

.contact-modal-header h2 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #2d3748;
}

.close-modal {
  background: none;
  border: none;
  font-size: 24px;
  color: #a0aec0;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.close-modal:hover {
  background: #f7fafc;
  color: #4a5568;
}

.contact-options {
  display: flex;
  justify-content: center;
  padding: 30px;
}

.contact-options.multiple {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 20px;
}

.contact-option-card {
  background: #f7fafc;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  padding: 24px;
  text-align: center;
  transition: all 0.2s ease;
}

.contact-option-card.single-option {
  max-width: 400px;
  width: 100%;
  border: 2px solid #007aff;
  background: linear-gradient(135deg, #f0f9ff 0%, #ffffff 100%);
}

.contact-option-card:hover {
  border-color: #007aff;
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 122, 255, 0.1);
}

.contact-option-icon {
  font-size: 32px;
  margin-bottom: 12px;
}

.contact-option-card h3 {
  font-size: 18px;
  font-weight: 600;
  color: #2d3748;
  margin: 0 0 8px 0;
}

.contact-option-card p {
  color: #4a5568;
  margin: 4px 0;
  font-size: 14px;
}

.contact-detail {
  font-weight: 600;
  color: #2d3748 !important;
  font-size: 15px !important;
}

.contact-hours {
  font-style: italic;
  color: #718096 !important;
}

.contact-option-btn {
  background: #007aff;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: 12px;
  width: 100%;
}

.contact-option-btn.primary {
  background: linear-gradient(135deg, #007aff 0%, #0056cc 100%);
  padding: 14px 32px;
  font-size: 16px;
  box-shadow: 0 4px 12px rgba(0, 122, 255, 0.3);
}

.contact-option-btn:hover {
  background: #0056cc;
  transform: translateY(-1px);
}

.support-ticket-form {
  padding: 30px;
  border-top: 1px solid #e2e8f0;
  background: #fafafa;
}

.support-ticket-form h3 {
  margin: 0 0 20px 0;
  font-size: 20px;
  font-weight: 600;
  color: #2d3748;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 6px;
  font-weight: 600;
  color: #2d3748;
  font-size: 14px;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 12px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.2s ease;
  font-family: inherit;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  outline: none;
  border-color: #007aff;
  box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 100px;
}

.ticket-form-actions {
  display: flex;
  gap: 12px;
  margin-top: 24px;
}

.ticket-form-actions .btn-primary,
.ticket-form-actions .btn-secondary {
  flex: 1;
  min-width: 120px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .account-status-container {
    padding: 16px;
  }

  .account-status-card {
    border-radius: 12px;
  }

  .status-header {
    padding: 30px 20px 16px;
  }

  .status-title {
    font-size: 24px;
  }

  .status-content {
    padding: 16px 20px;
  }

  .status-actions {
    padding: 16px 20px 24px;
    flex-direction: column;
  }

  .btn-primary,
  .btn-secondary {
    min-width: auto;
    width: 100%;
  }

  .contact-modal {
    margin: 10px;
    max-height: 95vh;
  }

  .contact-options {
    padding: 20px;
  }

  .contact-options.multiple {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .contact-modal-header {
    padding: 20px;
  }

  .support-ticket-form {
    padding: 20px;
  }

  .ticket-form-actions {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .status-header {
    padding: 24px 16px 12px;
  }

  .status-title {
    font-size: 20px;
  }

  .status-content {
    padding: 12px 16px;
  }

  .status-actions {
    padding: 12px 16px 20px;
  }

  .status-section h3 {
    font-size: 16px;
  }

  .btn-primary,
  .btn-secondary {
    padding: 14px 20px;
    font-size: 15px;
  }
}
