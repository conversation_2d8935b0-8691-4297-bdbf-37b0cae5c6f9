:root {
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light;
  color: #213547;
  background-color: #ffffff;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}
a:hover {
  color: #535bf2;
}

html {
  scroll-behavior: auto; /* Changed from smooth to auto for instant scroll */
}

/* Ensure scroll position resets */
html, body {
  scroll-behavior: auto !important;
}

html, body {
  margin: 0;
  padding: 0;
  min-width: 320px;
  min-height: 100vh;
  width: 100%;
  max-width: 100%;
  height: 100%;
  overflow-x: hidden;
  background-color: #ffffff;
  box-sizing: border-box;
  position: relative;
}

#root {
  width: 100%;
  max-width: 100%;
  min-height: 100vh;
  height: 100%;
  background-color: #ffffff;
  box-sizing: border-box;
  position: relative;
  overflow-x: hidden;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #1a1a1a;
  cursor: pointer;
  transition: border-color 0.25s;
}
button:hover {
  border-color: #646cff;
}
button:focus,
button:focus-visible {
  outline: 4px auto -webkit-focus-ring-color;
}

/* Always use light theme */
:root {
  color: #213547;
  background-color: #ffffff;
}

a:hover {
  color: #747bff;
}

button {
  background-color: #f9f9f9;
}

/* CRITICAL FIX: Force black styling for ALL stepper buttons */
/* This must override any Bootstrap or other CSS framework styles */
button.btn.btn-primary,
button.btn.btn-primary.stepper-button-override,
.stepper-button-override,
.btn.btn-primary.stepper-button-override,
button[type="submit"].btn.btn-primary,
.wizard-steps button.btn.btn-primary,
.professional-info button.btn.btn-primary {
  background-color: #000000 !important;
  background-image: none !important;
  border-color: #000000 !important;
  color: #ffffff !important;
  border: 1px solid #000000 !important;
  box-shadow: none !important;
}

button.btn.btn-primary:hover,
button.btn.btn-primary.stepper-button-override:hover,
.stepper-button-override:hover,
.btn.btn-primary.stepper-button-override:hover,
button[type="submit"].btn.btn-primary:hover,
.wizard-steps button.btn.btn-primary:hover,
.professional-info button.btn.btn-primary:hover {
  background-color: #333333 !important;
  background-image: none !important;
  border-color: #333333 !important;
  color: #ffffff !important;
  box-shadow: none !important;
}

button.btn.btn-primary:focus,
button.btn.btn-primary:active,
button.btn.btn-primary.active,
.btn.btn-primary:focus,
.btn.btn-primary:active,
.btn.btn-primary.active {
  background-color: #000000 !important;
  background-image: none !important;
  border-color: #000000 !important;
  color: #ffffff !important;
  box-shadow: none !important;
}
