import React from 'react';
import styled from 'styled-components';

// This is a simple wrapper component that centers its children vertically
const CenteredWrapper = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  padding: 0;
  margin: 0;
`;

const ForgotPasswordWrapper = ({ children }) => {
  return <CenteredWrapper>{children}</CenteredWrapper>;
};

export default ForgotPasswordWrapper;
