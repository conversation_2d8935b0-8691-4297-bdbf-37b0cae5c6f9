const mongoose = require('mongoose');

const individualProfileSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    unique: true
  },
  address: {
    street: {
      type: String,
      trim: true
    },
    city: {
      type: String,
      trim: true
    },
    postalCode: {
      type: String,
      trim: true
    },
    country: {
      type: String,
      default: 'France',
      trim: true
    }
  },
  energyIdentifiers: {
    pdl: {
      type: String,
      trim: true
    },
    prm: {
      type: String,
      trim: true
    },
    rae: {
      type: String,
      trim: true
    }
  },
  currentSupplier: {
    type: String,
    trim: true
  },
  consumptionDetails: {
    annualConsumption: Number,
    averageMonthlyBill: Number
  },
  preferredContactMethod: {
    type: String,
    enum: ['Email', 'Phone', 'SMS'],
    default: 'Email'
  },
  documents: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Document'
  }]
}, {
  timestamps: true
});

// Create indexes
individualProfileSchema.index({ userId: 1 });

const IndividualProfile = mongoose.model('IndividualProfile', individualProfileSchema);

module.exports = IndividualProfile;
