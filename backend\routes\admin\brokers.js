const express = require('express');
const router = express.Router();
const User = require('../../models/User');
const BrokerProfile = require('../../models/BrokerProfile');
const Contract = require('../../models/Contract');
const QuoteRequest = require('../../models/QuoteRequest');
const UserActivity = require('../../models/UserActivity');
const logger = require('../../utils/logger');
const { authMiddleware } = require('../../middleware/auth');

// Apply auth middleware to all admin routes
router.use(authMiddleware);

// Middleware to check if user is admin
const requireAdmin = async (req, res, next) => {
  try {
    const userEmail = req.user?.email;
    if (!userEmail) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    const user = await User.findOne({ email: userEmail });
    if (!user || user.userType !== 'Admin') {
      return res.status(403).json({
        success: false,
        message: 'Admin access required'
      });
    }

    req.adminUser = user;
    next();
  } catch (error) {
    logger.error('Admin middleware error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// Get all brokers with performance KPIs
router.get('/', requireAdmin, async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      status,
      verificationStatus,
      search,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    // Build filter for brokers
    const filter = { userType: 'Broker' };
    
    if (status && status !== 'all') {
      filter.status = status;
    }
    
    if (verificationStatus && verificationStatus !== 'all') {
      filter.verificationStatus = verificationStatus;
    }
    
    if (search) {
      filter.$or = [
        { email: { $regex: search, $options: 'i' } },
        { firstName: { $regex: search, $options: 'i' } },
        { lastName: { $regex: search, $options: 'i' } }
      ];
    }

    const skip = (parseInt(page) - 1) * parseInt(limit);
    const sortOptions = {};
    sortOptions[sortBy] = sortOrder === 'desc' ? -1 : 1;

    // Get brokers with their profiles
    const brokers = await User.find(filter)
      .select('-__v')
      .sort(sortOptions)
      .skip(skip)
      .limit(parseInt(limit))
      .lean();

    // Get broker profiles and performance data
    const brokersWithKPIs = await Promise.all(brokers.map(async (broker) => {
      const profile = await BrokerProfile.findOne({ userId: broker._id }).lean();
      
      // Calculate performance KPIs
      const [
        totalQuotes,
        totalContracts,
        totalCommissions,
        activeClients,
        monthlyQuotes,
        monthlyContracts
      ] = await Promise.all([
        QuoteRequest.countDocuments({ brokerId: broker._id }),
        Contract.countDocuments({ brokerId: broker._id }),
        Contract.aggregate([
          { $match: { brokerId: broker._id, 'commissions.commissionPaid': true } },
          { $group: { _id: null, total: { $sum: '$commissions.brokerCommission' } } }
        ]),
        Contract.countDocuments({ brokerId: broker._id, status: 'Active' }),
        QuoteRequest.countDocuments({ 
          brokerId: broker._id,
          createdAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }
        }),
        Contract.countDocuments({ 
          brokerId: broker._id,
          createdAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }
        })
      ]);

      const conversionRate = totalQuotes > 0 ? ((totalContracts / totalQuotes) * 100).toFixed(1) : 0;
      const totalCommissionAmount = totalCommissions[0]?.total || 0;

      return {
        ...broker,
        profile,
        kpis: {
          totalQuotes,
          totalContracts,
          totalCommissions: totalCommissionAmount,
          activeClients,
          monthlyQuotes,
          monthlyContracts,
          conversionRate: parseFloat(conversionRate)
        }
      };
    }));

    // Get total count for pagination
    const totalBrokers = await User.countDocuments(filter);
    const totalPages = Math.ceil(totalBrokers / parseInt(limit));

    res.json({
      success: true,
      data: {
        brokers: brokersWithKPIs,
        pagination: {
          currentPage: parseInt(page),
          totalPages,
          totalBrokers,
          hasNextPage: parseInt(page) < totalPages,
          hasPrevPage: parseInt(page) > 1
        }
      }
    });

  } catch (error) {
    logger.error('Error fetching brokers:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch brokers'
    });
  }
});

// Get broker details with full performance data
router.get('/:brokerId/details', requireAdmin, async (req, res) => {
  try {
    const { brokerId } = req.params;

    const broker = await User.findById(brokerId).select('-__v');
    if (!broker || broker.userType !== 'Broker') {
      return res.status(404).json({
        success: false,
        message: 'Broker not found'
      });
    }

    const profile = await BrokerProfile.findOne({ userId: brokerId })
      .populate('supplierPartnerships', 'firstName lastName email')
      .populate('verificationDocuments');

    // Get detailed performance metrics
    const [
      recentQuotes,
      recentContracts,
      monthlyStats,
      clientList
    ] = await Promise.all([
      QuoteRequest.find({ brokerId })
        .populate('userId', 'firstName lastName email')
        .sort({ createdAt: -1 })
        .limit(10),
      Contract.find({ brokerId })
        .populate('userId', 'firstName lastName email')
        .populate('supplierId', 'firstName lastName email')
        .sort({ createdAt: -1 })
        .limit(10),
      getMonthlyStats(brokerId),
      getClientList(brokerId)
    ]);

    res.json({
      success: true,
      data: {
        broker,
        profile,
        recentQuotes,
        recentContracts,
        monthlyStats,
        clientList
      }
    });

  } catch (error) {
    logger.error('Error fetching broker details:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch broker details'
    });
  }
});

// Helper function to get monthly statistics
async function getMonthlyStats(brokerId) {
  const sixMonthsAgo = new Date();
  sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

  const monthlyData = await Contract.aggregate([
    {
      $match: {
        brokerId: brokerId,
        createdAt: { $gte: sixMonthsAgo }
      }
    },
    {
      $group: {
        _id: {
          year: { $year: '$createdAt' },
          month: { $month: '$createdAt' }
        },
        contracts: { $sum: 1 },
        commissions: { $sum: '$commissions.brokerCommission' }
      }
    },
    {
      $sort: { '_id.year': 1, '_id.month': 1 }
    }
  ]);

  return monthlyData;
}

// Helper function to get client list
async function getClientList(brokerId) {
  const clients = await Contract.aggregate([
    {
      $match: { brokerId: brokerId }
    },
    {
      $group: {
        _id: '$userId',
        contractsCount: { $sum: 1 },
        totalValue: { $sum: '$contractDetails.estimatedAnnualCost' },
        lastContract: { $max: '$createdAt' }
      }
    },
    {
      $lookup: {
        from: 'users',
        localField: '_id',
        foreignField: '_id',
        as: 'user'
      }
    },
    {
      $unwind: '$user'
    },
    {
      $project: {
        _id: 1,
        contractsCount: 1,
        totalValue: 1,
        lastContract: 1,
        'user.firstName': 1,
        'user.lastName': 1,
        'user.email': 1,
        'user.userType': 1
      }
    },
    {
      $sort: { lastContract: -1 }
    },
    {
      $limit: 20
    }
  ]);

  return clients;
}

// Approve or reject broker application
router.patch('/:brokerId/verification', requireAdmin, async (req, res) => {
  try {
    const { brokerId } = req.params;
    const { verificationStatus, notes } = req.body;

    if (!['Verified', 'Rejected'].includes(verificationStatus)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid verification status'
      });
    }

    const broker = await User.findById(brokerId);
    if (!broker || broker.userType !== 'Broker') {
      return res.status(404).json({
        success: false,
        message: 'Broker not found'
      });
    }

    // Update broker verification status
    broker.verificationStatus = verificationStatus;
    if (verificationStatus === 'Verified') {
      broker.status = 'Approved'; // Use Approved instead of Active for consistency

      // Update verification details
      if (!broker.verificationDetails) {
        broker.verificationDetails = {};
      }
      broker.verificationDetails.reviewedAt = new Date();
      broker.verificationDetails.reviewedBy = req.adminUser._id;
      broker.verificationDetails.adminNotes = notes;
    } else if (verificationStatus === 'Rejected') {
      broker.status = 'Rejected';
    }
    await broker.save();

    // Update broker profile with notes
    await BrokerProfile.findOneAndUpdate(
      { userId: brokerId },
      {
        verificationStatus,
        verificationNotes: notes,
        verifiedAt: verificationStatus === 'Verified' ? new Date() : undefined,
        verifiedBy: req.adminUser._id
      }
    );

    // Log activity
    const action = verificationStatus === 'Verified' ? 'ApplicationApproved' : 'ApplicationRejected';
    await UserActivity.logActivity({
      userId: brokerId,
      activityType: 'Other',
      description: `Broker application ${verificationStatus.toLowerCase()} by admin`,
      details: {
        verificationStatus,
        notes,
        adminId: req.adminUser._id,
        adminEmail: req.adminUser.email,
        action: action
      },
      severity: 'High',
      isSystemGenerated: true
    });

    logger.info(`Admin ${req.adminUser.email} ${verificationStatus.toLowerCase()} broker ${broker.email}`);

    // Send email notification to broker about approval/rejection
    try {
      const emailService = require('../../services/emailService');

      if (verificationStatus === 'Verified') {
        // Send approval email with login link
        await emailService.sendBrokerApprovalEmail({
          brokerEmail: broker.email,
          brokerName: `${broker.firstName} ${broker.lastName}`,
          loginUrl: `${process.env.FRONTEND_URL || 'http://localhost:8080'}/login`,
          adminNotes: notes
        });
        logger.info('Broker approval email sent successfully');
      } else if (verificationStatus === 'Rejected') {
        // Send rejection email
        await emailService.sendBrokerRejectionEmail({
          brokerEmail: broker.email,
          brokerName: `${broker.firstName} ${broker.lastName}`,
          rejectionReason: notes,
          supportEmail: process.env.SUPPORT_EMAIL || '<EMAIL>'
        });
        logger.info('Broker rejection email sent successfully');
      }
    } catch (emailError) {
      logger.error('Failed to send broker notification email:', emailError);
      // Don't fail the approval if email fails
    }

    res.json({
      success: true,
      message: `Broker application ${verificationStatus.toLowerCase()} successfully`,
      data: broker
    });

  } catch (error) {
    logger.error('Error updating broker verification:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update broker verification'
    });
  }
});

// Assign client to broker
router.post('/:brokerId/assign-client', requireAdmin, async (req, res) => {
  try {
    const { brokerId } = req.params;
    const { clientId, assignmentType = 'manual' } = req.body;

    const broker = await User.findById(brokerId);
    const client = await User.findById(clientId);

    if (!broker || broker.userType !== 'Broker') {
      return res.status(404).json({
        success: false,
        message: 'Broker not found'
      });
    }

    if (!client || !['Individual', 'Professional'].includes(client.userType)) {
      return res.status(404).json({
        success: false,
        message: 'Client not found'
      });
    }

    // Update broker profile to add client
    await BrokerProfile.findOneAndUpdate(
      { userId: brokerId },
      {
        $addToSet: {
          assignedClients: {
            clientId,
            assignedAt: new Date(),
            assignedBy: req.adminUser._id,
            assignmentType
          }
        }
      }
    );

    // Log activities for both broker and client
    await Promise.all([
      UserActivity.logActivity({
        userId: brokerId,
        activityType: 'Other',
        description: `Client ${client.firstName} ${client.lastName} assigned by admin`,
        details: { clientId, assignmentType, adminId: req.adminUser._id },
        severity: 'Normal',
        isSystemGenerated: true
      }),
      UserActivity.logActivity({
        userId: clientId,
        activityType: 'Other',
        description: `Assigned to broker ${broker.firstName} ${broker.lastName} by admin`,
        details: { brokerId, assignmentType, adminId: req.adminUser._id },
        severity: 'Normal',
        isSystemGenerated: true
      })
    ]);

    logger.info(`Admin ${req.adminUser.email} assigned client ${client.email} to broker ${broker.email}`);

    res.json({
      success: true,
      message: 'Client assigned to broker successfully'
    });

  } catch (error) {
    logger.error('Error assigning client to broker:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to assign client to broker'
    });
  }
});

// Get broker performance analytics
router.get('/:brokerId/analytics', requireAdmin, async (req, res) => {
  try {
    const { brokerId } = req.params;
    const { period = '6months' } = req.query;

    let startDate;
    switch (period) {
      case '1month':
        startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
        break;
      case '3months':
        startDate = new Date(Date.now() - 90 * 24 * 60 * 60 * 1000);
        break;
      case '6months':
        startDate = new Date(Date.now() - 180 * 24 * 60 * 60 * 1000);
        break;
      case '1year':
        startDate = new Date(Date.now() - 365 * 24 * 60 * 60 * 1000);
        break;
      default:
        startDate = new Date(Date.now() - 180 * 24 * 60 * 60 * 1000);
    }

    const [
      quotesTrend,
      contractsTrend,
      commissionsTrend,
      topSuppliers,
      clientTypes
    ] = await Promise.all([
      getQuotesTrend(brokerId, startDate),
      getContractsTrend(brokerId, startDate),
      getCommissionsTrend(brokerId, startDate),
      getTopSuppliers(brokerId, startDate),
      getClientTypes(brokerId)
    ]);

    res.json({
      success: true,
      data: {
        period,
        quotesTrend,
        contractsTrend,
        commissionsTrend,
        topSuppliers,
        clientTypes
      }
    });

  } catch (error) {
    logger.error('Error fetching broker analytics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch broker analytics'
    });
  }
});

// Helper functions for analytics
async function getQuotesTrend(brokerId, startDate) {
  return await QuoteRequest.aggregate([
    {
      $match: {
        brokerId: brokerId,
        createdAt: { $gte: startDate }
      }
    },
    {
      $group: {
        _id: {
          year: { $year: '$createdAt' },
          month: { $month: '$createdAt' },
          week: { $week: '$createdAt' }
        },
        count: { $sum: 1 }
      }
    },
    {
      $sort: { '_id.year': 1, '_id.month': 1, '_id.week': 1 }
    }
  ]);
}

async function getContractsTrend(brokerId, startDate) {
  return await Contract.aggregate([
    {
      $match: {
        brokerId: brokerId,
        createdAt: { $gte: startDate }
      }
    },
    {
      $group: {
        _id: {
          year: { $year: '$createdAt' },
          month: { $month: '$createdAt' },
          week: { $week: '$createdAt' }
        },
        count: { $sum: 1 },
        totalValue: { $sum: '$contractDetails.estimatedAnnualCost' }
      }
    },
    {
      $sort: { '_id.year': 1, '_id.month': 1, '_id.week': 1 }
    }
  ]);
}

async function getCommissionsTrend(brokerId, startDate) {
  return await Contract.aggregate([
    {
      $match: {
        brokerId: brokerId,
        createdAt: { $gte: startDate },
        'commissions.brokerCommission': { $exists: true }
      }
    },
    {
      $group: {
        _id: {
          year: { $year: '$createdAt' },
          month: { $month: '$createdAt' }
        },
        totalCommissions: { $sum: '$commissions.brokerCommission' },
        paidCommissions: {
          $sum: {
            $cond: [
              '$commissions.commissionPaid',
              '$commissions.brokerCommission',
              0
            ]
          }
        }
      }
    },
    {
      $sort: { '_id.year': 1, '_id.month': 1 }
    }
  ]);
}

async function getTopSuppliers(brokerId, startDate) {
  return await Contract.aggregate([
    {
      $match: {
        brokerId: brokerId,
        createdAt: { $gte: startDate }
      }
    },
    {
      $group: {
        _id: '$supplierId',
        contractsCount: { $sum: 1 },
        totalValue: { $sum: '$contractDetails.estimatedAnnualCost' }
      }
    },
    {
      $lookup: {
        from: 'users',
        localField: '_id',
        foreignField: '_id',
        as: 'supplier'
      }
    },
    {
      $unwind: '$supplier'
    },
    {
      $sort: { contractsCount: -1 }
    },
    {
      $limit: 5
    }
  ]);
}

async function getClientTypes(brokerId) {
  return await Contract.aggregate([
    {
      $match: { brokerId: brokerId }
    },
    {
      $lookup: {
        from: 'users',
        localField: 'userId',
        foreignField: '_id',
        as: 'client'
      }
    },
    {
      $unwind: '$client'
    },
    {
      $group: {
        _id: '$client.userType',
        count: { $sum: 1 }
      }
    }
  ]);
}

module.exports = router;
