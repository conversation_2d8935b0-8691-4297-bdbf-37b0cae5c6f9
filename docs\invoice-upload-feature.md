# Invoice Upload Feature

This document provides an overview of the invoice upload feature, which allows users to upload energy bills to AWS S3 and store metadata in MongoDB.

## Feature Overview

The invoice upload feature enables users to:
- Upload energy bills (PDF, images, or documents)
- Add metadata about the invoice (date, amount, provider, etc.)
- Store files securely in AWS S3
- Save metadata in MongoDB for later retrieval

## Architecture

### Frontend Components
- `InvoiceUpload.jsx`: Reusable component for file selection and metadata input
- `UploadFirstInvoicePage.jsx`: Page for first-time invoice upload
- `invoice.service.js`: Service for communicating with the backend API

### Backend Components
- `Invoice.js`: MongoDB model for storing invoice metadata
- `invoiceController.js`: Controller for handling file uploads and metadata
- `invoices.js`: API routes for invoice operations

### AWS Integration
- Files are stored in the S3 bucket: `energy-app-uat-backend-files`
- Files are organized in folders: `invoices/[userType]/[cognitoId]/[filename]`
- Access is managed through IAM roles or credentials

## Setup Requirements

### Local Development
For local development, you need:
1. AWS credentials with S3 access
2. MongoDB connection
3. Environment variables in `.env` file:
   ```
   AWS_S3_BUCKET_NAME=energy-app-uat-backend-files
   AWS_ACCESS_KEY_ID=your_access_key_id
   AWS_SECRET_ACCESS_KEY=your_secret_access_key
   AWS_REGION=eu-west-3
   ```

### AWS Deployment
For AWS deployment:
1. Create an IAM role with S3 permissions
2. Attach the role to your AWS service (EC2, ECS, Lambda, etc.)
3. No hardcoded credentials needed - the application will use the instance role

## User Flow

1. User navigates to the invoice upload page
2. User selects a file to upload
3. User fills in metadata about the invoice
4. User clicks "Upload Invoice"
5. File is uploaded to S3 and metadata is saved to MongoDB
6. User receives confirmation of successful upload

## API Endpoints

### Upload Invoice
- **URL**: `/api/invoices/upload`
- **Method**: `POST`
- **Auth**: Required
- **Body**: FormData with file and metadata
- **Response**: Invoice metadata with S3 reference

### Get User Invoices
- **URL**: `/api/invoices/user/:cognitoId`
- **Method**: `GET`
- **Auth**: Required
- **Response**: List of user's invoices

### Get Invoice by ID
- **URL**: `/api/invoices/:id`
- **Method**: `GET`
- **Auth**: Required
- **Response**: Invoice details

### Get Invoice Download URL
- **URL**: `/api/invoices/:id/download`
- **Method**: `GET`
- **Auth**: Required
- **Response**: Pre-signed S3 URL for downloading

## Security Considerations

- Files are stored with private ACL in S3
- Access is controlled through IAM permissions
- File types are validated on the server
- File size is limited to 10MB
- Authentication is required for all operations

## Future Enhancements

1. **Invoice Processing**: Implement OCR to extract data from invoices
2. **Invoice Listing**: Create a page to list all uploaded invoices
3. **Invoice Details**: Create a page to view invoice details
4. **Bulk Upload**: Allow uploading multiple invoices at once
5. **Invoice Categories**: Add ability to categorize invoices

## Troubleshooting

### Common Issues

1. **Upload Fails**:
   - Check AWS credentials
   - Verify S3 bucket exists and is accessible
   - Check file size and type

2. **Cannot Retrieve Invoices**:
   - Verify MongoDB connection
   - Check authentication status
   - Verify user has permission to access invoices

3. **S3 Access Denied**:
   - Check IAM permissions
   - Verify bucket policy
   - Check AWS region configuration

For detailed setup instructions, see [S3 Setup Guide](./s3-setup.md).
